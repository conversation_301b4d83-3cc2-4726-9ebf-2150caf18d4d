-- Start a transaction to ensure all or nothing execution
BEGIN;

-- Update regEarningRateId for Timesheets
UPDATE
  public."TimeSheets"
SET
  reg_earning_rate_id = (
    SELECT
      id
    FROM
      "EarningRates" er
    WHERE
      er.user_id = "TimeSheets".worker_id
      AND er.type = 'REG'
      AND er.active = TRUE
    ORDER BY
      er.start_date DESC
    LIMIT
      1
  )
WHERE
  reg_earning_rate_id IS NULL;

-- or some other condition indicating they need updating
-- Update otEarningRateId for Timesheets
UPDATE
  public."TimeSheets"
SET
  ot_earning_rate_id = (
    SELECT
      id
    FROM
      "EarningRates" er
    WHERE
      er.user_id = "TimeSheets".worker_id
      AND er.type = 'OT'
      AND er.active = TRUE
    ORDER BY
      er.start_date DESC
    LIMIT
      1
  )
WHERE
  ot_earning_rate_id IS NULL;

-- or some other condition indicating they need updating
-- Commit the transaction
COMMIT;