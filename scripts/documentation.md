## How to onboard orgs

We expect customer to provide a Spreadsheet tha we can easily convert to CSV, 
    based on the following spreadsheet: https://docs.google.com/spreadsheets/d/1FyPp4W0kT0w-K4-Qr0NiQPvXneBL22cKxi3KI724YXM/edit#gid=0

### Manual steps

Go to the spreadsheet, open the page you which to export and download it as a csv file.

![](doc_images/export.png)

Then place the csv file inside the scripts folder.

### Runing the script

`node importOrganization.js customer.csv The_ORGANIZATION_NAME`

You must provide the csv as input and the name of the org, since the name is not contained in the CSV file.

The script will run some basics checks on the data, if the data is not clean it will stop and inform you.

Note: A blank hourlyWage will not stop the scripting since some admins/foreman don't provide it.

