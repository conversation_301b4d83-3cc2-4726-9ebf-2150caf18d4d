import fs from 'fs';
import readline from 'readline';

import axios from 'axios';

class OrgData { 
    constructor(){
        this.employees = [];
        this.employeesMap = {};
        this.projects = [];
        this.costCodes = [];

    }

    addCostCode(line){
        const [name, number] = line.split(',');
        this.costCodes.push({
            name, number
        });
    }

    addProject(line){
        const tokens = line.split(',');
        const name = tokens[0];
        let address = tokens[1];
        console.log("projec tokens : ", tokens);

        let lastTokenAt = 1;
        // for(let i = 2 ; i<tokens.length; i++){
        //     if(tokens[i].substr(tokens[i].length-1) === '"'){
        //         address += "," + tokens[i].substr(0, tokens[i].length -1 );
        //         lastTokenAt=i;
        //         break;
        //     }
        //     else {
        //         address += "," + tokens[i]
        //     }

        // }

        let projectNumber = tokens[lastTokenAt+1],
            customerName = tokens[lastTokenAt+2],
            foreman = tokens[lastTokenAt+3];

        this.projects.push({
            name, address, projectNumber, customerName, foreman
        });
    }

    addEmployee(line){
        let [name , role, employeeId, phoneNumber, hourlyRate] = line.split(',');
        role = role.toUpperCase();

        if(this.employeesMap[name]){
            throw new Error("Duplicate Employee");
        }

        this.employeesMap[name] = this.employees.length;

        phoneNumber = phoneNumber.replaceAll('-', '');
        
        if(hourlyRate=== ""){
            hourlyRate=0;
        }

        this.employees.push({
            name,
            role,
            employeeId,
            phoneNumber,
            hourlyRate
        })
    }

    isDataValid(){
        let isValid = true;
        for(let employee of this.employees){
            if( employee.name === "" || employee.role ==="" 
                || employee.employeeId === "" || employee.phoneNumber ===""
                || employee.phoneNumber.length < 12 || employee.hourlyRate === ""
            )
            {
                console.log("Wrong data for employee : ", employee);
                isValid = false;

            }
        }


        for(let project of this.projects){
            if(project.name == "" || project.address === "" || project.projectNumber == ""
                || project.customerName === "" || project.foreman === ""
            ){
                console.log("Wrong project data ", project);
                isValid = false;
            }

            if(!this.employeesMap[project.foreman]){
                console.log("No valid Foreman assigned to project ". project);
                isValid = false;
            }
        }

        for(let costCode of this.costCodes){
            if(costCode.name === "" || costCode.number === ""){
                console.log("Invalid Cost Code : ", CostCode)
                isValid = false;
            }
        }

        return isValid;
    }
}

const importFromCSV = async (orgData, csvFilePath, organizationName) => {

    const fileStream = fs.createReadStream(csvFilePath);

    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });


    let mode = "SEARCHING";

    for await (const line of rl) {

        if(mode==='SEARCHING'){
            if(line==="Full Name,Position (Foreman/Field Worker etc.),Employee ID,Mobile No,Hourly Wage"){
                mode = 'EMPLOYEE';

                console.log("found employee");
            }

            if(line==='Project Name,Address,Project Number,Customer Name,Foreman Name'){
                mode='PROJECT';
            }

            if(line==='Cost Code Title,Cost Code Number,,,'){
                mode='COSTCODE'
            }
        }
        else {
            if(line===',,,,'){
                mode='SEARCHING';
            }
            else {
                switch(mode){
                    case 'COSTCODE' :
                        orgData.addCostCode(line);
                    break;
                    case 'EMPLOYEE':
                        orgData.addEmployee(line);
                    break;
                    case 'PROJECT':
                        orgData.addProject(line);
                    break;
                }
            }
        }
    }
};

const csvFilePath = process.argv[2],
      organizationName = process.argv[3];

const orgData =  new OrgData();

importFromCSV(orgData, csvFilePath, organizationName);

const API_URL = 'http://localhost:3000';

const instance = axios.create({
    baseURL: `${API_URL}/_migrationAPI/csvRawImport`,
    timeout: 5000,
    headers: {
        'x-api-key' : "YEFYhOur0UVjnNZsUo0LmjZOSXZNu0N0MoYgu",
        'x-api-key-migration': '66faebd77cea6850ae6cad49646237dc6f90ad8948'
    }
  });



const migrateData = async ( org , orgName) => {
    let orgId;

    const response =  await instance({
        method: 'POST',
        url : '/organization',
        data : {
            name : organizationName
        }
    });

    orgId = response.data.data.orgId;
    console.log("orgID : ", orgId);

    
    for(let employee of org.employees){
        const response = await instance({
            method: 'POST',
            url : "/employee",
            data : {
                organizationId : orgId,
                ...employee
            }
        });

        orgData.employeesMap[employee.name] = response.data.data.userId;        
    }

    for(let project of org.projects){
        console.log("Project foreman : ", project.foreman)
        await instance({
            method: 'POST',
            url : "/projects",
            data : {
                organizationId : orgId,
                foremanId : orgData.employeesMap[project.foreman], 
                ...project
            }
        });
    }


    for( let costCode of org.costCodes){
        await instance({
            method: 'POST',
            url : "/costCodes",
            data : {
                organizationId : orgId,
                ...costCode
            }
        });
    }
}  
  
if( orgData.isDataValid()){
    migrateData(orgData);
}
