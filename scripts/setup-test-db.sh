#!/bin/bash

# Extract postgres user from .env file
DB_URI=$(grep POSTGRESS_DB_URI .env | cut -d'=' -f2 | tr -d '"')
PG_USER=$(echo $DB_URI | sed -n 's/postgres:\/\/\([^@]*\)@.*/\1/p')

if [ -z "$PG_USER" ]; then
    echo "Error: Could not extract postgres user from .env"
    exit 1
fi

echo "Using postgres user: $PG_USER"

# Update .env.test with the correct user
sed -i.bak "s|postgres://[^@]*@|postgres://$PG_USER@|" .env.test
rm -f .env.test.bak

# Check if hammr-test database exists
if psql -lqt | cut -d \| -f 1 | grep -qw hammr-test; then
    echo "Database hammr-test already exists"
else
    # Create the test database
    echo "Creating hammr-test database..."
    createdb hammr-test
    
    if [ $? -eq 0 ]; then
        echo "Successfully created hammr-test database"
    else
        echo "Error: Failed to create hammr-test database"
        exit 1
    fi
fi

# Create PostGIS extension in hammr-test database
echo "Creating PostGIS extension in hammr-test database..."
psql -d hammr-test -c "CREATE EXTENSION IF NOT EXISTS postgis;"

if [ $? -eq 0 ]; then
    echo "Successfully created PostGIS extension"
else
    echo "Error: Failed to create PostGIS extension. Make sure PostGIS is installed."
    echo "On macOS: brew install postgis"
    echo "On Ubuntu: sudo apt-get install postgis postgresql-<version>-postgis-<version>"
    exit 1
fi

# Verify PostGIS installation
echo "Verifying PostGIS installation..."
psql -d hammr-test -c "SELECT PostGIS_version();"

if [ $? -eq 0 ]; then
    echo "PostGIS verification successful"
else
    echo "PostGIS verification failed"
    exit 1
fi

echo "Test environment setup complete!" 