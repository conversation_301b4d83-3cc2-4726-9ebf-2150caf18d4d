// backfill-dot-rates.ts
import { Op } from "sequelize";
// Make sure you're importing sequelize correctly
import { EarningRateService } from "../app/services/earningrate";
import { CheckEarningRateManager } from "../app/services/checkearningratemanager";
import { User, EarningRate, Organization, TimeSheet, Classification } from "../app/models";
import { EARNING_RATE_TYPES } from "../app/models/earningrate";
import { createRelations } from "../app/models/relations";
import { CheckService } from "../app/services/check";

// Initialize database relations
createRelations();

// Metadata key to identify rates created by this script
const SCRIPT_METADATA_KEY = "createdByBackfillDotScript";

/**
 * Backfill DOT earning codes for classifications in Check and update references
 *
 * @param organizationId The ID of the organization to process
 */
async function backfillClassificationDotEarningCodes(organizationId: number) {
  console.log(`\n=== Starting Classification DOT Earning Code Backfill for Organization ${organizationId} ===`);

  const organization = await Organization.findByPk(organizationId);

  if (!organization || !organization.checkCompanyId) {
    console.log("Organization is not set up for payroll in Check. Skipping classification DOT code backfill.");

    return;
  }

  console.log(
    `Processing organization: ${organization.name} (ID: ${organizationId}) with Check company ID: ${organization.checkCompanyId}`
  );

  // Find all classifications in this organization
  const classifications = await Classification.findAll({
    where: {
      organizationId: organizationId,
      // Only get classifications that already have regular and OT earning codes but no DOT code
      [Op.and]: [
        { checkRegEarningCodeId: { [Op.ne]: null } },
        { checkOtEarningCodeId: { [Op.ne]: null } },
        {
          [Op.or]: [{ checkDotEarningCodeId: null }, { checkDotEarningCodeId: "" }],
        },
      ],
    },
  });

  console.log(`Found ${classifications.length} classifications that need DOT earning codes`);

  if (classifications.length === 0) {
    return;
  }

  const checkService = new CheckService();

  // Process each classification
  for (const classification of classifications) {
    try {
      console.log(`Processing classification ${classification.id} - ${classification.name}`);

      // Create a DOT earning code in Check based on the classification
      const checkDotEarningCodePayload = checkService.handleCreateCheckPayload(
        {
          name: `${classification.name}`,
          checkCompanyId: organization.checkCompanyId,
          type: "double_overtime",
        },
        "earning_codes",
        "POST"
      );

      let checkDotEarningCode;
      try {
        // Make the API call to Check
        checkDotEarningCode = await checkService.post("/earning_codes", checkDotEarningCodePayload);
        console.log(`Created Check DOT earning code: ${checkDotEarningCode.id}`);
      } catch (checkError: any) {
        // Handle the case where a deactivated earning code with same name already exists
        if (checkError?.type === "earning_code_already_exists") {
          console.log(
            `Earning code already exists for classification ${classification.id} - attempting to find and reactivate it`
          );

          try {
            // Attempt to find the existing earning code by listing all earning codes for the company
            // and filtering by name and type
            const allEarningCodes = await checkService.get(`/earning_codes?company=${organization.checkCompanyId}`);

            const matchingEarningCode = allEarningCodes.results.find(
              (code: any) =>
                code.name === checkDotEarningCodePayload.name && code.type === "double_overtime" && !code.active // Find the inactive one
            );

            if (matchingEarningCode) {
              console.log(`Found inactive DOT earning code ${matchingEarningCode.id} - reactivating`);

              // Reactivate the earning code by setting active=true
              checkDotEarningCode = await checkService.patch(`/earning_codes/${matchingEarningCode.id}`, {
                active: true,
              });

              console.log(`Successfully reactivated DOT earning code: ${matchingEarningCode.id}`);
            } else {
              // If we couldn't find an inactive matching earning code, try looking for an active one
              const activeMatchingCode = allEarningCodes.results.find(
                (code: any) =>
                  code.name === checkDotEarningCodePayload.name && code.type === "double_overtime" && code.active
              );

              if (activeMatchingCode) {
                console.log(`Found already active DOT earning code ${activeMatchingCode.id} - using this one`);
                checkDotEarningCode = activeMatchingCode;
              } else {
                console.error(
                  `Could not find any existing DOT earning code for ${classification.name} despite the error`
                );
                // Skip to next classification if we can't find or reactivate the DOT earning code
                continue;
              }
            }
          } catch (findError) {
            console.error(
              `Error finding/reactivating existing DOT earning code for classification ${classification.id}:`,
              findError
            );
            // Skip to next classification if we can't find or reactivate the DOT earning code
            continue;
          }
        } else {
          console.error(
            `Error creating DOT earning code in Check for classification ${classification.id}:`,
            checkError?.type || checkError
          );
          // Skip to next classification if we can't create the DOT earning code
          continue;
        }
      }

      // Update the classification with the new or reactivated DOT earning code ID
      await classification.update({
        checkDotEarningCodeId: checkDotEarningCode.id,
        metadata: {
          ...classification.metadata,
          [SCRIPT_METADATA_KEY]: true,
          dotEarningCodeCreatedAt: new Date().toISOString(),
          reactivated: checkDotEarningCode.reactivated || false,
        },
      });

      console.log(
        `Updated classification ${classification.id} with Check DOT earning code ID: ${checkDotEarningCode.id}`
      );

      // Update all other classifications with the same name and organization ID
      try {
        // Find all classifications with the same name and organization ID
        const sameNameClassifications = await Classification.findAll({
          where: {
            organizationId: organizationId,
            name: classification.name,
            id: { [Op.ne]: classification.id }, // Exclude the current classification we just updated
            [Op.or]: [{ checkDotEarningCodeId: null }, { checkDotEarningCodeId: "" }],
          },
        });

        if (sameNameClassifications.length > 0) {
          console.log(
            `Found ${sameNameClassifications.length} other classifications with same name "${classification.name}" to update`
          );

          // Update each classification to use the same DOT earning code ID
          for (const sameNameClassification of sameNameClassifications) {
            try {
              await sameNameClassification.update({
                checkDotEarningCodeId: checkDotEarningCode.id,
                metadata: {
                  ...sameNameClassification.metadata,
                  [SCRIPT_METADATA_KEY]: true,
                  dotEarningCodeCreatedAt: new Date().toISOString(),
                  sharedFromClassificationId: classification.id,
                },
              });

              console.log(
                `Updated classification ${sameNameClassification.id} with shared Check DOT earning code ID: ${checkDotEarningCode.id}`
              );
            } catch (updateError) {
              console.error(
                `Error updating classification ${sameNameClassification.id} with shared DOT earning code:`,
                updateError
              );
            }
          }
        }
      } catch (error) {
        console.error(`Error finding/updating classifications with same name "${classification.name}":`, error);
      }
    } catch (error) {
      console.error(`Error processing classification ${classification.id}:`, error);
    }
  }

  console.log("Classification DOT earning code backfill completed");
}

/**
 * Script to clean up Double Overtime earning rates created by this script.
 * This undoes the changes made by the backfill script.
 *
 * @param organizationId The ID of the organization to process
 */
async function cleanupDoubleOvertimeRates(organizationId: number) {
  console.log(`Cleaning up DOT rates for organization ${organizationId}...`);

  // Find all DOT rates in the organization that were likely created by this script
  // First try to find rates with our metadata marker
  let dotRates = await EarningRate.findAll({
    where: {
      organization_id: organizationId,
      type: "DOT",
      // Looking for rates with our metadata marker
      // JSON containment operators vary by database, this works for PostgreSQL
      metadata: {
        [Op.ne]: null,
      },
    },
  });

  // Filter to only include rates with our metadata key
  dotRates = dotRates.filter((rate) => {
    try {
      const metadata = rate.metadata || {};

      return metadata[SCRIPT_METADATA_KEY] === true;
    } catch (e) {
      return false;
    }
  });

  // Fallback: if we didn't find any with metadata, look for naming pattern
  if (dotRates.length === 0) {
    console.log("No rates found with script metadata, falling back to name-based detection");
    dotRates = await EarningRate.findAll({
      where: {
        organization_id: organizationId,
        type: "DOT",
        name: {
          [Op.like]: "%- Double Overtime",
        },
      },
    });
  }

  console.log(`Found ${dotRates.length} DOT rates to deactivate and delete`);

  // First deactivate all rates to avoid Check validation issues
  for (const rate of dotRates) {
    try {
      // First deactivate the rate
      await rate.update({
        active: false,
        metadata: {
          ...rate.metadata,
          deactivatedAt: new Date().toISOString(),
        },
      });
      console.log(`Deactivated DOT rate ${rate.id} for user ${rate.userId}`);

      // If the rate has a Check ID, we should deactivate it in Check too
      if (rate.checkEarningRateId) {
        try {
          const checkEarningRateManager = new CheckEarningRateManager();
          const user = await User.findByPk(rate.userId);
          if (user?.checkEmployeeId) {
            await checkEarningRateManager.deactivateEarningRate(rate.checkEarningRateId);
            console.log(`Deactivated DOT rate ${rate.id} in Check`);
          }
        } catch (checkError) {
          console.error(`Error deactivating DOT rate ${rate.id} in Check:`, checkError);
        }
      }
    } catch (error) {
      console.error(`Error deactivating DOT rate ${rate.id}:`, error);
    }
  }

  // Wait a moment to ensure Check has processed deactivations
  console.log("Waiting 3 seconds for deactivations to process...");
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // FIXED: First reset dotEarningRateId on timesheets BEFORE deleting rates
  // to avoid foreign key constraint violations
  console.log("Resetting dotEarningRateId on timesheets...");
  const dotRateIds = dotRates.map((rate) => rate.id);

  // Only reset timesheets that reference our DOT rates
  const updated = await TimeSheet.update(
    { dotEarningRateId: null },
    {
      where: {
        organization_id: organizationId,
        dotEarningRateId: {
          [Op.in]: dotRateIds,
        },
      },
    }
  );

  console.log(`Reset dotEarningRateId on ${updated[0]} timesheets`);

  // Now delete the rates after removing references
  for (const rate of dotRates) {
    try {
      await rate.destroy();
      console.log(`Deleted DOT rate ${rate.id} for user ${rate.userId}`);
    } catch (error) {
      console.error(`Error deleting DOT rate ${rate.id}:`, error);
    }
  }

  // Now clean up classification DOT earning codes
  console.log("\nCleaning up Classification DOT earning codes...");

  // Find classifications with DOT earning codes created by this script
  let classifications = await Classification.findAll({
    where: {
      organizationId: organizationId,
      checkDotEarningCodeId: { [Op.ne]: null },
      metadata: { [Op.ne]: null },
    },
  });

  // Filter to only include classifications with our metadata key
  classifications = classifications.filter((classification) => {
    try {
      const metadata = classification.metadata || {};

      return metadata[SCRIPT_METADATA_KEY] === true;
    } catch (e) {
      return false;
    }
  });

  console.log(`Found ${classifications.length} classifications with DOT earning codes to cleanup`);

  // Clean up each classification's DOT earning code
  if (classifications.length > 0) {
    const checkService = new CheckService();

    for (const classification of classifications) {
      try {
        // Deactivate the DOT earning code in Check if possible
        if (classification.checkDotEarningCodeId) {
          try {
            // First deactivate the earning code in Check by setting active=false
            console.log(`Deactivating DOT earning code ${classification.checkDotEarningCodeId} in Check`);
            try {
              await checkService.patch(`/earning_codes/${classification.checkDotEarningCodeId}`, { active: false });
              console.log(`Successfully deactivated DOT earning code ${classification.checkDotEarningCodeId} in Check`);
            } catch (deactivateError) {
              console.error(
                `Error deactivating DOT earning code ${classification.checkDotEarningCodeId} in Check:`,
                deactivateError
              );
            }

            // Wait a brief moment to ensure Check has processed the deactivation
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Then update the classification to remove the reference
            console.log(`Removing checkDotEarningCodeId from classification ${classification.id}`);
            await classification.update({
              checkDotEarningCodeId: null,
              metadata: {
                ...classification.metadata,
                dotEarningCodeRemovedAt: new Date().toISOString(),
                dotEarningCodeDeactivated: true,
              },
            });
          } catch (error) {
            console.error(`Error removing DOT earning code reference from classification ${classification.id}:`, error);
          }
        }
      } catch (error) {
        console.error(`Error cleaning up classification ${classification.id}:`, error);
      }
    }
  }

  console.log("Cleanup completed");
}

/**
 * Script to backfill Double Overtime earning rates for all users with active hourly rates
 * in a specified organization.
 *
 * @param organizationId The ID of the organization to process
 */
async function backfillDoubleOvertimeRates(organizationId: number) {
  const earningRateService = new EarningRateService();
  const checkEarningRateManager = new CheckEarningRateManager();

  // Simplified organization query
  const organization = await Organization.findByPk(organizationId);

  if (!organization) {
    console.error(`Organization with ID ${organizationId} not found`);

    return;
  }

  console.log(`Processing organization: ${organization.name} (ID: ${organizationId})`);

  // Get all users in the organization with active hourly earning rates
  const users = await User.findAll({
    where: {
      organization_id: organizationId,
    },
  });

  console.log(`Found ${users.length} users with active hourly rates in organization ${organizationId}`);

  // Process each user
  for (const user of users) {
    try {
      console.log(`Processing user ${user.id} - ${user.firstName} ${user.lastName}`);

      // Get ALL earning rates for this user (not just active ones)
      const earningRates = await earningRateService.findAll({
        where: {
          user_id: user.id,
          organization_id: organizationId,
          period: "HOURLY",
          type: "REG",
        },
        order: [["start_date", "DESC"]],
      });

      console.log(`Found ${earningRates.length} regular earning rates for user ${user.id}`);

      // A map to keep track of which DOT rate corresponds to which REG rate
      const regToDotRateMap = new Map();

      // Create DOT rates for each REG rate
      for (const regRate of earningRates) {
        // Check if there's already a DOT rate for this REG rate with matching dates
        const existingDotRate = await EarningRate.findOne({
          where: {
            user_id: user.id,
            organization_id: organizationId,
            period: "HOURLY",
            type: "DOT",
            start_date: regRate.startDate,
            end_date: regRate.endDate,
          },
        });

        if (existingDotRate) {
          console.log(`DOT rate already exists for REG rate ${regRate.id}. Skipping.`);
          regToDotRateMap.set(regRate.id, existingDotRate.id);
          continue;
        }

        const dotRateName = regRate.name === "Regular" ? "Double Overtime" : `${regRate.name} - Double Overtime`;

        const regAmount = parseFloat(regRate.amount);
        const dotAmount = (regAmount * 2).toFixed(2);

        console.log(`Creating DOT rate for user ${user.id} with amount ${dotAmount} based on REG rate ${regRate.id}`);

        // Create the DOT rate directly with the model to ensure fields are properly set
        const dotRate = await EarningRate.create({
          name: dotRateName,
          amount: dotAmount,
          period: "HOURLY",
          active: regRate.active,
          type: EARNING_RATE_TYPES.DOT,
          userId: user.id,
          organizationId: organizationId,
          startDate: regRate.startDate,
          endDate: regRate.endDate,
          metadata: {
            createdFrom: `REG rate ID ${regRate.id}`,
            [SCRIPT_METADATA_KEY]: true,
            createdAt: new Date().toISOString(),
          },
        });

        regToDotRateMap.set(regRate.id, dotRate.id);

        // Try to sync with Check if the user has a Check employee ID
        if (user?.checkEmployeeId) {
          try {
            console.log(`Syncing DOT rate with Check for user ${user.id}`);

            // If the regRate is not active, deactivate the DOT rate in Check right after creation
            const needsDeactivation = !regRate.active;

            const checkEarningRateIds = await checkEarningRateManager.synchronizeEarningRates(
              user.checkEmployeeId,
              [dotRate],
              []
            );

            if (checkEarningRateIds && checkEarningRateIds.length > 0) {
              // Update the check earning rate ID directly on the model
              await dotRate.update({
                checkEarningRateId: checkEarningRateIds[0],
                metadata: {
                  ...dotRate.metadata,
                  syncedWithCheck: true,
                  checkEarningRateIdCreatedAt: new Date().toISOString(),
                },
              });
              console.log(`Updated DOT rate ${dotRate.id} with Check ID ${checkEarningRateIds[0]}`);

              // If the rate should not be active, deactivate it in Check
              if (needsDeactivation && checkEarningRateIds[0]) {
                console.log(`Deactivating DOT rate ${dotRate.id} in Check as its REG rate is inactive`);
                await checkEarningRateManager.deactivateEarningRate(checkEarningRateIds[0]);
                console.log(`Successfully deactivated DOT rate ${dotRate.id} in Check`);
              }
            }
          } catch (checkError) {
            console.error(`Error syncing with Check for user ${user.id}:`, checkError);

            // If we get the "active_earning_rate_already_exists" error, try to deactivate and recreate
            if (checkError?.type === "active_earning_rate_already_exists") {
              // Find existing DOT rates in Check that might be causing conflicts
              try {
                console.log(`Attempting to find and deactivate conflicting DOT rate for user ${user.id}`);

                // Search for existing active DOT rates with similar names
                const existingDotRates = await EarningRate.findAll({
                  where: {
                    user_id: user.id,
                    organization_id: organizationId,
                    type: "DOT",
                    active: true,
                    checkEarningRateId: {
                      [Op.ne]: null,
                    },
                  },
                });

                // Deactivate any found rates
                if (existingDotRates.length > 0) {
                  console.log(`Found ${existingDotRates.length} potentially conflicting DOT rates`);

                  for (const existingRate of existingDotRates) {
                    console.log(`Deactivating conflicting DOT rate ${existingRate.id}`);
                    await existingRate.update({ active: false });

                    // Deactivate in Check if possible
                    if (existingRate.checkEarningRateId) {
                      try {
                        await checkEarningRateManager.deactivateEarningRate(existingRate.checkEarningRateId);
                        console.log(`Deactivated conflicting DOT rate ${existingRate.id} in Check`);
                      } catch (deactivateError) {
                        console.error(`Failed to deactivate rate ${existingRate.id} in Check:`, deactivateError);
                      }
                    }
                  }

                  // Wait briefly for deactivation to take effect
                  await new Promise((resolve) => setTimeout(resolve, 2000));

                  // Try creating in Check again
                  console.log(`Retrying DOT rate sync for user ${user.id}`);
                  try {
                    const retryCheckEarningRateIds = await checkEarningRateManager.synchronizeEarningRates(
                      user.checkEmployeeId,
                      [dotRate],
                      []
                    );

                    if (retryCheckEarningRateIds && retryCheckEarningRateIds.length > 0) {
                      await dotRate.update({
                        checkEarningRateId: retryCheckEarningRateIds[0],
                        metadata: {
                          ...dotRate.metadata,
                          syncedWithCheck: true,
                          checkEarningRateIdCreatedAt: new Date().toISOString(),
                          retrySuccessful: true,
                        },
                      });
                      console.log(`Successfully synced DOT rate ${dotRate.id} with Check after retry`);

                      // If the rate should not be active, deactivate it in Check
                      if (!regRate.active && retryCheckEarningRateIds[0]) {
                        console.log(
                          `Deactivating DOT rate ${dotRate.id} in Check after retry as its REG rate is inactive`
                        );
                        await checkEarningRateManager.deactivateEarningRate(retryCheckEarningRateIds[0]);
                        console.log(`Successfully deactivated DOT rate ${dotRate.id} in Check after retry`);
                      }
                    }
                  } catch (retryError) {
                    console.error(`Retry failed for DOT rate ${dotRate.id}:`, retryError);
                    // Mark this error in metadata
                    await dotRate.update({
                      metadata: {
                        ...dotRate.metadata,
                        syncError: JSON.stringify(retryError),
                        retryFailed: true,
                      },
                    });
                  }
                }
              } catch (conflictError) {
                console.error(`Error handling rate conflict for user ${user.id}:`, conflictError);
              }
            }
          }
        } else {
          console.log(`Skipping Check sync for user ${user.id} - no checkEmployeeId available`);
        }
      }

      // Now update all non-PAID timesheets to use the corresponding DOT rate
      if (regToDotRateMap.size > 0) {
        console.log(`Updating timesheets for user ${user.id}`);

        // Get all non-PAID timesheets for this user
        const timesheets = await TimeSheet.findAll({
          where: {
            worker_id: user.id,
            organization_id: organizationId,
            status: {
              [Op.ne]: "PAID",
            },
          },
        });

        console.log(`Found ${timesheets.length} non-PAID timesheets for user ${user.id}`);

        // Update each timesheet
        for (const timesheet of timesheets) {
          const regRateId = timesheet.regEarningRateId;
          const dotRateId = regToDotRateMap.get(regRateId);

          if (dotRateId) {
            console.log(`Updating timesheet ${timesheet.id} with DOT rate ${dotRateId}`);
            await timesheet.update({ dotEarningRateId: dotRateId });
          } else {
            console.log(`No matching DOT rate found for timesheet ${timesheet.id} (REG rate: ${regRateId})`);
          }
        }
      }
    } catch (error) {
      console.error(`Error processing user ${user.id}:`, error);
    }
  }

  // Final message to help with potential manual fix if needed
  console.log("\n===== IMPORTANT =====");
  console.log("If organization_id is still null for any DOT rates created by this script,");
  console.log("you can fix them manually with this SQL query:");
  console.log(
    `UPDATE "EarningRates" SET organization_id = ${organizationId} WHERE organization_id IS NULL AND type = 'DOT' AND metadata->>'${SCRIPT_METADATA_KEY}' = 'true';`
  );
  console.log("=====================");

  console.log(`Completed backfilling DOT rates for organization ${organizationId}`);
}

// Get organization ID and mode from command line arguments
const organizationId = parseInt(process.argv[2], 10);
const mode = process.argv[3] || "backfill";

if (isNaN(organizationId)) {
  console.error("Please provide a valid organization ID as an argument");
  console.error("Usage: npx tsx -r dotenv/config scripts/backfillDot.ts <organizationId> [cleanup]");
  process.exit(1);
}

// Run the script in the appropriate mode
if (mode === "cleanup") {
  cleanupDoubleOvertimeRates(organizationId)
    .then(() => {
      console.log("Cleanup completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Cleanup failed:", error);
      process.exit(1);
    });
} else {
  // Run both backfill operations
  backfillDoubleOvertimeRates(organizationId)
    .then(() => backfillClassificationDotEarningCodes(organizationId))
    .then(() => {
      console.log("Script completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}
