-- Start a transaction to ensure all or nothing execution
BEGIN;

-- Common Table Expression to select users with a non-null hourlyRate
WITH UsersWithRates AS (
  SELECT
    id,
    organization_id,
    hourly_rate
  FROM
    public."Users" u -- Ensure the table name matches exactly with its declaration
  WHERE
    hourly_rate IS NOT NULL
) -- Insert regular and overtime earning rates
INSERT INTO
  "EarningRates" (
    user_id,
    organization_id,
    amount,
    period,
    name,
    type,
    active,
    start_date,
    created_at,
    updated_at
  )
SELECT
  id AS user_id,
  organization_id,
  CAST(ROUND(hourly_rate :: numeric, 2) AS VARCHAR) AS amount,
  -- Convert to numeric, round to 2 decimal places, then cast to VARCHAR
  CAST('HOURLY' AS public."enum_EarningRates_period") AS period,
  'Regular' AS name,
  CAST('REG' as public."enum_EarningRates_type") AS type,
  TRUE AS active,
  NOW() AS start_date,
  NOW() AS created_at,
  NOW() as updated_at
FROM
  UsersWithRates
UNION
ALL
SELECT
  id AS user_id,
  organization_id,
  CAST(
    ROUND((hourly_rate * 1.5) :: numeric, 2) AS VARCHAR
  ) AS amount,
  -- Calculate overtime, convert to numeric, round, then cast to VARCHAR
  CAST('HOURLY' AS public."enum_EarningRates_period") AS period,
  'Overtime' AS name,
  CAST('OT' as public."enum_EarningRates_type") AS type,
  TRUE AS active,
  NOW() AS start_date,
  NOW() AS created_at,
  NOW() as updated_at
FROM
  UsersWithRates;

-- Commit the transaction
COMMIT;