# Journal Entry Payroll Category Calculation

## Core Logic

- Main mapping logic in `integrationusertokenservice.ts`: `mapPayrollToJournalEntry`, `mapPayrollToJournalEntryPerEmployee`, `mapPayrollToJournalEntryPerDepartment`, `mapPayrollToJournalEntryPerProject`
- Calculation helpers in `glaccountmappings.ts`: `processTaxesForPayroll`, `processBenefitsForPayroll`, `processPayrollItem`
- GL accounts from `GlAccountMapping` records based on `payrollCategory` keys

```javascript
// Example: Creating the mapping used in the functions (from mapPayrollToJournalEntryPerEmployee)
const glAccountMap: { [key: string]: string } = {};
glAccountMappings.forEach((mapping: GlAccountMapping) => {
  glAccountMap[mapping.payrollCategory] = mapping.accountId;
});
// Used like: glAccountMap["FEDERAL_TAXES"] or glAccountMap["WAGES_AND_SALARIES"]
```

## Calculation Breakdown

### 1. Federal Tax Liability

- **How:** Calculates _employer's_ portion (FICA, FUTA) using `processTaxesForPayroll` helper, filtering taxes by `payer === "company"` and federal keywords
- **Journal Entry:**
  - **Debit (Expense):** GL account mapped to `FEDERAL_TAXES`
  - **Credit (Liability):** GL account mapped to `EMPLOYER_TAXES`

```javascript
// --- Federal Tax Liability ---
// 1. Calculate Employer Federal Tax Expense (using processTaxesForPayroll helper)
const categorizedTaxes = processTaxesForPayroll(payroll);
const federalTaxExpense = categorizedTaxes["FEDERAL_TAXES"] || 0;

// 2. Create Journal Entry Lines (Example from mapPayrollToJournalEntry)
// Debit (Expense)
journalEntry.line_items.push({
  account_id: accountMap["FEDERAL_TAXES"], // Mapped GL Account
  total_amount: federalTaxExpense,
  description: "Federal Tax Expense",
});
// Credit (Liability) - Often uses EMPLOYER_TAXES account
journalEntry.line_items.push({
  account_id: accountMap["EMPLOYER_TAXES"], // Mapped GL Account
  total_amount: -federalTaxExpense,
  description: `Federal Tax Liability`,
});
```

### 2. Employer Taxes (Category & Liability Account)

- **How:** Covers multiple things:
  - **Employer Tax Expenses (Debit):** Federal + State employer portions. State taxes categorized by `processTaxesForPayroll` based on state names
  - **Employee Tax Withholdings (Credit Liability):** Federal/State/Local taxes withheld from employee pay (`payer === "employee"`), calculated via `processPayrollItem`
- **Journal Entry:**
  - **Debit (State Tax Expense):** Uses state-specific GL accounts like `accountMap["STATE_TAXES_CA"]`
  - **Credit (Combined Tax Liability):** Both **Employee Withholdings** AND **Employer Tax Portions** credited to the GL account mapped to `EMPLOYER_TAXES`. **This consolidation is likely causing confusion.**

```javascript
// --- Employer Taxes (Broader) ---

// State Tax Expense (Debit - Example from mapPayrollToJournalEntry)
// processTaxesForPayroll categorizes by state like STATE_TAXES_CA
journalEntry.line_items.push({
  account_id: accountMap[`STATE_TAXES_${stateCode}`], // Mapped GL Account per state
  total_amount: stateTaxAmount, // Calculated amount for the specific state
  description: `State Tax Expense - ${stateCode}`,
});

// Employee Tax Withholdings (Liability - Credit)
// processPayrollItem calculates employeeTaxes per employee
const employeeTaxesTotal = parseFloat(payroll.totals.employee_taxes); // Aggregated total
journalEntry.line_items.push({
  account_id: accountMap["EMPLOYER_TAXES"], // Often the same liability account for EE W/H
  total_amount: -employeeTaxesTotal,
  description: "EMPLOYER TAXES", // Represents EE Withholdings Liability here
});

// Employer State Tax Liability (Credit)
// Sum of all employer state tax amounts
journalEntry.line_items.push({
  account_id: accountMap["EMPLOYER_TAXES"], // Also often uses this account
  total_amount: -totalStateTaxes, // Sum of employer portions for all states
  description: `State Tax Liability - state taxes`,
});
```

### 3. Other Payroll Liabilities (Credits)

- **Net Pay:** Aggregated `net_pay` from payroll items/totals. Credited to `BANK_ACCOUNT`
- **Garnishments / Post-Tax Deductions:** Summed from `garnishments` and `post_tax_deductions` arrays. Credited to `GARNISHMENTS_PAYABLE`
- **Benefits (EE Contribution Liability):** Calculated by `processBenefitsForPayroll`. Credited to `EMPLOYER_TAXES` or specific benefit liability account (`accountMap[BenefitName_EE]`) if mapped

```javascript
// --- Other Payroll Liabilities (Credits) ---

// Net Pay (Credit)
journalEntry.line_items.push({
  account_id: accountMap["BANK_ACCOUNT"],
  total_amount: -employeeNet, // Calculated from payroll totals/items
  description: "BANK ACCOUNT",
});

// Garnishments / Post-Tax Deductions (Credit)
// Calculated via processPayrollItem and aggregated
journalEntry.line_items.push({
  account_id: accountMap["GARNISHMENTS_PAYABLE"],
  total_amount: -totalGarnishments, // Sum of amounts from payroll items
  description: "GARNISHMENTS AND POST-TAX DEDUCTIONS",
});

// Employee Benefit Contributions (Credit Liability)
// processBenefitsForPayroll calculates totalEmployeeContributions
journalEntry.line_items.push({
  account_id: accountMap["EMPLOYER_TAXES"], // Often the default liability account
  // Or accountMap[BenefitName_EE] if specifically mapped
  total_amount: -employeeBenefitsTotal,
  description: "Benefits (EE contribution)",
});
```

## Consolidation Differences (Department, Employee, Project)

- Logic similar but applied per group (employee, dept, project)
- Descriptions include group name: `- Employee Name`, `- Department Name`, `- Project Name`
- **Project calculation is most complex:**
  - Uses timesheet wages/hours (`ExtendedTimesheet` data from `TimesheetsService`) to distribute costs before calculating taxes/benefits
  - Uses `IntegrationMapping` to potentially link Hammr Project IDs to external system Project IDs
  - This distribution logic can cause inconsistencies compared to simpler payroll views

## Key Helper Functions (in `glaccountmappings.ts`)

- `processTaxesForPayroll`: Categorizes employer/employee taxes (Federal/State)
- `processBenefitsForPayroll`: Categorizes employer/employee benefit amounts
- `processPayrollItem`: Extracts key values (wages, taxes, benefits, net pay) from a payroll item
- `processPayrollByProject`: Handles cost distribution for project-based consolidation
- `createJournalEntryLinesForProject`: Creates JE lines for project consolidation, potentially adding external project ID
- `validateAndBalanceJournalEntry`: Checks if debits equal credits
