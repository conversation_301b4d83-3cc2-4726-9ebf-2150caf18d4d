# Architecture

The `backend` repo is losely follows ideas from MVC without the V and inspired by the [Nest.js Framework](https://docs.nestjs.com/#philosophy) for Node.js apps.

At the heart of this is really what we call `providers` or `services` and `controllers`. Then there are additional patterns that help with various techniques of modifying, transforming, validating, etc. incoming requests.

The repository leverages more object-oriented concepts for `services` and `controllers` and some examples are provided below.

## Folder Structure

The root directory for the project has a primary directory containing the bulk of the application logic inside `app`. Sometimes this is might be `src` in other projects.

The biggest components for this `backend` will be the `controllers`, `services` and the `routes` that pieces all the dependencies together.

**Current Structure:**

```
app
├── controllers
│   ├── costcodes.ts
│   ├── projects.ts
│   └── users.ts
├── crons
│   └── dailyStats.ts
├── index.ts
├── lib
│   └── sequelize
│       └── index.ts
├── models
│   ├── costcode.ts
│   ├── index.ts
│   ├── organization.ts
│   ├── project.ts
│   ├── relations.ts
│   ├── timesheet.ts
│   └── user.ts
├── routes
│   ├── api
│   │   ├── authentication.ts
│   │   ├── costcodes.ts
│   │   ├── csvRawImport.ts
│   │   ├── dashboard.ts
│   │   ├── index.ts
│   │   ├── projects.ts
│   │   ├── reports.ts
│   │   ├── timesheets.ts
│   │   └── users.ts
│   └── middlewares
│       ├── session.ts
│       └── validatation.ts
├── services
│   ├── costcodes.ts
│   ├── organizations.ts
│   ├── projects.ts
│   └── users.ts
├── types
│   └── session.d.ts
└── util
    ├── casingConvert.js
    ├── env.ts
    ├── errors.ts
    ├── financial.ts
    └── sendSMS.ts
```

## Services (Providers)

With the notion of separating concerns, the rough idea is to keep as much business logic as possible inside this layer.

There's various levels of abstraction one could use here, but in the interest of **not over-engineering**, having a service that has a data-access interface injected and facilitates communications between the resources is a decent baseline.

```ts
import { Cat } from "./interfaces/cat.interface";

export class CatsService {
  private readonly cats: Cat[] = [];

  create(cat: Cat) {
    this.cats.push(cat);
  }

  findAll(): Cat[] {
    return this.cats;
  }
}
```

## Controllers

> Controllers are responsible for handling incoming requests and returning responses to the client.

Controllers typically have methods that map to HTTP request types in routes.

Controllers will typically inject `services` to fulfill a request and it will occassionally facilitate communications between multiple `services` or providers.

```ts
export class CatsController {
  get(): string {
    return "This action returns all cats";
  }
}
```

## Routes

Routing refers to how an application’s endpoints (URIs) respond to client requests.

In [Express](https://expressjs.com/en/guide/routing.html), we can create nested routes that can take a route prefix and a series of middleware before passing the request to the controller for fulfillment.

We want to use the `route` files to include any specific middleware pertinent to the route and also be explicit about the endpoint. Then have the route delegate to a controller method.

## Middleware

To quote:

```
Middleware functions can perform the following tasks:
*  execute any code.
*  make changes to the request and the response objects.
*  end the request-response cycle.
*  call the next middleware function in the stack.
*  if the current middleware function does not end the request-response cycle, it must call next() to pass control to the next middleware function. Otherwise, the request will be left hanging.
```

Middleware will be important for various techniques `backend` might want to utilize. `backend` currently has middleware for sessions, but you could imagine there could be middleware attached to routes for a number of things including data validation or perhaps additional data manipulation before the request hits a controller.

Without some of the primitives that say a framework like Nest.js might provide to handle various scenarios, `middleware` is sort of like the umbrella for `backend`.
