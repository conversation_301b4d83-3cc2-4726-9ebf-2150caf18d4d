## Readme for "Orcheestration" Downstream

This is important and should be recorded.

Early in 2024, <PERSON><PERSON><PERSON> embarked on a refactor to move to a unidirectional data flow from clients to downstream services. Before this refactor happened, (web)[https://github.com/Hammr-Inc/web] would make calls pseudo-directly (<PERSON>'s proxy via Hammr's backend) to Check for payroll related entities (this included company, employees, contractors, etc). This was structured in such a way because Hammr's web was built on top of Hammr's white-label payroll provider's fast start app called "Express". Essentially Express came with these architectural decisions for us while Hammr focused on integrating payroll quickly to go to market in early 2023.

Doing the refactor, Hammr's backend now handles a lot more complexity. Previously requests that could be routed directly to Check after moving through the proxy from Hammr's backend was straight forward - the property names, the relevant data (especially Check-related ids) were typically available on the proxy request.

Now that Hammr moves into to be the source of truth for data with data-flow essentially always going through backend first so long as the model/entities exist, this means that any downstream service now (Check) and potentially any in the future (perhaps CRM, etc), will potentially need it's own validation and authorization logic.

Check's authorization logic comes in the form of `validationHelpers.ts` where it is essentially attached to a more abstract middleware called `isAuthorizedForCheckRequest`

This is great right? That means we just need to have some additional middleware for validation and authorization and attach them to route handlers right?

In theory that could be the case, but it's much more nuanced. One way this is nuanced is that Hammr supports `Payroll` and `Non-Payroll` customers. Non-Payroll customers shouldn't have any data that should be "massaged" for validation/authorization nor should these requests make it "downstream" to Check and/or any future third parties not applicable to non-payroll customers.

This is where conditional middleware comes in (such as to see if these various middlewares should apply for the incoming request) and an "orchestration" service come in.

Given the context, let's discuss implementation and architectural decisions.

This orchestration service is a thin wrapper around any existing service written for backend. It should be protocol agnostic meaning if the third party service uses REST, then service orchestrator should in theory just wrap around that REST call. If the third party leverages graphql, the same concept should apply so long as the proper data formatting/shape is provided.

// TO BE FILLED IN - one of the key tenants is hopefully not duplicating DB reads unnecessarily. Check authorization requires specific data and then the controller when it sends any data downstream will also likely need some form of data - we want to reduce this. Current idea is to attach the necessary "fetched" data to the request object.

For any given request, there is potentially many middleware a request will pass through. There will be common or shared ones (ones that are higher up the stack), and then more nuanced or refined ones that may be only relevant to a specific route.

The assumption is same rationale would be applied to any future downstream third party service.
