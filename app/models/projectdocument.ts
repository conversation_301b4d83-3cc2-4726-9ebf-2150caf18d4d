import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Project from "./project";
import Organization from "./organization";

class ProjectDocument extends Model {
  public id!: number;
  public objectId!: string;
  public name!: string;
  public isDeleted: boolean;
  public projectId: ForeignKey<Project["id"]>;
  public createdBy: ForeignKey<User["id"]>;
  public organizationId: ForeignKey<Organization["id"]>;
}

ProjectDocument.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    objectId: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "projectDocument",
    tableName: "ProjectDocuments",
  }
);

export default ProjectDocument;
