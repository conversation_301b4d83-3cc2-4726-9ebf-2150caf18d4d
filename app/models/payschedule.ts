import {
  CreationOptional,
  DataTypes,
  ForeignKey,
  InferAttributes,
  InferCreationAttributes,
  Model,
  Op,
} from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "./organization";
import { PayFrequency } from "@/types/checkDto";

export const PAY_FREQUENCY: PayFrequency[] = ["weekly", "biweekly", "semimonthly", "monthly", "quarterly", "annually"];

export default class PaySchedule extends Model<InferAttributes<PaySchedule>, InferCreationAttributes<PaySchedule>> {
  declare id: CreationOptional<number>;
  declare payFrequency: PayFrequency;
  declare firstPayday: string;
  declare secondPayday?: string;
  declare firstPeriodEnd: string;
  declare isActive: boolean;
  declare checkPayScheduleId?: string;

  declare organizationId: ForeignKey<Organization["id"]>;
}

PaySchedule.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    payFrequency: {
      type: DataTypes.ENUM(...PAY_FREQUENCY),
      allowNull: false,
      defaultValue: "biweekly",
    },
    firstPayday: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    secondPayday: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    firstPeriodEnd: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    checkPayScheduleId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "paySchedule",
    tableName: "PaySchedules",
    hooks: {
      beforeCreate: async (instance) => {
        if (instance.isActive) {
          await PaySchedule.update({ isActive: false }, { where: { organizationId: instance.organizationId } });
        }
      },
      beforeUpdate: async (instance) => {
        if (instance.isActive) {
          await PaySchedule.update(
            { isActive: false },
            {
              where: {
                organizationId: instance.organizationId,
                id: { [Op.ne]: instance.id },
              },
            }
          );
        }
      },
    },
  }
);
