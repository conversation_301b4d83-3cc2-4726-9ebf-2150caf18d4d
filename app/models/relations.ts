import User from "@/models/user";
import Organization from "@/models/organization";
import Project from "@/models/project";
import TimeSheet from "@/models/timesheet";
import CostCode from "@/models/costcode";
import ScheduleEvent from "@/models/scheduleevent";
import UserScheduleEvent from "@/models/userscheduleevent";
import Break from "./break";
import ScheduleEventNotification from "./scheduleeventnotification";
import DeviceToken from "./devicetoken";
import ProjectPhoto from "./projectphoto";
import Crew from "./crew";
import CrewMember from "./crewmember";
import Department from "./department";
import ProjectPhotosCollection from "./projectphotoscollection";
import FeatureFlags from "./featureflags";
import TimesheetHistory from "./timesheethistory";
import BreakHistory from "./breakhistory";
import ProjectDocument from "./projectdocument";
import UserLocation from "./userlocation";
import TimesheetAlert from "./timesheetalert";
import EarningRate from "./earningrate";
import CompanyBenefit from "./companybenefit";
import EmployeeBenefit from "./employeebenefit";
import WageTable from "./wagetable";
import Classification from "./classification";
import UserClassification from "./userclassification";
import FringeBenefit from "./fringebenefit";
import FringeBenefitClassification from "./fringebenefitclassification";
import IntegrationUserToken from "./integrationusertoken";
import GlAccountMapping from "./glaccountmapping";
import GlAccountMappingSetting from "./glaccountmappingsetting";
import IntegrationSyncHistory from "./integrationsynchistory";
import PaySchedule from "@/models/payschedule";
import TimeTrackingSettings from "./timetrackingsettings";
import OvertimeSettings from "./overtimesettings";
import PrevailingWageSettings from "@/models/prevailingwagesettings";
import WorkersCompCode from "@/models/workersCompCode";
import EmployeeDocument from "./employeedocument";
import EmployeeCertification from "./employeecertification";
import TimeOffPolicy from "./timeoffpolicy";
import TimeOffPolicyEnrollment from "./timeoffpolicyenrollment";
import TimeOffRequest from "./timeoffrequest";
import DailyReport from "./dailyreport";
import EmployeeReimbursement from "./employeereimbursement";
import IntegrationMapping from "./integrationMapping";
import IntegrationObjectSetting from "./integrationobjectsetting";
import APIKey from "./apiKey";
import InjuryReport from "./injuryreport";
import InjuryPhoto from "./injuryphoto";
import Equipment from "./equipment";
import EquipmentCategory from "./equipmentcategory";
import EquipmentScheduleEvent from "./equipmentscheduleevent";

export const createRelations = () => {
  User.belongsTo(Organization, {
    foreignKey: "organizationId",
  });

  Organization.hasMany(Project, {
    foreignKey: "organizationId",
  });

  User.belongsTo(User, {
    foreignKey: "managerId",
    as: "manager",
  });

  User.hasMany(User, {
    foreignKey: "managerId",
    as: "employees",
  });

  TimeSheet.belongsTo(Organization, {
    foreignKey: "organizationId",
  });

  User.hasMany(TimeSheet, {
    foreignKey: "workerId",
  });

  TimeSheet.belongsTo(User, {
    foreignKey: "workerId",
  });

  User.hasMany(Project, {
    foreignKey: "foremanId",
    constraints: false,
  });

  Organization.hasMany(CostCode, {
    foreignKey: "organizationId",
  });

  CostCode.hasMany(TimeSheet, {
    foreignKey: "costCodeId",
  });

  TimeSheet.belongsTo(CostCode, {
    foreignKey: "costCodeId",
    constraints: false,
  });

  Project.hasMany(TimeSheet, {
    foreignKey: "projectId",
  });

  TimeSheet.belongsTo(Project, {
    foreignKey: "projectId",
  });

  TimeSheet.belongsTo(User, {
    foreignKey: "createdBy",
    as: "createdByUser",
  });

  TimeSheet.belongsTo(User, {
    foreignKey: "editedBy",
    as: "editedByUser",
  });

  Break.belongsTo(TimeSheet, {
    foreignKey: "timesheetId",
  });

  Break.belongsTo(User, { foreignKey: "editedBy", as: "editedByUser" });

  TimeSheet.hasMany(Break, {
    foreignKey: "timesheetId",
  });

  ScheduleEvent.belongsTo(Project, {
    foreignKey: "projectId",
  });

  Project.hasMany(ScheduleEvent, {
    foreignKey: "projectId",
  });

  ScheduleEvent.belongsTo(CostCode, {
    foreignKey: "costCodeId",
  });

  CostCode.hasMany(ScheduleEvent, {
    foreignKey: "costCodeId",
  });

  ScheduleEvent.belongsTo(Organization, {
    foreignKey: "organizationId",
  });

  Organization.hasMany(ScheduleEvent, {
    foreignKey: "organizationId",
  });

  UserScheduleEvent.belongsTo(User, { foreignKey: "userId" });
  UserScheduleEvent.belongsTo(ScheduleEvent, { foreignKey: "scheduleEventId" });

  User.hasMany(UserScheduleEvent, { foreignKey: "userId" });
  ScheduleEvent.hasMany(UserScheduleEvent, { foreignKey: "scheduleEventId" });

  User.belongsToMany(ScheduleEvent, { through: UserScheduleEvent });
  ScheduleEvent.belongsToMany(User, { through: UserScheduleEvent });

  ScheduleEventNotification.belongsTo(ScheduleEvent, { foreignKey: "scheduleEventId" });
  ScheduleEvent.hasMany(ScheduleEventNotification, { foreignKey: "scheduleEventId" });

  User.hasMany(DeviceToken, { foreignKey: "userId" });
  DeviceToken.belongsTo(User, { foreignKey: "userId" });

  ProjectPhoto.belongsTo(ProjectPhotosCollection, { foreignKey: "collectionId" });
  ProjectPhotosCollection.hasMany(ProjectPhoto, { foreignKey: "collectionId" });

  ProjectPhotosCollection.belongsTo(Project, { foreignKey: "projectId" });
  Project.hasMany(ProjectPhotosCollection, { foreignKey: "projectId" });

  ProjectPhotosCollection.belongsTo(TimeSheet, { foreignKey: "timesheetId" });
  TimeSheet.hasMany(ProjectPhotosCollection, { foreignKey: "timesheetId" });

  ProjectPhotosCollection.belongsTo(User, { foreignKey: "createdBy" });
  User.hasMany(ProjectPhotosCollection, { foreignKey: "createdBy" });

  ProjectPhotosCollection.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(ProjectPhotosCollection, { foreignKey: "organizationId" });

  Crew.belongsTo(User, { foreignKey: "crewLead", as: "crewLeadUser" });
  User.hasMany(Crew, { foreignKey: "crewLead", as: "crewLeadUser" });

  CrewMember.belongsTo(User, { foreignKey: "userId", as: "crewMemberUser" });
  User.hasMany(CrewMember, { foreignKey: "userId", as: "crewMemberUser" });

  CrewMember.belongsTo(Crew, { foreignKey: "crewId" });
  Crew.hasMany(CrewMember, { foreignKey: "crewId" });

  Crew.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(Crew, { foreignKey: "organizationId" });

  FeatureFlags.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasOne(FeatureFlags, { foreignKey: "organizationId" });

  Organization.hasOne(TimeTrackingSettings, {
    foreignKey: "organizationId",
    as: "timeTrackingSettings",
  });

  TimeTrackingSettings.belongsTo(Organization, {
    foreignKey: "organizationId",
    as: "organization",
  });

  Organization.hasOne(PaySchedule, {
    foreignKey: "organizationId",
    as: "paySchedule",
    scope: {
      isActive: true,
    },
  });

  PaySchedule.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(PaySchedule, { foreignKey: "organizationId" });

  BreakHistory.belongsTo(Break, { foreignKey: "breakId" });

  TimesheetHistory.belongsTo(TimeSheet, { foreignKey: "timesheetId" });
  TimeSheet.hasMany(TimesheetHistory, { foreignKey: "timesheetId" });

  BreakHistory.belongsTo(TimeSheet, { foreignKey: "timesheetId" });
  TimeSheet.hasMany(BreakHistory, { foreignKey: "timesheetId" });

  TimesheetHistory.belongsTo(User, { foreignKey: "createdBy" });
  User.hasMany(TimesheetHistory, { foreignKey: "createdBy" });

  BreakHistory.belongsTo(User, { foreignKey: "createdBy" });
  User.hasMany(BreakHistory, { foreignKey: "createdBy" });
  ProjectDocument.belongsTo(Project, { foreignKey: "projectId" });
  Project.hasMany(ProjectDocument, { foreignKey: "projectId" });
  ProjectDocument.belongsTo(User, { foreignKey: "createdBy" });
  User.hasMany(ProjectDocument, { foreignKey: "createdBy" });

  ProjectDocument.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(ProjectDocument, { foreignKey: "organizationId" });

  UserLocation.belongsTo(User, { foreignKey: "userId" });
  User.hasMany(UserLocation, { foreignKey: "userId" });
  UserLocation.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(UserLocation, { foreignKey: "organizationId" });
  UserLocation.belongsTo(TimeSheet, { foreignKey: "timesheetId" });
  TimeSheet.hasMany(UserLocation, { foreignKey: "timesheetId" });
  UserLocation.belongsTo(Project, { foreignKey: "geofenceProjectId" });
  Project.hasMany(UserLocation, { foreignKey: "geofenceProjectId" });
  UserLocation.belongsTo(Break, { foreignKey: "breakId" });
  Break.hasMany(UserLocation, { foreignKey: "breakId" });

  TimesheetAlert.belongsTo(TimeSheet, { foreignKey: "timesheetId" });
  TimeSheet.hasMany(TimesheetAlert, { foreignKey: "timesheetId" });
  TimesheetAlert.belongsTo(User, { foreignKey: "resolvedBy", as: "resolvedByUser" });
  User.hasMany(TimesheetAlert, { foreignKey: "resolvedBy" });

  EarningRate.belongsTo(User, { foreignKey: "userId", as: "user" });
  EarningRate.belongsTo(Organization, { foreignKey: "organizationId" });
  TimeSheet.belongsTo(EarningRate, { foreignKey: "regEarningRateId", as: "regEarningRate" });
  TimeSheet.belongsTo(EarningRate, { foreignKey: "otEarningRateId", as: "otEarningRate" });
  TimeSheet.belongsTo(EarningRate, { foreignKey: "dotEarningRateId", as: "dotEarningRate" });
  User.hasMany(EarningRate, { foreignKey: "userId", as: "earningRates" });
  Organization.hasMany(EarningRate, { foreignKey: "organizationId", as: "earningRates" });

  CompanyBenefit.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(CompanyBenefit, { foreignKey: "organizationId" });

  EmployeeBenefit.belongsTo(User, { foreignKey: "userId" });
  User.hasMany(EmployeeBenefit, { foreignKey: "userId" });
  CompanyBenefit.hasMany(EmployeeBenefit, { foreignKey: "companyBenefitId", as: "employeeBenefits" });
  EmployeeBenefit.belongsTo(CompanyBenefit, {
    foreignKey: "companyBenefitId",
    as: "companyBenefit",
  });
  Organization.hasMany(EmployeeBenefit, { foreignKey: "organizationId" });
  EmployeeBenefit.belongsTo(Organization, { foreignKey: "organizationId" });

  WageTable.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(WageTable, { foreignKey: "organizationId" });
  WageTable.hasMany(Project, { foreignKey: "wageTableId" });
  Project.belongsTo(WageTable, { foreignKey: "wageTableId" });

  FringeBenefit.belongsTo(WageTable, { foreignKey: "wageTableId" });
  WageTable.hasMany(FringeBenefit, { foreignKey: "wageTableId", as: "fringeBenefits" });
  FringeBenefit.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(FringeBenefit, { foreignKey: "organizationId" });

  FringeBenefitClassification.belongsTo(FringeBenefit, { foreignKey: "fringeBenefitId" });
  FringeBenefit.hasMany(FringeBenefitClassification, {
    foreignKey: "fringeBenefitId",
    as: "fringeBenefitClassifications",
  });
  FringeBenefitClassification.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(FringeBenefitClassification, { foreignKey: "organizationId" });
  FringeBenefitClassification.belongsTo(Classification, { foreignKey: "classificationId" });
  Classification.hasMany(FringeBenefitClassification, { foreignKey: "classificationId" });
  Classification.belongsTo(WageTable, { foreignKey: "wageTableId" });
  WageTable.hasMany(Classification, { foreignKey: "wageTableId", as: "classifications" });
  Classification.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(Classification, { foreignKey: "organizationId" });
  TimeSheet.belongsTo(UserClassification, { foreignKey: "userClassificationId" });
  UserClassification.hasMany(TimeSheet, { foreignKey: "userClassificationId" });

  UserClassification.belongsTo(User, { foreignKey: "userId" });
  User.hasMany(UserClassification, { foreignKey: "userId" });
  UserClassification.belongsTo(Classification, { foreignKey: "classificationId" });
  Classification.hasMany(UserClassification, { foreignKey: "classificationId" });
  UserClassification.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(UserClassification, { foreignKey: "organizationId" });
  UserClassification.belongsTo(WageTable, { foreignKey: "wageTableId" });
  WageTable.hasMany(UserClassification, { foreignKey: "wageTableId" });

  User.hasMany(IntegrationUserToken, { foreignKey: "createdBy", as: "integrationUserTokens" });
  IntegrationUserToken.belongsTo(User, { foreignKey: "createdBy" });
  IntegrationUserToken.belongsTo(Organization, { foreignKey: "organizationId" });

  IntegrationUserToken.hasMany(GlAccountMapping, {
    foreignKey: "integrationUserTokenId",
    onDelete: "CASCADE",
  });
  GlAccountMapping.belongsTo(IntegrationUserToken, { foreignKey: "integrationUserTokenId" });
  User.hasMany(GlAccountMapping, { foreignKey: "createdBy" });
  GlAccountMapping.belongsTo(Organization, { foreignKey: "organizationId" });

  GlAccountMapping.belongsTo(Department, { foreignKey: "departmentId" });
  Department.hasMany(GlAccountMapping, { foreignKey: "departmentId" });

  IntegrationUserToken.hasMany(GlAccountMappingSetting, {
    foreignKey: "integrationUserTokenId",
    onDelete: "CASCADE",
  });
  GlAccountMappingSetting.belongsTo(IntegrationUserToken, { foreignKey: "integrationUserTokenId" });
  GlAccountMappingSetting.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(GlAccountMappingSetting, { foreignKey: "organizationId" });
  User.hasMany(GlAccountMappingSetting, { foreignKey: "createdBy" });

  IntegrationSyncHistory.belongsTo(IntegrationUserToken, { foreignKey: "integrationUserTokenId" });
  IntegrationUserToken.hasMany(IntegrationSyncHistory, { foreignKey: "integrationUserTokenId" });

  // Add IntegrationMapping relations
  IntegrationMapping.belongsTo(IntegrationUserToken, { foreignKey: "integrationUserTokenId", as: "integration" });
  IntegrationUserToken.hasMany(IntegrationMapping, { foreignKey: "integrationUserTokenId", as: "mappings" });

  // Add IntegrationObjectSetting relations
  IntegrationObjectSetting.belongsTo(IntegrationUserToken, { foreignKey: "integrationUserTokenId", as: "integration" });
  IntegrationUserToken.hasMany(IntegrationObjectSetting, {
    foreignKey: "integrationUserTokenId",
    as: "objectSettings",
  });

  Organization.hasMany(OvertimeSettings, { foreignKey: "organizationId", as: "overtimeSettings" });

  OvertimeSettings.belongsTo(Organization, { foreignKey: "organizationId" });

  // Add relationship between Project and OvertimeSettings
  Project.belongsTo(OvertimeSettings, { foreignKey: "overtimeSettingsId", as: "overtimeSettings" });
  OvertimeSettings.hasMany(Project, { foreignKey: "overtimeSettingsId" });

  Organization.hasOne(PrevailingWageSettings, { foreignKey: "organizationId", as: "prevailingWageSettings" });

  PrevailingWageSettings.belongsTo(Organization, { foreignKey: "organizationId" });

  WorkersCompCode.hasMany(CostCode, { foreignKey: "workersCompCodeId", as: "costCodes" });
  TimeSheet.belongsTo(WorkersCompCode, { foreignKey: "workersCompCodeId", as: "workersCompCode" });
  WorkersCompCode.hasMany(TimeSheet, { foreignKey: "workersCompCodeId", as: "timesheets" });
  CostCode.belongsTo(WorkersCompCode, { foreignKey: "workersCompCodeId", as: "workersCompCode" });
  User.belongsTo(WorkersCompCode, { foreignKey: "workersCompCodeId", as: "workersCompCode" });

  EmployeeDocument.belongsTo(User, { foreignKey: "userId" });
  User.hasMany(EmployeeDocument, { foreignKey: "userId" });
  EmployeeDocument.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(EmployeeDocument, { foreignKey: "organizationId" });
  EmployeeDocument.belongsTo(User, { foreignKey: "createdBy" });
  User.hasMany(EmployeeDocument, { foreignKey: "createdBy" });

  EmployeeCertification.belongsTo(User, { foreignKey: "userId", as: "user" });
  User.hasMany(EmployeeCertification, { foreignKey: "userId" });
  EmployeeCertification.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(EmployeeCertification, { foreignKey: "organizationId" });
  EmployeeCertification.belongsTo(User, { foreignKey: "createdBy", as: "createdByUser" });
  User.hasMany(EmployeeCertification, { foreignKey: "createdBy" });

  TimeOffPolicy.belongsTo(Organization, {
    foreignKey: "organizationId",
    as: "organization",
  });

  TimeOffPolicy.belongsToMany(User, {
    through: TimeOffPolicyEnrollment,
    foreignKey: "timeOffPolicyId",
    otherKey: "userId",
    as: "users",
  });

  TimeOffPolicy.hasMany(TimeOffPolicyEnrollment, {
    foreignKey: "timeOffPolicyId",
    as: "enrollments",
  });

  User.belongsToMany(TimeOffPolicy, {
    through: TimeOffPolicyEnrollment,
    foreignKey: "userId",
    otherKey: "timeOffPolicyId",
    as: "timeOffPolicies",
  });
  TimeOffPolicyEnrollment.belongsTo(TimeOffPolicy, { foreignKey: "timeOffPolicyId", as: "timeOffPolicy" });
  TimeOffPolicyEnrollment.belongsTo(User, { foreignKey: "userId", as: "user" });

  TimeOffRequest.belongsTo(User, { foreignKey: "userId", as: "user" });
  TimeOffRequest.belongsTo(User, { foreignKey: "reviewedBy", as: "reviewer" });
  TimeOffRequest.belongsTo(TimeOffPolicy, { foreignKey: "timeOffPolicyId", as: "timeOffPolicy" });
  User.hasMany(TimeOffRequest, { foreignKey: "userId", as: "timeOffRequests" });
  User.hasMany(TimeOffRequest, { foreignKey: "reviewedBy", as: "reviewedTimeOffRequests" });
  TimeOffPolicy.hasMany(TimeOffRequest, { foreignKey: "timeOffPolicyId", as: "timeOffRequests" });

  // daily reports
  Project.hasMany(DailyReport, { foreignKey: "projectId" });
  DailyReport.belongsTo(Project, { foreignKey: "projectId" });

  DailyReport.belongsTo(User, { foreignKey: "createdBy" });
  User.hasMany(DailyReport, { foreignKey: "createdBy" });

  DailyReport.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(DailyReport, { foreignKey: "organizationId" });
  // daily reports end

  // Employee Reimbursement relations
  User.hasOne(EmployeeReimbursement, { foreignKey: "userId" });
  EmployeeReimbursement.belongsTo(User, { foreignKey: "userId" });
  EmployeeReimbursement.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(EmployeeReimbursement, { foreignKey: "organizationId" });

  User.belongsTo(Department, { foreignKey: "departmentId" });
  Department.hasMany(User, { foreignKey: "departmentId" });

  APIKey.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(APIKey, { foreignKey: "organizationId" });

  // Injury Report relations
  InjuryReport.belongsTo(User, { foreignKey: "createdBy", as: "creator" });
  InjuryReport.belongsTo(User, { foreignKey: "resolvedBy", as: "resolvedByUser" });
  InjuryReport.belongsTo(Organization, { foreignKey: "organizationId" });
  InjuryReport.belongsTo(User, { foreignKey: "userId", as: "user" });
  InjuryReport.belongsTo(TimeSheet, { foreignKey: "timesheetId" });

  // Injury Report - Injury Photo
  InjuryReport.hasMany(InjuryPhoto, { foreignKey: "injuryReportId" });
  InjuryPhoto.belongsTo(InjuryReport, { foreignKey: "injuryReportId" });
  InjuryPhoto.belongsTo(User, { foreignKey: "createdBy" });

  User.hasMany(InjuryReport, { foreignKey: "userId", as: "user" });
  Organization.hasMany(InjuryReport, { foreignKey: "organizationId" });
  TimeSheet.hasOne(InjuryReport, { foreignKey: "timesheetId" });

  // Equipment relations
  Equipment.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(Equipment, { foreignKey: "organizationId" });

  Equipment.belongsTo(EquipmentCategory, { foreignKey: "categoryId", as: "category" });
  EquipmentCategory.hasMany(Equipment, { foreignKey: "categoryId" });

  EquipmentCategory.belongsTo(Organization, { foreignKey: "organizationId" });
  Organization.hasMany(EquipmentCategory, { foreignKey: "organizationId" });

  TimeSheet.belongsTo(Equipment, { foreignKey: "equipmentId", as: "equipment" });
  Equipment.hasMany(TimeSheet, { foreignKey: "equipmentId", as: "timesheets" });

  // EquipmentScheduleEvent relations
  EquipmentScheduleEvent.belongsTo(Equipment, { foreignKey: "equipmentId" });
  Equipment.hasMany(EquipmentScheduleEvent, { foreignKey: "equipmentId" });

  EquipmentScheduleEvent.belongsTo(ScheduleEvent, { foreignKey: "scheduleEventId" });
  ScheduleEvent.hasMany(EquipmentScheduleEvent, { foreignKey: "scheduleEventId" });

  // Many-to-many relationships
  Equipment.belongsToMany(ScheduleEvent, { through: EquipmentScheduleEvent });
  ScheduleEvent.belongsToMany(Equipment, { through: EquipmentScheduleEvent });
};
