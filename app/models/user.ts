import { DataTypes, ForeignK<PERSON>, Model, NonAttribute } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import { ExtendedEmployeeBenefit } from "@/models/employeebenefit";
import EarningRate, { ExtendedEarningRate } from "@/models/earningrate";
import TimeSheet from "./timesheet";
import WorkersCompCode from "./workersCompCode";
import TimeOffPolicy from "@/models/timeoffpolicy";
import Department from "@/models/department";
import TimeOffPolicyEnrollment from "@/models/timeoffpolicyenrollment";

export const WORKER_CLASSIFICATION = ["EMPLOYEE", "CONTRACTOR"] as const;
export const ROLE = ["ADMIN", "FOREMAN", "WORKER"] as const;
export const USER_STATUS = ["INVITED", "ONBOARDED"];
export const GENDER = ["MALE", "FEMALE", "NON_BINARY", "PREFER_NOT_TO_ANSWER"];
export const ETHNICITY = [
  "AMER<PERSON>AN_INDIAN",
  "ASIA<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "HIS<PERSON>NI<PERSON>",
  "OTHER_PACIFIC_ISLANDER",
  "WHITE",
  "TWO_OR_MORE",
  "PREFER_NOT_TO_ANSWER",
] as const;
export const VETERAN_STATUS = [
  "ACTIVE_DUTY_WARTIME_OR_CAMPAIGN_BADGE_VETERAN",
  "ARMED_FORCES_SERVICE_MEDAL_VETERAN",
  "DISABLED_VETERAN",
  "RECENTLY_SEPARATED_VETERAN",
  "NOT_A_VETERAN",
  "PROTECTED_VETERAN",
  "PREFER_NOT_TO_ANSWER",
];

class User extends Model {
  declare id: number;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare managerId: ForeignKey<User["id"]>;
  declare departmentId: ForeignKey<Department["id"]>;
  declare role: typeof ROLE[number];
  declare firstName: string;
  declare lastName?: string;
  declare employeeId?: string;
  declare phone?: string;
  declare email?: string;
  declare gender?: string;
  declare ethnicity?: typeof ETHNICITY[number];
  declare veteranStatus?: string;
  declare emergencyContactFirstName?: string;
  declare emergencyContactLastName?: string;
  declare emergencyContactRelationship?: string;
  declare emergencyContactPhone?: string;
  declare profilePhotoObjectId?: string;
  declare otpSmsCode?: string;
  declare otpCodeSentAt?: Date;
  declare clockInReminderAt?: Date;
  declare clockOutReminderAt?: Date;
  declare isClockInReminderEnabled?: boolean;
  declare isClockOutReminderEnabled?: boolean;
  declare status: string;
  declare position: string;
  declare workerClassification?: typeof WORKER_CLASSIFICATION[number];
  declare checkEmployeeId?: string;
  declare checkContractorId?: string;
  declare isArchived: boolean;
  declare workersCompCodeId?: ForeignKey<WorkersCompCode["id"]>;
  declare timeOffPolicies?: NonAttribute<TimeOffPolicy[]>; // Note this is optional since it's only populated when explicitly requested in code
  declare earningRates?: NonAttribute<EarningRate[]>; // Note this is optional since it's only populated when explicitly requested in code
  declare timeOffPolicyEnrollment?: NonAttribute<TimeOffPolicyEnrollment>;
  declare employeeBenefits?: NonAttribute<ExtendedEmployeeBenefit[]>;
  declare timesheets?: NonAttribute<TimeSheet[]>;
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    role: {
      type: DataTypes.ENUM(...ROLE),
      allowNull: false,
      defaultValue: "WORKER",
    },
    firstName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "First name is required",
        },
      },
    },
    lastName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Last name is required",
        },
      },
    },
    phone: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    email: DataTypes.STRING(100),
    gender: {
      type: DataTypes.ENUM(...GENDER),
      allowNull: true,
    },
    ethnicity: {
      type: DataTypes.ENUM(...ETHNICITY),
      allowNull: true,
    },
    veteranStatus: {
      type: DataTypes.ENUM(...VETERAN_STATUS),
      allowNull: true,
    },
    emergencyContactFirstName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    emergencyContactLastName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    emergencyContactRelationship: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    emergencyContactPhone: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    profilePhotoObjectId: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    workerClassification: {
      type: DataTypes.ENUM(...WORKER_CLASSIFICATION),
      allowNull: false,
      defaultValue: "EMPLOYEE",
    },
    checkEmployeeId: DataTypes.STRING(100),
    checkContractorId: DataTypes.STRING(100),
    status: DataTypes.ENUM(...USER_STATUS),
    position: DataTypes.STRING(100),
    otpSmsCode: DataTypes.STRING(100),
    otpCodeSentAt: DataTypes.DATE,
    employeeId: DataTypes.STRING(100),
    clockInReminderAt: DataTypes.TIME,
    clockOutReminderAt: DataTypes.TIME,
    isClockInReminderEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    isClockOutReminderEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    departmentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "Departments",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "user",
    tableName: "Users",
  }
);

export interface ExtendedUser extends User {
  organizationName: string;
  organizationId: number;
  managerId: number;
  organizationCheckCompanyId: string;
  organizationIsRegisteredOnCheck: boolean;
  isCompanyAdmin: boolean;
  organization?: Partial<Organization>;
  manager?: Partial<User>;
  timesheetId?: number;
  clockIn?: number;
  geofenceExitAt?: number;
  clockInLocation?: number[];
  description?: string;
  project?: {
    id: number;
    name: string;
  };
  breaks?: any[];
  totalMinutesToday?: number;
  timesheetsForSwitch?: any[];
  earningRates?: ExtendedEarningRate[];
  timesheets?: TimeSheet[];
}

export type ExtendedUserPartial = Partial<User> & {
  salary?: number;
  earningRates?: ExtendedEarningRate[];
};

export default User;
