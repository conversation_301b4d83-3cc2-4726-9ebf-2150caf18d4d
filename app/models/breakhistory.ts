import { Model, DataTypes, ForeignKey } from "sequelize";
import User from "./user";
import TimeSheet from "./timesheet";
import sequelize from "@/lib/sequelize";

class BreakHistory extends Model {
  public id!: number;
  public breakId?: number; // nullable because we don't have a breakId for CREATE
  public history: string;
  public type: string;
  declare createdBy: ForeignKey<User["id"]>;
  declare timesheetId: ForeignKey<TimeSheet["id"]>;
}

BreakHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    breakId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    history: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM("CREATE", "UPDATE", "DELETE"),
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "breakHistory",
    tableName: "BreakHistories",
  }
);

export default BreakHistory;
