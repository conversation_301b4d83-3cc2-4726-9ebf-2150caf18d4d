import { Model, DataTypes, ForeignKey, NonAttribute } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import EquipmentCategory from "@/models/equipmentcategory";

class Equipment extends Model {
  declare id: number;
  declare name: string;
  declare categoryId: ForeignKey<EquipmentCategory["id"]>;
  declare year: number;
  declare hourlyCost: number;
  declare isArchived: boolean;
  declare organizationId: ForeignKey<Organization["id"]>;
  // not saved in the db, pulled from the category
  declare categoryName?: string;
  declare category?: NonAttribute<EquipmentCategory>;

  declare timesheets?: any[];
}

Equipment.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: EquipmentCategory,
        key: "id",
      },
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    hourlyCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "equipment",
    tableName: "Equipments",
  }
);

export default Equipment;
