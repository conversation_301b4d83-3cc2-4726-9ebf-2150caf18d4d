import { CreationOptional, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import TimeOffPolicy from "@/models/timeoffpolicy";
import User from "@/models/user";

class TimeOffPolicyEnrollment extends Model<
  InferAttributes<TimeOffPolicyEnrollment>,
  InferCreationAttributes<TimeOffPolicyEnrollment>
> {
  declare id: number;
  declare timeOffPolicyId: ForeignKey<TimeOffPolicy["id"]>;
  declare userId: ForeignKey<User["id"]>;
  declare startDate: Date;
  declare startingBalance: number;

  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
}

TimeOffPolicyEnrollment.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
    startDate: DataTypes.DATE,
    startingBalance: DataTypes.FLOAT,
  },
  {
    sequelize,
    tableName: "TimeOffPolicyEnrollments",
    modelName: "TimeOffPolicyEnrollment",
  }
);

export default TimeOffPolicyEnrollment;
