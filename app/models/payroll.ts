import dayjs from "dayjs";
import { Organization } from "@/models";
import { ExtendedTimesheet, UserEarningsSummary } from "@/models/timesheet";
import TimeOffRequest from "@/models/timeoffrequest";
import { Payroll } from "@/types/check";
import { ExtendedEmployeeReimbursement } from "./employeereimbursement";

export interface PayrollPeriodDates {
  periodStartTimestamp: number;
  periodEndTimestamp: number;
  adjustedDates: {
    start: dayjs.Dayjs;
    end: dayjs.Dayjs;
  };
}

export interface UserToRemoveSummaryFrom {
  id: number;
  firstName: string;
  lastName: string;
  checkEmployeeId?: string;
  checkContractorId?: string;
}

export interface PayrollProcessingData {
  type?: "regular" | "off_cycle" | "amendment";
  payroll: Payroll;
  organization: Organization;
  periodDates: PayrollPeriodDates;
  timesheets: ExtendedTimesheet[];
  timeOffRequests: TimeOffRequest[];
  userEarningsSummaries: UserEarningsSummary[];
  salariedEmployeeSummaries: UserEarningsSummary[];
  contractorSummaries?: UserEarningsSummary[];
  hasUnapprovedTimesheets: boolean;
  usersMissingCheckIds: UserToRemoveSummaryFrom[];
  employeesNotFinishedOnboarding: UserToRemoveSummaryFrom[];
  contractorsNotFinishedOnboarding: UserToRemoveSummaryFrom[];
  hasMissingPayrollUsers: boolean;
  // salariedEmployeesToSkip is an array of Check employee ids
  salariedEmployeesToSkip?: string[];
  perDiemReimbursements?: ExtendedEmployeeReimbursement[];
}

export interface PayrollUpdateData extends PayrollProcessingData {
  payloadData: any;
  existingPayrollData: Payroll;
  checkEmployeeData: any[];
}
