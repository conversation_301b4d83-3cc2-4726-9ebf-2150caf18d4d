import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";

class EmployeeCertification extends Model {
  declare id: number;
  declare objectId: string;
  declare title: string;
  declare number: string;
  declare issuingEntity: string;
  declare completionDate: Date;
  declare expirationDate: Date;
  declare isArchived: boolean;
  declare createdBy: ForeignKey<User["id"]>; // person who created the document
  declare userId: ForeignKey<User["id"]>; // person who the document is for
  declare organizationId: ForeignKey<Organization["id"]>;
}

EmployeeCertification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    title: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    objectId: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    number: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    issuingEntity: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    completionDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    expirationDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "employeeCertification",
    tableName: "EmployeeCertifications",
  }
);

export default EmployeeCertification;
