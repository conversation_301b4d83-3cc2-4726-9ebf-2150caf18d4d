import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import WageTable from "@/models/wagetable";
import { ExtendedFringeBenefitClassification } from "@/models/fringebenefitclassification";

class Classification extends Model {
  public id!: number;
  public name!: string;
  public basePay!: string;
  public fringePay!: string;
  public startDate!: Date;
  public endDate: Date;
  public checkRegEarningCodeId: string;
  public checkOtEarningCodeId: string;
  public checkDotEarningCodeId: string;
  declare wageTableId: ForeignKey<WageTable["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare metadata?: JSON;
}

Classification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    basePay: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "0.00",
    },
    fringePay: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "0.00",
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    checkRegEarningCodeId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    checkOtEarningCodeId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    checkDotEarningCodeId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "classification",
    tableName: "Classifications",
  }
);

export default Classification;

export interface ExtendedClassification extends Classification {
  wageTable?: WageTable;
  sumOfFringeBenefits?: number;
  sumOfEmployeeBenefits?: number;
  cashFringe?: string;
  regRateOfPay?: number;
  otRateOfPay?: number;
  organization?: Organization;
  fringeBenefitClassifications?: ExtendedFringeBenefitClassification[];
}
