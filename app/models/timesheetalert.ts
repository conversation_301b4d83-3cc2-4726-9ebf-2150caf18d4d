import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "@/models/user";
import TimeSheet from "./timesheet";

class TimesheetAlert extends Model {
  public id!: number;
  public type: string;
  public isResolved: boolean;
  public resolutionNote: string;
  public resolutionDate: Date;
  declare timesheetId: ForeignKey<TimeSheet["id"]>;
  declare resolvedBy: ForeignKey<User["id"]>;
}

TimesheetAlert.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    type: {
      type: DataTypes.ENUM("CLOCK_OUT_LOCATION_OUTSIDE_GEOFENCE", "CLOCK_OUT_LOCATION_MISSING"),
      allowNull: false,
    },
    isResolved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    resolutionNote: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    resolutionDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "timesheetAlert",
    tableName: "TimesheetAlerts",
  }
);

export default TimesheetAlert;
