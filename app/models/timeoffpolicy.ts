import { DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model, NonAttribute } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import EarningRate from "./earningrate";
import User from "@/models/user";

export enum TimeOffPolicyType {
  PTO = "PTO",
  SICK = "SICK",
  UNPAID = "UNPAID",
  PERSONAL = "PERSONAL",
  BEREAVEMENT = "BEREAVEMENT",
  JURY_DUTY = "JURY_DUTY",
  VOLUNTEER = "VOLUNTEER",
}

export enum AccrualMethod {
  FIXED = "FIXED",
  ACCRUED = "ACCRUED",
  HOURS_WORKED = "HOURS_WORKED",
}

export default class TimeOffPolicy extends Model<
  InferAttributes<TimeOffPolicy>,
  InferCreationAttributes<TimeOffPolicy>
> {
  declare id: number;
  declare name: string;
  declare type: TimeOffPolicyType;
  declare isLimited: boolean;
  declare endDate?: Date;
  declare accrualMethod?: AccrualMethod;

  // used for HOURS_WORKED accrual method
  declare accrualHoursRate?: number;
  declare accrualHoursInterval?: number;

  //----------------------------------------------------------------------
  // virtual properties which are set in the service when requesting time-off policies for a user
  declare availableHours?: number;
  declare accruedHours?: number;
  declare accruedHoursBasedOnAccrualLimit?: number;
  declare usedHours?: number;
  declare carryoverHours?: number;
  //----------------------------------------------------------------------

  // if this is unset, the accrue calculation algorithm will use the employee's startDate instead
  // (or enrollment date, whichever is closer to the current date)
  declare accrualResetDate?: string;
  declare carryoverLimit?: number;
  declare addNewEmployeesAutomatically?: boolean;
  declare accrualLimit?: number;
  declare organizationId: ForeignKey<Organization["id"]>;

  // potential joins
  declare earningRates?: NonAttribute<EarningRate[]>;
  declare users?: NonAttribute<User[]>;
}

TimeOffPolicy.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM(...Object.values(TimeOffPolicyType)),
      allowNull: false,
    },
    isLimited: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    endDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    accrualMethod: {
      type: DataTypes.ENUM(...Object.values(AccrualMethod)),
      allowNull: true,
    },
    accrualHoursRate: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    accrualResetDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    carryoverLimit: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },

    //-----------------------------------------------------------------------
    // These fields are returned when requesting user time off policies
    // They are used to verify how many hours a user has accrued on a policy
    availableHours: {
      type: DataTypes.VIRTUAL,
    },
    accruedHours: {
      type: DataTypes.VIRTUAL,
    },
    accruedHoursBasedOnAccrualLimit: {
      type: DataTypes.VIRTUAL,
    },
    usedHours: {
      type: DataTypes.VIRTUAL,
    },
    carryoverHours: {
      type: DataTypes.VIRTUAL,
    },
    //-----------------------------------------------------------------------

    addNewEmployeesAutomatically: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    accrualLimit: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    accrualHoursInterval: {
      type: DataTypes.FLOAT,
      allowNull: true,
      comment:
        "Number of hours worked needed to accrue time off. For example, 16 means employee accrues time off for every 16 hours worked",
    },
  },
  {
    sequelize,
    tableName: "TimeOffPolicies",
    modelName: "TimeOffPolicy",
  }
);
