import { Model, DataTypes } from "sequelize";
import sequelize from "@/lib/sequelize";

class DeviceToken extends Model {
  declare id: number;
  public token!: string;
  public userId!: number;
  public platform!: string;
}

DeviceToken.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    token: DataTypes.STRING,
    userId: DataTypes.INTEGER,
    platform: {
      type: DataTypes.ENUM("IOS", "ANDROID"),
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "deviceToken",
    tableName: "DeviceTokens",
  }
);

export default DeviceToken;
