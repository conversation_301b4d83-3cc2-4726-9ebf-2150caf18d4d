import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";

class WorkersCompCode extends Model {
  declare id: number;
  public name!: string;
  public code!: string;
  public isArchived!: boolean;
  public organizationId!: ForeignKey<Organization["id"]>;
}

WorkersCompCode.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "workersCompCode",
    tableName: "WorkersCompCodes",
  }
);

export default WorkersCompCode;
