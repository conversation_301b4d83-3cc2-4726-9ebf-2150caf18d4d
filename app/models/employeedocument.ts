import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";

class EmployeeDocument extends Model {
  declare id: number;
  declare objectId: string;
  declare name: string;
  declare isArchived: boolean;
  declare createdBy: ForeignKey<User["id"]>; // person who created the document
  declare userId: ForeignKey<User["id"]>; // person who the document is for
  declare organizationId: ForeignKey<Organization["id"]>;
}

EmployeeDocument.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    objectId: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "employeeDocument",
    tableName: "EmployeeDocuments",
  }
);

export default EmployeeDocument;
