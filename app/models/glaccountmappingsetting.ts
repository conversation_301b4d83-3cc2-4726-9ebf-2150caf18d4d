import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "@/models/user";
import IntegrationUserToken from "./integrationusertoken";
import Organization from "./organization";

export const GL_ACCOUNT_MAPPING_SETTINGS = {
  autoSync: {
    name: "Auto Sync",
    values: [
      { name: "Enabled", value: true },
      { name: "Disabled", value: false },
    ],
  },
  consolidateJournalEntryBy: {
    name: "Consolidate Journal Entries By",
    values: [
      { name: "Default", value: "DEFAULT" },
      // { name: "Employee", value: "EMPLOYEE" },
      { name: "Department", value: "DEPARTMENT" },
      { name: "Project", value: "PROJECT" },
    ],
  },
  entryDate: {
    name: "Entry Date",
    values: [
      { name: "Pay date", value: "PAY_DATE" },
      { name: "End of pay period", value: "END_OF_PAY_PERIOD" },
    ],
  },
};

// Derive other constants from the main object
export const GL_ACCOUNT_MAPPING_SETTING_TYPES = Object.keys(GL_ACCOUNT_MAPPING_SETTINGS);
export const CONSOLIDATE_JOURNAL_ENTRY_TYPES = GL_ACCOUNT_MAPPING_SETTINGS.consolidateJournalEntryBy.values.map(
  (v) => v.value
);
export const ENTRY_DATE_TYPES = GL_ACCOUNT_MAPPING_SETTINGS.entryDate.values.map((v) => v.value);

export const CONSOLIDATE_BY = {
  SINGLE_ENTRY: "SINGLE_ENTRY",
  EMPLOYEE: "EMPLOYEE",
  DEPARTMENT: "DEPARTMENT",
  PROJECT: "PROJECT",
} as const;

class GlAccountMappingSetting extends Model {
  public id!: number;
  public autoSync?: boolean;
  public entryDate?: string;
  public consolidateJournalEntryBy?: string;
  public createdBy: ForeignKey<User["id"]>;
  public integrationUserTokenId: ForeignKey<IntegrationUserToken["id"]>;
  public organizationId: ForeignKey<Organization["id"]>;
}

GlAccountMappingSetting.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    autoSync: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    entryDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    consolidateJournalEntryBy: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
    },
    integrationUserTokenId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "IntegrationUserTokens",
        key: "id",
      },
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Organizations",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "glAccountMappingSetting",
    tableName: "GlAccountMappingSettings",
  }
);

export default GlAccountMappingSetting;
