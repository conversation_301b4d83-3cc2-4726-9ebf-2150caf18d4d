import { Model, DataTypes } from "sequelize";
import sequelize from "@/lib/sequelize";

class Break extends Model {
  public id!: number;
  public start!: Date;
  public end?: Date;
  public isDeleted: boolean;
  public isManual: boolean;
  public createdBy: number;
  public editedBy: number;
  public timesheetId: number;
  public rawStart?: number;
  public rawEnd?: number;
}

Break.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    start: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    end: DataTypes.DATE,
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isManual: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    createdBy: DataTypes.INTEGER,
    editedBy: DataTypes.INTEGER,
  },
  {
    sequelize,
    modelName: "break",
    tableName: "Breaks",
  }
);

export default Break;
