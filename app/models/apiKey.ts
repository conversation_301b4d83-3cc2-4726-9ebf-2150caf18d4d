import { DataTypes, Foreign<PERSON><PERSON>, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "./organization";

class APIKey extends Model {
  declare id: number;
  public apiKey!: string;
  declare organizationId: ForeignKey<Organization["id"]>;
}

APIKey.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    apiKey: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
  },
  {
    sequelize,
    modelName: "apiKey",
    tableName: "APIKeys",
  }
);

export default APIKey;
