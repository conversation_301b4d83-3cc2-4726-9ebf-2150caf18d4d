import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";

class EquipmentCategory extends Model {
  declare id: number;
  declare name: string;
  declare organizationId: ForeignKey<Organization["id"]>;
}

EquipmentCategory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "equipmentCategory",
    tableName: "EquipmentCategories",
    indexes: [
      {
        unique: true,
        fields: ["name", "organization_id"],
      },
    ],
  }
);

export default EquipmentCategory;
