import { Model, DataTypes } from "sequelize";
import sequelize from "@/lib/sequelize";

class UserScheduleEvent extends Model {
  userId!: number;
  scheduleEventId!: number;
}

UserScheduleEvent.init(
  {
    userId: DataTypes.INTEGER,
    scheduleEventId: DataTypes.INTEGER,
  },
  {
    sequelize,
    modelName: "userScheduleEvent",
    tableName: "UserScheduleEvents",
  }
);

export default UserScheduleEvent;
