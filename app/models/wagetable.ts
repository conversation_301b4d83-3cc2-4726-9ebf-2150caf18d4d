import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import { ExtendedUser } from "./user";
import { ExtendedClassification } from "./classification";
import { ExtendedFringeBenefit } from "./fringebenefit";
import { ExtendedEmployeeBenefit } from "./employeebenefit";

class WageTable extends Model {
  public id!: number;
  public name!: string;
  public description?: string;
  declare organizationId: ForeignKey<Organization["id"]>;
}

WageTable.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    description: DataTypes.STRING(2000),
  },
  {
    sequelize,
    modelName: "wageTable",
    tableName: "WageTables",
  }
);

export default WageTable;

export interface ExtendedWageTable extends WageTable {
  user?: ExtendedUser;
  classification?: ExtendedClassification;
  fringeBenefits?: ExtendedFringeBenefit[];
  employeeBenefits?: ExtendedEmployeeBenefit[];
  cashFringe?: number;
}
