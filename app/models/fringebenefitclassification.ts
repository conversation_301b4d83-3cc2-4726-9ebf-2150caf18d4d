import { Model, DataTypes, ForeignKey } from "sequelize";

import sequelize from "@/lib/sequelize";

import FringeBenefit, { FRINGE_CATEGORY_TYPES } from "@/models/fringebenefit";
import Organization from "@/models/organization";
import Classification from "@/models/classification";

class FringeBenefitClassification extends Model {
  public id!: number;
  public amount: string; // this is assumed per hour
  public startDate!: Date;
  public endDate: Date;
  public fringeBenefitId!: ForeignKey<FringeBenefit["id"]>;
  public classificationId!: ForeignKey<Classification["id"]>;
  public organizationId!: ForeignKey<Organization["id"]>;
}

FringeBenefitClassification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    amount: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "0.00",
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    fringeBenefitId: {
      type: DataTypes.INTEGER,
      references: {
        model: FringeBenefit,
        key: "id",
      },
      allowNull: false,
    },
    classificationId: {
      type: DataTypes.INTEGER,
      references: {
        model: Classification,
        key: "id",
      },
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "fringeBenefitClassification",
    tableName: "FringeBenefitClassifications",
  }
);

export default FringeBenefitClassification;

export interface ExtendedFringeBenefitClassification extends FringeBenefitClassification {
  fringeBenefit?: FringeBenefit;
  classification?: Classification;
  organization?: Organization;
}

export interface SimplifiedFringeBenefitClassification {
  id: number;
  name: string;
  category: typeof FRINGE_CATEGORY_TYPES[number];
  amount: string;
}
