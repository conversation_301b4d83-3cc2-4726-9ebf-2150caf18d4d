import { Model, DataTypes } from "sequelize";
import sequelize from "@/lib/sequelize";

class EquipmentScheduleEvent extends Model {
  equipmentId!: number;
  scheduleEventId!: number;
}

EquipmentScheduleEvent.init(
  {
    equipmentId: DataTypes.INTEGER,
    scheduleEventId: DataTypes.INTEGER,
  },
  {
    sequelize,
    modelName: "equipmentScheduleEvent",
    tableName: "EquipmentScheduleEvents",
  }
);

export default EquipmentScheduleEvent;
