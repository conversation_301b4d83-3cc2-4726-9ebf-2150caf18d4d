import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import IntegrationUserToken from "./integrationusertoken";

class IntegrationSyncHistory extends Model {
  public id!: number;
  public payrollId: string;
  public syncedAt: Date;
  public integrationUserTokenId: ForeignKey<IntegrationUserToken["id"]>;
}

IntegrationSyncHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    payrollId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    syncedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    failedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    integrationUserTokenId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "IntegrationUserTokens",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "integrationSyncHistory",
    tableName: "IntegrationSyncHistories",
  }
);

export default IntegrationSyncHistory;
