import { Model, DataTypes, ForeignKey, CreationOptional } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "@/models/user";
import Organization from "@/models/organization";

export enum EARNING_RATE_PERIODS {
  HOURLY = "HOURLY",
  ANNUALLY = "ANNUALLY",
  PIECE = "PIECE",
}

export enum EARNING_RATE_TYPES {
  REG = "REG",
  OT = "OT",
  DOT = "DOT",
}

class EarningRate extends Model {
  public id!: number;
  public amount: string;
  public period: string; // debated whether we needed; but flexibility is good and this will map to check
  public userId: ForeignKey<User["id"]>;
  public organizationId: ForeignKey<Organization["id"]>;
  public name: string;
  public active: boolean;
  public type: string; // we want type so that we can manage reg vs ot earning rates systematically
  public weeklyHours: number;
  public checkEarningRateId: string;
  public startDate: Date;
  public endDate: Date;
  public metadata?: JSON;

  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
}

/*

The idea behind earning rate is that there will ever ONLY BE TWO ACTIVE earning rates for a user at a time.
The two active earnings rates should be reg and ot.

*/

EarningRate.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    amount: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "0.00",
    },
    period: {
      type: DataTypes.ENUM(...Object.values(EARNING_RATE_PERIODS)),
      allowNull: false,
      defaultValue: EARNING_RATE_PERIODS.HOURLY, // default to HOURLY
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "Regular",
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    type: {
      // hopefully values are: REG or OT or something else for prevailing wage/etc - add to enum
      type: DataTypes.ENUM(...Object.values(EARNING_RATE_TYPES)),
      allowNull: false,
      defaultValue: EARNING_RATE_TYPES.REG, // default to REG
    },
    checkEarningRateId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    weeklyHours: {
      type: DataTypes.DECIMAL(5, 2), // 5 digits, 2 decimal places - ex: 168.00 or 97.50
      allowNull: true,
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: new Date(),
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "Users",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "earningRate",
    tableName: "EarningRates",
  }
);

export interface ExtendedEarningRate extends EarningRate {
  startDateTimestamp?: number;
  endDateTimestamp?: number;
  effectiveStartDate?: number;
}

export default EarningRate;
