import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";

class Crew extends Model {
  public id!: number;
  public name!: string;
  declare crewLead: ForeignKey<User["id"]>;
  declare createdBy: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
}

Crew.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: DataTypes.STRING,
  },
  {
    sequelize,
    modelName: "crew",
    tableName: "Crews",
  }
);

export default Crew;
