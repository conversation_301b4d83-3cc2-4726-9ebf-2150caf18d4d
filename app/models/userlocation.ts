import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";
import TimeSheet from "./timesheet";
import Break from "./break";
import { Geometry } from "geojson";

export const PLATFORM_TYPES = ["IOS", "ANDROID"];

export const APP_STATE_TYPES = ["BACKGROUND", "FOREGROUND"];

export const LOCATION_PERMISSION_TYPES = [
  "NOT_DETERMINED",
  "WHEN_IN_USE",
  "ALWAYS",
  "DENIED",
  "RESTRICTED",
  "TURNED_OFF",
];

export const LOCATION_EVENT_TYPES = [
  "GEOFENCE_ENTER",
  "GEOFENCE_EXIT",
  "LOCATION_CHANGE",
  "CLOCK_IN",
  "CLOCK_OUT",
  "BREAK_START",
  "BREAK_END",
];

class UserLocation extends Model {
  public id!: number;
  public locationCoordinates!: Geometry;
  // will store location's address so clients don't have to reverse geocode them every time
  // if geofenceProjectId is assigned, clients will display the project name instead of locationAddress
  public locationAddress: string;
  // it's not necessary that this projectId matches the project of the timesheetId
  // it could happen that user is clocked-in on project A and enters the geofence of project B
  // in which case we will create a UserLocation of type GEOFENCE_ENTER with projectId of project B
  declare geofenceProjectId: ForeignKey<TimeSheet["id"]>;
  /** Speed in meters per second (m/s) */
  public speed: number;
  /** Horizontal accuracy in meters (m) */
  public horizontalAccuracy: number;
  /** Vertical accuracy in meters (m) */
  public verticalAccuracy: number;
  /** Altitude in meters (m) */
  public altitude: number;
  public loggedAt!: Date;
  public platform!: string;
  public appState!: string;
  public locationPermission!: string;
  public preciseLocationEnabled!: boolean;
  public locationEvent!: string;
  declare userId: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare timesheetId: ForeignKey<TimeSheet["id"]>;
  declare breakId: ForeignKey<Break["id"]>; // if locationEvent is BREAK_START or BREAK_END
}

UserLocation.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    locationCoordinates: {
      type: DataTypes.GEOMETRY("POINT"),
      allowNull: false,
    },
    locationAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    speed: DataTypes.FLOAT,
    horizontalAccuracy: DataTypes.FLOAT,
    verticalAccuracy: DataTypes.FLOAT,
    altitude: DataTypes.FLOAT,
    loggedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    platform: {
      type: DataTypes.ENUM(...PLATFORM_TYPES),
      allowNull: false,
    },
    appState: {
      type: DataTypes.ENUM(...APP_STATE_TYPES),
      allowNull: false,
    },
    locationPermission: {
      type: DataTypes.ENUM(...LOCATION_PERMISSION_TYPES),
      allowNull: false,
    },
    preciseLocationEnabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    locationEvent: {
      type: DataTypes.ENUM(...LOCATION_EVENT_TYPES),
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "userLocation",
    tableName: "UserLocations",
  }
);

export default UserLocation;
