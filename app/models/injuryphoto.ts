import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import InjuryReport from "./injuryreport";

class InjuryPhoto extends Model {
  public id!: number;
  public objectId!: string;
  public isDeleted!: boolean;
  declare injuryReportId: ForeignKey<InjuryReport["id"]>;
  declare createdBy: ForeignKey<User["id"]>;
}

InjuryPhoto.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    objectId: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "injuryPhoto",
    tableName: "InjuryPhotos",
  }
);

export default InjuryPhoto;
