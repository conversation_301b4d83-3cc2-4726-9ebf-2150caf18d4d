import { Model, DataTypes, ForeignKey } from "sequelize";
import User from "./user";
import sequelize from "@/lib/sequelize";

class TimesheetHistory extends Model {
  public id!: number;
  public timesheetId: number;
  public history: string;
  public type: string;
  declare createdBy: ForeignKey<User["id"]>;
}

TimesheetHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    timesheetId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    history: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM("UPDATE"),
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "timesheetHistory",
    tableName: "TimesheetHistories",
  }
);

export default TimesheetHistory;
