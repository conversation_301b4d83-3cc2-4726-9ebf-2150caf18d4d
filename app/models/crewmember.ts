import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Crew from "./crew";

class CrewMember extends Model {
  public id!: number;
  declare crewId: ForeignKey<Crew["id"]>;
  declare userId: ForeignKey<User["id"]>;
}

CrewMember.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
  },
  {
    sequelize,
    modelName: "crewMember",
    tableName: "CrewMembers",
  }
);

export default CrewMember;
