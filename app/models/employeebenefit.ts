import { Model, DataTypes, ForeignKey } from "sequelize";
import { uniq } from "lodash";

import sequelize from "@/lib/sequelize";

import User from "@/models/user";
import CompanyBenefit from "@/models/companybenefit";

import { CONTRIBUTION_TYPES } from "@/models/companybenefit";

export const CHANGE_REASON = ["OPEN_ENROLLMENT", "VOLUNTARY_CHANGE", "QUALIFYING_LIFE_EVENT", "ADMIN_CORRECTION"];

export const UNENROLL_REASON = [
  "TERMINATION",
  "OPEN_ENROLLMENT",
  "VOLUNTARY_UNENROLLMENT",
  "QUALIFYING_LIFE_EVENT",
  "ADMIN_CORRECTION",
];

export const ALL_CHANGE_REASONS = uniq([...CHANGE_REASON, ...UNENROLL_REASON]);

class EmployeeBenefit extends Model {
  public id!: number;
  public name!: string;
  public contributionType!: string; // percentage vs amount - required
  // when period is MONTHLY, we will use companyPeriodAmount and employeePeriodAmount
  // when period is null, we will use companyContributionAmount and employeeContributionAmount
  public period: string; // can be MONTHLY or null
  public companyContributionPercent: number; // float
  public employeeContributionPercent: number; // float
  public companyPeriodAmount: string;
  public employeePeriodAmount: string;
  public companyContributionAmount: string;
  public employeeContributionAmount: string;
  public benefitStartDate: string; // string YYYY-MM-DD
  public benefitEndDate: string; // string YYYY-MM-DD
  public changeReason: string;
  public checkBenefitId: string;
  public metadata?: JSON; // we can have this, but might be useful to sync our ids on check's side with their metadata
  public companyBenefitId: ForeignKey<CompanyBenefit["id"]>;
  public userId: ForeignKey<User["id"]>;
  public organizationId: ForeignKey<User["organizationId"]>;
}

EmployeeBenefit.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    contributionType: {
      type: DataTypes.ENUM(...CONTRIBUTION_TYPES),
      allowNull: false,
    },
    period: {
      type: DataTypes.ENUM("MONTHLY"),
      allowNull: true,
    },
    companyContributionPercent: {
      type: DataTypes.FLOAT,
      allowNull: true,
      defaultValue: 0.0,
    },
    employeeContributionPercent: {
      type: DataTypes.FLOAT,
      allowNull: true,
      defaultValue: 0.0,
    },
    // companyPeriodAmount and employeePeriodAmount are per month amounts
    companyPeriodAmount: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: "0.00",
    },
    employeePeriodAmount: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: "0.00",
    },
    // companyContributionAmount and employeeContributionAmount are per pay period amounts
    companyContributionAmount: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: "0.00",
    },
    employeeContributionAmount: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: "0.00",
    },
    benefitStartDate: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    benefitEndDate: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    changeReason: {
      type: DataTypes.ENUM(...ALL_CHANGE_REASONS),
      allowNull: true,
    },
    checkBenefitId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "employeeBenefits",
    tableName: "EmployeeBenefits",
  }
);

export default EmployeeBenefit;

export interface ExtendedEmployeeBenefit extends EmployeeBenefit {
  companyBenefit?: CompanyBenefit;
  user?: User;
  category?: string; // this needs a helper/method to allocate this value from the parent company benefit
  fringeHourlyContribution?: number;
  employeeHourlyContribution?: number;
}
