import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";

// In the transformation the only values we care about are reimbursementAmount and resourceId
// userId will be used to get the resourceId - either checkEmployeeId or checkContractorId
export interface ExtendedEmployeeReimbursement {
  id: number;
  reimbursementAmount: string;
  userId?: number;
  organizationId?: ForeignKey<Organization["id"]>;
  // extra query attributes below
  resourceId?: string;
  user?: User;
  // code is added in the transformation - it should always be "per_diem"
  code?: string;
}

class EmployeeReimbursement extends Model {
  declare id: number;
  declare reimbursementAmount: string;
  declare userId: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
}

EmployeeReimbursement.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    reimbursementAmount: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: "0.00",
    },
  },
  {
    sequelize,
    modelName: "employeeReimbursement",
    tableName: "EmployeeReimbursements",
  }
);

export default EmployeeReimbursement;
