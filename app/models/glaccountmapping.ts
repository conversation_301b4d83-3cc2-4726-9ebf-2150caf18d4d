import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "@/models/user";
import IntegrationUserToken from "./integrationusertoken";
import Organization from "./organization";
import Department from "./department";

export const PAYROLL_CATEGORY_GROUPS = {
  ASSETS: ["BANK_ACCOUNT"],
  EXPENSES: [
    "WAGES_AND_SALARIES",
    "CONTRACTOR_PAYMENTS",
    "EMPLOYER_TAX_EXPENSE",
    "EMPLOYER_BENEFITS_EXPENSE",
    "EXPENSE_REIMBURSEMENTS",
  ],
  LIABILITIES: [
    "EMPLOYEE_BENEFITS_LIABILITY",
    "EMPLOYER_BENEFITS_LIABILITY",
    "GARNISHMENTS_PAYABLE",
    "MANUAL_PAYMENTS_LIABILITY",
  ],
} as const;

export const PAYROLL_CATEGORY_TYPES: string[] = [
  ...PAYROLL_CATEGORY_GROUPS.ASSETS,
  ...PAYROLL_CATEGORY_GROUPS.EXPENSES,
  ...PAYROLL_CATEGORY_GROUPS.LIABILITIES,
];

export const PAYROLL_CATEGORY_TYPES_MAPPING = {
  // Assets
  BANK_ACCOUNT: "Bank Account",
  // Expenses
  WAGES_AND_SALARIES: "Wages and Salaries",
  CONTRACTOR_PAYMENTS: "Contractor Payments",
  EMPLOYER_TAX_EXPENSE: "Employer Taxes",
  EMPLOYER_BENEFITS_EXPENSE: "Employer Benefits Expense",
  EXPENSE_REIMBURSEMENTS: "Expense Reimbursements",
  // Liabilities
  EMPLOYEE_BENEFITS_LIABILITY: "Employee Benefits Withheld Payable",
  EMPLOYER_BENEFITS_LIABILITY: "Employer Benefits Contribution Payable",
  GARNISHMENTS_PAYABLE: "Garnishments/Deductions Payable",
  MANUAL_PAYMENTS_LIABILITY: "Manual Payments Liability",
};

class GlAccountMapping extends Model {
  public id!: number;
  public accountId!: string;
  public accountName?: string;
  public platformId?: string;
  public payrollCategory?: string;
  public accountType?: string;
  public accountCategory?: string;
  public accountCostClass?: string;
  public departmentId?: ForeignKey<Department["id"]>;
  public createdBy: ForeignKey<User["id"]>;
  public integrationUserTokenId: ForeignKey<IntegrationUserToken["id"]>;
  public organizationId: ForeignKey<Organization["id"]>;
}

GlAccountMapping.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    accountId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    accountName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    platformId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    payrollCategory: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    accountType: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    accountCategory: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    accountCostClass: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    departmentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "Departments",
        key: "id",
      },
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
    },
    integrationUserTokenId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "IntegrationUserTokens",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "glAccountMapping",
    tableName: "GlAccountMappings",
  }
);

export default GlAccountMapping;
