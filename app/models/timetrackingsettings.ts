import { DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "./organization";

export const TIMESHEET_ROUNDING_TYPES = ["ROUND_UP", "ROUND_DOWN", "ROUND_NEAREST", "EMPLOYEE_FRIENDLY"];
export const TIMESHEET_ROUNDING_INTERVALS = ["FIVE_MINUTES", "TEN_MINUTES", "FIFTEEN_MINUTES"];

export interface RoundingSettings {
  timesheetRoundingEnabled: boolean;
  timesheetRoundingType: string;
  timesheetRoundingInterval: string;
}

class TimeTrackingSettings extends Model<
  InferAttributes<TimeTrackingSettings>,
  InferCreationAttributes<TimeTrackingSettings>
> {
  declare id: number;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare clockInReminderAt?: Date;
  declare clockOutReminderAt?: Date;
  declare allowWorkersToAddEditTime: boolean;
  declare allowForemanToCreateProjects: boolean;
  declare areBreaksPaid: boolean;
  declare areRealtimeBreaksEnabled: boolean;
  declare areRealtimeBreakRemindersEnabled: boolean;
  declare realtimeBreakStartReminderAt?: Date;
  declare realtimeBreakEndReminderAfter?: number;
  declare useDecimalHours: boolean;
  declare breakOptions: number[];
  declare locationBreadcrumbingEnabled?: boolean;
  declare isClockinClockoutPhotosEnabled: boolean;
  declare showWagesToWorkers: boolean;
  declare timesheetRoundingEnabled: boolean;
  declare timesheetRoundingType: string;
  declare timesheetRoundingInterval: string;
  declare isCostCodeRequired: boolean;
  declare isInjuryReportRequired: boolean;
  declare isDriveTimeEnabled: boolean;
  declare driveTimeRate: number;
}

TimeTrackingSettings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: Organization,
        key: "id",
      },
    },
    clockInReminderAt: DataTypes.TIME,
    clockOutReminderAt: DataTypes.TIME,
    allowWorkersToAddEditTime: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    areBreaksPaid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    areRealtimeBreaksEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    areRealtimeBreakRemindersEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    realtimeBreakStartReminderAt: DataTypes.TIME,
    realtimeBreakEndReminderAfter: DataTypes.INTEGER,
    useDecimalHours: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    breakOptions: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      defaultValue: [0, 15, 30, 60],
      allowNull: false,
    },
    locationBreadcrumbingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isClockinClockoutPhotosEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    showWagesToWorkers: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    timesheetRoundingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    timesheetRoundingType: {
      type: DataTypes.ENUM(...TIMESHEET_ROUNDING_TYPES),
      allowNull: true,
    },
    timesheetRoundingInterval: {
      type: DataTypes.ENUM(...TIMESHEET_ROUNDING_INTERVALS),
      allowNull: true,
    },
    isCostCodeRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isInjuryReportRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    allowForemanToCreateProjects: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    isDriveTimeEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    driveTimeRate: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "timeTrackingSettings",
    tableName: "TimeTrackingSettings",
  }
);

export default TimeTrackingSettings;
