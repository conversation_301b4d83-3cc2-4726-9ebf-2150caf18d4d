import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";
import TimeSheet from "./timesheet";

export default class InjuryReport extends Model {
  declare id: number;
  declare note: string;
  declare createdBy: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare timesheetId: ForeignKey<TimeSheet["id"]>;
  declare userId: ForeignKey<User["id"]>;
  declare resolvedBy: ForeignKey<User["id"]>;
  declare resolutionNote: string;
  declare resolutionDate: Date;
  declare isResolved: boolean;
}

InjuryReport.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    timesheetId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    resolvedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    resolutionNote: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    isResolved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    resolutionDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "injuryReport",
    tableName: "InjuryReports",
  }
);
