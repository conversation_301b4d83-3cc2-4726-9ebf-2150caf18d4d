import { DataTypes, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import PaySchedule from "./payschedule";
import FeatureFlags from "./featureflags";
import TimeTrackingSettings from "./timetrackingsettings";
import OvertimeSettings from "./overtimesettings";
import PrevailingWageSettings from "./prevailingwagesettings";

class Organization extends Model {
  declare id: number;
  public name!: string;
  public checkCompanyId?: string;
  public isRegisteredOnCheck?: boolean;
  public slug?: string;
  public isChurned!: boolean;
  public timezone!: string;
  public stripeCustomerId?: string;
  public metadata?: Record<string, any>;

  // Add virtual fields for feature flags
  public isSchedulingEnabled!: boolean;
  public isPayrollEnabled!: boolean;
  public isMessagingEnabled!: boolean;
  public isEquipmentTrackingEnabled!: boolean;

  declare paySchedule?: PaySchedule;
  declare timeTrackingSettings: TimeTrackingSettings;
  declare overtimeSettings: OvertimeSettings;
  declare prevailingWageSettings: PrevailingWageSettings;
  declare featureFlag?: FeatureFlags;
}

Organization.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    checkCompanyId: DataTypes.STRING(100),
    isRegisteredOnCheck: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    slug: DataTypes.STRING(100),
    isSchedulingEnabled: {
      type: DataTypes.VIRTUAL,
      get() {
        return this.getDataValue("featureFlag")?.isSchedulingEnabled;
      },
    },
    isPayrollEnabled: {
      type: DataTypes.VIRTUAL,
      get() {
        return this.getDataValue("featureFlag")?.isPayrollEnabled;
      },
    },
    isMessagingEnabled: {
      type: DataTypes.VIRTUAL,
      get() {
        return this.getDataValue("featureFlag")?.isMessagingEnabled;
      },
    },
    isEquipmentTrackingEnabled: {
      type: DataTypes.VIRTUAL,
      get() {
        return this.getDataValue("featureFlag")?.isEquipmentTrackingEnabled;
      },
    },
    isChurned: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    timezone: {
      type: DataTypes.STRING(100),
      defaultValue: "America/Los_Angeles",
      allowNull: false,
    },
    stripeCustomerId: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "organization",
    tableName: "Organizations",
  }
);

export interface ExtendedOrganization extends Organization {
  isSchedulingEnabled: boolean;
  isPayrollEnabled: boolean;
  isMessagingEnabled: boolean;
  isEquipmentTrackingEnabled: boolean;
}

export default Organization;
