import { Model, DataTypes, ForeignKey, NonAttribute } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import WageTable from "./wagetable";
import User, { ExtendedUser } from "./user";
import Classification, { ExtendedClassification } from "./classification";
import { ExtendedEmployeeBenefit } from "./employeebenefit";
import { SimplifiedFringeBenefitClassification } from "./fringebenefitclassification";

class UserClassification extends Model {
  public id!: number;
  public basePay!: string;
  public fringePay!: string;
  public startDate!: Date;
  public endDate?: Date;
  declare classificationId: ForeignKey<Classification["id"]>;
  declare userId: ForeignKey<User["id"]>;
  declare wageTableId: ForeignKey<WageTable["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;

  declare classification?: NonAttribute<Classification>;
}

UserClassification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    basePay: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "0.00",
    },
    fringePay: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "0.00",
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "userClassification",
    tableName: "UserClassifications",
  }
);

export default UserClassification;

export interface ExtendedUserClassification extends UserClassification {
  user?: ExtendedUser;
  cashFringe?: string;
  sumOfFringeBenefits?: number;
  sumOfEmployeeBenefits?: number;
  regRateOfPay?: number;
  otRateOfPay?: number;
  dotRateOfPay?: number;
  classification?: ExtendedClassification;
  wageTable?: WageTable;
  employeeBenefits?: ExtendedEmployeeBenefit[];
  fringeBenefits?: SimplifiedFringeBenefitClassification[];
}
