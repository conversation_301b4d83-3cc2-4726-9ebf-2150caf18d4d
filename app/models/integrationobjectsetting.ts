import { Model, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, NonAttribute } from "sequelize";
import sequelize from "@/lib/sequelize";
import IntegrationUserToken from "@/models/integrationusertoken";

export const OBJECT_TYPES = {
  PAYROLL: "PAYROLL",
  EMPLOYEES: "EMPLOYEES",
  PROJECTS: "PROJECTS",
  TIMESHEETS: "TIMESHEETS",
} as const;

export const SYNC_TYPES = {
  PUSH: "PUSH",
  PULL: "PULL",
  MAP: "MAP",
} as const;

class IntegrationObjectSetting extends Model<
  InferAttributes<IntegrationObjectSetting>,
  InferCreationAttributes<IntegrationObjectSetting>
> {
  declare id: number;
  declare integrationUserTokenId: ForeignKey<IntegrationUserToken["id"]>;
  declare objectType: keyof typeof OBJECT_TYPES;
  declare isEnabled: boolean;
  declare syncType: keyof typeof SYNC_TYPES;
  declare lastSyncedAt?: Date;

  declare integration: NonAttribute<IntegrationUserToken>;
}

IntegrationObjectSetting.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    integrationUserTokenId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    objectType: {
      type: DataTypes.ENUM(...Object.values(OBJECT_TYPES)),
      allowNull: false,
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    syncType: {
      type: DataTypes.ENUM(...Object.values(SYNC_TYPES)),
      allowNull: false,
    },
    lastSyncedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "integrationObjectSetting",
    tableName: "IntegrationObjectSettings",
  }
);

export default IntegrationObjectSetting;
