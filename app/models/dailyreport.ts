import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Project from "./project";
import Organization from "./organization";

class DailyReport extends Model {
  public id!: number;
  public date!: Date;
  public summary!: string;
  public injuryNotes!: string;
  public equipmentNotes!: string;
  public materialNotes!: string;
  public signOffName!: string;
  public objectId!: string;
  public projectId: ForeignKey<Project["id"]>;
  public createdBy: ForeignKey<User["id"]>;
  public organizationId: ForeignKey<Organization["id"]>;
}

DailyReport.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    summary: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    injuryNotes: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    equipmentNotes: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    materialNotes: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    signOffName: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    objectId: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    projectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "dailyReport",
    tableName: "DailyReports",
  }
);

export default DailyReport;
