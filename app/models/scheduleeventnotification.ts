import { Model, DataTypes } from "sequelize";
import sequelize from "@/lib/sequelize";

class ScheduleEventNotification extends Model {
  public id!: number;
  public scheduleEventId!: number;
  public sentAt?: Date;
  public scheduledAt?: Date;
  public notifyViaText!: boolean;
  public notifyViaPush!: boolean;
  public userIds: number[];
  public notificationType!: string;
}

ScheduleEventNotification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    scheduleEventId: DataTypes.INTEGER,
    sentAt: DataTypes.DATE,
    scheduledAt: DataTypes.DATE,
    notifyViaText: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    notifyViaPush: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    userIds: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: false,
    },
    notificationType: {
      // CREATED: When a schedule event is created or when a user is added to an existing schedule event
      // UPDATED: When a schedule event is updated
      // ADDED_USERS: When users are added to a schedule event
      // REMOVED_USERS: When users are removed from a schedule event
      // CANCELLED: When a schedule event is cancelled i.e. deleted
      type: DataTypes.ENUM("CREATED", "UPDATED", "ADDED_USERS", "REMOVED_USERS", "CANCELLED"),
      defaultValue: "CREATED",
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "scheduleEventNotification",
    tableName: "ScheduleEventNotifications",
  }
);

export default ScheduleEventNotification;
