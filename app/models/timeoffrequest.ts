import {
  CreationOptional,
  DataTypes,
  ForeignKey,
  InferAttributes,
  InferCreationAttributes,
  Model,
  NonAttribute,
} from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "@/models/user";
import TimeOffPolicy from "@/models/timeoffpolicy";

export enum TimeOffRequestStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  DECLINED = "DECLINED",
  PAID = "PAID",
}

export default class TimeOffRequest extends Model<
  InferAttributes<TimeOffRequest>,
  InferCreationAttributes<TimeOffRequest>
> {
  declare id: number;
  declare startDate: Date;
  declare endDate: Date;
  declare userId: ForeignKey<User["id"]>;
  declare totalHours: number;
  declare requestNotes?: string;
  declare status: TimeOffRequestStatus;
  declare reviewedBy: ForeignKey<User["id"]>;
  declare timeOffPolicyId: ForeignKey<TimeOffPolicy["id"]>;
  declare timeOffPolicy?: NonAttribute<TimeOffPolicy>;
  declare declineNotes?: string;
  declare checkPayrollId?: string;
  declare user?: NonAttribute<User>; // Note this is optional since it's only populated when explicitly requested in code
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
}

TimeOffRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    startDate: {
      type: DataTypes.DATE,
      defaultValue: new Date(),
      allowNull: false,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    totalHours: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM(...Object.values(TimeOffRequestStatus)),
      allowNull: false,
      defaultValue: TimeOffRequestStatus.PENDING,
    },
    requestNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    declineNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    checkPayrollId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  },
  {
    sequelize,
    modelName: "TimeOffRequest",
    tableName: "TimeOffRequests",
    underscored: true,
    hooks: {
      beforeSave: async (instance: TimeOffRequest) => {
        // is status is PAID already, we don't need to automatically set the status anymore
        if (instance.status === TimeOffRequestStatus.PAID) {
          return;
        }

        if (instance.reviewedBy) {
          if (instance.declineNotes) {
            instance.status = TimeOffRequestStatus.DECLINED;
          } else {
            instance.status = TimeOffRequestStatus.APPROVED;
          }
        } else {
          instance.status = TimeOffRequestStatus.PENDING;
        }
      },
    },
  }
);
