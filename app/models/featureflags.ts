import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "./organization";

class FeatureFlags extends Model {
  declare id: number;
  public isPayrollEnabled!: boolean;
  public isSchedulingEnabled!: boolean;
  public isMessagingEnabled!: boolean;
  public isEquipmentTrackingEnabled!: boolean;
  declare organizationId: ForeignKey<Organization["id"]>;
}

FeatureFlags.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    isPayrollEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isSchedulingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isMessagingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isEquipmentTrackingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "featureFlags",
    tableName: "FeatureFlags",
  }
);

export default FeatureFlags;
