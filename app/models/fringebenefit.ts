import { Model, DataTypes, ForeignKey } from "sequelize";

import sequelize from "@/lib/sequelize";

import WageTable from "@/models/wagetable";
import Organization from "@/models/organization";
import FringeBenefitClassification from "@/models/fringebenefitclassification";

export const FRINGE_CATEGORY_TYPES = ["TRAINING_FUND_CONTRIBUTION"];

class FringeBenefit extends Model {
  public id!: number;
  public name!: string;
  public category!: string;
  public wageTableId: ForeignKey<WageTable["id"]>;
  public organizationId: ForeignKey<Organization["id"]>;
}

FringeBenefit.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    category: {
      type: DataTypes.ENUM(...FRINGE_CATEGORY_TYPES),
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "fringeBenefits",
    tableName: "FringeBenefits",
  }
);

export default FringeBenefit;

export interface ExtendedFringeBenefit extends FringeBenefit {
  fringeBenefitClassifications?: FringeBenefitClassification[];
}
