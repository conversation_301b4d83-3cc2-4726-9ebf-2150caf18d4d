import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Project from "./project";
import TimeSheet from "./timesheet";
import Organization from "./organization";

class ProjectPhotosCollection extends Model {
  public id!: number;
  public note!: string;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare projectId: ForeignKey<Project["id"]>;
  declare timesheetId: ForeignKey<TimeSheet["id"]>;
  declare createdBy: ForeignKey<User["id"]>;
}

ProjectPhotosCollection.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    note: DataTypes.STRING(2000),
  },
  {
    sequelize,
    modelName: "projectPhotosCollection",
    tableName: "ProjectPhotosCollections",
  }
);

export default ProjectPhotosCollection;
