import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import Organization from "./organization";

export default class Department extends Model {
  declare id: number;
  declare name: string;
  declare createdBy: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
}

Department.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: DataTypes.STRING,
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },

  {
    sequelize,
    modelName: "department",
    tableName: "Departments",
  }
);
