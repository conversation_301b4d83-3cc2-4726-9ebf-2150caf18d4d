import { DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";

export const WEEK_DAYS = ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"] as const;
export type WeekDay = typeof WEEK_DAYS[number];

export const OVERTIME_DISTRIBUTION = ["SEQUENTIAL", "WEIGHTED"];

class OvertimeSettings extends Model<InferAttributes<OvertimeSettings>, InferCreationAttributes<OvertimeSettings>> {
  declare id: number;
  declare name: string; // cannot be named default unless it comes from global settings
  declare description: string;
  declare weeklyOvertimeEnabled: boolean;
  declare dailyOvertimeEnabled: boolean;
  declare dailyOvertimeThreshold: number;
  declare weeklyOvertimeThreshold: number;
  declare overtimeMultiplier: number;
  declare weekStartDay: WeekDay;
  declare dailyDoubleOvertimeEnabled: boolean;
  declare dailyDoubleOvertimeThreshold: number;
  declare overtimeDays: WeekDay[];
  declare doubleOvertimeDays: WeekDay[];
  // archive vs active flag as well
  declare isActive: boolean;
  // flag for isDefault - only one overtime setting can be the default
  declare isDefault: boolean;

  // global settings - these get inherited by all children overtime settings
  declare overtimeDistribution: typeof OVERTIME_DISTRIBUTION[number];
  declare automaticOvertimeCalculation: boolean; // by default true - if set to false, manual overtime is assumed

  // org id
  declare organizationId: ForeignKey<Organization["id"]>;
}

OvertimeSettings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    weeklyOvertimeEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    dailyOvertimeEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    dailyOvertimeThreshold: {
      type: DataTypes.INTEGER,
      defaultValue: 480, // 8 hours in minutes
      allowNull: false,
    },
    weeklyOvertimeThreshold: {
      type: DataTypes.INTEGER,
      defaultValue: 2400, // 40 hours in minutes
      allowNull: false,
    },
    overtimeMultiplier: {
      type: DataTypes.FLOAT,
      defaultValue: 1.5,
      allowNull: false,
    },
    weekStartDay: {
      type: DataTypes.ENUM(...WEEK_DAYS),
      allowNull: false,
      defaultValue: "MONDAY",
    },
    dailyDoubleOvertimeEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    dailyDoubleOvertimeThreshold: {
      type: DataTypes.INTEGER,
      defaultValue: 720, // 12 hours in minutes
      allowNull: false,
    },
    overtimeDistribution: {
      type: DataTypes.ENUM(...OVERTIME_DISTRIBUTION),
      allowNull: false,
      defaultValue: "SEQUENTIAL",
    },
    overtimeDays: {
      type: DataTypes.JSON, // really, the type is WeekDay[]
      allowNull: false,
      defaultValue: [], // Default to empty list
    },
    doubleOvertimeDays: {
      type: DataTypes.JSON, // really, the type is WeekDay[]
      allowNull: false,
      defaultValue: [], // Default to empty list
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    automaticOvertimeCalculation: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "overtimeSettings",
    tableName: "OvertimeSettings",
    indexes: [
      {
        unique: true,
        fields: ["name", "organization_id"],
      },
      {
        name: "unique_default_per_organization",
        unique: true,
        fields: ["organization_id"],
        where: {
          is_default: true,
        },
      },
      {
        name: "idx_overtime_settings_default_lookup",
        fields: ["organization_id", "is_default"],
      },
    ],
  }
);

export default OvertimeSettings;
