import { Point } from "geojson";
import { Model, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, NonAttribute } from "sequelize";

import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import WageTable from "@/models/wagetable";
import OvertimeSettings from "@/models/overtimesettings";

export const PREVAILING_WAGE_CATEGORY = ["FEDERAL", "STATE", "LOCAL"];

class Project extends Model<InferAttributes<Project>, InferCreationAttributes<Project>> {
  public id!: number;
  public name!: string;
  public customerName?: string;
  public address?: string;
  public projectNumber?: string;
  public startDate?: Date;
  public foremanId?: number;
  public location?: Point;
  public isGeofenced!: boolean;
  public isArchived!: boolean;
  // stored in feet
  public geofenceRadius?: number;
  public hoursBudget?: number;
  public costBudget?: number;
  public notes?: string;
  public metadata?: Record<string, any>;

  // used for prevailing wage projects
  public isPrevailingWage?: boolean;
  public prevailingWageCategory?: string;
  public prevailingWageState?: string;
  public prevailingWageDirProjectId?: string;
  public prevailingWageAwardingBody?: string;
  public prevailingWagePrimeContractorName?: string;
  public prevailingWageBidAwardDate?: Date;
  public prevailingWageIsSubcontractor?: boolean;
  public prevailingWagePrimeContractorAddress?: string;

  declare organizationId: ForeignKey<Organization["id"]>;
  declare wageTableId: ForeignKey<WageTable["id"]>;
  declare overtimeSettingsId: ForeignKey<OvertimeSettings["id"]>;

  declare overtimeSettings?: NonAttribute<OvertimeSettings>;
}

Project.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    projectNumber: DataTypes.STRING(100),
    startDate: DataTypes.DATEONLY,
    customerName: DataTypes.STRING(100),
    address: DataTypes.STRING(500),
    location: DataTypes.GEOMETRY("POINT"),
    isGeofenced: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    geofenceRadius: {
      type: DataTypes.INTEGER,
      defaultValue: 1000,
      allowNull: true,
    },
    isPrevailingWage: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    prevailingWageCategory: {
      type: DataTypes.ENUM(...PREVAILING_WAGE_CATEGORY),
      allowNull: true,
    },
    prevailingWageState: DataTypes.STRING(100),
    prevailingWageDirProjectId: DataTypes.STRING(100),
    prevailingWageAwardingBody: DataTypes.STRING(100),
    prevailingWagePrimeContractorName: DataTypes.STRING(100),
    prevailingWageBidAwardDate: DataTypes.DATEONLY,
    prevailingWageIsSubcontractor: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    prevailingWagePrimeContractorAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    hoursBudget: DataTypes.FLOAT,
    costBudget: DataTypes.FLOAT,
    notes: DataTypes.STRING(2000),
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "project",
    tableName: "Projects",
  }
);

export default Project;

export interface ExtendedProject extends Project {
  wageTable?: WageTable;
  overtimeSettings?: OvertimeSettings;
}
