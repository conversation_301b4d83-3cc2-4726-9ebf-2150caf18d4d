import { Model, DataTypes } from "sequelize";
import sequelize from "@/lib/sequelize";

class ScheduleEvent extends Model {
  public id!: number;
  public startTime!: Date;
  public endTime!: Date;
  public note?: string;
  public organizationId!: number;
  public costCodeId?: number;
  public projectId!: number;
  public linkedEventId?: string;
  public isDeleted: boolean;
  public createdBy: number;
}

ScheduleEvent.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    startTime: DataTypes.DATE,
    endTime: DataTypes.DATE,
    note: DataTypes.STRING(2000),
    organizationId: DataTypes.INTEGER,
    costCodeId: DataTypes.INTEGER,
    projectId: DataTypes.INTEGER,
    linkedEventId: DataTypes.STRING,
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    createdBy: DataTypes.INTEGER,
  },
  {
    sequelize,
    modelName: "scheduleEvent",
    tableName: "ScheduleEvents",
  }
);

export default ScheduleEvent;
