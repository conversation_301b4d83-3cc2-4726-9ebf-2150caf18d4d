import { CreationOptional, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import IntegrationUserToken from "./integrationusertoken";

export enum INTEGRATION_OBJECT_TYPE {
  "CUSTOMER" = "CUSTOMER",
  "PROJECT" = "PROJECT",
  "EMPLOYEE" = "EMPLOYEE",
  "TIMESHEET" = "TIMESHEET",
}

class IntegrationMapping extends Model<
  InferAttributes<IntegrationMapping>,
  InferCreationAttributes<IntegrationMapping>
> {
  public declare id: CreationOptional<number>;
  public declare objectType: INTEGRATION_OBJECT_TYPE;
  public declare hammrId: number;
  public declare externalId: string;
  public declare integrationUserTokenId: ForeignKey<IntegrationUserToken["id"]>;
  public declare lastSyncedAt?: Date;
}

IntegrationMapping.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    hammrId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    objectType: {
      type: DataTypes.ENUM(...Object.values(INTEGRATION_OBJECT_TYPE)),
      allowNull: false,
    },
    externalId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    lastSyncedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: "integrationsMapping",
    tableName: "IntegrationsMappings",
    indexes: [
      {
        unique: true,
        fields: ["hammr_id", "external_id", "object_type", "integration_user_token_id"],
        name: "unique_integration_mapping",
      },
    ],
  }
);

export default IntegrationMapping;
