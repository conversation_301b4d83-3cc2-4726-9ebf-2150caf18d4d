import { Model, DataTypes, ForeignKey, InferAttributes, InferCreationAttributes } from "sequelize";
import sequelize from "@/lib/sequelize";
import WorkersCompCode from "./workersCompCode";
import Organization from "@/models/organization";

class CostCode extends Model<InferAttributes<CostCode>, InferCreationAttributes<CostCode>> {
  declare id: number;
  declare number: string;
  declare name: string;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare workersCompCodeId?: ForeignKey<WorkersCompCode["id"]>;
}

CostCode.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    name: DataTypes.STRING(100),
    number: DataTypes.STRING(100),
  },
  {
    sequelize,
    modelName: "costCode",
    tableName: "CostCodes",
  }
);

export default CostCode;
