import { CreationOptional, DataTypes, <PERSON><PERSON><PERSON>, HasManyGetAssociationsMixin, Model, NonAttribute } from "sequelize";

import sequelize from "@/lib/sequelize";

import User from "@/models/user";
import Break from "@/models/break";
import Project from "@/models/project";
import CostCode from "@/models/costcode";
import BreakHistory from "@/models/breakhistory";
import TimesheetHistory from "@/models/timesheethistory";
import EarningRate from "@/models/earningrate";
import UserClassification, { ExtendedUserClassification } from "@/models/userclassification";
import { ExtendedFringeBenefitClassification } from "@/models/fringebenefitclassification";
import { CheckBenefitOverride } from "@/types/check";
import WorkersCompCode from "@/models/workersCompCode";
import Organization, { ExtendedOrganization } from "./organization";
import UserLocation from "@/models/userlocation";
import InjuryReport from "@/models/injuryreport";
import InjuryPhoto from "@/models/injuryphoto";
import Equipment from "@/models/equipment";

export const TIMESHEET_STATUSES = ["CLOCKED_IN", "SUBMITTED", "APPROVED", "REJECTED", "PAID"] as const;

class TimeSheet extends Model {
  declare id: number;
  declare clockIn: Date;
  declare clockOut?: Date;
  declare description: string;
  declare isManual: boolean;
  declare workerId: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare costCodeId: ForeignKey<CostCode["id"]>;
  declare projectId: ForeignKey<Project["id"]>;
  declare isDeleted: boolean;
  declare createdBy: ForeignKey<User["id"]>;
  declare editedBy: ForeignKey<User["id"]>;
  public regEarningRateId: ForeignKey<EarningRate["id"]>;
  public otEarningRateId: ForeignKey<EarningRate["id"]>;
  public dotEarningRateId: ForeignKey<EarningRate["id"]>;
  declare breakDuration: number; // in seconds
  declare otDuration?: number; // in seconds
  declare dotDuration?: number; // in seconds
  declare driveTimeDuration?: number; // in seconds
  declare status: typeof TIMESHEET_STATUSES[number];
  declare approvedAt: Date;
  declare hideGeofenceWarning: boolean;
  declare clockedOutBySwitch: boolean;
  declare userClassificationId: ForeignKey<UserClassification["id"]>;
  declare checkPayrollId: string;
  declare workersCompCodeId?: ForeignKey<WorkersCompCode["id"]>;
  declare equipmentId?: ForeignKey<Equipment["id"]>;

  declare userLocations?: NonAttribute<UserLocation[]>;
  declare breaks?: NonAttribute<Break[]>;
  declare user?: NonAttribute<User>;
  declare project?: NonAttribute<Project>;

  declare getBreaks: HasManyGetAssociationsMixin<Break>;

  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
}

TimeSheet.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    clockIn: DataTypes.DATE,
    clockOut: DataTypes.DATE,
    description: DataTypes.STRING(2000),
    isManual: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    createdBy: DataTypes.INTEGER,
    editedBy: DataTypes.INTEGER,
    breakDuration: DataTypes.INTEGER,
    otDuration: DataTypes.INTEGER,
    dotDuration: DataTypes.INTEGER,
    driveTimeDuration: DataTypes.INTEGER,
    hideGeofenceWarning: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    clockedOutBySwitch: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM(...TIMESHEET_STATUSES),
      defaultValue: "CLOCKED_IN",
      allowNull: false,
    },
    approvedAt: DataTypes.DATE,
    checkPayrollId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    equipmentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    // leaving a note here where it is possible in the future we extend Timesheets to record the attributed numbers once the STATUS = PAID along with checkPayrollId
  },
  {
    sequelize,
    modelName: "timesheet",
    tableName: "TimeSheets",
  }
);

export interface ExtendedTimesheet extends TimeSheet {
  costCode?: CostCode;
  userClassification?: ExtendedUserClassification;
  fringeBenefitClassifications?: ExtendedFringeBenefitClassification[];
  breaks?: Break[];
  breakHistory?: BreakHistory[];
  timesheetHistory?: TimesheetHistory[];
  regEarningRate?: EarningRate;
  otEarningRate?: EarningRate;
  dotEarningRate?: EarningRate;
  hasTimesheetHistory?: boolean;
  hasUnresolvedAlerts?: boolean;
  otPolicyId?: number; // overtime policy id
  clockInDate?: string;
  clockOutDate?: string;
  clockInTimestamp?: number;
  clockOutTimestamp?: number;
  // potentially nest calculations into calculations object
  hourlyWage?: number; // per hour
  otHourlyWage?: number; // per hour
  dotHourlyWage?: number; // per hour
  cashFringeRate?: number; // per hour
  companyDynamicFringeContributionRate?: number; // per hour
  employeeDynamicFringeContributionPerecent?: number;
  totalMinutes?: number; // total minutes worked - aggregated
  regularMinutes?: number;
  overtimeMinutes?: number;
  doubleOvertimeMinutes?: number;
  breakMinutes?: number;
  regularWages?: number;
  overtimeWages?: number;
  doubleOvertimeWages?: number;
  driveTimeMinutes?: number;
  driveTimeWages?: number;
  totalWages?: number;
  companyDynamicFringeAllocation?: number; // is a total dynamic fringe allocation for a given timesheet -> dyhamicFringeContributionRate * ((regularMinutes + overtimeMinutes) / 60)
  employeeDynamicFringeAllocation?: number; // takes the dynamic fringe employee contribution perecent and
  workersCompCode?: WorkersCompCode;
  organization?: ExtendedOrganization;
  rawClockIn?: number; // timestamps for mobile
  rawClockOut?: number; // timestamps for mobile
  injuryReports?: (InjuryReport & {
    resolvedByUser?: User;
    injuryPhotos?: InjuryPhoto[];
  })[];
}

export interface WageSummary {
  isPrevailingWage: boolean;
  classificationName: string;
  checkRegEarningCodeId: string;
  checkOtEarningCodeId: string;
  checkDotEarningCodeId: string;
  checkRegEarningRateId?: string;
  checkOtEarningRateId?: string;
  checkDotEarningRateId?: string;
  hourlyWage: number;
  otHourlyWage: number;
  dotHourlyWage: number;
  regularMinutes: number;
  overtimeMinutes: number;
  doubleOvertimeMinutes: number;
  breakMinutes: number;
  driveTimeMinutes: number;
  regularWages: number;
  overtimeWages: number;
  doubleOvertimeWages: number;
  driveTimeWages: number;
  totalWages: number;
  metadata?: Record<string, any>;
}

export interface UserEarningsSummary {
  id: number; // userId
  firstName: string;
  lastName: string;
  compensationType?: string;
  hourlyWage: number;
  otHourlyWage: number;
  dotHourlyWage: number;
  salary?: number;
  checkEmployeeId: string;
  checkContractorId: string;
  regularMinutes: number;
  totalMinutes: number;
  overtimeMinutes: number;
  doubleOvertimeMinutes: number;
  breakMinutes: number;
  totalWages: number;
  overtimeWages?: number;
  doubleOvertimeWages?: number;
  checkRegEarningRateId?: string;
  checkOtEarningRateId?: string;
  checkDotEarningRateId?: string;
  checkDynamicFringeBenefitId?: string;
  timeWorkedByDay: Record<string, string | number | Date>[];
  nonPWTimesheetsSummary?: WageSummary[];
  pwTimesheetsSummary?: WageSummary[];
  checkBenefitOverrides?: CheckBenefitOverride[];
  reimbursements?: WageSummary[];
  skip?: boolean;
}

export interface ExtendedUserEarningsSummary extends UserEarningsSummary {
  paymentMethod: string; // can be null from Check
  checkDynamicFringeBenefit?: string;
  reimbursements?: WageSummary[];
  otherEarnings?: WageSummary[];
}

export default TimeSheet;
