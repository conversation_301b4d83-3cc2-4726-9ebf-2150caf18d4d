import { DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model, NonAttribute } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "@/models/user";
import Organization from "./organization";
import IntegrationMapping from "@/models/integrationMapping";
import IntegrationObjectSetting from "@/models/integrationobjectsetting";

export const PROVIDERS = ["RUTTER", "MANUAL"];
export const PROVIDERS_MAPPING = {
  RUTTER: "RUTTER",
  MANUAL: "MANUAL",
};

export const INTEGRATION_SUPPORTED_PLATFORMS = {
  QUICKBOOKS: {
    id: "QUICKBOOKS",
    urlId: "quickbooks",
    name: "<PERSON>B<PERSON>s Online",
    logo: "/img/integrations/quickbooks.svg",
    description: "Seamless collaboration and document management.",
    provider: "RUTTER",
  },
  QUICKBOOKS_DESKTOP: {
    id: "QUICKBOOKS_DESKTOP",
    urlId: "quickbooks-desktop",
    name: "<PERSON>Books Desktop",
    logo: "/img/integrations/quickbooks.svg",
    description: "For conducting virtual meetings and interviews.",
    provider: "RUTTER",
  },
  SAGE_INTACCT: {
    id: "SAGE_INTACCT",
    urlId: "sage-intacct",
    name: "Sage Intacct",
    logo: "/img/integrations/sage.svg",
    description: "For conducting virtual meetings and interviews.",

    provider: "RUTTER",
  },
  XERO: {
    id: "XERO",
    urlId: "xero",
    name: "Xero",
    logo: "/img/integrations/xero.svg",
    description: "For conducting virtual meetings and interviews.",
    provider: "RUTTER",
  },
  NETSUITE: {
    id: "NETSUITE",
    urlId: "netsuite",
    name: "NetSuite",
    logo: "/img/integrations/netsuite.svg",
    description: "For conducting virtual meetings and interviews.",
    provider: "RUTTER",
  },
  FOUNDATION: {
    id: "FOUNDATION",
    urlId: "foundation",
    name: "Foundation",
    logo: "/img/integrations/foundation.svg",
    description: "For conducting virtual meetings and interviews.",
    provider: "MANUAL",
  },
  WORKMANS_DASHBOARD: {
    id: "WORKMANS_DASHBOARD",
    urlId: "workmans-dashboard",
    name: "Workman's Dashboard",
    logo: "/img/integrations/workmans-dashboard.svg",
    description: "Sync internal projects from Workman's Dashboard into Hammr.",
    provider: "MANUAL",
  },
};

class IntegrationUserToken extends Model<
  InferAttributes<IntegrationUserToken>,
  InferCreationAttributes<IntegrationUserToken>
> {
  declare id: number;
  declare accessToken?: string; // Optional accessToken
  declare platform: string; // e.g. `QUICKBOOKS_DESKTOP`
  declare provider: string; // will be `RUTTER` or `MANUAL`
  declare isEnabled: boolean;
  declare storeUniqueId?: string;
  declare createdBy: ForeignKey<User["id"]>;
  declare organizationId: ForeignKey<Organization["id"]>; // Each org can have 1 token per platform, so any admin can use this token to fetch data from the platform

  declare mappings?: NonAttribute<IntegrationMapping[]>;
  declare organization?: NonAttribute<Organization>;
  declare objectSettings?: NonAttribute<IntegrationObjectSetting[]>;
}

IntegrationUserToken.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    accessToken: {
      type: DataTypes.STRING,
      allowNull: true, // Making accessToken optional
    },
    platform: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    provider: {
      type: DataTypes.ENUM(...PROVIDERS),
      allowNull: false,
      defaultValue: "RUTTER",
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    storeUniqueId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Organizations",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "integrationUserToken",
    tableName: "IntegrationUserTokens",
  }
);

export default IntegrationUserToken;
