import { Model, DataTypes, ForeignKey } from "sequelize";

import sequelize from "@/lib/sequelize";

import Organization from "@/models/organization";
import EmployeeBenefit from "@/models/employeebenefit";

export const CATEGORY_TYPES = [
  "MEDICAL",
  "DENT<PERSON>",
  "VISI<PERSON>",
  "LIF<PERSON>",
  "DISABILITY",
  "401K",
  "ROTH_401K",
  "FSA_MEDICAL",
  "FSA_DEPENDENT_CARE",
  "HSA",
  "SIMPLE_IRA",
];

export const CONTRIBUTION_TYPES = ["PERCENT", "AMOUNT", "DYNAMIC"];

class CompanyBenefit extends Model {
  public id!: number;
  public name!: string;
  public category!: string; // equivalent to benefit type - required
  public contributionType!: string; // percentage vs amount - required
  public benefitStartDate: string; // string YYYY-MM-DD
  public benefitEndDate: string; // string YYYY-MM-DD
  public benefitProviderName: string; // although optional - we should encourage this
  public benefitProviderAddress: string; // although optional - we should encourage this
  public benefitProviderPhone: string; // although optional - we should encourage this
  public isApprovedFringe: boolean;
  public checkCompanyBenefitId: string;
  public metadata?: JSON; // we can have this, but might be useful to sync our ids on check's side with their metadata
  public organizationId: ForeignKey<Organization["id"]>;
}

CompanyBenefit.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    category: {
      type: DataTypes.ENUM(...CATEGORY_TYPES),
      allowNull: false,
      defaultValue: "MEDICAL",
    },
    contributionType: {
      type: DataTypes.ENUM(...CONTRIBUTION_TYPES),
      allowNull: false,
    },
    benefitStartDate: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    benefitEndDate: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    benefitProviderName: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    benefitProviderAddress: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    benefitProviderPhone: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    isApprovedFringe: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    checkCompanyBenefitId: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "companyBenefits",
    tableName: "CompanyBenefits",
  }
);

export default CompanyBenefit;

export interface ExtendedCompanyBenefit extends CompanyBenefit {
  employeeBenefits?: EmployeeBenefit[];
  numberEnrolled?: number;
}
