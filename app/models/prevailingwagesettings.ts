import { DataTypes, ForeignKey, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";

class PrevailingWageSettings extends Model<
  InferAttributes<PrevailingWageSettings>,
  InferCreationAttributes<PrevailingWageSettings>
> {
  declare id: number;
  declare organizationId: ForeignKey<Organization["id"]>;
  declare allowCustomPrevailingWagePerEmployee: boolean;
  declare overridePwIfBelowRegularRate: boolean;
}

PrevailingWageSettings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    allowCustomPrevailingWagePerEmployee: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    overridePwIfBelowRegularRate: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "prevailingWageSettings",
    tableName: "PrevailingWageSettings",
  }
);

export default PrevailingWageSettings;
