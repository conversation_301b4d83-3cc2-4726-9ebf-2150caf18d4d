import { Model, DataTypes, ForeignKey } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./user";
import ProjectPhotosCollection from "./projectphotoscollection";

class ProjectPhoto extends Model {
  public id!: number;
  public objectId!: string;
  public isDeleted!: boolean;
  declare collectionId: ForeignKey<ProjectPhotosCollection["id"]>;
  declare createdBy: ForeignKey<User["id"]>;
}

ProjectPhoto.init(
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    objectId: {
      type: DataTypes.STRING(2000),
      allowNull: false,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "projectPhoto",
    tableName: "ProjectPhotos",
  }
);

export default ProjectPhoto;
