import { TimeSheet } from "../models";
import { Op } from "sequelize";
import dayjs from "dayjs";
import { sendSlackMessage } from "../util/slack";
const cron = require("node-cron");

const { ENVIRONMENT } = process.env;

export const initDailyStats = () => {
  cron.schedule(
    "0 0 10 * * *",
    async () => {
      if (ENVIRONMENT !== "PRODUCTION") {
        return;
      }

      const date = new Date();
      const dateInPST = date.toLocaleString("en-US", { timeZone: "America/Los_Angeles" });
      const startOfDayPST = dayjs(dateInPST).subtract(1, "day").hour(0).minute(0).second(0).toISOString();
      const endOfDayPST = dayjs(dateInPST).subtract(1, "day").hour(23).minute(59).second(59).toISOString();

      const timesheets = await TimeSheet.findAll({
        where: {
          organizationId: { [Op.not]: 1 },
          clockIn: { [Op.between]: [startOfDayPST, endOfDayPST] },
        },
      });

      sendSlackMessage(`Timesheets created yesterday: ${timesheets.length}`);
    },
    {
      scheduled: true,
      timezone: "America/Los_Angeles",
    }
  );
};
