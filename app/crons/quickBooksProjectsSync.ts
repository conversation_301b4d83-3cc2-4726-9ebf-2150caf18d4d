import { Organization } from "../models";
import { IntegrationSettingsService } from "../services/integrations/settings";
import { OBJECT_TYPES } from "@/models/integrationobjectsetting";

const cron = require("node-cron");

const { ENVIRONMENT } = process.env;

// the reason we have a cron specially designed for QuickBooks projects syncing is because QB doesn't have a way to notify us when a client creates a project on their platform
export const initQuickBooksProjectsSync = () => {
  const integrationSettingsService = new IntegrationSettingsService();

  cron.schedule(
    "0 0 22 * * *", // 10PM PT
    async () => {
      if (ENVIRONMENT !== "PRODUCTION") {
        return;
      }

      // Get all organizations
      const organizations = await Organization.findAll();

      // For each organization, check if PROJECTS integration is enabled
      for (const organization of organizations) {
        const integrationProjectsSync = await integrationSettingsService.getIntegrationObjectSetting(
          organization.id,
          OBJECT_TYPES.PROJECTS
        );

        if (integrationProjectsSync?.integration.isEnabled && integrationProjectsSync?.isEnabled) {
          // Call syncProjects method for this organization
          await integrationSettingsService.syncProjects(organization.id);
          await integrationProjectsSync.update({
            lastSyncedAt: new Date(),
          });
        }
      }
    },
    {
      scheduled: true,
      timezone: "America/Los_Angeles",
    }
  );
};
