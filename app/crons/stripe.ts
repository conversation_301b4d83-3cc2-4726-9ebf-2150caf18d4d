import { TimeSheet, Organization, User } from "../models";
import { Op } from "sequelize";
import dayjs from "dayjs";
import { sendSlackMessage } from "../util/slack";
const cron = require("node-cron");
const {
  ENVIRONMENT,
  STRIPE_SECRET,
  STRIPE_TIMETRACKING_PRODUCT_ID,
  STRIPE_PAYROLL_PRODUCT_ID,
  STRIPE_SCHEDULING_PRODUCT_ID,
  STRIPE_CHAT_PRODUCT_ID,
} = process.env;
const stripeSlackChannel = "C06385T8M7B";
const stripe = require("stripe")(STRIPE_SECRET);

export const initStripeIntegration = () => {
  cron.schedule(
    "0 0 22 * * *",
    async () => {
      if (ENVIRONMENT !== "PRODUCTION") {
        return;
      }

      runStripeIntegration();
    },
    {
      scheduled: true,
      timezone: "America/Los_Angeles",
    }
  );
};

export async function runStripeIntegration(numberOfDays = 1) {
  const currentTime = Math.floor(Date.now() / 1000);
  const oneDayFromNow = currentTime + numberOfDays * 24 * 60 * 60;

  const subscriptions = await fetchAllSubscriptions();

  const renewingSubscriptions = subscriptions.filter((subscription: { current_period_end: any }) => {
    const renewalTime = subscription.current_period_end;

    return renewalTime >= currentTime && renewalTime <= oneDayFromNow;
  });

  for (const subscription of renewingSubscriptions) {
    const stripeCustomerId = subscription.customer;
    const subscriptionItemsToUpdate = [];

    const organization = await Organization.findOne({
      where: {
        stripeCustomerId,
      },
    });

    if (organization === null) {
      await sendSlackMessage(
        `Organization not found for Stripe customer ID: ${stripeCustomerId}`,
        stripeSlackChannel
      );

      continue;
    }

    const url =
      ENVIRONMENT === "PRODUCTION"
        ? `https://dashboard.stripe.com/customers/${stripeCustomerId}`
        : `https://dashboard.stripe.com/test/customers/${stripeCustomerId}`;

    for (const item of subscription.items.data) {
      const productId = item.plan.product;

      switch (productId) {
        case STRIPE_TIMETRACKING_PRODUCT_ID:
        case STRIPE_SCHEDULING_PRODUCT_ID:
        case STRIPE_CHAT_PRODUCT_ID: {
          const numberOfActiveUsers = await calculateNumberOfActiveUsers(organization);
          if (numberOfActiveUsers === null) {
            continue;
          }

          await sendSlackMessage(
            `Number of active users for <${url}|${organization.name}>: ${numberOfActiveUsers} | Number of seats: ${item.quantity}`,
            stripeSlackChannel
          );

          subscriptionItemsToUpdate.push({
            id: item.id,
            quantity: numberOfActiveUsers,
          });

          break;
        }
        case STRIPE_PAYROLL_PRODUCT_ID: {
          const numberOfPayrollUsers = await calculateNumberOfPayrollUsers(organization);
          if (numberOfPayrollUsers === null) {
            continue;
          }

          await sendSlackMessage(
            `Number of payroll users for <${url}|${organization.name}>: ${numberOfPayrollUsers}  | Number of seats: ${item.quantity}`,
            stripeSlackChannel
          );

          subscriptionItemsToUpdate.push({
            id: item.id,
            quantity: numberOfPayrollUsers,
          });
          break;
        }
        default: {
          await sendSlackMessage(`Unknown product ID: ${productId}`, stripeSlackChannel);
          continue;
        }
      }
    }

    // await stripe.subscriptions.update(subscription.id, {
    //   proration_behavior: 'none',
    //   items: subscriptionItemsToUpdate,
    // });
  }
}

async function calculateNumberOfActiveUsers(organization: Organization): Promise<number | null> {
  const organizationId = organization.id;
  const numberOfUsersWithTimesheets = await User.count({
    where: {
      organizationId,
      role: {
        [Op.in]: ["FOREMAN", "WORKER"],
      },
    },
    distinct: true,
    include: [
      {
        model: TimeSheet,
        where: {
          organizationId,
          clockIn: {
            [Op.gte]: dayjs().subtract(30, "days").toDate(),
          },
          isDeleted: false,
        },
        required: true,
      },
    ],
  });

  const numberOfAdminUsers = await User.count({
    where: {
      organizationId,
      isArchived: false,
      role: "ADMIN",
    },
  });

  return numberOfUsersWithTimesheets + numberOfAdminUsers;
}

async function calculateNumberOfPayrollUsers(organization: Organization): Promise<number | null> {
  const organizationId = organization.id;

  const numberOfPayrollUsers = await User.count({
    where: {
      organizationId,
      isArchived: false,
      [Op.or]: [
        {
          checkEmployeeId: {
            [Op.ne]: null,
          },
        },
        {
          checkContractorId: {
            [Op.ne]: null,
          },
        },
      ],
    },
  });

  return numberOfPayrollUsers;
}

const fetchAllSubscriptions = async () => {
  let allSubscriptions: any = [];
  let lastId;

  // eslint-disable-next-line no-constant-condition
  while (true) {
    const params: any = { limit: 100 };
    if (lastId) {
      params.starting_after = lastId;
    }

    const subscriptions = await stripe.subscriptions.list(params);

    allSubscriptions = [...allSubscriptions, ...subscriptions.data];

    if (subscriptions.has_more) {
      lastId = subscriptions.data[subscriptions.data.length - 1].id;
    } else {
      break;
    }
  }

  return allSubscriptions;
};
