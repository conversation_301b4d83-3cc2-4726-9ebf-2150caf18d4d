import { ScheduleEvent, User, ScheduleEventNotification, DeviceToken, Project, Organization } from "../models";
import { sendSMS } from "../util/sendSMS";
import { Op } from "sequelize";
import * as Sentry from "@sentry/node";
const cron = require("node-cron");
const apn = require("node-apn");
const admin = require("firebase-admin");
const serviceAccount = require("../../hammr-time-a66ce-firebase-adminsdk-zglse-8cd80c32d1.json");
const { ENVIRONMENT } = process.env;

const options = {
  token: {
    key: "./AuthKey_87BK6FDGNS.p8",
    keyId: "87BK6FDGNS",
    teamId: "F9X4A3X45F",
  },
  production: ENVIRONMENT === "PRODUCTION",
};

const apnProvider = new apn.Provider(options);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const getScheduleEventNotifications = async (options: any) => {
  return ScheduleEventNotification.findAll({
    include: [
      {
        model: ScheduleEvent,
        required: true,
        include: [
          {
            model: User,
            required: true,
            through: { attributes: [] },
            attributes: ["id", "firstName", "lastName", "phone"],
            include: [
              {
                model: DeviceToken,
              },
            ],
          },
          {
            model: Project,
            required: true,
            attributes: ["name", "address"],
          },
          {
            model: Organization,
            required: true,
            attributes: ["timezone"],
          },
        ],
      },
    ],
    raw: false,
    ...options,
  });
};

export const initSchedulingNotifications = () => {
  cron.schedule("* * * * *", async function () {
    try {
      const now = new Date();
      now.setMilliseconds(0); // round down to the nearest second
      const currentTime = new Date(now.getTime());
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

      const scheduleEventNotifications = await getScheduleEventNotifications({
        where: {
          scheduledAt: {
            [Op.gte]: oneMinuteAgo,
            [Op.lt]: currentTime,
          },
        },
      });

      const scheduleEventNotificationsFromLastTenMinutes = await getScheduleEventNotifications({
        where: {
          scheduledAt: {
            [Op.gte]: new Date(oneMinuteAgo.getTime() - 10 * 60 * 1000),
            [Op.lt]: oneMinuteAgo,
          },
          sentAt: null,
        },
        limit: 10,
      });

      await Promise.all(
        [...scheduleEventNotifications, ...scheduleEventNotificationsFromLastTenMinutes].map(async (event: any) => {
          try {
            await sendNotifications(event);
            event.sentAt = new Date();
            await event.save();
          } catch (err) {
            Sentry.captureException(err);
          }
        })
      );
    } catch (err) {
      Sentry.captureException(err);
    }
  });
};

async function sendNotifications(scheduleEventNotification: any) {
  const iOSNotification = iOSNotificationObject();
  const timezone = scheduleEventNotification.scheduleEvent.organization.timezone;

  for (const user of scheduleEventNotification.scheduleEvent.users) {
    // skip users that are not in the notification list
    // this could happen if some users were already notified and then new users were added to the schedule event
    if (scheduleEventNotification.userIds.indexOf(user.id) === -1) {
      continue;
    }

    const { title, message } = notificationTitleAndMessage(scheduleEventNotification, timezone);

    if (scheduleEventNotification.notifyViaText) {
      await sendSMSNotification(user.phone, title, message);
    }

    if (scheduleEventNotification.notifyViaPush) {
      const iosTokens = user.deviceTokens
        .filter((deviceToken: any) => deviceToken.platform === "IOS")
        .map((deviceToken: any) => deviceToken.token);
      const androidTokens = user.deviceTokens
        .filter((deviceToken: any) => deviceToken.platform === "ANDROID")
        .map((deviceToken: any) => deviceToken.token);

      if (iosTokens.length > 0) {
        await sendiOSNotification(iOSNotification, title, message, iosTokens);
      }

      if (androidTokens.length > 0) {
        await sendAndroidNotification(title, message, androidTokens);
      }
    }
  }
}

async function sendSMSNotification(phone: string, title: string, message: string) {
  try {
    const smsMessage = title + "\n" + message;
    sendSMS(phone, smsMessage);
  } catch (err) {
    Sentry.captureException(err);
  }
}

async function sendiOSNotification(notification: any, title: string, message: string, tokens: string[]) {
  notification.alert = {
    title: title,
    body: message,
  };

  apnProvider
    .send(notification, tokens)
    .then((result: any) => {
      if (result.failed.length > 0) {
        throw new Error(`Failed to send ios push notification: ${result.failed[0].response.reason}`);
      }
    })
    .catch((err: any) => {
      Sentry.captureException(err);
    });
}

async function sendAndroidNotification(title: string, message: string, tokens: string[]) {
  const notification = {
    notification: {
      title: title,
      body: message,
    },
    tokens: tokens,
  };

  admin
    .messaging()
    .sendMulticast(notification)
    .catch((error: any) => {
      Sentry.captureException(error);
    });
}

const iOSNotificationObject = (): any => {
  const note = new apn.Notification();
  note.sound = "default";
  note.topic = "com.hammr-backoffice.Hammr";

  return note;
};

const notificationTitleAndMessage = (
  scheduleEventNotification: any,
  timezone: string
): { title: string; message: string } => {
  if (["CREATED", "ADDED_USERS", "UPDATED"].includes(scheduleEventNotification.notificationType)) {
    return notificationTitleAndMessageForCreatedAndUpdatedEvent(scheduleEventNotification, timezone);
  } else if (["REMOVED_USERS", "CANCELLED"].includes(scheduleEventNotification.notificationType)) {
    return notificationTitleAndMessageForCancelledEvent(scheduleEventNotification, timezone);
  }
};

const notificationTitleAndMessageForCreatedAndUpdatedEvent = (
  scheduleEventNotification: any,
  timezone: string
): { title: string; message: string } => {
  const { datePart, timePart } = generateDateAndTimeParts(scheduleEventNotification, timezone);
  const title =
    scheduleEventNotification.notificationType == "UPDATED"
      ? `Your schedule for ${datePart} has been updated`
      : `Your schedule for ${datePart}`;
  const message = generateMessage(scheduleEventNotification, timePart);

  return { title, message };
};

const notificationTitleAndMessageForCancelledEvent = (
  scheduleEventNotification: any,
  timezone: string
): { title: string; message: string } => {
  const { datePart, timePart } = generateDateAndTimeParts(scheduleEventNotification, timezone);
  const title = `The job on ${datePart} is canceled`;
  const message = generateMessage(scheduleEventNotification, timePart);

  return { title, message };
};

const generateDateAndTimeParts = (scheduleEventNotification: any, timezone: string) => {
  const startTimeDate = scheduleEventNotification.scheduleEvent.startTime;
  const datePart = startTimeDate.toLocaleString("en-US", {
    weekday: "long",
    month: "short",
    day: "numeric",
    timeZone: timezone,
  });
  const timePart = startTimeDate.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
    timeZone: timezone,
  });

  return { datePart, timePart };
};

const generateMessage = (scheduleEventNotification: any, timePart: string) => {
  let message = `${scheduleEventNotification.scheduleEvent.project.name} at ${timePart}`;
  if (scheduleEventNotification.scheduleEvent.project.address) {
    message = message.concat(`\nAddress: ${scheduleEventNotification.scheduleEvent.project.address}`);
  }

  return message;
};
