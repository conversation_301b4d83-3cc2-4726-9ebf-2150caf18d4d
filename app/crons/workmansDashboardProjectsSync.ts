import { Organization } from "../models";
import { IntegrationSettingsService } from "../services/integrations/settings";
import { OBJECT_TYPES } from "@/models/integrationobjectsetting";
import { WorkmansDashboardService } from "@/services/integrations/workmansDashboardService";

// TODO - in the new Sage Intacct implementation PR, the sync code should be moved in its own implementation class
//    This file must only call <PERSON><PERSON> after we merge the Sage Intacct PR

const cron = require("node-cron");

const { ENVIRONMENT } = process.env;

// the reason we have a cron specially designed for QuickBooks projects syncing is because QB doesn't have a way to notify us when a client creates a project on their platform
export const initWorkmansDashboardProjectsSync = async () => {
  const integrationSettingsService = new IntegrationSettingsService();

  cron.schedule(
    "0 0 * * * *", // Every hour, at minute 0
    async () => {
      if (ENVIRONMENT !== "PRODUCTION" && ENVIRONMENT !== "STAGING") {
        return;
      }

      // Get all organizations
      const organizations = await Organization.findAll();

      const workmansDashboardService = new WorkmansDashboardService();
      // For each organization, check if PROJECTS integration is enabled
      for (const organization of organizations) {
        const integrationProjectsSync = await integrationSettingsService.getIntegrationObjectSetting(
          organization.id,
          OBJECT_TYPES.PROJECTS,
          "WORKMANS_DASHBOARD"
        );

        //------------------------------------------------
        // The below code will have to be moved inside the WorkmansDashboard integration class which will be created once we merge the Sage Intacct PR.
        // The Sage Intacct PR has a more robust way of handling each integration's logic
        if (integrationProjectsSync?.integration) {
          await workmansDashboardService.syncProjects(integrationProjectsSync.integration.id);
        }
      }
    },
    {
      scheduled: true,
      timezone: "America/Los_Angeles",
    }
  );
};
