import HttpError from "@/errors/HTTPError";
import * as Sen<PERSON> from "@sentry/node";
import fetch, { RequestInit } from "node-fetch";

export abstract class HTTPService {
  protected abstract baseUrl: string;
  protected abstract getHeaders(): Record<string, string>;

  protected async request<T>(url: string | URL, options: RequestInit): Promise<T> {
    try {
      const initOptions = {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
      };
      const response = await fetch(url, initOptions);

      if (!response.ok) {
        throw await response.json();
      }

      return response.json();
    } catch (error) {
      Sentry.captureException(error);
      throw new HttpError(
        500,
        `Failed to make ${options.method} request: ${error.error_message ?? error.message ?? JSON.stringify(error)}`
      );
    }
  }

  protected async post<T>(
    endpoint: string,
    data: any,
    queryParams?: Record<string, any>,
    additionalHeaders?: Record<string, string>
  ): Promise<T> {
    const url = new URL(`${this.baseUrl}/${endpoint}`);
    if (queryParams) {
      Object.entries(queryParams).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }

    return this.request<T>(url, {
      method: "POST",
      body: JSON.stringify(data),
      headers: additionalHeaders,
    });
  }

  protected async get<T>(
    endpoint: string,
    queryParams?: Record<string, any>,
    additionalHeaders?: Record<string, string>
  ): Promise<T> {
    const url = new URL(`${this.baseUrl}/${endpoint}`);
    if (queryParams) {
      Object.entries(queryParams).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }

    return this.request<T>(url, {
      method: "GET",
      headers: additionalHeaders,
    });
  }
}
