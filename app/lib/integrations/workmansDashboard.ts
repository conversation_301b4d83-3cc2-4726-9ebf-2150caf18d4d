import { HTTPService } from "./httpService";
import * as Sen<PERSON> from "@sentry/node";
import HttpError from "@/errors/HTTPError";
import fetch, { RequestInit } from "node-fetch";

const { WORKMANS_DASHBOARD_API_URL, WORKMANS_DASHBOARD_API_KEY_FOR_CLIENT_RAINIER_CONSTRUCTION } = process.env;

export class WorkmansDashboard extends HTTPService {
  protected baseUrl = `${WORKMANS_DASHBOARD_API_URL}`;

  protected getHeaders() {
    return {
      Authorization: `Bearer ${WORKMANS_DASHBOARD_API_KEY_FOR_CLIENT_RAINIER_CONSTRUCTION}`,
    };
  }

  projects(startDate: string, endDate: string) {
    // internally it should look like this api/jobs?startDate=2025-01-02&endDate=2025-01-28
    return this.get("api/jobs", { startDate, endDate }).then((jobs) => jobs ?? { jobs: [], job_count: 0 });
  }

  /**
   * WorkmansDashboard is a bit special when it comes to empty lists: it returns 404 instead of an empty array
   *    For this reason, we need to manually process this error and return undefined instead.
   */
  protected async request<T>(url: string | URL, options: RequestInit): Promise<T> {
    try {
      const initOptions = {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
      };
      const response = await fetch(url, initOptions);

      if (!response.ok) {
        const error = await response.json();
        if (response.status === 404) {
          return undefined;
        }
        throw error;
      }

      return response.json();
    } catch (error) {
      Sentry.captureException(error);
      throw new HttpError(
        500,
        `Failed to make ${options.method} request: ${error.error_message ?? error.message ?? JSON.stringify(error)}`
      );
    }
  }
}
