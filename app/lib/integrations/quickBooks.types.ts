export interface QuickBooksRef {
  value: string;
  name?: string;
  type?: string;
}

interface MetaData {
  CreateTime: string; // eg. "2025-02-26T08:00:00-08:00"
  LastUpdatedTime: string; // eg. "2025-02-26T08:00:00-08:00"
}

export interface QuickBooksTimeActivity {
  TxnDate: string;
  NameOf: string;
  EmployeeRef: QuickBooksRef;
  CustomerRef: QuickBooksRef;
  ItemRef: QuickBooksRef;
  BillableStatus: string;
  Taxable: boolean;
  HourlyRate: number;
  CostRate: number;
  BreakHours: number;
  BreakMinutes: number;
  BreakSeconds: number;
  StartTime: string; // eg. "2025-02-26T08:00:00-08:00"
  EndTime: string; // eg. "2025-02-26T17:00:00-08:00"
  domain: string;
  sparse: boolean;
  Id: string;
  SyncToken: string;
  MetaData: MetaData;
}

export interface QuickBooksEmployee {
  BillableTime: boolean;
  domain: "QBO";
  sparse: boolean;
  Id: string;
  SyncToken: string;
  MetaData: MetaData;
  GivenName: string;
  FamilyName: string;
  DisplayName: string;
  PrintOnCheckName: string;
  Active: true;
  V4IDPseudonym: string;
}

export interface QuickBooksTimeActivityData {
  startTime: string;
  endTime: string;
  txnDate: string;
  breakHours: number;
  breakMinutes: number;
  employeeRef: QuickBooksRef;
  customerRef?: QuickBooksRef;
  projectRef?: QuickBooksRef;
}

export interface QuickBooksCustomer {
  Taxable: boolean;
  Job: boolean;
  BillWithParent: boolean;
  ParentRef?: QuickBooksRef;
  Level: number;
  Balance: number;
  BalanceWithJobs: number;
  CurrencyRef: QuickBooksRef;
  PreferredDeliveryMethod: string;
  ClientEntityId: string;
  domain: string;
  sparse: boolean;
  Id: string;
  SyncToken: string;
  MetaData: MetaData;
  FullyQualifiedName: string;
  DisplayName: string;
  CompanyName: string;
  PrintOnCheckName: string;
  Active: boolean;
  V4IDPseudonym: string;
}

export interface QuickBooksJournalEntryLine {
  Id?: string;
  Description?: string;
  Amount: number;
  DetailType: "JournalEntryLineDetail";
  JournalEntryLineDetail: {
    PostingType: "Debit" | "Credit";
    AccountRef: QuickBooksRef;
    Entity?: {
      Type: "Customer" | "Project";
      EntityRef: QuickBooksRef;
    };
  };
  ProjectRef?: QuickBooksRef;
}

export interface QuickBooksJournalEntry {
  Id?: string;
  SyncToken?: string;
  TxnDate: string;
  CurrencyRef?: QuickBooksRef;
  PrivateNote?: string;
  DocNumber?: string;
  Line: QuickBooksJournalEntryLine[];
}

export interface QuickBooksJournalEntryResponse {
  JournalEntry: QuickBooksJournalEntry;
}
