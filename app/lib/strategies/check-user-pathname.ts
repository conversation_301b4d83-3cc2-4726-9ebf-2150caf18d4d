import { Request } from "express";
import { CheckPathnameStrategy } from "./check-pathname-strategy";
import { ExtendedUser } from "@/models/user";
import { UsersService } from "@/services/users";
import { getIdFromPathName } from "@/util/path";

export interface ValidationReqData {
  pathName: string;
  checkResourceId: string;
}

/**
 *
 * @param methodName {string} - Should be either "POST" or "PATCH"
 * @param currentReqPath {string} - The current request path
 * @param targetedPathname {string} - Should be either "employees" or "contractors"
 * @param user {ExtendedUser} - The user object
 * @returns {Object} ValidationReqData
 */
export const prepareForCheckUserValidation = async (
  methodName: string,
  currentReqPath: string,
  targetedPathname = "employees",
  user: ExtendedUser
) => {
  const validationReqData: Partial<ValidationReqData> = {};

  // if the target param exists
  // const targetedPathname = req.body?.target || "employees";
  const targetedResourceId = getIdFromPathName(currentReqPath);

  if (methodName === "POST") {
    if (targetedPathname === "employees") {
      validationReqData.pathName = `/employees`;
      validationReqData.checkResourceId = "";
    } else if (targetedPathname === "contractors") {
      validationReqData.pathName = `/contractors`;
      validationReqData.checkResourceId = "";
    }
  } else if (methodName === "PATCH" && targetedResourceId) {
    const usersService = new UsersService();
    const userToEvaluate = await usersService.findOne({
      where: {
        id: targetedResourceId,
        organizationId: user.organizationId,
      },
      simple: true,
      forceRawResponse: true,
    });

    if (targetedPathname === "employees" && userToEvaluate.workerClassification === "EMPLOYEE") {
      validationReqData.pathName = `/employees/${userToEvaluate.checkEmployeeId}`;
      validationReqData.checkResourceId = userToEvaluate.checkEmployeeId;
    } else if (targetedPathname === "contractors" && userToEvaluate.workerClassification === "CONTRACTOR") {
      validationReqData.pathName = `/contractors/${userToEvaluate.checkContractorId}`;
      validationReqData.checkResourceId = userToEvaluate.checkContractorId;
    }
  } else {
    validationReqData.pathName = "";
    validationReqData.checkResourceId = "";
  }

  return validationReqData;
};

export class UserPathnameStrategy extends CheckPathnameStrategy {
  async execute(
    req: Request,
    user: any,
    method: string,
    pathName: string,
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    _body: any
  ): Promise<string | null> {
    // Step 1: Validate the target
    if (!this.validateTarget(req)) {
      throw new Error("Invalid target"); // We'll catch this error in the middleware and handle the response there.
    }

    const targetedPathname = req.body?.target || "employees";

    // Step 2: Prepare for user validation
    const validationData: Partial<ValidationReqData> = await this.prepareForValidation(
      method,
      pathName,
      targetedPathname,
      user
    );

    // Step 3: Handle edge cases
    if (!this.handleEdgeCases(targetedPathname, validationData, method)) {
      return null; // This will signal the middleware to just call next() and not proceed further.
    }

    // Step 4: Side effects are handled separately in `applySideEffects`

    // Step 5: Update pathName based on validation data
    return validationData.pathName;
  }

  validateTarget(req: Request): boolean {
    return !(req.body?.target && req.body.target !== "employees" && req.body.target !== "contractors");
  }

  async prepareForValidation(
    method: string,
    pathName: string,
    target: string,
    user: any
  ): Promise<Partial<ValidationReqData>> {
    return prepareForCheckUserValidation(method, pathName, target, user);
  }

  handleEdgeCases(
    targetedPathname: string,
    validationData: Partial<ValidationReqData>,
    method: string
  ): boolean {
    return (
      !(targetedPathname === "employees" && !validationData.checkResourceId && method !== "POST") &&
      !(targetedPathname === "contractors" && !validationData.checkResourceId && method !== "POST")
    );
  }

  async applySideEffects(req: Request, user: any): Promise<void> {
    // Assign company to body and req.checkCompanyId
    req.body.company = user?.organizationCheckCompanyId || null;
    req.checkCompanyId = user?.organizationCheckCompanyId || null;
  }
}
