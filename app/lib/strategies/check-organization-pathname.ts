/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request } from "express";
import { CheckPathnameStrategy } from "./check-pathname-strategy";

export const generateCompanyResourcePathname = (methodName: string, checkCompanyId: string) => {
  // not in use
  if (methodName === "POST") {
    return `/companies`;
  }

  if (methodName === "PATCH") {
    return `/companies/${checkCompanyId}`;
  }

  return "";
};

export class OrganizationPathnameStrategy extends CheckPathnameStrategy {
  async execute(_req: Request, user: any, method: string, _pathName: string, _body: any) {
    return generateCompanyResourcePathname(method, user.organizationCheckCompanyId);
  }
}
