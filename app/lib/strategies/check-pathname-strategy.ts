/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import { Request } from "express";
import { ExtendedUser } from "@/models/user";

export class CheckPathnameStrategy {
  async execute(
    _req: Request,
    _user: ExtendedUser,
    _method: string,
    _pathName: string,
    _body: any
  ): Promise<string | null> {
    return null; // Default no-op
  }

  async applySideEffects(_req: Request, _user: ExtendedUser): Promise<void> {
    // Default no-op, override in subclasses as needed
  }
}
