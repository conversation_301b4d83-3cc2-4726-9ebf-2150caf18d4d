import Sequelize from "sequelize";

const { POSTGRESS_DB_URI } = process.env;

if (!POSTGRESS_DB_URI) {
  throw new Error("POSTGRESS_DB_URI env is not set");
}
// hack for fixing a Sequelize bug where it returns a string for Decimal columns
(Sequelize as any).DataTypes.postgres.DECIMAL.parse = parseFloat;
const sequelize: Sequelize.Sequelize = new Sequelize.Sequelize(POSTGRESS_DB_URI, {
  dialect: "postgres",
  define: {
    underscored: true,
  },
  logging: false,
  pool: {
    max: 50,
  },
});

export const successfullyConnectedToPostgress = async () => {
  try {
    await sequelize.authenticate();
    console.log("Postgress connection has been established successfully.");

    return true;
  } catch (error) {
    console.error("Failed to connect to Postgress", error);
  }

  return false;
};

export default sequelize;
