import * as dotenv from "dotenv";
dotenv.config();

import fetch from "node-fetch";
import User from "@/models/user";
import EarningRate from "@/models/earningrate";
import sequelize from "@/lib/sequelize";

const checkApiUrl = process.env.CHECK_API_URL;
const checkApiKey = process.env.CHECK_API_KEY;

if (!checkApiUrl || !checkApiKey) {
  throw new Error("CHECK_API_URL or CHECK_API_KEY is not defined");
}

// Helper function to partition an array into chunks of specified size
function partitionArray(array: any[], size: number): any[][] {
  return array.reduce((acc, val, i) => {
    if (i % size === 0) acc.push(array.slice(i, i + size));

    return acc;
  }, []);
}

// Main function to update earning rates
async function updateEarningRates(): Promise<void> {
  try {
    await sequelize.authenticate();
    console.log("Connection has been established successfully.");
    const users = await User.findAll();

    const usersWithCheckEmployeeId = users.filter((user) => user.checkEmployeeId);
    const listOfEmployeeIds = usersWithCheckEmployeeId.map((user) => `id=${user.checkEmployeeId}`);
    console.log(`Found ${listOfEmployeeIds.length} users with checkEmployeeId`);
    const partitions = partitionArray(listOfEmployeeIds, 25);

    console.log(listOfEmployeeIds, "this is list of employee ids");
    console.log(partitions, "this is partitions");

    for (const partition of partitions) {
      const queryString = partition.join("&");
      let nextPageUrl = `${checkApiUrl}/employees?${queryString}`;
      console.log(`Fetch req to: ${nextPageUrl}`);

      while (nextPageUrl) {
        const response = await fetch(nextPageUrl, {
          headers: { Authorization: `Bearer ${checkApiKey}` },
        });
        const data = await response.json();

        for (const employee of data.results) {
          const user = usersWithCheckEmployeeId.find((u) => u.checkEmployeeId === employee.id);
          if (user) {
            const { primary_earning_rate, primary_ot_earning_rate } = employee.metadata;

            if (!primary_earning_rate || !primary_ot_earning_rate) {
              // if the employee is in a company that never fully onboarded on Hammr payroll,
              // skip creating or updating earning rates in Hammr
              // K&L, Rancho, Riegle, Madson
              const companiesToSkip = [
                "com_a1T2gzLpeZWugV4VV6F0",
                "com_ur87qd1jo6poFA2A1RxY",
                "com_EYaVhbjVF78AsBLD57Uu",
                "com_09Iuwx3XZSOIppdJGPTo",
              ];
              if (companiesToSkip.includes(employee.company)) {
                continue;
              }

              console.log(`Check Employee ${employee.id} is missing primary_earning_rate or primary_ot_earning_rate`);
            }

            // fetch the earning rates for the employee and then compare it to the user.hourlyRate
            const checkEarningRatesResponse = await fetch(
              `${checkApiUrl}/earning_rates?employee=${employee.id}&active=true`,
              {
                headers: { Authorization: `Bearer ${checkApiKey}` },
              }
            );

            const checkEarningRatesData = await checkEarningRatesResponse.json();

            // check a few things - check if the employee.metadta.primary_earning_rate + employee.metadata.primarty_ot_earning_rate matches the same earning rates in Check - then check if the user's hourlyRate matches the amount in the earning rate
            const primaryEarningRate = checkEarningRatesData.results.find((er: any) => er.id === primary_earning_rate);
            const primaryOtEarningRate = checkEarningRatesData.results.find(
              (er: any) => er.id === primary_ot_earning_rate
            );

            if (!primaryEarningRate || !primaryOtEarningRate) {
              console.log(`Check Employee ${employee.id} is missing primary_earning_rate or primary_ot_earning_rate`);
              continue;
            }

            // if earning rate doesn't exist, create it from payroll data
            const existingEarningRate = await EarningRate.findOne({
              where: { user_id: user.id, type: "REG", active: true },
            });

            if (existingEarningRate) {
              if (parseFloat(existingEarningRate.amount) !== parseFloat(primaryEarningRate?.amount)) {
                console.log(
                  "Earning rate amount mismatch for user: ",
                  user.id,
                  "check employee id: ",
                  user.checkEmployeeId,
                  "existing earning rate amount: ",
                  existingEarningRate.amount,
                  "primary earning rate amount in payroll: ",
                  primaryEarningRate?.amount
                );
              }
              await EarningRate.update(
                { checkEarningRateId: primary_earning_rate, amount: primaryEarningRate?.amount },
                { where: { user_id: user.id, type: "REG", active: true } }
              );
            } else {
              console.log("Creating earning rate for user: ", user.id, "check employee id: ", user.checkEmployeeId);
              await EarningRate.create({
                name: primaryEarningRate?.name,
                amount: primaryEarningRate?.amount,
                period: primaryEarningRate?.period.toUpperCase(),
                active: true,
                type: "REG",
                startDate: new Date().valueOf(),
                userId: user.id,
                checkEarningRateId: primary_earning_rate,
              });
            }

            const existingOtEarningRate = await EarningRate.findOne({
              where: { user_id: user.id, type: "OT", active: true },
            });

            if (existingOtEarningRate) {
              if (parseFloat(existingOtEarningRate.amount) !== parseFloat(primaryOtEarningRate?.amount)) {
                console.log(
                  "Earning rate amount mismatch for user: ",
                  user.id,
                  "check employee id: ",
                  user.checkEmployeeId,
                  "existing earning rate amount: ",
                  existingEarningRate.amount,
                  "primary ot earning rate amount in payroll: ",
                  primaryEarningRate?.amount
                );
              }

              await EarningRate.update(
                { checkEarningRateId: primary_ot_earning_rate, amount: primaryOtEarningRate?.amount },
                { where: { user_id: user.id, type: "OT", active: true } }
              );
            } else {
              // create ot earning rate
              console.log("Creating OT earning rate for user: ", user.id, "check employee id: ", user.checkEmployeeId);
              await EarningRate.create({
                name: primaryOtEarningRate?.name,
                amount: primaryOtEarningRate?.amount,
                period: primaryOtEarningRate?.period.toUpperCase(),
                active: true,
                type: "OT",
                startDate: new Date().valueOf(),
                userId: user.id,
                checkEarningRateId: primary_ot_earning_rate,
              });
            }
          }
        }

        nextPageUrl = data.next;
      }
    }
  } catch (error) {
    console.error("Failed to update earning rates:", error);
  } finally {
    await sequelize.close();
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  updateEarningRates();
}
