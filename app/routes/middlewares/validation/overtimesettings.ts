import Joi from "joi";

import { WEEK_DAYS, OVERTIME_DISTRIBUTION } from "@/models/overtimesettings";

export const updateOvertimeSettingsSchema = Joi.object({
  name: Joi.string().optional(), // optional name update
  description: Joi.string().allow(null).allow("").optional(),
  weeklyOvertimeEnabled: Joi.boolean().optional(),
  dailyOvertimeEnabled: Joi.boolean().optional(),
  dailyOvertimeThreshold: Joi.number().min(0).optional(),
  weeklyOvertimeThreshold: Joi.number().min(0).optional(),
  overtimeMultiplier: Joi.number().min(1).optional(),
  weekStartDay: Joi.string()
    .valid(...WEEK_DAYS)
    .optional(),
  aggregatePwForDailyOvertime: Joi.boolean().optional(),
  dailyDoubleOvertimeEnabled: Joi.boolean().optional(),
  dailyDoubleOvertimeThreshold: Joi.number().min(0).optional(),
  overtimeDistribution: Joi.string()
    .valid(...OVERTIME_DISTRIBUTION)
    .optional(), // this property should only be allowed when modifying "default" overtime settings
  overtimeDays: Joi.array()
    .items(Joi.string().valid(...WEEK_DAYS))
    .unique()
    .max(7)
    .empty()
    .optional(),
  doubleOvertimeDays: Joi.array()
    .items(Joi.string().valid(...WEEK_DAYS))
    .unique()
    .max(7)
    .empty()
    .optional(),
  isActive: Joi.boolean().optional(),
  automaticOvertimeCalculation: Joi.boolean().optional(),
});

// we can change this, but much of the create settings below are set to optional because the idea is these new overtime setting rows will inherit from default overtime settings if they are not specified
export const createOvertimeSettingsSchema = updateOvertimeSettingsSchema.keys({
  name: Joi.string().required(),
  description: Joi.string().allow(null).allow("").optional(),
  weeklyOvertimeEnabled: Joi.boolean().optional(),
  dailyOvertimeEnabled: Joi.boolean().optional(),
  dailyOvertimeThreshold: Joi.number().min(0).optional(),
  weeklyOvertimeThreshold: Joi.number().min(0).optional(),
  overtimeMultiplier: Joi.number().min(1).optional(),
  dailyDoubleOvertimeEnabled: Joi.boolean().optional(),
  dailyDoubleOvertimeThreshold: Joi.number().min(0).optional(),
  overtimeDays: Joi.array()
    .items(Joi.string().valid(...WEEK_DAYS))
    .unique()
    .max(7)
    .empty()
    .optional(),
  doubleOvertimeDays: Joi.array()
    .items(Joi.string().valid(...WEEK_DAYS))
    .unique()
    .max(7)
    .empty()
    .optional(),
});
