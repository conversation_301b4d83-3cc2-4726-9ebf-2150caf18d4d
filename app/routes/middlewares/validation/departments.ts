import Joi from "joi";

export const departmentSchema = Joi.object({
  name: Joi.string().required(),
  departmentMembers: Joi.array().items(Joi.number()),
});

export const updateDepartmentSchema = Joi.object({
  name: Joi.string().required(),
  departmentMembers: Joi.array().items(Joi.number()),
});

export const updateDepartmentMemberSchema = Joi.object({
  userId: Joi.number().required(),
  departmentId: Joi.number().required(),
});

export const bulkAssignDepartmentMembersSchema = Joi.object({
  departmentId: Joi.number().required(),
  userIds: Joi.array().items(Joi.number()).required(),
});
