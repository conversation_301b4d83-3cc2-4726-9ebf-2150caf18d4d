import Joi from "joi";
import { REPORT_FORMAT } from "@/models/reports";

export const getCertifiedPayrollProjectSchema = Joi.object({
  payrollId: Joi.string().required(),
  projectId: Joi.number().integer().required(),
  signatoryName: Joi.string().required(),
  signatoryTitle: Joi.string().required(),
  reportFormat: Joi.string()
    .valid(...Object.values(REPORT_FORMAT))
    .required(),
  reportType: Joi.string().valid("pdf", "csv", null).optional(),
  isLastCertifiedPayrollReport: Joi.boolean().optional(),
  remarks: Joi.string().optional().allow(""),
  payrollWeekEnding: Joi.string().isoDate().optional().messages({
    "string.isoDate": '"payrollWeekEnding" must be in YYYY-MM-DD format',
  }),
});

export const generate401kSchema = Joi.object({
  payrollId: Joi.string().required(),
});

// Schema for AA-202 report parameters
export const aa202Schema = Joi.object({
  projectId: Joi.number().integer().required(),
  // Validate dates ensuring endDate is after startDate
  startDate: Joi.date().iso().required().messages({
    "date.format": '"startDate" must be in YYYY-MM-DD format',
  }),
  endDate: Joi.date().iso().required().greater(Joi.ref("startDate")).messages({
    "date.format": '"endDate" must be in YYYY-MM-DD format',
    "date.greater": '"endDate" must be after "startDate"',
  }),
  percentOfWorkCompleted: Joi.number().min(0).max(100).required(),
  tradeOrCraft: Joi.string().optional().allow(""),
  signatoryName: Joi.string().required(),
  signatoryTitle: Joi.string().required(),
});
