import Joi from "joi";

import { TIMESHEET_STATUSES } from "@/models/timesheet";

import {
  APP_STATE_TYPES,
  LOCATION_EVENT_TYPES,
  LOCATION_PERMISSION_TYPES,
  PLATFORM_TYPES,
} from "@/models/userlocation";

const BREAK_DURATION_ALLOWED_VALUES = [0, 900, 1800, 3600];

export const updateClockActionLocationSchema = Joi.object({
  lat: Joi.number().optional(),
  long: Joi.number().optional(),
  speed: Joi.number().optional(),
  horizontalAccuracy: Joi.number().optional(),
  verticalAccuracy: Joi.number().optional(),
  altitude: Joi.number().optional(),
  loggedAt: Joi.number().optional(), // timestamp so number
  platform: Joi.string()
    .valid(...PLATFORM_TYPES)
    .optional(),
  appState: Joi.string()
    .valid(...APP_STATE_TYPES)
    .optional(),
  locationPermission: Joi.string()
    .valid(...LOCATION_PERMISSION_TYPES)
    .optional(),
  preciseLocationEnabled: Joi.boolean().optional(),
  locationEvent: Joi.string()
    .valid(...LOCATION_EVENT_TYPES)
    .optional(),
  userId: Joi.number().optional(),
  timesheetId: Joi.number().optional(),
  breakId: Joi.number().allow(null).optional(),
  geofenceProjectId: Joi.number().allow(null).optional(),
  organizationId: Joi.number().allow(null).optional(),
});

export const timesheetReportQuerySchema = Joi.object({
  from: Joi.number(),
  to: Joi.number(),
  offsetTimestamp: Joi.number(),
  offsetId: Joi.number(),
  pageSize: Joi.number(),
  isResolved: Joi.boolean(),
  userId: Joi.string().pattern(/^\d+(,\d+)*$/),
  projectId: Joi.string().pattern(/^\d+(,\d+)*$/),
  costCodeId: Joi.string().pattern(/^-1|\d+(,\d+)*$/), // this could be a -1 to indiciate "null" cost codes as cost code is optional
  status: Joi.string().valid(...TIMESHEET_STATUSES),
  equipmentId: Joi.string().pattern(/^\d+(,\d+)*$/),
})
  .and("from", "to")
  .messages({ "object.and": "Please send both 'from' and 'to' parameters" });

export const createTimesheetSchema = Joi.object({
  userId: Joi.number().required(),
  projectId: Joi.number().required(),
  costCodeId: Joi.number().optional().allow(null),
  userClassificationId: Joi.number().optional().allow(null),
  description: Joi.string().optional().allow("").allow(null),
  clockIn: Joi.number().required(),
  clockOut: Joi.number().optional().allow(null),
  workersCompCodeId: Joi.number().optional().allow(null),
  breakDuration: Joi.number()
    .valid(...BREAK_DURATION_ALLOWED_VALUES)
    .optional()
    .allow(null),
  clockInLocation: updateClockActionLocationSchema.optional().allow(null),
  clockOutLocation: updateClockActionLocationSchema.optional().allow(null),
  clockedOutBySwitch: Joi.boolean().optional(),
  breaks: Joi.array().optional(),
  status: Joi.string()
    .valid(...TIMESHEET_STATUSES)
    .optional(),
  isManual: Joi.boolean().optional(),
  otDuration: Joi.number().min(0).optional().allow(null),
  dotDuration: Joi.number().min(0).optional().allow(null),
  driveTimeDuration: Joi.number().min(0).optional().allow(null),
  equipmentId: Joi.number().optional().allow(null),
});

export const updateTimesheetSchema = Joi.object({
  projectId: Joi.number().optional(),
  costCodeId: Joi.number().optional().allow(null),
  description: Joi.string().optional().allow("").allow(null),
  clockIn: Joi.number().optional().allow(null),
  clockOut: Joi.number().optional().allow(null),
  clockOutLocation: updateClockActionLocationSchema.optional().allow(null),
  breakDuration: Joi.number()
    .valid(...BREAK_DURATION_ALLOWED_VALUES)
    .optional()
    .allow(null),
  hideGeofenceWarning: Joi.boolean().optional(),
  clockedOutBySwitch: Joi.boolean().optional(),
  breaks: Joi.array().optional(),
  userClassificationId: Joi.number().optional().allow(null),
  status: Joi.string()
    .valid(...TIMESHEET_STATUSES.filter((status) => status !== "PAID"))
    .optional(),
  workerId: Joi.number().optional(),
  organizationId: Joi.number().optional(),
  workersCompCodeId: Joi.number().optional().allow(null),
  isManual: Joi.boolean().optional(),
  isDeleted: Joi.boolean().optional(),
  regEarningRateId: Joi.number().optional(),
  otEarningRateId: Joi.number().optional(),
  userId: Joi.number().optional(),
  otDuration: Joi.number().min(0).optional().allow(null),
  dotDuration: Joi.number().min(0).optional().allow(null),
  driveTimeDuration: Joi.number().min(0).optional().allow(null),
  equipmentId: Joi.number().optional().allow(null),
});

export const createBulkTimesheetsSchema = Joi.array().items(createTimesheetSchema).min(1).required();

export const bulkDeleteTimesheetsSchema = Joi.object({
  timesheetIds: Joi.array().items(Joi.number()).min(1).required(),
});
