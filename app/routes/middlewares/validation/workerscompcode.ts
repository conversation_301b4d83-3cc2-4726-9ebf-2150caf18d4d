import Joi from "joi";
import { CostCode } from "@/models";
import { isModel } from "@/middlewares/validatation";
import WorkersCompCode from "@/models/workersCompCode";

export const workersCompCodeSchema = Joi.object({
  name: Joi.string().min(1).max(100).required(),
  code: Joi.string().min(1).max(100).required(),
  isArchived: Joi.boolean().optional(),
});

export const assignCostCodesSchema = Joi.object({
  costCodeIds: Joi.array().items(Joi.number()).external(isModel(CostCode)).required(),
});

export const assignWorkersSchema = Joi.array()
  .items(
    Joi.object({
      userId: Joi.number().required(),
      workersCompCodeId: Joi.number().allow(null).required().external(isModel(WorkersCompCode)),
    })
  )
  .required();
