import Joi from "joi";

export const injuryReportSchema = Joi.object({
  note: Joi.string().required(),
  timesheetId: Joi.number().required(),
  userId: Joi.number().required(),
  injuryPhotos: Joi.array().items(Joi.string()).min(1).required(),
  resolvedBy: Joi.number().optional(),
  resolutionNote: Joi.string().optional(),
});

export const injuryReportUpdateSchema = Joi.object({
  resolvedBy: Joi.number().required(),
  resolutionNote: Joi.string().required(),
  resolutionDate: Joi.number().required(),
});
