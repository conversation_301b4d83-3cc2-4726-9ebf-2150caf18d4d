import Joi from "joi";

export const createDailyReportSchema = Joi.object({
  date: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      "string.pattern.base": "date with value {#value} fails to match YYYY-MM-DD format",
    }),
  summary: Joi.string().allow(null).allow("").optional(),
  injuryNotes: Joi.string().allow(null).allow("").optional(),
  equipmentNotes: Joi.string().allow(null).allow("").optional(),
  materialNotes: Joi.string().allow(null).allow("").optional(),
  signOffName: Joi.string().required(),
  projectId: Joi.number().required(),
  selectedPhotoIds: Joi.array().items(Joi.string()).optional().allow(null),
});
