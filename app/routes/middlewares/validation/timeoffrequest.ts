import Joi from "joi";
import { TimeOffRequestStatus } from "@/models/timeoffrequest";

const startDateValidation = Joi.date().timestamp("unix");

const endDateValidation = Joi.date().timestamp("unix");

export const createTimeOffRequestSchema = Joi.object({
  userId: Joi.number().integer().positive().optional(),
  timeOffPolicyId: Joi.number().integer().positive().required(),
  startDate: startDateValidation.required(),
  endDate: endDateValidation.required(),
  totalHours: Joi.number().positive().required(),
  requestNotes: Joi.string().optional().allow(""),
});

export const updateTimeOffRequestSchema = Joi.object({
  startDate: startDateValidation.optional(),
  endDate: endDateValidation.optional(),
  totalHours: Joi.number().positive().optional(),
  requestNotes: Joi.string().optional().allow(""),
}).min(1);

export const listTimeOffRequestsSchema = Joi.object({
  userId: Joi.alternatives()
    .try(Joi.array().items(Joi.number().integer().positive()), Joi.number().integer().positive())
    .optional(),
  status: Joi.alternatives()
    .try(
      Joi.array().items(Joi.string().valid(...Object.values(TimeOffRequestStatus))),
      Joi.string().valid(...Object.values(TimeOffRequestStatus))
    )
    .optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  page: Joi.number().integer().min(1).optional().default(1),
  limit: Joi.number().integer().min(0).max(100).optional().default(20),
});

export const declineTimeOffRequestSchema = Joi.object({
  declineNotes: Joi.string().required(),
});
