import Joi from "joi";
import { Request, Response, NextFunction } from "express";
import dayjs from "dayjs";
import { Op } from "sequelize";

import { CATEGORY_TYPES, CONTRIBUTION_TYPES } from "@/models/companybenefit";
import { CHANGE_REASON, UNENROLL_REASON } from "@/models/employeebenefit";
import { EmployeeBenefitsService } from "@/services/employeebenefits";

const dateFormat = /^\d{4}-\d{2}-\d{2}$/;
const amountFormat = /^\d+\.\d{2}$/;

const employeeBenefitsService = new EmployeeBenefitsService();

export const createCompanyBenefitSchema = Joi.object({
  name: Joi.string().required(),
  category: Joi.string()
    .valid(...CATEGORY_TYPES)
    .required(),
  contributionType: Joi.string()
    .valid(...CONTRIBUTION_TYPES)
    .required(),
  benefitStartDate: Joi.string().pattern(dateFormat).required().messages({
    "string.pattern.base": "benefitStartDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  benefitEndDate: Joi.string().pattern(dateFormat).messages({
    "string.pattern.base": "benefitEndDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  benefitProviderName: Joi.string().allow("").allow(null).optional(),
  benefitProviderAddress: Joi.string().allow("").allow(null).optional(),
  benefitProviderPhone: Joi.string().allow("").allow(null).optional(),
  isApprovedFringe: Joi.boolean(),
});

export const updateCompanyBenefitSchema = Joi.object({
  name: Joi.string().optional(),
  category: Joi.string()
    .valid(...CATEGORY_TYPES)
    .optional(),
  contributionType: Joi.string()
    .valid(...CONTRIBUTION_TYPES)
    .optional(),
  benefitStartDate: Joi.string().pattern(dateFormat).optional().messages({
    "string.pattern.base": "benefitStartDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  benefitEndDate: Joi.string().pattern(dateFormat).optional().allow(null).messages({
    "string.pattern.base": "benefitEndDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  benefitProviderName: Joi.string().allow("").allow(null).optional(),
  benefitProviderAddress: Joi.string().allow("").allow(null).optional(),
  benefitProviderPhone: Joi.string().allow("").allow(null).optional(),
  checkCompanyBenefitId: Joi.string().optional(),
  isApprovedFringe: Joi.boolean().optional(),
});

export const createEmployeeBenefitSchema = Joi.object({
  name: Joi.string().required(),
  companyContributionPercent: Joi.number().min(0).optional(),
  employeeContributionPercent: Joi.number().min(0).optional(),
  companyPeriodAmount: Joi.string().pattern(amountFormat).optional(),
  employeePeriodAmount: Joi.string().pattern(amountFormat).optional(),
  companyContributionAmount: Joi.string().pattern(amountFormat).optional(),
  employeeContributionAmount: Joi.string().pattern(amountFormat).optional(),
  contributionType: Joi.string()
    .valid(...CONTRIBUTION_TYPES)
    .required(),
  period: Joi.string().valid("MONTHLY", null).optional(),
  category: Joi.string()
    .valid(...CATEGORY_TYPES)
    .optional(),
  benefitStartDate: Joi.string().pattern(dateFormat).required().messages({
    "string.pattern.base": "benefitStartDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  benefitEndDate: Joi.string().pattern(dateFormat).optional().messages({
    "string.pattern.base": "benefitEndDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  checkBenefitId: Joi.string().optional(),
  effectiveDate: Joi.string().pattern(dateFormat).optional(),
  userId: Joi.number().required(),
  companyBenefitId: Joi.number().required(),
});

export const updateEmployeeBenefitSchema = Joi.object({
  name: Joi.string().optional(),
  companyContributionPercent: Joi.number().min(0).optional(),
  employeeContributionPercent: Joi.number().min(0).optional(),
  companyPeriodAmount: Joi.string().pattern(amountFormat).optional(),
  employeePeriodAmount: Joi.string().pattern(amountFormat).optional(),
  companyContributionAmount: Joi.string().pattern(amountFormat).optional(),
  employeeContributionAmount: Joi.string().pattern(amountFormat).optional(),
  contributionType: Joi.string()
    .valid(...CONTRIBUTION_TYPES)
    .optional(),
  period: Joi.string().valid("MONTHLY", null).optional(),
  category: Joi.string()
    .valid(...CATEGORY_TYPES)
    .optional(),
  benefitStartDate: Joi.string().pattern(dateFormat).optional().messages({
    "string.pattern.base": "benefitStartDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  benefitEndDate: Joi.string().pattern(dateFormat).optional().messages({
    "string.pattern.base": "benefitEndDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  checkBenefitId: Joi.string().optional(),
  effectiveDate: Joi.string().pattern(dateFormat).optional(),
  changeReason: Joi.string()
    .valid(...CHANGE_REASON)
    .required(),
  // require userId for the time being
  userId: Joi.number().required(),
});

export const deactivateEmployeeBenefitSchema = Joi.object({
  effectiveDate: Joi.string().pattern(dateFormat).required().messages({
    "string.pattern.base": "effectiveDate with value {#value} fails to match YYYY-MM-DD format",
  }),
  changeReason: Joi.string()
    .valid(...UNENROLL_REASON)
    .required(),
});

export const validateNoPreExistingDynamicBenefit = async (req: Request, res: Response, next: NextFunction) => {
  if (req.body.contributionType === "DYNAMIC") {
    const benefitStartDate = req.body.benefitStartDate || dayjs().format("YYYY-MM-DD");

    const employeeBenefits = await employeeBenefitsService.findAll({
      where: {
        contributionType: "DYNAMIC",
        organizationId: req.session.user.organizationId,
        userId: req.body.userId,
        [Op.or]: [
          { benefitEndDate: null },
          {
            benefitEndDate: {
              [Op.gte]: benefitStartDate,
            },
          },
        ],
      },
    });

    if (employeeBenefits.length > 0) {
      return res.status(400).json({
        result: "error",
        error: "Cannot create a new dynamic benefit for an employee with an existing dynamic benefit",
      });
    }
  }
  next();
};
