import Joi from "joi";

export const createClassificationSchema = Joi.object({
  name: Joi.string().required(),
  basePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  fringePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  startDate: Joi.number().integer().required(),
  endDate: Joi.number()
    .integer()
    .allow(null)
    .optional()
    .allow(null)
    .when("startDate", {
      is: Joi.exist(),
      then: Joi.number().integer().min(Joi.ref("startDate")),
    }),
  wageTableId: Joi.number().required(),
  fringeBenefitClassifications: Joi.array()
    .items({
      amount: Joi.string().required(),
      fringeBenefitId: Joi.number().required(),
      startDate: Joi.number().integer().required(),
    })
    .optional(),
});

export const updateClassificationSchema = Joi.object({
  name: Joi.string().optional(),
  basePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .optional(),
  fringePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .optional(),
  effectiveDate: Joi.number().integer().required(),
  endDate: Joi.number().integer().allow(null).optional(),
  wageTableId: Joi.number().optional(),
});
