import Joi from "joi";

export const getEquipmentQuerySchema = Joi.object({
  includeIsArchived: Joi.boolean().default(false),
  categoryIds: Joi.string().default("").allow(""),
});

export const createEquipmentSchema = Joi.object({
  name: Joi.string().required(),
  categoryName: Joi.string().required(),
  year: Joi.number().integer().allow(null),
  hourlyCost: Joi.number().required(),
});

export const updateEquipmentSchema = Joi.object({
  name: Joi.string(),
  categoryName: Joi.string(),
  year: Joi.number().integer().allow(null),
  hourlyCost: Joi.number(),
  isArchived: Joi.boolean(),
}).min(1); // Ensure at least one field is provided for update
