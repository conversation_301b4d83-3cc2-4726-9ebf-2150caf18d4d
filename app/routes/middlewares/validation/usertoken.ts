import Joi from "joi";

export const createUserTokenSchema = Joi.object({
  publicToken: Joi.string().optional(),
  platform: Joi.string().optional(),
  provider: Joi.string().required(),
});

export const createJournalEntrySchema = Joi.object({
  payrollId: Joi.string().required(),
  integrationUserTokenId: Joi.number().required(),
  platform: Joi.string().required(),
});

export const updateJournalEntrySchema = Joi.object({
  payrollId: Joi.string().required(),
  syncHistoryId: Joi.number().required(),
  integrationUserTokenId: Joi.number().required(),
  platform: Joi.string().required(),
});

export const exportPayrollJournalEntryToCsvSchema = Joi.object({
  payrollId: Joi.string().required(),
});
