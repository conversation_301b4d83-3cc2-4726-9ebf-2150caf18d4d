import Joi from "joi";
import { AccrualMethod, TimeOffPolicyType } from "../../../models/timeoffpolicy";

export const createTimeOffPolicySchema = Joi.object({
  name: Joi.string().required(),
  type: Joi.string()
    .valid(...Object.values(TimeOffPolicyType))
    .required(),
  isLimited: Joi.boolean().required(),
  accrualMethod: Joi.string()
    .valid(...Object.values(AccrualMethod))
    .when("isLimited", { is: true, then: Joi.required(), otherwise: Joi.forbidden() }),
  accrualHoursRate: Joi.number()
    .positive()
    .when("isLimited", { is: true, then: Joi.required(), otherwise: Joi.forbidden() }),
  accrualHoursInterval: Joi.number()
    .positive()
    .when("accrualMethod", {
      is: AccrualMethod.HOURS_WORKED,
      then: Joi.required(),
      otherwise: Joi.allow(null),
    }),
  accrualResetDate: Joi.string()
    .pattern(/^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/)
    .required()
    .messages({
      "string.pattern.base": "Accrual Reset Date must be of format MM-DD",
    })
    .allow(null)
    .optional(),
  carryoverLimit: Joi.number().optional().allow("").min(0).when("isLimited", {
    is: true,
    then: Joi.optional(),
    otherwise: Joi.forbidden(),
  }),
  accrualLimit: Joi.number().min(0).when("isLimited", { is: true, then: Joi.optional(), otherwise: Joi.forbidden() }),
  addNewEmployeesAutomatically: Joi.boolean().required(),
});

export const updateFullPolicySchema = Joi.object({
  name: Joi.string().optional(),
  type: Joi.string()
    .valid(...Object.values(TimeOffPolicyType))
    .optional(),
  accrualMethod: Joi.string()
    .valid(...Object.values(AccrualMethod))
    .optional(),
  accrualHoursRate: Joi.number().positive().optional(),
  accrualHoursInterval: Joi.number()
    .positive()
    .when("accrualMethod", {
      is: AccrualMethod.HOURS_WORKED,
      then: Joi.required(),
      otherwise: Joi.allow(null),
    }),
  accrualResetDate: Joi.string()
    .pattern(/^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/)
    .required()
    .messages({
      "string.pattern.base": "Accrual Reset Date must be of format MM-DD",
    })
    .allow(null)
    .optional(),
  carryoverLimit: Joi.number().allow("").min(0).optional(),
  accrualLimit: Joi.number().min(0).optional(),
  addNewEmployeesAutomatically: Joi.boolean().optional(),
}).min(1);

export const enrollEmployeeSchema = Joi.object({
  userIds: Joi.array().items(Joi.number()).required(),
  startingBalance: Joi.number().min(0).optional(),
});

export const listTimeOffPoliciesSchema = Joi.object({
  includeArchived: Joi.boolean().optional(),
});
