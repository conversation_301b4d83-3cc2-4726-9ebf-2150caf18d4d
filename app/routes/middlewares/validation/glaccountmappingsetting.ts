import { PAYROLL_CATEGORY_TYPES } from "@/models/glaccountmapping";
import GlAccountMappingSetting from "@/models/glaccountmappingsetting";
import { GlAccountMapping } from "@/models/index";
import { NextFunction, Request, Response } from "express";
import Joi from "joi";

export const createGlAccountMappingSettingSchema = Joi.object({
  autoSync: Joi.boolean().optional().allow(null),
  consolidateJournalEntryBy: Joi.string().optional().allow(null),
  entryDate: Joi.string().optional().allow(null),
  platform: Joi.string().optional().allow(null),
  integrationUserTokenId: Joi.number().required(),
});

export const updateGlAccountMappingSettingSchema = Joi.object({
  autoSync: Joi.boolean().optional().allow(null),
  consolidateJournalEntryBy: Joi.string().optional().allow(null),
  entryDate: Joi.string().optional().allow(null),
  integrationUserTokenId: Joi.number().required(),
});

export const validateGlAccountMappingSetting = async (req: Request, res: Response, next: NextFunction) => {
  const organizationId = req.session.user.organizationId;
  const glAccountMappingSettingId = Number(req.params.id);
  if (req.body.autoSync === true) {
    const glAccountMappingSetting = await GlAccountMappingSetting.findOne({
      where: {
        id: glAccountMappingSettingId,
      },
    });

    if (!glAccountMappingSetting) {
      return res.status(404).json({
        error: "Gl account mapping setting not found",
      });
    }

    // user wants to enable auto sync
    // check that all payroll categories are mapped to a GL account
    const glAccountMappings = await GlAccountMapping.findAll({
      where: {
        organizationId,
        integrationUserTokenId: glAccountMappingSetting.integrationUserTokenId,
      },
    });

    if (glAccountMappings.length !== PAYROLL_CATEGORY_TYPES.length) {
      return res.status(400).json({
        error: "Not all payroll categories are mapped to a GL account",
      });
    }
  }

  next();
};
