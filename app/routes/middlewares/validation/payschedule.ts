import Joi from "joi";
import { PAY_FREQUENCY } from "@/models/payschedule";
import { PayFrequency } from "@/types/checkDto";

export const createPayScheduleSchema = Joi.object({
  payFrequency: Joi.string().valid(...PAY_FREQUENCY),
  firstPayday: Joi.date()
    .iso()
    .custom((value) => value.toISOString().split("T")[0])
    .required(),
  secondPayday: Joi.date()
    .iso()
    .custom((value) => value.toISOString().split("T")[0])
    .when("payFrequency", {
      is: "semimonthly" as PayFrequency,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    }),
  firstPeriodEnd: Joi.date()
    .iso()
    .custom((value) => value.toISOString().split("T")[0])
    .required(),
}).required();
