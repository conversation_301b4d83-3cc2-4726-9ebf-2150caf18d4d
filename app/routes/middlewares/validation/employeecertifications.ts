import Joi from "joi";

export const createEmployeeCertificationSchema = Joi.object({
  title: Joi.string().required(),
  objectId: Joi.string().required(),
  userId: Joi.string().required(),
  number: Joi.string().optional().allow(""),
  issuingEntity: Joi.string().optional(),
  completionDate: Joi.date().optional(),
  expirationDate: Joi.date().optional(),
});

export const getEmployeeCertificationsSchema = Joi.object({
  userId: Joi.number().optional(),
  expiredOrExpiring: Joi.boolean().optional(),
});
