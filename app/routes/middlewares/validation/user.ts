import Joi from "joi";
import { phone } from "phone";
import dayjs from "dayjs";
import { WORKER_CLASSIFICATION, ROLE } from "@/models/user";

const dateAcceptanceFormat = /^\d{4}-\d{2}-\d{2}$/;
const timeAcceptanceFormat = "HH:mm:ss";

export const timeValidator = (
  value: string,
  helpers: Joi.CustomHelpers<string>,
  timeFormat: string = timeAcceptanceFormat
) => {
  if (value && !dayjs(value, timeFormat, true).isValid()) {
    return helpers.error("string.timeFormat");
  }

  return value;
};

export const phoneValidator = (value: string, helpers: Joi.CustomHelpers<string>) => {
  const result = phone(value, { validateMobilePrefix: false, strictDetection: false });
  if (!result.isValid) {
    return helpers.error("string.phoneNumber");
  }

  return value;
};

export const createUserSchema = Joi.object({
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  phoneNumber: Joi.string().custom(phoneValidator, "phone number validation").required(),
  role: Joi.string()
    .valid(...ROLE)
    .required(),
  position: Joi.string().allow("").allow(null),
  hourlyRate: Joi.number().min(0).allow(null),
  salary: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .allow(null)
    .allow(""),
  email: Joi.string().email().allow("").allow(null),
  employeeId: Joi.string().allow("").allow(null),
  fromPayroll: Joi.boolean().allow(null),
  shouldAddToPayroll: Joi.boolean().allow(null),
});

export const updateUserSchema = Joi.object({
  firstName: Joi.string(),
  lastName: Joi.string(),
  phoneNumber: Joi.string().custom(phoneValidator, "phone number validation"),
  role: Joi.string().valid(...ROLE),
  position: Joi.string(),
  hourlyRate: Joi.number().min(0),
  salary: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .allow(null)
    .allow(""),
  email: Joi.string().email().allow("").allow(null),
  dob: Joi.string().regex(dateAcceptanceFormat).allow("").allow(null),
  address1: Joi.string().allow("").allow(null),
  city: Joi.string().allow("").allow(null),
  state: Joi.string().allow("").allow(null),
  postalCode: Joi.string().allow("").allow(null),
  clockInReminderAt: Joi.string().allow("").allow(null).custom(timeValidator, "validate clock-in time"),
  clockOutReminderAt: Joi.string().allow("").allow(null).custom(timeValidator, "validate clock-out time"),
  isClockInReminderEnabled: Joi.boolean().allow(null),
  isClockOutReminderEnabled: Joi.boolean().allow(null),
  employeeId: Joi.string().allow("").allow(null),
  checkEmployeeId: Joi.string().allow("").allow(null),
  checkContractorId: Joi.string().allow("").allow(null),
  workerClassification: Joi.string()
    .valid(...WORKER_CLASSIFICATION)
    .allow("")
    .allow(null),
  isArchived: Joi.boolean().allow(null),
}).messages({
  "string.timeFormat": "{#label} must be in HH:mm:ss format",
  "string.phoneNumber": "The provided phone number is not valid",
});

export const sendOnboardingLinkSchema = Joi.object({
  onboardingLink: Joi.string().required(),
});
