import Joi from "joi";

export const userClassificationSchema = Joi.object({
  userIds: Joi.array().items(Joi.number()).required(),
  classificationId: Joi.number().required(),
  basePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  fringePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  startDate: Joi.date().required(),
  endDate: Joi.date().allow(null).optional(),
});

export const unassignUserClassificationSchema = Joi.object({
  userClassificationIds: Joi.array().items(Joi.number()).required(),
  endDate: Joi.date().optional().allow(null),
});

export const createUserClassificationSchema = Joi.object({
  userId: Joi.number().required(),
  classificationId: Joi.number().required(),
  startDate: Joi.date().required(),
  endDate: Joi.date().allow(null).optional(),
  basePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  fringePay: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  wageTableId: Joi.number().required(),
});

export const updateUserClassificationSchema = Joi.object({
  effectiveDate: Joi.date().required(),
  userClassifications: Joi.array()
    .items(
      Joi.object({
        userClassificationId: Joi.number().required(),
        basePay: Joi.string()
          .allow(null)
          .allow("")
          .pattern(/^\d+\.\d{2}$/)
          .optional(),
        fringePay: Joi.string()
          .allow(null)
          .allow("")
          .pattern(/^\d+\.\d{2}$/)
          .optional(),
      })
    )
    .required(),
});
