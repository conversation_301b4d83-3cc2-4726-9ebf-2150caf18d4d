import Joi from "joi";

export const createGlAccountMappingSchema = Joi.object({
  platform: Joi.string().required(),
  payrollCategory: Joi.string().required(),
  accountId: Joi.string().required(),
  platformId: Joi.string().optional().allow(""),
  accountName: Joi.string().optional().allow(""),
  accountType: Joi.string().optional().allow(""),
  accountCategory: Joi.string().optional().allow(""),
  accountCostClass: Joi.string().optional().allow(""),
  integrationUserTokenId: Joi.number().required(),
});

export const updateGlAccountMappingSchema = Joi.object({
  platform: Joi.string().required(),
  payrollCategory: Joi.string().required(),
  accountId: Joi.string().required(),
  platformId: Joi.string().optional().allow(""),
  accountName: Joi.string().optional().allow(""),
  accountType: Joi.string().optional().allow(""),
  accountCategory: Joi.string().optional().allow(""),
  accountCostClass: Joi.string().optional().allow(""),
  integrationUserTokenId: Joi.number().required(),
});

export const departmentMappingsSchema = Joi.object({
  mappings: Joi.object()
    .pattern(
      Joi.string(),
      Joi.object({
        accountId: Joi.string().required(),
        accountName: Joi.string().required(),
        accountType: Joi.string().required(),
        accountCategory: Joi.string().required(),
      })
    )
    .required(),
  platformId: Joi.string().required(),
  integrationUserTokenId: Joi.number().required(),
});
