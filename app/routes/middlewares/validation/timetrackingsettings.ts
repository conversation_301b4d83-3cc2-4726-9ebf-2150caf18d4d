import Joi from "joi";
import { TIMESHEET_ROUNDING_INTERVALS, TIMESHEET_ROUNDING_TYPES } from "@/models/timetrackingsettings";

export const updateTimeTrackingSettingsSchema = Joi.object({
  clockInReminderAt: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](\+[0-5][0-9])?$/)
    .allow(null)
    .optional()
    .messages({
      "string.pattern.base": "clockInReminderAt must be in HH:mm:ss+HH format",
    }),
  clockOutReminderAt: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .allow(null)
    .optional()
    .messages({
      "string.pattern.base": "clockOutReminderAt must be in HH:mm:ss format",
    }),
  allowWorkersToAddEditTime: Joi.boolean().optional(),
  allowForemanToCreateProjects: Joi.boolean().optional(),
  areBreaksPaid: Joi.boolean().optional(),
  areRealtimeBreaksEnabled: Joi.boolean().optional(),
  areRealtimeBreakRemindersEnabled: Joi.boolean().optional(),
  realtimeBreakStartReminderAt: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](\+[0-5][0-9])?$/)
    .allow(null)
    .optional()
    .messages({
      "string.pattern.base": "realtimeBreakStartReminderAt must be in HH:mm:ss+HH format",
    }),
  realtimeBreakEndReminderAfter: Joi.number().integer().min(0).allow(null).optional(),
  useDecimalHours: Joi.boolean().optional(),
  breakOptions: Joi.array().items(Joi.number().integer().min(0)).optional().messages({
    "number.base": "breakOptions must be an array of numbers",
    "array.base": "breakOptions must be an array of numbers",
  }),
  locationBreadcrumbingEnabled: Joi.boolean().optional(),
  isClockinClockoutPhotosEnabled: Joi.boolean().optional(),
  showWagesToWorkers: Joi.boolean().optional(),
  timesheetRoundingEnabled: Joi.boolean().optional(),
  timesheetRoundingType: Joi.string().valid(...TIMESHEET_ROUNDING_TYPES),
  timesheetRoundingInterval: Joi.string().valid(...TIMESHEET_ROUNDING_INTERVALS),
  isCostCodeRequired: Joi.boolean().optional(),
  isInjuryReportRequired: Joi.boolean().optional(),
  isDriveTimeEnabled: Joi.boolean().optional(),
  driveTimeRate: Joi.number().optional(),
});
