import Joi from "joi";
import { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";

export const integrationMappingSchema = Joi.object({
  objectType: Joi.string().valid(...Object.values(INTEGRATION_OBJECT_TYPE)),
  mappings: Joi.array().items(
    Joi.object({
      hammrId: Joi.number().required(),
      externalId: Joi.alternatives().try(Joi.number().allow(null), Joi.string().allow(null), Joi.allow(null)),
    })
  ),
});
