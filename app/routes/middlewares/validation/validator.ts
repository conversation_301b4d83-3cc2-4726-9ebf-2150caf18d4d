import { NextFunction, Request, Response } from "express";
import <PERSON><PERSON> from "joi";

const options = {
  errors: {
    wrap: {
      label: "",
    },
  },
};

const validateData = async (
  data: any,
  validationObject: Joi.Schema,
  errorMessage = "Something went wrong during validation"
) => {
  try {
    const result = await validationObject.validateAsync(data, options);

    return { success: true, result };
  } catch (error) {
    if (error instanceof Joi.ValidationError) {
      return {
        success: false,
        status: 400,
        error: error.details.map((err) => err.message).join(", "),
      };
    }
    console.error(error);

    return {
      success: false,
      status: 500,
      error: errorMessage,
    };
  }
};

export const validatorMw = (validationObject: Joi.Schema) =>
  async function (req: Request, res: Response, next: NextFunction) {
    const result = await validateData(req.body, validationObject);
    if (result.success) {
      return next();
    }

    return res.status(result.status).json({ error: result.error });
  };

export const validateQuery = (validationObject: Joi.Schema) =>
  async function (req: Request, res: Response, next: NextFunction) {
    const result = await validateData(
      req.query,
      validationObject,
      "Something went wrong during query parameter validation"
    );

    if (result.success) {
      // this will set the values with the correct types back in the request's query parameters
      // this is super handy when the UI passes the "true" string, and we get back the actual `true` boolean
      req.query = result.result;

      return next();
    }

    return res.status(result.status).json({ error: result.error });
  };
