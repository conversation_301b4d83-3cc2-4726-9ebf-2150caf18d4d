import Joi from "joi";

import { FRINGE_CATEGORY_TYPES } from "@/models/fringebenefit";

const fringeBenefitClassification = Joi.object().keys({
  amount: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  startDate: Joi.string().optional(),
  endDate: Joi.string().optional(),
  classificationId: Joi.number().required(),
});

export const createFringeBenefitSchema = Joi.object({
  name: Joi.string().required(),
  category: Joi.string()
    .valid(...FRINGE_CATEGORY_TYPES)
    .required(),
  wageTableId: Joi.number().required(),
  fringeBenefitClassifications: Joi.array().items(fringeBenefitClassification).optional(),
});

// only name changable
export const updateFringeBenefitSchema = Joi.object({
  name: Joi.string().required(),
});

export const createFringeBenefitClassificationSchema = Joi.object({
  amount: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
  startDate: Joi.number().required(),
  endDate: Joi.number().optional(),
  fringeBenefitId: Joi.number().required(),
  classificationId: Joi.number().required(),
});

export const postFringeBenefitClassificationSchema = Joi.object({
  fringeBenefitClassifications: Joi.array().items(createFringeBenefitClassificationSchema).required(),
});

export const updateFringeBenefitClassificationSchema = Joi.object({
  amount: Joi.string()
    .pattern(/^\d+\.\d{2}$/)
    .required(),
});
