import Joi from "joi";

export const payrollSchema = Joi.object({
  id: Joi.string().alphanum().max(100).required(),
});

export const createPayrollSchema = Joi.object({
  periodStart: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      "string.pattern.base": "periodStart with value {#value} fails to match YYYY-MM-DD format",
    }),
  periodEnd: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      "string.pattern.base": "periodEnd with value {#value} fails to match YYYY-MM-DD format",
    }),
  payday: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      "string.pattern.base": "payday with value {#value} fails to match YYYY-MM-DD format",
    }),
  // lots of other parameters that the Check api supports
  processingPeriod: Joi.string().optional(),
  type: Joi.string().optional(),
  payFrequency: Joi.string().optional(),
  fundingMethod: Joi.string().optional(),
  paySchedule: Joi.string().optional(),
  payrollItems: Joi.array().optional(),
  contractorPayments: Joi.array().optional(),
  offCycleOptions: Joi.object().optional(),
  metadata: Joi.object().optional(),
});

export const updatePayrollSchema = Joi.object({
  periodStart: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .optional()
    .allow(null)
    .messages({
      "string.pattern.base": "periodStart with value {#value} fails to match YYYY-MM-DD format",
    }),
  periodEnd: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .optional()
    .allow(null)
    .messages({
      "string.pattern.base": "periodEnd with value {#value} fails to match YYYY-MM-DD format",
    }),
  payday: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .optional()
    .allow(null)
    .messages({
      "string.pattern.base": "payday with value {#value} fails to match YYYY-MM-DD format",
    }),
  processingPeriod: Joi.string().optional().allow(null),
  type: Joi.string().optional().allow(null),
  payFrequency: Joi.string().optional().allow(null),
  fundingMethod: Joi.string().optional().allow(null),
  paySchedule: Joi.string().optional().allow(null),
  payrollItems: Joi.array().optional().allow(null),
  contractorPayments: Joi.array().optional().allow(null),
  offCycleOptions: Joi.object().optional().allow(null),
  metadata: Joi.object().optional().allow(null),
  earnings: Joi.array()
    .items(
      Joi.object({
        resourceId: Joi.string().required(),
        description: Joi.string().optional().allow(null),
        type: Joi.string().required(),
        amount: Joi.string().optional().allow(null).allow(""),
        hours: Joi.number().optional().allow(null),
      })
    )
    .optional()
    .allow(null),
  reimbursements: Joi.array()
    .items(
      Joi.object({
        resourceId: Joi.string().required(),
        amount: Joi.string().optional().allow(null).allow(""),
        description: Joi.string().optional().allow(null).allow(""),
        code: Joi.string().optional().allow(null).allow(""),
      })
    )
    .optional()
    .allow(null),
});
