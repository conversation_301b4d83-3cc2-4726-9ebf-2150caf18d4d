import { NextFunction, Request, Response } from "express";

export const validateCreateEarningRate = (req: Request, res: Response, next: NextFunction) => {
  const {
    name,
    amount,
    userId,
    period = "hourly",
    type = "reg",
    checkEarningRateId,
    startDate = new Date(),
    endDate = null,
    metadata = "{}",
  } = req.body;

  // all fields are required except period, endDate and metadata
  if (!name || !amount || !startDate || !userId) {
    return res.status(400).json({
      error: "Missing required fields",
    });
  }

  // can typically validate type as well here, but the values come as string from Postman and potentially as their intended type from client(s) - in order to do this, we'll need to convert the values to their intended type
  if (typeof name !== "string") {
    return res.status(400).json({
      error: "Name must be a string",
    });
  }

  const amountNumber = parseFloat(amount);
  if (isNaN(amountNumber)) {
    return res.status(400).json({
      error: "Invalid amount",
    });
  }

  if (checkEarningRateId !== null && checkEarningRateId !== undefined && typeof checkEarningRateId !== "string") {
    return res.status(400).json({
      error: "Check Earning Rate Id must be a string",
    });
  }

  // potentially enforce type values here such as: reg, ot, etc
  if (type !== null && type !== undefined && typeof type !== "string") {
    return res.status(400).json({
      error: "Type must be a string",
    });
  }

  if (userId !== null && userId !== undefined && isNaN(parseInt(userId))) {
    return res.status(400).json({
      error: "Invalid userId",
    });
  }

  if (startDate !== null && startDate !== undefined && isNaN(new Date(+startDate).valueOf())) {
    return res.status(400).json({
      error: "Invalid startDate",
    });
  }

  if (endDate !== null && endDate !== undefined && isNaN(new Date(+endDate).valueOf())) {
    return res.status(400).json({
      error: "Invalid endDate",
    });
  }

  if (period !== null && period !== undefined && typeof period !== "string") {
    return res.status(400).json({
      error: "Period must be a string",
    });
  }

  const parsedMetadata = JSON.parse(metadata);
  if (metadata !== null && typeof parsedMetadata !== "object") {
    return res.status(400).json({
      error: "Metadata must be an object",
    });
  }

  // if all fields are valid, proceed to the next middleware
  next();
};

export const validateGetAllEarningRates = (req: Request, res: Response, next: NextFunction) => {
  const { userId } = req.query;

  if (userId !== null && userId !== undefined && isNaN(parseInt(userId as string))) {
    return res.status(400).json({
      error: "Invalid userId",
    });
  }

  next();
};
