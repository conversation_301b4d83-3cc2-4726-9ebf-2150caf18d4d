import Joi from "joi";
import { PREVAILING_WAGE_CATEGORY } from "@/models/project";

export const createProjectSchema = Joi.object({
  name: Joi.string().required(),
  projectNumber: Joi.string().allow(null).allow("").optional(),
  customerName: Joi.string().allow(null).allow("").optional(),
  address: Joi.string().allow(null).allow("").optional(),
  wageTableId: Joi.number().allow(null).allow("").optional(),
  overtimeSettingsId: Joi.number().allow(null).optional(),
  foremanId: Joi.number().allow(null).allow("").optional(),
  startDate: Joi.date().allow(null).optional(),
  lat: Joi.alternatives()
    .try(Joi.string(), Joi.number())
    .when("isGeofenced", {
      is: true,
      then: Joi.required(),
      otherwise: Joi.allow(null).allow("").optional(),
    }),
  long: Joi.alternatives()
    .try(Joi.string(), Joi.number())
    .when("isGeofenced", {
      is: true,
      then: Joi.required(),
      otherwise: Joi.allow(null).allow("").optional(),
    }),
  isGeofenced: Joi.boolean().required(),
  geofenceRadius: Joi.number().optional().allow(null),
  isPrevailingWage: Joi.boolean().optional(),
  prevailingWageIsSubcontractor: Joi.boolean().when("isPrevailingWage", {
    is: true,
    then: Joi.required(),
    otherwise: Joi.allow(null).allow("").optional(),
  }),
  prevailingWageDirProjectId: Joi.string().allow(null).allow("").optional(),
  prevailingWageAwardingBody: Joi.string().allow(null).allow("").optional(),
  prevailingWagePrimeContractorName: Joi.string().allow(null).allow("").optional(),
  prevailingWagePrimeContractorAddress: Joi.string().allow(null).allow("").optional(),
  prevailingWageBidAwardDate: Joi.date().allow(null).allow("").optional(),
  prevailingWageCategory: Joi.string()
    .valid(...PREVAILING_WAGE_CATEGORY)
    .when("isPrevailingWage", {
      is: true,
      then: Joi.required(),
      otherwise: Joi.allow(null).allow("").optional(),
    }),
  prevailingWageState: Joi.string().when("prevailingWageCategory", {
    is: "STATE",
    then: Joi.required(),
    otherwise: Joi.allow(null).allow("").optional(),
  }),
  hoursBudget: Joi.number().optional().allow(null),
  costBudget: Joi.number().optional().allow(null),
  notes: Joi.string().optional().allow(null).max(200),
});

export const updateProjectSchema = Joi.object({
  name: Joi.string().optional(),
  projectNumber: Joi.string().allow(null).allow("").optional(),
  customerName: Joi.string().allow(null).allow("").optional(),
  address: Joi.string().allow(null).allow("").optional(),
  wageTableId: Joi.number().allow(null).allow("").optional(),
  overtimeSettingsId: Joi.number().allow(null).optional(),
  foremanId: Joi.number().allow(null).allow("").optional(),
  startDate: Joi.date().allow(null).allow("").optional(),
  lat: Joi.alternatives().try(Joi.string(), Joi.number()).allow(null).allow("").optional(),
  long: Joi.alternatives().try(Joi.string(), Joi.number()).allow(null).allow("").optional(),
  isGeofenced: Joi.boolean().optional(),
  isPrevailingWage: Joi.boolean().optional(),
  geofenceRadius: Joi.number().allow(null).allow("").optional(),
  prevailingWageDirProjectId: Joi.string().allow(null).allow("").optional(),
  prevailingWageAwardingBody: Joi.string().allow(null).allow("").optional(),
  prevailingWagePrimeContractorName: Joi.string().allow(null).allow("").optional(),
  prevailingWagePrimeContractorAddress: Joi.string().allow(null).allow("").optional(),
  prevailingWageBidAwardDate: Joi.date().allow(null).allow("").optional(),
  prevailingWageCategory: Joi.string()
    .valid(...PREVAILING_WAGE_CATEGORY)
    .optional(),
  prevailingWageState: Joi.string().allow(null).allow("").optional(),
  isArchived: Joi.boolean().optional(),
  prevailingWageIsSubcontractor: Joi.boolean().when("isPrevailingWage", {
    is: true,
    then: Joi.required(),
    otherwise: Joi.allow(null).allow("").optional(),
  }),
  hoursBudget: Joi.number().optional().allow(null),
  costBudget: Joi.number().optional().allow(null),
  notes: Joi.string().optional().allow(null).allow("").max(2000),
});
