import parseurl from "parseurl";
import { NextFunction, Request, Response } from "express";

import { UsersService } from "@/services/users";
import { validateRequest } from "@/util/validationHelpers";

import { CheckPathnameStrategy } from "@/lib/strategies/check-pathname-strategy";
import { UserPathnameStrategy } from "@/lib/strategies/check-user-pathname";
import { OrganizationPathnameStrategy } from "@/lib/strategies/check-organization-pathname";
import { basePath } from "@/util/env";

const API_KEY_ALLOWED_ENDPOINTS = [{ path: `${basePath}/timesheets`, methods: ["GET"] }];

export const userIsAdminOrForeman = (req: Request, res: Response, next: NextFunction) => {
  if (req.session.user.role !== "ADMIN" && req.session.user.role !== "FOREMAN") {
    return res.status(403).json({
      error: "You are not allowed to access this resource",
    });
  }

  next();
};

export const userIsAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (req.session.user.role !== "ADMIN") {
    return res.status(403).json({
      error: "You are not allowed to access this resource",
    });
  }

  next();
};

export const userIsPartofOrg = (req: Request, res: Response, next: NextFunction) => {
  if (req.session.user.organizationId !== parseInt(req.params.id)) {
    return res.status(403).json({
      error: "You are not allowed to access this resource",
    });
  }

  next();
};

export const refreshUserRoleIfNeeded = async (req: Request, res: Response, next: NextFunction) => {
  const usersService = new UsersService();

  const user = await usersService.findOne({
    where: {
      id: req.session.user.id,
      organizationId: req.session.user.organizationId,
    },
    simple: true,
    forceRawResponse: true,
  });

  if (user?.role !== req.session.user.role) {
    req.session.user.role = user?.role;
  }

  next();
};

export const userAccessIsAllowed = async (req: Request, res: Response, next: NextFunction) => {
  const org = req.organization;

  // If organization is not active, return 401
  if (org?.isChurned) {
    return res.status(401).json({
      error: "Organization is not active",
    });
  }

  // If no session, check if API key authentication is allowed for this endpoint and method
  const isApiKeyAllowed = API_KEY_ALLOWED_ENDPOINTS.some(
    (endpoint) => req.path.startsWith(endpoint.path) && endpoint.methods.includes(req.method)
  );

  if (req.apiKeyType === "organization") {
    if (isApiKeyAllowed) {
      return next();
    } else {
      return res.status(401).json({
        error: "Not authorized to access this resource",
      });
    }
  }

  // For session authentication, continue with user checks
  const usersService = new UsersService();

  const currentUserData = await usersService.findOne({
    where: {
      id: req.session.user.id,
      organizationId: req.session.user.organizationId,
    },
    simple: true,
    forceRawResponse: true,
  });

  // another check to ensure there is currentUserData
  if (!currentUserData) {
    return res.status(401).json({
      error: "Session user not found",
    });
  }

  // req.session.user is unreliable as this could potentially be inaccurate when the isArchived flag is updated
  if (currentUserData?.isArchived) {
    return res.status(401).json({
      error: "User is not active",
    });
  }

  next();
};

const checkPathnameStrategies: Record<string, CheckPathnameStrategy> = {
  user: new UserPathnameStrategy(),
  organization: new OrganizationPathnameStrategy(),
};

export const isAuthorizedForCheckRequest = async (req: Request, res: Response, next: NextFunction) => {
  // should mimic isAuthorizedForRequest in `web` closely
  const queryParams = req.query;
  const method = req.method;
  const body = req.body;
  let pathName = parseurl(req).pathname;

  try {
    // get user data - requirement in validateRequest
    const usersService = new UsersService();
    const user = await usersService.findOne({
      where: {
        id: req.session.user.id,
        organizationId: req.session.user.organizationId,
      },
      simple: true,
      forceRawResponse: true,
    });

    for (const key in checkPathnameStrategies) {
      if (pathName.includes(key)) {
        const strategy = checkPathnameStrategies[key];
        const modifiedPathName = await strategy.execute(req, user, method, pathName, req.body);

        if (modifiedPathName === null) {
          next();

          return;
        }

        await strategy.applySideEffects(req, user); // Apply side effects

        pathName = modifiedPathName; // Update pathName if modified
        break;
      }
    }

    const isAuthorized = await validateRequest(req.session.user.role, user, pathName, method, queryParams, body);

    if (!isAuthorized) {
      return res.status(403).json({
        error: "Not authorized",
      });
    }
  } catch (err) {
    console.error("Error obtaining validation data:", err);

    next(err);
  }

  next();
};
