import { Request, Response, NextFunction } from "express";

import { isAuthorizedForCheckRequest } from "@/middlewares/authorization";

// Check specific routing middleware (should validaet or not)
export const conditionalCheckReqMiddlware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // if check payroll customer (org has checkCompanyId) - other check validation middleware can check other requirements
    if (req.organization.checkCompanyId && req.organization.isPayrollEnabled) {
      isAuthorizedForCheckRequest(req, res, next);
    } else {
      // skip to next handler
      next();
    }
  } catch (err) {
    console.error("Error passing check request middleware:", err);

    next(err);
  }

  return;
};
