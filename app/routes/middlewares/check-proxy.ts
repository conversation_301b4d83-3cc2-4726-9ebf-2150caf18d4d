/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import { Router } from "express";
import { createProxyMiddleware } from "http-proxy-middleware";
import { Request, Response, NextFunction } from "express";
import { CheckService } from "@/services/check";

import { isAuthorizedForCheckRequest } from "@/middlewares/authorization";

// TODO -> this is not the cleanest way of handling this request with the Check Proxy, in the future we could use a Controller method to handle this
const handlePdfDownload = async (req: Request, res: Response, next: NextFunction) => {
  // Check if this is a paper_checks.pdf request
  if (req.path.includes("paper_checks.pdf")) {
    try {
      const checkService = new CheckService();

      // Use the full URL if it's from the allowed origins
      const fullUrl = `${process.env.CHECK_RENDER_URL}${req.url}`;

      const response = await checkService.downloadFile(fullUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get("content-type");
      if (!contentType?.includes("application/pdf")) {
        throw new Error("Invalid content type received");
      }

      const pdfBuffer = await response.arrayBuffer();

      // Set appropriate headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", 'inline; filename="paper_checks.pdf"');

      // Send the PDF data
      return res.send(Buffer.from(pdfBuffer));
    } catch (error) {
      console.error("Error downloading PDF:", error);

      const errorMessage =
        error.message === "URL origin not in allowlist" ? "Invalid PDF URL" : "Failed to download PDF";

      next(error);
    }
  }
  next();
};

export const checkProxyMiddleware = createProxyMiddleware({
  target: `${process.env.CHECK_API_URL}`,
  changeOrigin: true,
  pathRewrite: function (path, _req) {
    return path.replace("/api/check-proxy", "");
  },
  onProxyReq: (proxyReq, req, _res) => {
    proxyReq.setHeader("Authorization", `Bearer ${process.env.CHECK_API_KEY}`);
    if (Object.keys(req.body).length > 0) {
      const bodyData = JSON.stringify(req.body);
      const contentType = req.header("Content-Type");
      if (contentType) proxyReq.setHeader("Content-Type", contentType);
      proxyReq.setHeader("Content-Length", Buffer.byteLength(bodyData));
      proxyReq.write(bodyData);
    }
  },
});

export const checkProxyRouter = Router();

// Add the PDF handler before the proxy middleware
checkProxyRouter.use("", [isAuthorizedForCheckRequest, handlePdfDownload], checkProxyMiddleware);
