import { Model, ModelStatic } from "sequelize";
import { NextFunction, Request, Response } from "express";

export default function modelExistsAndIsPartOfOrg(model: ModelStatic<Model>, parameterName = "id", message?: string) {
  return async function (request: Request, response: Response, next: NextFunction) {
    const id = request.params[parameterName];

    const instance = (await model.findOne({ where: { id } })) as Model & { organizationId: number };

    if (!instance) {
      return response.status(404).json({
        message: message ?? `${model.name} not found`,
      });
    }

    if (instance.organizationId !== request.organization.id) {
      return response.status(403).json({
        message: `Not authorized`,
      });
    }

    return next();
  };
}
