import { Request, Response, NextFunction } from "express";

import { UsersService } from "@/services/users";
import { EarningRateService } from "@/services/earningrate";

const usersService = new UsersService();
const earningRateService = new EarningRateService();

// This middleware checks if the earning rate belongs to the organization of the user
export const earningRateBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.session.user.id; // Assuming user context is attached to req.user

  if (req?.params?.id) {
    const earningRateId = req.params.id; // Or however you're getting this
    const earningRate = await earningRateService.findOne({
      where: {
        id: earningRateId,
      },
    });

    if (!earningRate) {
      return res.status(404).send("Earning rate not found");
    }

    const user = await usersService.findOne({
      where: {
        id: userId,
        organizationId: req.session.user.organizationId,
      },
      simple: true,
      forceRawResponse: true,
    });

    if (earningRate.organizationId !== user.organizationId) {
      return res.status(403).send("Not authorized to access this earning rate");
    }
  }

  next();
};
