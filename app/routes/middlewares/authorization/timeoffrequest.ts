import { NextFunction, Request, Response } from "express";
import TimeOffRequest from "@/models/timeoffrequest";
import TimeOffPolicy from "@/models/timeoffpolicy";

export const timeOffRequestBelongsToOrg = async (request: Request, response: Response, next: NextFunction) => {
  const organizationId = request.session.user.organizationId;
  const timeOffRequestId = request?.params?.id;

  if (timeOffRequestId) {
    const instance = await TimeOffRequest.findOne({
      where: {
        id: timeOffRequestId,
      },
      include: [{ model: TimeOffPolicy, as: "timeOffPolicy" }],
    });

    if (!instance) {
      return response.status(404).json({ message: "Time-off request not found" });
    }

    if (instance.timeOffPolicy.organizationId !== organizationId) {
      return response.status(403).json({ message: "Not authorized to access this time-off request" });
    }
  }

  next();
};
