import { NextFunction, Request, Response } from "express";

import { User } from "@/models";

// this should eventually be more generalized - to stop scope creep - this should be it for now
// the idea is this should eventually replace fragmented auth checks like userIsPartofOrg and validateOrganizationId when this is made more general
// middleware check if the targeted resource belongs to the user's organization
export const checkResourceBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  // targeted userId is passed in body. check if the user belongs to the organization
  const parsedUserId = parseInt(req.body.userId, 10);
  const orgId = req.session.user.organizationId;

  const targetedUser: User = await User.findOne({
    where: {
      id: parsedUserId,
      organizationId: orgId,
    },
  });

  if (!targetedUser) {
    return res.status(404).send("User not found");
  }

  if (targetedUser.organizationId !== orgId) {
    return res.status(403).send("Not authorized to access this user");
  }

  // attach the targeted user to the request object if found and authorized
  req.targetedUser = targetedUser;

  next();
};

export const resourceFetchBelongsToUserOrAdminOrForeman = async (req: Request, res: Response, next: NextFunction) => {
  if (req.session.user && (req.session.user.role === "ADMIN" || req.session.user.role === "FOREMAN")) {
    return next();
  }

  if (req.apiKeyType === "organization") {
    return next();
  }

  const userIds = (req.query?.userId as string)?.split(",").map((id) => parseInt(id, 10)) || [];
  if (userIds && userIds.includes(req.session.user.id) && userIds.length === 1) {
    return next();
  }

  return res.status(403).json({
    error: "Not authorized to access this user",
  });
};
