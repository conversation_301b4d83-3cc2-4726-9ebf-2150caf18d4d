import { NextFunction, Request, Response } from "express";

import { TimesheetsService } from "@/services/timesheets";
import { ExtendedTimesheet } from "@/models/timesheet";

const timesheetsService = new TimesheetsService();

// sequence this check after timesheetBelongsToOrg
// method primarily used for PATCH timesheets/:id route - any updates to the timesheet
export const allowedToUpdateTimesheet = async (req: Request, res: Response, next: NextFunction) => {
  // various scenario checks based on status of timesheet and role
  const timesheet = req.targetedTimesheet;
  const currentTimesheetStatus = timesheet.status;

  if (
    req.session.user.role !== "ADMIN" &&
    req.session.user.role !== "FOREMAN" &&
    req.session.user.id !== timesheet.workerId
  ) {
    return res.status(403).json({
      error: "Not allowed to access this resource",
    });
  }

  // first check if status = PAID, if so, no updates allowed
  if (currentTimesheetStatus === "PAID") {
    return res.status(403).json({
      error: "Can't edit a timesheet once it's status is paid",
    });
  }
  // check if worker is allowed to edit their own timesheet - org level setting
  if (!req.organization.timeTrackingSettings.allowWorkersToAddEditTime && req.session.user.role === "WORKER") {
    // It's tricky to know when a user is trying to edit a timesheet vs when they are just trying to clock out
    // When a user is clocking out, that could include fields such as clockOut, description, breakDuration, clockOutLocation
    // One way to allow clock out but not allow editing is to check if the timesheet is a current timesheet
    // In that case, still don't allow updating the clockIn field
    // since that could be an instance of the user trying to edit a current timesheet instead of clocking out

    if (timesheet.clockOut !== null || Object.keys(req.body).includes("clockIn")) {
      return res.status(403).json({
        error: "Editing timesheets is disabled. Ask a supervisor or admin to edit it for you.",
      });
    }
  }

  // if we made it here, the user is allowed to update the timesheet
  next();
};

export const allowedToCreateTimesheet = async (req: Request, res: Response, next: NextFunction) => {
  const { userId } = req.body;
  const userIdInt = parseInt(userId);

  // workers can only create timesheets for themselves
  if (req.session.user.role === "WORKER" && req.session.user.id === userIdInt) {
    return next();
  }

  // only foreman or admin can create timesheets for other users
  if (req.session.user.role === "FOREMAN" || req.session.user.role === "ADMIN") {
    return next();
  }

  // reject all other cases
  return res.status(403).send("Not allowed to access this resource");
};

export const timesheetBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const timesheetId = req.body.timesheetId ? parseInt(req.body.timesheetId, 10) : parseInt(req.params.timesheetId, 10);

  const timesheet: ExtendedTimesheet | null = await timesheetsService.findOne({
    where: {
      id: timesheetId,
      organizationId: userOrgId,
    },
  });

  if (!timesheet || timesheet.organizationId !== userOrgId) {
    return res.status(404).send("Timesheet not found");
  }

  // attach targeted timesheet to req object if found and authorized
  req.targetedTimesheet = timesheet;

  next();
};
