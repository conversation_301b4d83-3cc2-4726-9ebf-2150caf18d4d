import { Request, Response, NextFunction } from "express";
import { doesResourceIdBelongToCompany } from "@/util/validationHelpers";
import { getRootFromPathName } from "@/util/path";

export const checkResourceMapping: Record<string, string> = {
  payrolls: "payrolls",
};

// we use this directly for payroll because hammr doesn't have a payrolls resource
export const checkPayrollResourceBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  // we can leave a TO DO to eventually extend this like `validateRequest` in `validationHelpers` but in the interest of time, we'll just apply this narrowly in scope to payrolls and make it more general later
  if (req?.organization?.isPayrollEnabled && req?.organization?.checkCompanyId) {
    // validate that the resourceId belongs to the company
    const resource = getRootFromPathName(req.path);
    const resourceId = req.params.id || req.body.resourceId;
    const companyId = req.organization.checkCompanyId;
    const checkResourceBelongsToCompany = await doesResourceIdBelongToCompany(
      `${checkResourceMapping[resource]}/${resourceId}`,
      companyId
    );

    if (!checkResourceBelongsToCompany) {
      return res.status(403).json({ message: "Resource not found" });
    }
  }

  next();
};
