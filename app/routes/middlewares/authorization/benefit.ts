import { Request, Response, NextFunction } from "express";

import { CompanyBenefitsService } from "@/services/companybenefits";
import { EmployeeBenefitsService } from "@/services/employeebenefits";

const companyBenefitsService = new CompanyBenefitsService();
const employeeBenefitsService = new EmployeeBenefitsService();

// This middleware checks if the earning rate belongs to the organization of the user
export const companyBenefitBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const companyBenefitId = req?.body?.companyBenefitId ? req.body.companyBenefitId : req?.params?.id;

  if ((req?.method === "POST" || req?.method === "PATCH") && companyBenefitId) {
    const companyBenefit = await companyBenefitsService.findOne({
      where: {
        id: companyBenefitId,
      },
    });

    if (!companyBenefit) {
      return res.status(404).send("Company benefit not found");
    }

    // we want to seperate this and use this to be more secure. however, it could be confusing when debugging, so it's possible we adjust this to 403 when developing
    // proxy for does not have access
    if (companyBenefit.organizationId !== userOrgId) {
      return res.status(404).send("Company benefit not found");
    }
  }

  next();
};

export const employeeBenefitBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const employeeBenefitId = req?.body?.employeeBenefitId ? req.body.employeeBenefitId : req?.params?.id;

  if (req?.method === "PATCH" && employeeBenefitId) {
    const employeeBenefit = await employeeBenefitsService.findOne({
      where: {
        id: employeeBenefitId,
      },
    });

    if (!employeeBenefit) {
      return res.status(404).send("Employee benefit not found");
    }

    if (employeeBenefit.organizationId !== userOrgId) {
      return res.status(403).send("Not authorized to access this employee benefit");
    }
  }

  next();
};
