import User from "@/models/user";
import { Request, Response, NextFunction } from "express";

export const usersBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const userIds = req.body.userIds;

  // validate that all userIds belong to the organization
  const users = await User.findAll({
    where: {
      id: userIds,
    },
  });

  // if any userId doesn't map to a user, return 404
  if (users.length !== userIds.length) {
    return res.status(404).send("User not found");
  }

  // if any user doesn't belong to the organization, return 404
  if (users.some((user) => user.organizationId !== userOrgId)) {
    return res.status(404).send("User not found");
  }

  next();
};
