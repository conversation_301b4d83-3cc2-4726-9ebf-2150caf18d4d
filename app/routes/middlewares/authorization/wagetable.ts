import { Request, Response, NextFunction } from "express";
import { WageTableService } from "@/services/wagetable";

const wageTableService = new WageTableService();

export const wageTableBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const wageTableId = req.body.wageTableId ? parseInt(req.body.wageTableId, 10) : parseInt(req.params.id, 10);
  const wageTable = await wageTableService.findOne({
    where: {
      id: wageTableId,
    },
  });

  if (!wageTable || wageTable.organizationId !== userOrgId) {
    return res.status(404).send("Wage table not found");
  }

  next();
};
