import { Request, Response, NextFunction } from "express";
import Classification from "@/models/classification";
import UserClassification from "@/models/userclassification";
import { Op } from "sequelize";

export const classificationBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const classificationId = req.body.classificationId
    ? parseInt(req.body.classificationId, 10)
    : parseInt(req.params.id, 10);

  const classification = await Classification.findOne({
    where: {
      id: classificationId,
    },
  });

  if (!classification || classification.organizationId !== userOrgId) {
    return res.status(404).send("Classification not found");
  }

  // attach targeted classification to req object if found and authorized
  req.targetedClassification = classification;

  next();
};

// applies to checking for bulk userClassificationids
export const userClassificationsBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;

  const userClassifications = await UserClassification.findAll({
    where: {
      id: { [Op.in]: req.body.userClassificationIds },
      organizationId: userOrgId,
    },
  });

  if (userClassifications.length !== req.body.userClassificationIds.length) {
    return res.status(404).send("User classifications not found");
  }

  next();
};
