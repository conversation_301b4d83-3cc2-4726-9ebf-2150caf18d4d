import { Request, Response, NextFunction } from "express";
import { ProjectsService } from "@/services/projects";

const projectService = new ProjectsService();

export const projectBelongsToOrg = async (req: Request, res: Response, next: NextFunction) => {
  const userOrgId = req.session.user.organizationId;
  const projectId = parseInt(req.params.id, 10);
  const project = await projectService.findOne({
    where: {
      id: projectId,
    },
  });

  if (!project) {
    return res.status(404).send("Project not found");
  }

  if (project.organizationId !== userOrgId) {
    return res.status(403).send("Not authorized to access this project");
  }

  next();
};
