import { Request } from "express";
import { Model, ModelStatic } from "sequelize";
import { CustomHelpers } from "joi";

export function isMobileRequest(request: Request): boolean {
  if (!request || !request.headers) return false;
  const platform = request.headers["platform"];

  return platform && (platform === "ios" || platform === "android");
}

function parseVersion(versionString: string) {
  const [major, minor] = versionString.split(".").map(Number);

  return { major, minor };
}

function isVersionGreaterOrEqual(version1: any, version2: any) {
  if (version1.major > version2.major) return true;
  if (version1.major < version2.major) return false;

  return version1.minor >= version2.minor;
}

export function appVersionIsAboveOrEqual(minimumAppVersion: string, request: Request): boolean {
  const appVersion = request.headers["app-version"] as string;
  if (!appVersion) return false;

  const currentVersion = parseVersion(appVersion);
  const minimumVersion = parseVersion(minimumAppVersion);

  return isVersionGreaterOrEqual(currentVersion, minimumVersion);
}

export function isModel(model: ModelStatic<Model>) {
  return async function isModel(value: number | number[], helpers: CustomHelpers) {
    if (!Array.isArray(value)) {
      value = [value];
    }

    value = value.filter(Boolean);
    const items = await model.findAll({
      where: {
        id: value,
      },
    });

    if (items.length !== value.length) {
      return helpers.error("any.invalid");
    }

    return value;
  };
}
