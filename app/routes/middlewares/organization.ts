// middleware to make organization availabe in the req object for easier access downstream
import { Request, Response, NextFunction } from "express";
import { OrganizationsService } from "@/services/organizations";

const organizationsService = new OrganizationsService();

export const organizationMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (req.apiKeyOrganizationId) {
      const organization = await organizationsService.findOne({
        where: {
          id: req.apiKeyOrganizationId,
        },
        simple: true,
        forceRawResponse: true,
      });

      req.organization = organization;

      return next();
    }

    // For session authentication
    const user = req.session.user;

    // fetch organization data from user's orgId
    const organization = await organizationsService.findOne({
      where: {
        id: user.organizationId,
      },
      simple: true,
      forceRawResponse: true,
    });

    // Attach the organization data to the req object
    req.organization = organization;

    next();
  } catch (error) {
    console.error("Error fetching organization data:", error);
    next(error);
  }
};
