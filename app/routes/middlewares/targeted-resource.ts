import { Request, Response, NextFunction } from "express";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

// we can abstract and generalize this later - this is improtant, but we can leave this for now
import { CompanyBenefit, User } from "@/models";
import { CompanyBenefitsService } from "@/services/companybenefits";
import { EmployeeBenefitsService } from "@/services/employeebenefits";
import { ProjectsService } from "@/services/projects";
import { TimesheetsService } from "@/services/timesheets";
import { CheckService } from "@/services/check";

const companyBenefitsService = new CompanyBenefitsService();
const employeeBenefitsService = new EmployeeBenefitsService();
const projectsService = new ProjectsService();
const timesheetsService = new TimesheetsService();
const checkService = new CheckService();

// extends dayjs with utc plugin
dayjs.extend(utc);
dayjs.extend(timezone);

export const fetchTargetedCompanyBenefit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let companyBenefitId;
    if (req.method === "GET") {
      companyBenefitId = parseFloat(req.params?.id);
    }

    if (req.method === "POST" || req.method === "PATCH") {
      companyBenefitId = parseFloat(req.body?.companyBenefitId);
    }

    const resource = await companyBenefitsService.findOne({
      where: {
        id: companyBenefitId,
      },
    });

    if (!resource) {
      return res.status(404).json({
        error: "Company Benefit not found",
      });
    }

    req.targetedCompanyBenefit = resource;
    next();
  } catch (error) {
    console.error("Error fetching resource:", error);

    next(error);
  }
};

export const fetchTargetedEmployeeBenefit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let employeeBenefitId;
    if (req.method === "GET" || req.method === "PATCH") {
      employeeBenefitId = parseFloat(req.params?.id);
    }

    if (req.method === "POST") {
      employeeBenefitId = parseFloat(req.body?.employeeBenefitId);
    }

    const resource = await employeeBenefitsService.findOne({
      where: {
        id: employeeBenefitId,
      },
      include: [
        {
          model: CompanyBenefit,
          as: "companyBenefit",
        },
        {
          model: User,
        },
      ],
    });

    if (!resource) {
      return res.status(404).json({
        error: "Employee Benefit not found",
      });
    }

    req.targetedEmployeeBenefit = resource;
    next();
  } catch (error) {
    console.error("Error fetching resource:", error);

    next(error);
  }
};

export const fetchTargetedProject = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let projectId;
    if (req.method === "GET") {
      projectId = parseFloat(req.params?.id);
    }

    if (req.method === "POST" || req.method === "PATCH") {
      projectId = parseFloat(req.body?.projectId);
    }

    const resource = await projectsService.findOne({
      where: {
        id: projectId,
      },
    });

    if (!resource) {
      return res.status(404).json({
        error: "Project not found",
      });
    }

    req.targetedProject = resource;
    next();
  } catch (error) {
    console.error("Error fetching resource:", error);

    next(error);
  }
};

export const fetchTargetedPayroll = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let payrollId; // payrollId is a string and not a number
    if (req.method === "GET") {
      payrollId = req.params?.id;
    }

    if (req.method === "POST" || req.method === "PATCH") {
      payrollId = req.body?.payrollId;
    }

    const endpoint = `/payrolls/${payrollId}`;
    let payroll = await checkService.get(endpoint);

    // If payroll status is draft, call the preview endpoint
    if (payroll.status === "draft") {
      const previewEndpoint = `/payrolls/${payrollId}/preview`;
      payroll = await checkService.get(previewEndpoint);
    }

    req.targetedPayroll = payroll;
    next();
  } catch (error) {
    console.error("Error fetching payroll:", error);

    next(error);
  }
};

export const fetchTargetedTimesheetForProjectStart = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let projectId;

    const timezone = req.organization?.timezone || "America/Los_Angeles";
    const payroll = req.targetedPayroll;

    if (req.method === "GET") {
      projectId = parseFloat(req.params?.id);
    }

    if (req.method === "POST" || req.method === "PATCH") {
      projectId = parseFloat(req.body?.projectId);
    }

    // this is a returned date or null
    const firstClockInDate = await timesheetsService.getFirstClockInDate(projectId);

    if (!firstClockInDate) {
      return res.status(404).json({
        error: "No timesheets found for this project.",
      });
    }

    // some additional validation - firstClockInDate can't be less than payrollWeekEnding
    if (firstClockInDate && req.method === "POST") {
      const payrollWeekEnding = dayjs.tz(payroll.period_end, timezone).endOf("day");

      const firstClockInDateToLocal = dayjs(firstClockInDate).tz(timezone);

      if (payrollWeekEnding.isBefore(firstClockInDateToLocal)) {
        return res.status(400).json({
          error: "Payroll Week Ending can't be before work started on this project",
        });
      }
    }

    req.targetedProjectStartDate = firstClockInDate;
    next();
  } catch (err) {
    console.error("Error fetching resource:", err);

    next(err);
  }
};
