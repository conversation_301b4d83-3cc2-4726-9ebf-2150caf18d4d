const Session = require("express-session");
const SQLiteStore = require("connect-sqlite3")(Session);
const { ENVIRONMENT } = process.env;
import APIKey from "@/models/apiKey";

const storeOptions = {
  dir: "/data",
  db: process.env.NODE_ENV === "test" ? ":memory:" : undefined,
};
// store sqlite file in /data folder in production since application folder is ephemeral on Render
export const sessionStore =
  ENVIRONMENT === "test"
    ? undefined
    : ENVIRONMENT === "DEVELOPMENT"
    ? new SQLiteStore()
    : new SQLiteStore(storeOptions);

const {
  SESSION_SECRET,
  API_PUBLIC_TOKEN,
  MIGRATION_API_SECRET_TOKEN,
  MINIMUM_APP_VERSION_IOS,
  MINIMUM_APP_VERSION_ANDROID,
  DOMAIN,
} = process.env;

export const expressSession = Session({
  key: "user_sid",
  store: sessionStore,
  secret: SESSION_SECRET,
  httpOnly: true,
  saveUninitialized: true,
  resave: false,
  cookie: {
    domain: DOMAIN,
    secure: false, //true breaks production for some unknown reason
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
  },
});

export const userIsAuthenticated = async (request: any, response: any, next: any) => {
  // Check for session authentication first
  if (request.session.user && request.session.user.isAuthenticated) {
    return next();
  }

  if (request.apiKeyType === "organization" && request.apiKeyOrganizationId) {
    return next();
  }

  return response.status(401).json({
    error: "Authorization not valid",
  });
};

export const apiIsAuthenticated = async (request: any, response: any, next: any) => {
  // Skip checks for CheckHQ webhooks
  if (request.path.includes("/webhooks/check-webhook")) {
    return next();
  }

  if (!request.headers["x-api-key"]) {
    return response.status(400).json({
      error: "API key is missing",
    });
  }

  const apiKey = request.headers["x-api-key"];

  // First check if it matches the public API token
  if (apiKey === API_PUBLIC_TOKEN) {
    request.apiKeyType = "public";

    return next();
  }

  // If not the public token, check if it's a valid organization API key
  try {
    const apiKeyRecord = await APIKey.findOne({
      where: { apiKey },
    });

    if (apiKeyRecord) {
      request.apiKeyType = "organization";
      request.apiKeyOrganizationId = apiKeyRecord.organizationId;

      return next();
    }
  } catch (error) {
    console.error("Error authenticating with API key:", error);
  }

  // If we get here, the API key is not valid in either context
  return response.status(401).json({
    error: "API key not valid",
  });
};

export const migrationAPIIsAuthenticated = (request: any, response: any, next: any) => {
  if (!request.headers["x-api-key-migration"]) {
    return response.status(400).json({
      error: "Migration API token is missing",
    });
  }

  if (request.headers["x-api-key-migration"] !== MIGRATION_API_SECRET_TOKEN) {
    return response.status(401).json({
      error: "Migration API token not valid",
    });
  }

  next();
};

export const appVersionIsSupported = (request: any, response: any, next: any) => {
  if (request.headers["x-api-key-migration"] === MIGRATION_API_SECRET_TOKEN) {
    return next();
  }

  // Skip checks for CheckHQ webhooks
  if (request.path.includes("/webhooks/check-webhook")) {
    return next();
  }

  if (!request.headers["platform"]) {
    return response.status(426).send({
      error: "Platform header is missing",
    });
  }

  if (!request.headers["app-version"]) {
    return response.status(426).send({
      error: "App version header is missing",
    });
  }

  const platform = request.headers["platform"];
  const appVersion = parseFloat(request.headers["app-version"]);

  if (platform == "ios" && !isNaN(appVersion) && appVersion < parseFloat(MINIMUM_APP_VERSION_IOS)) {
    // Send old response format to support old app for time being
    if (appVersion < parseFloat("7.9")) {
      return response.status(426).send({
        result: "failure",
        error: "App version not supported. Please update to the latest version from the App Store.",
        errorCode: 426,
      });
    } else {
      return response.status(426).send({
        error: "App version not supported. Please update to the latest version from the App Store.",
      });
    }
  }

  if (platform == "android" && !isNaN(appVersion) && appVersion < parseFloat(MINIMUM_APP_VERSION_ANDROID)) {
    // Send old response format to support old app for time being
    if (appVersion < parseFloat("6.8")) {
      return response.status(426).send({
        result: "failure",
        error: "App version not supported. Please update to the latest version from Play Store.",
        errorCode: 426,
      });
    } else {
      return response.status(426).send({
        error: "App version not supported. Please update to the latest version from Play Store.",
      });
    }
  }

  next();
};
