import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { IntegrationUserTokensController } from "@/controllers/integrationusertoken";
import {
  createJournalEntrySchema,
  createUserTokenSchema,
  exportPayrollJournalEntryToCsvSchema,
  updateJournalEntrySchema,
} from "@/middlewares/validation/usertoken";
import { validatorMw } from "@/middlewares/validation/validator";

export const integrationUserTokensRouter = Router();
const integrationUserTokensController = new IntegrationUserTokensController();

/* Get supported platforms */
integrationUserTokensRouter.get(
  "/integration-user-tokens/supported-platforms",
  [userIsAdmin],
  integrationUserTokensController.getSupportedPlatforms
);

/* GET all user tokens */
integrationUserTokensRouter.get("/integration-user-tokens", [userIsAdmin], integrationUserTokensController.get);

/* Create user token */
integrationUserTokensRouter.post(
  "/integration-user-tokens",
  [userIsAdmin, validatorMw(createUserTokenSchema)],
  integrationUserTokensController.create
);

/* Delete user token */
integrationUserTokensRouter.delete(
  "/integration-user-tokens/:id",
  [userIsAdmin],
  integrationUserTokensController.delete
);

/* GET providers */
integrationUserTokensRouter.get(
  "/integration-user-tokens/providers",
  [userIsAdmin],
  integrationUserTokensController.getProviders
);

/* Create journal entry */
integrationUserTokensRouter.post(
  "/integration-user-tokens/journal-entry",
  [userIsAdmin, validatorMw(createJournalEntrySchema)],
  integrationUserTokensController.createJournalEntry
);

/* Update journal entry */
integrationUserTokensRouter.patch(
  "/integration-user-tokens/journal-entry",
  [userIsAdmin, validatorMw(updateJournalEntrySchema)],
  integrationUserTokensController.updateJournalEntry
);

/* Export payroll journal entry to csv */
integrationUserTokensRouter.post(
  "/integration-user-tokens/payroll-journal-entry-to-csv",
  [userIsAdmin, validatorMw(exportPayrollJournalEntryToCsvSchema)],
  integrationUserTokensController.exportPayrollJournalEntryToCsv
);
