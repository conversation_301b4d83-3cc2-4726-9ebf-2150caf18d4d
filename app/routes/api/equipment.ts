import { Router } from "express";

import { EquipmentController } from "@/controllers/equipment";
import { userIsAdminOr<PERSON>oreman } from "@/middlewares/authorization";
import { validateQuery, validatorMw } from "@/middlewares/validation/validator";
import {
  createEquipmentSchema,
  getEquipmentQuerySchema,
  updateEquipmentSchema,
} from "@/middlewares/validation/equipment";
import modelExistsAndIsPartOfOrg from "@/middlewares/modelExistsAndIsPartOfOrg";
import { Equipment } from "@/models";

export const equipmentRouter = Router();
const equipmentController = new EquipmentController();

/* GET all equipment for organization */
equipmentRouter.get("/equipments", [validateQuery(getEquipmentQuerySchema)], equipmentController.get);

/* GET one equipment */
equipmentRouter.get("/equipments/:id", [modelExistsAndIsPartOfOrg(Equipment)], equipmentController.getOne);

/* POST create equipment */
equipmentRouter.post(
  "/equipments",
  [userIsAdminOr<PERSON>oreman, validatorMw(createEquipmentSchema)],
  equipmentController.create
);

/* PATCH update equipment */
equipmentRouter.patch(
  "/equipments/:id",
  [userIsAdminOrForeman, modelExistsAndIsPartOfOrg(Equipment), validatorMw(updateEquipmentSchema)],
  equipmentController.update
);

/* DELETE equipment */
equipmentRouter.delete(
  "/equipments/:id",
  [userIsAdminOrForeman, modelExistsAndIsPartOfOrg(Equipment)],
  equipmentController.delete
);
