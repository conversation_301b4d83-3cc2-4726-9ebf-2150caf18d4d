import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { OvertimeSettingsController } from "@/controllers/overtimesettings";
import { validatorMw } from "@/middlewares/validation/validator";
import { createOvertimeSettingsSchema, updateOvertimeSettingsSchema } from "@/middlewares/validation/overtimesettings";

export const overtimeSettingsRouter = Router();
const overtimeSettingsController = new OvertimeSettingsController();

overtimeSettingsRouter.patch(
  "/overtime-settings",
  [userIsAdmin, validatorMw(updateOvertimeSettingsSchema)],
  overtimeSettingsController.update
);

overtimeSettingsRouter.post(
  "/overtime-settings",
  [userIsAdmin, validatorMw(createOvertimeSettingsSchema)],
  overtimeSettingsController.create
);

overtimeSettingsRouter.delete("/overtime-settings/:id", [userIsAdmin], overtimeSettingsController.delete);

overtimeSettingsRouter.get("/overtime-settings/:id", [userIsAdmin], overtimeSettingsController.findOne);

overtimeSettingsRouter.get("/overtime-settings", [userIsAdmin], overtimeSettingsController.findAll);

overtimeSettingsRouter.patch(
  "/overtime-settings/:id",
  [userIsAdmin, validatorMw(updateOvertimeSettingsSchema)],
  overtimeSettingsController.update
);
