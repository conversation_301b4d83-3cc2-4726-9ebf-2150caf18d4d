import { Router } from "express";
import { userIsAdmin, userIsAdmin<PERSON>r<PERSON><PERSON>man } from "@/middlewares/authorization";
import { WorkersCompCodeController } from "@/controllers/workerscompcode";
import { validatorMw } from "@/middlewares/validation/validator";
import {
  assignCostCodesSchema,
  assignWorkersSchema,
  workersCompCodeSchema,
} from "@/middlewares/validation/workerscompcode";

export const workersCompCodeRouter = Router();
const workersCompCodeController = new WorkersCompCodeController();

workersCompCodeRouter.get("/workers-comp-codes", [userIsAdminOr<PERSON>oreman], workersCompCodeController.get);

workersCompCodeRouter.post(
  "/workers-comp-codes",
  [userIsAdmin, validatorMw(workersCompCodeSchema)],
  workersCompCodeController.create
);

workersCompCodeRouter.patch(
  "/workers-comp-codes/assign-workers",
  [userIsAdmin, validatorMw(assignWorkersSchema)],
  workersCompCodeController.assignWorkers
);

workersCompCodeRouter.patch(
  "/workers-comp-codes/:id",
  [userIsAdmin, validatorMw(workersCompCodeSchema)],
  workersCompCodeController.update
);

workersCompCodeRouter.patch(
  "/workers-comp-codes/:id/cost-codes",
  [userIsAdmin, validatorMw(assignCostCodesSchema)],
  workersCompCodeController.assignCostCodes
);
