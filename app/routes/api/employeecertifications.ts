import { Router } from "express";
import { validateQuery, validatorMw } from "@/middlewares/validation/validator";
import {
  createEmployeeCertificationSchema,
  getEmployeeCertificationsSchema,
} from "@/middlewares/validation/employeecertifications";
import { EmployeeCertificationController } from "@/controllers/employeecertification";
export const employeeCertificationRouter = Router();
const employeeCertificationController = new EmployeeCertificationController();

employeeCertificationRouter.post(
  "/employee-certifications",
  [validatorMw(createEmployeeCertificationSchema)],
  employeeCertificationController.create
);
employeeCertificationRouter.get(
  "/employee-certifications",
  [validateQuery(getEmployeeCertificationsSchema)],
  employeeCertificationController.get
);
employeeCertificationRouter.delete("/employee-certifications/:id", employeeCertificationController.delete);
