import { Router } from "express";
import { userIsAdminOr<PERSON><PERSON><PERSON> } from "@/middlewares/authorization";
import {
  createFringeBenefitSchema,
  updateFringeBenefitSchema,
} from "@/middlewares/validation/fringebenefits";

import { FringeBenefitController } from "@/controllers/fringebenefit";
import { validatorMw } from "@/middlewares/validation/validator";

export const fringeBenefitsRouter = Router();
const fringeBenefit = new FringeBenefitController();

/* GET fringe benefits. */
fringeBenefitsRouter.get("/fringe-benefits", fringeBenefit.get);

/* GET one fringe benefit */
fringeBenefitsRouter.get("/fringe-benefits/:id", fringeBenefit.getOne);

/* POST fringe benefit */
fringeBenefitsRouter.post(
  "/fringe-benefits",
  [
    userIsAdminOrForeman, // permissions check
    validatorMw(createFringeBenefitSchema),
  ],
  fringeBenefit.create
);

/* PATCH fringe benefit */
fringeBenefitsRouter.patch(
  "/fringe-benefits/:id",
  [userIsAdminO<PERSON><PERSON><PERSON><PERSON>, validatorMw(updateFringeBenefitSchema)],
  fringeBenefit.update
);
