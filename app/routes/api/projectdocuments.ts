import { Router } from "express";
import { ProjectDocumentController } from "@/controllers/projectdocument";

export const projectDocumentRouter = Router();
const projectDocumentController = new ProjectDocumentController();

projectDocumentRouter.post("/project-documents", projectDocumentController.create);
projectDocumentRouter.post("/project-documents-ssr", projectDocumentController.getSSR);
projectDocumentRouter.get("/project-documents", projectDocumentController.get);
projectDocumentRouter.delete("/project-documents/:id", projectDocumentController.delete);
