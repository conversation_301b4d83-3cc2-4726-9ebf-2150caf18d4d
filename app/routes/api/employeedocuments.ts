import { Router } from "express";
import { EmployeeDocumentController } from "@/controllers/employeedocument";
import { createEmployeeDocumentSchema } from "@/middlewares/validation/employeedocuments";
import { validatorMw } from "@/middlewares/validation/validator";

export const employeeDocumentRouter = Router();
const employeeDocumentController = new EmployeeDocumentController();

employeeDocumentRouter.post(
  "/employee-documents",
  [validatorMw(createEmployeeDocumentSchema)],
  employeeDocumentController.create
);
employeeDocumentRouter.get("/employee-documents", employeeDocumentController.get);
employeeDocumentRouter.delete("/employee-documents/:id", employeeDocumentController.delete);
