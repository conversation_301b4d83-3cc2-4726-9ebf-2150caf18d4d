import {
  Break,
  BreakHistory,
  Classification,
  CompanyBenefit,
  CostCode,
  EarningRate,
  EmployeeBenefit,
  FringeBenefit,
  FringeBenefitClassification,
  InjuryPhoto,
  InjuryReport,
  Project,
  TimeSheet,
  TimesheetAlert,
  TimesheetHistory,
  User,
  UserClassification,
  Equipment,
  EquipmentCategory,
} from "@/models";
import { determineValidEarningRatesByClockIn, serializeTimesheet } from "@/util/timesheetHelper";
import sequelize from "@/lib/sequelize";
import { resourceFetchBelongsToUserOrAdminOrForeman } from "@/middlewares/authorization/helpers";
import {
  allowedToCreateTimesheet,
  allowedToUpdateTimesheet,
  timesheetBelongsToOrg,
} from "@/middlewares/authorization/timesheets";
import { validateQuery, validatorMw } from "@/middlewares/validation/validator";
import {
  createBulkTimesheetsSchema,
  createTimesheetSchema,
  timesheetReportQuerySchema,
  updateTimesheetSchema,
  bulkDeleteTimesheetsSchema,
} from "@/middlewares/validation/timesheets";

import { TimesheetHistoryService } from "@/services/timesheethistory";
import { createOrUpdateTimesheetResponse, TIMESHEET_ATTRIBUTES, TimesheetsService } from "@/services/timesheets";
import { TimesheetsController } from "@/controllers/timesheets";
import { NextFunction, Request, Response } from "express";
import UserLocation from "@/models/userlocation";
import { serializeUserLocation, UserLocationService } from "@/services/userlocation";
import { EarningRateService } from "@/services/earningrate";

import { serializeProject } from "@/services/projects";
import { TimesheetAlertService } from "@/services/timesheetalert";
import { ExtendedOrganization } from "@/models/organization";
import WorkersCompCode from "@/models/workersCompCode";
import { BreaksService } from "@/services/breaks";
import { Op } from "sequelize";
import { getNearestFullWeekIntervals } from "@/util/dateHelper";
import { userIsAdminOrForeman } from "@/middlewares/authorization";

interface HistoryEntry {
  id: number;
  type: string;
  history: any;
  createdAt: string;
  createdBy: number;
  user: {
    firstName: string;
    lastName: string;
  };
}

interface FlattenedEntry {
  property: string;
  previousValue: any;
  newValue: any;
  type: string;
  createdAt: string;
  createdBy: {
    id: number;
    firstName: string;
    lastName: string;
  };
}

export const initTimesheetsRoutes = (app: any, basePath: string) => {
  const BASE_RESOURCE = "timesheets";
  const timesheetsController = new TimesheetsController();
  const breaksService = new BreaksService();
  const userLocationService = new UserLocationService();
  const timesheetAlertService = new TimesheetAlertService();
  const timesheetHistoryService = new TimesheetHistoryService();
  const earningRateService = new EarningRateService();
  const timesheetsService = new TimesheetsService();

  app.post(`${basePath}/${BASE_RESOURCE}/approve`, [userIsAdminOrForeman], timesheetsController.approveTimesheets);
  app.post(`${basePath}/${BASE_RESOURCE}/unapprove`, [userIsAdminOrForeman], timesheetsController.unApproveTimesheets);

  app.get(
    `${basePath}/${BASE_RESOURCE}`,
    [resourceFetchBelongsToUserOrAdminOrForeman, validateQuery(timesheetReportQuerySchema)],
    timesheetsController.get
  );

  app.post(
    `${basePath}/${BASE_RESOURCE}/`,
    [allowedToCreateTimesheet, validatorMw(createTimesheetSchema)],
    timesheetsController.create
  );

  app.patch(
    `${basePath}/${BASE_RESOURCE}/:timesheetId`,
    [timesheetBelongsToOrg, allowedToUpdateTimesheet, validatorMw(updateTimesheetSchema)],
    async (request: Request, response: Response, next: NextFunction) => {
      const { timesheetId } = request.params;

      const timesheet = request.targetedTimesheet;
      const organization = request.organization as ExtendedOrganization;
      const timeSheetUserId = timesheet.workerId;
      const organizationId = request.session.user.organizationId;

      const {
        projectId,
        costCodeId,
        description,
        clockIn,
        clockOut,
        clockOutLocation = null,
        breakDuration,
        hideGeofenceWarning,
        clockedOutBySwitch,
        breaks,
        status = null,
        workersCompCodeId,
        userClassificationId = null,
        otDuration,
        dotDuration,
        driveTimeDuration,
        equipmentId,
      } = request.body;

      let clockInInt = 0,
        clockOutInt = 0,
        projectIdInt = 0,
        costCodeIdInt = 0,
        breakDurationInt = 0,
        otDurationInt = 0,
        dotDurationInt = 0,
        driveTimeDurationInt = 0,
        equipmentIdInt = 0;

      if (clockIn) {
        clockInInt = parseInt(clockIn);
      }

      if (clockOut) {
        clockOutInt = parseInt(clockOut);
      }

      // slowly move this into schema validation
      if (clockInInt && clockOutInt) {
        if (clockInInt >= clockOutInt) {
          return response.status(400).json({
            error: "Clock in cannot be after clock out",
          });
        }
      } else if (clockOutInt && timesheet.clockIn && timesheet.clockIn.getTime() >= clockOutInt) {
        return response.status(400).json({
          error: "Clock out cannot be before clock in",
        });
      } else if (clockInInt && timesheet.clockOut && timesheet.clockOut.getTime() <= clockInInt) {
        return response.status(400).json({
          error: "Clock in cannot be after clock out",
        });
      } else if (clockInInt && timesheet.clockOut == null) {
        // don't allow clock in to be in the future
        // this could happen when user is editing an active timesheet to update the clockIn to a past time
        if (clockInInt > Date.now()) {
          return response.status(400).json({
            error: "Clock in cannot be in the future",
          });
        }
      }

      if (projectId) {
        projectIdInt = parseInt(projectId);
      }

      if (costCodeId) {
        costCodeIdInt = parseInt(costCodeId);
      }

      if (breakDuration) {
        breakDurationInt = parseInt(breakDuration);
      }

      if (otDuration) {
        otDurationInt = parseInt(otDuration);
      }

      if (driveTimeDuration) {
        driveTimeDurationInt = parseInt(driveTimeDuration);
      }

      if (equipmentId) {
        equipmentIdInt = parseInt(equipmentId);
      }

      if (dotDuration) {
        dotDurationInt = parseInt(dotDuration);
      }

      // if any field except clockOutLocation is updated, then we should set isEditedBy
      let isTimesheetEdited = false;

      const patchUpdates: any = {};
      if (projectIdInt) {
        patchUpdates.projectId = projectIdInt;
        isTimesheetEdited = true;
      }

      if (costCodeIdInt) {
        patchUpdates.costCodeId = costCodeIdInt;
        isTimesheetEdited = true;
      }

      if (equipmentIdInt) {
        patchUpdates.equipmentId = equipmentIdInt;
        isTimesheetEdited = true;
      }

      if (workersCompCodeId !== undefined) {
        patchUpdates.workersCompCodeId = parseInt(workersCompCodeId);
        isTimesheetEdited = true;
      }

      if (description != null) {
        patchUpdates.description = description;
        isTimesheetEdited = true;
      }

      if (clockInInt) {
        patchUpdates.clockIn = clockInInt;
        isTimesheetEdited = true;
      }

      if (clockOutInt) {
        patchUpdates.clockOut = clockOutInt;
        isTimesheetEdited = true;
      }

      // only check if breakDuration was sent in the request
      // don't check for breakDurationInt since it could be 0
      if (breakDuration !== undefined) {
        patchUpdates.breakDuration = breakDurationInt;
        isTimesheetEdited = true;
      }

      if (otDuration !== undefined) {
        patchUpdates.otDuration = otDurationInt;
        isTimesheetEdited = true;
      }

      if (dotDuration !== undefined) {
        patchUpdates.dotDuration = dotDurationInt;
        isTimesheetEdited = true;
      }

      if (driveTimeDuration !== undefined) {
        patchUpdates.driveTimeDuration = driveTimeDurationInt;
        isTimesheetEdited = true;
      }

      if (hideGeofenceWarning != null) {
        patchUpdates.hideGeofenceWarning = hideGeofenceWarning;
      }

      if (userClassificationId !== null) {
        patchUpdates.userClassificationId = parseInt(userClassificationId);
        isTimesheetEdited = true;
      }

      if (isTimesheetEdited) {
        patchUpdates.editedBy = request.session.user.id;
      }

      if (clockedOutBySwitch != null) {
        patchUpdates.clockedOutBySwitch = clockedOutBySwitch;
      }

      // if the timesheet is clocked in and the request is to clockOut, then set the status to SUBMITTED
      if (timesheet.status === "CLOCKED_IN" && patchUpdates.clockOut !== undefined) {
        patchUpdates.status = "SUBMITTED";
      } else if (timesheet.status === "APPROVED" && request.session.user.role !== "ADMIN") {
        // if the timesheet is approved and the request is not made by an admin, then reset the status back to SUBMITTED,
        // so that it requires re-approval
        patchUpdates.status = "SUBMITTED";
        patchUpdates.approvedAt = null;
      } else if (status !== null) {
        patchUpdates.status = status;
      }

      const transaction = await sequelize.transaction();

      try {
        // do the timesheet history update first
        const existingTimesheetData: any = await timesheetsService.findOne({
          where: { id: timesheetId },
          include: [
            {
              model: Project,
              attributes: ["id", "name", "isPrevailingWage"],
            },
            {
              model: CostCode,
              attributes: ["id", "name"],
            },
            {
              model: WorkersCompCode,
              required: false,
              as: "workersCompCode",
            },
            {
              model: UserClassification,
              as: "userClassification",
              required: false,
              include: [
                {
                  model: Classification,
                  required: false,
                },
              ],
            },
          ],
          transaction,
        });

        // create history entries if the timesheet is not running OR if it's running and the PATCH request is not a clockOut request
        if (
          existingTimesheetData.clockOut !== null ||
          (existingTimesheetData.clockOut === null && Object.keys(request.body).includes("clockOut") === false)
        ) {
          // if patchUpdates has projectId and/or costCodeId, then we need to update the project and/or costCode name
          if (patchUpdates.projectId) {
            patchUpdates.project = await Project.findOne({ where: { id: patchUpdates.projectId }, transaction });
          }

          if (patchUpdates.costCodeId) {
            // used for timesheets history
            patchUpdates.costCode = await CostCode.findOne({
              where: { id: patchUpdates.costCodeId },
              transaction,
            });
          }

          if (patchUpdates.workersCompCodeId) {
            // used for timesheets history
            patchUpdates.workersCompCode = await WorkersCompCode.findOne({
              where: { id: patchUpdates.workersCompCodeId },
              transaction,
            });
          }

          if (patchUpdates.userClassificationId) {
            const userClassification = await UserClassification.findByPk(patchUpdates.userClassificationId, {
              transaction,
              include: [
                {
                  model: Classification,
                  required: true,
                  as: "classification",
                },
              ],
            });

            // I know it's a bit confusing why
            //    we are assigning `userClassification.classification` to `patchUpdates.userClassification`,
            //    but here is the explanation:
            //        We are saving in DB the "name" of the entity that was changed. In this case, a userClassification
            //        doesn't have a "name", but it has a "classification" parent property that has a "name".
            // This is a hack is it's a signal this feature is not robust enough and needs to be refactored.
            patchUpdates.userClassification = userClassification.classification;
          }

          // essentially breakDuration shouldn't be a property passed to formatTimesheetHistory if realTimeBreaks are enabled
          // FUTURE TO DO: can make sure the client doesnt send non-modified breakDuration in the request
          // Or we keep this in because technically the breakDuration does change from `null` at inception to a non-null value (0) from the client
          if (
            organization.timeTrackingSettings.areRealtimeBreaksEnabled &&
            Object.keys(patchUpdates).includes("breakDuration")
          ) {
            delete patchUpdates.breakDuration;
          }

          // to prevent creating a history for status change from CLOCKED_IN to SUBMITTED
          // we need to remove status from this new cloned patchUpdates object called historyPatchUpdates
          const historyPatchUpdates = patchUpdates;
          if (Object.keys(historyPatchUpdates).includes("status")) {
            const oldStatus = existingTimesheetData.status;
            const newStatus = historyPatchUpdates.status;

            if (
              (oldStatus === "SUBMITTED" && newStatus === "APPROVED") ||
              (oldStatus === "APPROVED" && newStatus === "SUBMITTED")
            ) {
              // keep the status change in the history
              historyPatchUpdates.status = newStatus;
            } else {
              // remove the status from history for all other status changes
              delete historyPatchUpdates.status;
            }
          }

          if (Object.keys(patchUpdates).length > 0) {
            const timesheetHistoryObj = timesheetHistoryService.formatTimesheetHistory(
              {
                ...existingTimesheetData.toJSON(),

                // I know it's a bit confusing why
                //    we are assigning `userClassification.classification` to `patchUpdates.userClassification`,
                //    but here is the explanation:
                //        We are saving in DB the "name" of the entity that was changed. In this case, a userClassification
                //        doesn't have a "name", but it has a "classification" parent property that has a "name".
                // This is a hack is it's a signal this feature is not robust enough and needs to be refactored.
                userClassification: existingTimesheetData.userClassification?.classification?.toJSON(),
              },
              historyPatchUpdates,
              "UPDATE",
              request.session.user.id
            );

            if (timesheetHistoryObj.history && timesheetHistoryObj.history !== "{}") {
              await TimesheetHistory.create(timesheetHistoryObj, { transaction });
            }
          }

          // after creating History Object - clean up the added properties if exist
          if (patchUpdates.project) {
            delete patchUpdates.project;
          }

          if (patchUpdates.costCode) {
            delete patchUpdates.costCode;
          }
        }

        // we want to grab the earning rates should the earning rate ids need to update based on clockIn
        if (patchUpdates.clockIn) {
          const earningRates = await earningRateService.findAll({
            where: {
              user_id: timeSheetUserId,
              organization_id: organizationId,
            },
            transaction,
          });

          // earningRatesToUse is a tuple where the first element is the reg earning rate to use and the second element is the ot earning rate to use
          const earningRatesToUse = determineValidEarningRatesByClockIn(earningRates, patchUpdates.clockIn);

          patchUpdates.regEarningRateId = earningRatesToUse[0]?.id;
          patchUpdates.otEarningRateId = earningRatesToUse[1]?.id;
          patchUpdates.dotEarningRateId = earningRatesToUse[2]?.id;
        }

        // if project of the timesheet was updated from a PW project to a non-PW project, then classificationId should be set to null
        if (patchUpdates.projectId && existingTimesheetData.project.isPrevailingWage) {
          const newProject = await Project.findOne({ where: { id: patchUpdates.projectId }, transaction });
          if (newProject.isPrevailingWage === false) {
            patchUpdates.userClassificationId = null;
          }
        }

        await TimeSheet.update(patchUpdates, {
          where: {
            id: timesheetId,
            organizationId,
          },
          transaction,
        });

        const updatedTimesheet: any = await timesheetsService.findOne({
          where: { id: timesheetId },
          transaction,
        });

        // it's a clock out request if timesheet doesn't already have a clockOut and the request contains clockOut
        const isClockOutRequest =
          existingTimesheetData.clockOut === null && Object.keys(request.body).includes("clockOut") === true;

        // store clock out location in UserLocation
        if (isClockOutRequest && clockOutLocation !== null) {
          await userLocationService.create(
            {
              ...clockOutLocation,
              timesheetId: timesheetId,
              organizationId,
            },
            transaction
          );
        }

        // create alerts related to this clock out if needed
        // only create alerts if this is a clock out request and
        // is done by the user himself and not by an admin or foreman since the app, by design won't send location in that case
        if (isClockOutRequest && request.session.user.id === timeSheetUserId) {
          timesheetAlertService.createClockoutLocationAlertIfNeeded(
            clockOutLocation,
            updatedTimesheet,
            organization.timeTrackingSettings.locationBreadcrumbingEnabled,
            transaction
          );
        }

        if (organization.timeTrackingSettings.areRealtimeBreaksEnabled && breaks) {
          try {
            await breaksService.createOrUpdate(
              request.session.user.id,
              request.body.breaks,
              updatedTimesheet,
              false,
              organizationId,
              transaction
            );
          } catch (error: any) {
            if (error.name === "BreakValidationError") {
              await transaction.rollback();

              return response.status(422).json({
                error: error.message,
              });
            }
            throw error;
          }
        }

        // do this after updating breaks, in case an existing break was updated to a new time
        await breaksService.validateExistingBreaks(updatedTimesheet, transaction);

        const timesheetData = await createOrUpdateTimesheetResponse(updatedTimesheet.id, organizationId, transaction);

        await transaction.commit();

        return response.json({
          data: timesheetData,
        });
      } catch (error) {
        await transaction.rollback();

        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/:timesheetId/history`,

    async (request: Request, response: Response, next: NextFunction) => {
      const { timesheetId } = request.params;
      const organizationId = request.session.user.organizationId;
      if (!organizationId) {
        return response.status(400).json({
          error: "Missing organizationId in session",
        });
      }

      const timesheet = await timesheetsService.findOne({
        where: {
          id: timesheetId,
          organizationId,
        },
      });

      if (!timesheet) {
        return response.status(404).json({
          error: "Timesheet not found",
        });
      }

      try {
        let timesheetHistory: any = await timesheetsService.findOne({
          where: {
            id: timesheetId,
          },
          attributes: TIMESHEET_ATTRIBUTES,
          include: [
            {
              model: BreakHistory,
              attributes: ["id", "type", "history", "createdAt", "createdBy"],
              include: [
                {
                  model: User,
                  attributes: ["firstName", "lastName"],
                },
              ],
            },
            {
              model: TimesheetHistory,
              attributes: ["id", "type", "history", "createdAt", "createdBy"],
              include: [
                {
                  model: User,
                  attributes: ["firstName", "lastName"],
                },
              ],
            },
            {
              model: Project,
              attributes: [
                "id",
                "name",
                "isGeofenced",
                "geofenceRadius",
                "location",
                "isPrevailingWage",
                "wageTableId",
              ],
            },
            {
              model: User,
              attributes: ["id", "firstName", "lastName"],
              as: "user",
            },
          ],
          order: [["createdAt", "DESC"]],
        });

        // deserialize the timesheet and break histories
        // Handle breakHistories
        timesheetHistory.breakHistories.forEach((breakHistory: Partial<BreakHistory>) => {
          try {
            if (breakHistory.history) {
              breakHistory.history = JSON.parse(breakHistory.history);
            }
          } catch (e) {
            console.error("Failed to parse breakHistory history JSON", e);
            // Optionally, set breakHistory.history to a default value or keep as is
          }
        });

        // Handle timesheetHistories
        timesheetHistory.timesheetHistories.forEach((timesheetHistory: Partial<TimesheetHistory>) => {
          try {
            if (timesheetHistory.history) {
              timesheetHistory.history = JSON.parse(timesheetHistory.history);
            }
          } catch (e) {
            console.error("Failed to parse timesheetHistory history JSON", e);
            // Optionally, set timesheetHistory.history to a default value or keep as is
          }
        });

        const transformedTimesheetHistory = transformApiResponse(timesheetHistory);
        // replace the timesheetHistory.breakHistories and timesheetHistory.timesheetHistories with the transformed versions
        timesheetHistory = timesheetHistory.toJSON();
        // remove the old individual history props
        delete timesheetHistory.breakHistories;
        delete timesheetHistory.timesheetHistories;
        timesheetHistory.timesheetHistory = transformedTimesheetHistory.timesheetHistory;

        return response.json({
          data: timesheetHistory,
        });
      } catch (error) {
        console.log("something went wrong ", error);

        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/:timesheetId`,

    async (request: Request, response: Response, next: NextFunction) => {
      const organization = request.organization;
      const { timesheetId } = request.params;

      try {
        const timesheet = await timesheetsService.findOne({
          where: {
            id: timesheetId,
            organizationId: organization.id,
          },
        });

        const startDayOfWeek =
          request.organization.paySchedule.payFrequency === "weekly"
            ? request.organization.overtimeSettings.weekStartDay
            : "MONDAY";

        const adjustedDates = getNearestFullWeekIntervals(
          timesheet.clockIn.getTime(),
          timesheet.clockOut ? timesheet.clockOut.getTime() : Date.now().valueOf(),
          startDayOfWeek
        );

        const timesheets = await timesheetsService.findAll({
          where: {
            clockIn: {
              [Op.between]: [adjustedDates.start.valueOf(), adjustedDates.end.valueOf()],
            },
            organizationId: organization.id,
          },
          attributes: [...TIMESHEET_ATTRIBUTES, "userClassificationId"],
          include: [
            {
              model: User,
              include: [
                {
                  model: EmployeeBenefit,
                  required: false,
                  include: [
                    {
                      model: CompanyBenefit,
                      as: "companyBenefit",
                    },
                  ],
                },
              ],
            },
            {
              model: Project,
              attributes: [
                "id",
                "name",
                "isGeofenced",
                "geofenceRadius",
                "location",
                "isPrevailingWage",
                "wageTableId",
              ],
            },
            {
              model: CostCode,
              where: {
                organizationId: organization.id,
              },
              attributes: ["id", "name", "number"],
              required: false,
            },
            {
              model: UserClassification,
              as: "userClassification",
              required: false,
              include: [
                {
                  model: Classification,
                  required: false,
                },
              ],
            },
            {
              model: User,
              attributes: ["firstName", "lastName"],
              as: "createdByUser",
            },
            {
              model: User,
              attributes: ["firstName", "lastName"],
              as: "editedByUser",
            },
            {
              model: Break,
              where: {
                isDeleted: false,
              },
              required: false,
              include: [
                {
                  model: User,
                  as: "editedByUser",
                  attributes: ["firstName", "lastName"],
                },
              ],
            },
            {
              model: TimesheetHistory,
              separate: true,
              attributes: ["id", "type", "history", "createdAt", "createdBy"],
            },
            {
              model: BreakHistory,
              separate: true,
              attributes: ["id", "type", "history", "createdAt", "createdBy"],
            },
            {
              model: UserLocation,
              separate: true,
              attributes: [
                "id",
                "locationCoordinates",
                "locationAddress",
                "loggedAt",
                "locationEvent",
                "speed",
                "horizontalAccuracy",
                "breakId",
              ],
              include: [
                {
                  model: Project,
                  attributes: ["id", "name", "isGeofenced", "geofenceRadius", "location", "isPrevailingWage"],
                },
              ],
            },
            {
              model: TimesheetAlert,
              required: false,
              include: [
                {
                  model: User,
                  attributes: ["firstName", "lastName"],
                  as: "resolvedByUser",
                },
              ],
            },
            {
              model: EarningRate,
              as: "regEarningRate",
              required: false,
              where: {
                period: {
                  [Op.ne]: "ANNUALLY",
                },
              },
            },
            {
              model: EarningRate,
              as: "otEarningRate",
              required: false,
              where: {
                period: {
                  [Op.ne]: "ANNUALLY",
                },
              },
            },
            {
              model: UserClassification,
              as: "userClassification",
              required: false,
              include: [
                {
                  model: Classification,
                  required: false,
                },
              ],
            },
            {
              model: InjuryReport,
              required: false,
              as: "injuryReport",
              include: [
                {
                  model: InjuryPhoto,
                  attributes: ["id", "objectId"],
                },
                {
                  model: User,
                  attributes: ["id", "firstName", "lastName"],
                  as: "resolvedByUser",
                },
              ],
            },
            {
              model: Equipment,
              as: "equipment",
              required: false,
              attributes: ["id", "name", "hourlyCost", "year"],
              include: [
                {
                  model: EquipmentCategory,
                  as: "category",
                  attributes: ["id", "name"],
                },
              ],
            },
          ],
        });

        const fringeBenefitData = await FringeBenefitClassification.findAll({
          where: {
            organizationId: request.organization.id,
          },
          include: [
            {
              model: Classification,
              required: true,
            },
            {
              model: FringeBenefit,
              required: true,
            },
          ],
        });

        const enrichedTimesheets = timesheetsService.enrichTimesheetsWithFringeBenefitClassifications(
          timesheets,
          fringeBenefitData
        );
        const modifiedResults = timesheetsService.enrichTimesheets(
          enrichedTimesheets,
          organization.paySchedule.payFrequency
        );

        const flattenedAttributedTimesheets = timesheetsService.generateFlattenedAttributedTimesheets(
          modifiedResults,
          adjustedDates.start.valueOf(),
          organization
        );

        let timesheetJSON = flattenedAttributedTimesheets.find((item) => item.id === timesheet.id) as any;
        timesheetJSON = serializeTimesheet(timesheetJSON);
        timesheetJSON.project = serializeProject(timesheetJSON.project);

        const clockIn = timesheetJSON.rawClockIn?.valueOf() ?? timesheetJSON.clockIn?.valueOf();
        const clockOut = timesheetJSON.rawClockOut?.valueOf() ?? timesheetJSON.clockOut?.valueOf();
        timesheetJSON.userLocations = transformAndSerializeUserLocations(
          timesheetJSON.userLocations,
          clockIn,
          clockOut,
          timesheetJSON.breaks,
          timesheetJSON.editedBy
        );

        timesheetJSON.userLocations = addTimesheetAlertsToUserLocations(
          timesheetJSON.userLocations,
          timesheetJSON.timesheetAlerts,
          timesheetJSON.clockOut
        );

        delete timesheetJSON.timesheetAlerts;

        timesheetJSON.userLocations.sort((a: any, b: any) => {
          return new Date(a.loggedAt).getTime() - new Date(b.loggedAt).getTime();
        });

        // if the user is not authorized to see the timesheet earnings, remove them
        if (request.session.user.role !== "ADMIN" && timesheetJSON.workerId !== request.session.user.id) {
          delete timesheetJSON.regEarningRate;
          delete timesheetJSON.otEarningRate;
          delete timesheetJSON.hourlyWage;
          delete timesheetJSON.otHourlyWage;
          delete timesheetJSON.regularWages;
          delete timesheetJSON.totalWages;
          delete timesheetJSON.overtimeWages;
        }

        return response.json({
          data: {
            timesheet: timesheetJSON,
          },
        });
      } catch (err) {
        console.log("timesheets error ->", err);

        next(err);
      }
    }
  );

  app.delete(
    `${basePath}/${BASE_RESOURCE}/:timesheetId`,
    [timesheetBelongsToOrg, allowedToUpdateTimesheet],
    async (request: Request, response: Response, next: NextFunction) => {
      const organizationId = request.session.user.organizationId;
      if (!organizationId) {
        return response.status(400).json({
          error: "Missing organizationId in session",
        });
      }

      const timesheetId = parseInt(request.params.timesheetId);

      if (!timesheetId) {
        return response.status(400).json({
          error: "Missing timesheetId param",
        });
      }

      const timesheet = await timesheetsService.findOne({
        where: {
          id: timesheetId,
          organizationId,
        },
      });

      if (!timesheet) {
        return response.status(404).json({
          error: "Timesheet not found",
        });
      }

      if (
        request.session.user.role !== "ADMIN" &&
        request.session.user.role !== "FOREMAN" &&
        request.session.user.id !== timesheet.workerId
      ) {
        return response.status(403).json({
          error: "You are not authorized to delete this timesheet",
        });
      }

      try {
        await TimeSheet.update(
          {
            isDeleted: true,
          },
          {
            where: {
              id: timesheetId,
              organizationId,
            },
          }
        );

        return response.json({});
      } catch (error) {
        console.log("something went wrong ", error);

        next(error);
      }
    }
  );

  app.post(
    `${basePath}/${BASE_RESOURCE}/bulk`,
    [allowedToCreateTimesheet, validatorMw(createBulkTimesheetsSchema)],
    timesheetsController.createBulk
  );

  app.post(
    `${basePath}/${BASE_RESOURCE}/bulk-delete`,
    [userIsAdminOrForeman, validatorMw(bulkDeleteTimesheetsSchema)],
    timesheetsController.bulkDelete
  );
};

function transformApiResponse(apiResponse: any): any {
  const flattenHistory = (entry: HistoryEntry, historyType: string, typePrefix?: string): FlattenedEntry[] => {
    const flattened: FlattenedEntry[] = [];
    for (const prop in entry.history) {
      const history = entry.history[prop];
      flattened.push({
        property: typePrefix ? `${typePrefix}-${prop}` : `${prop}`,
        previousValue: history.prev ?? "None",
        newValue: history.new ?? "None",
        type: historyType,
        createdAt: entry.createdAt,
        createdBy: {
          id: entry.createdBy,
          firstName: entry.user.firstName,
          lastName: entry.user.lastName,
        },
      });
    }

    return flattened;
  };

  const transformedResponse = { ...apiResponse };
  transformedResponse.breakHistories = apiResponse.breakHistories.flatMap((entry: HistoryEntry) =>
    flattenHistory(entry, entry.type, "break")
  );
  transformedResponse.timesheetHistories = apiResponse.timesheetHistories.flatMap((entry: HistoryEntry) =>
    flattenHistory(entry, entry.type, "timesheet")
  );

  // remove any properties that shouldn't be returned

  // then concatenate the breakHistories and timesheetHistories and sort by createdAt (descending)
  const combinedHistories = transformedResponse.breakHistories.concat(transformedResponse.timesheetHistories);

  combinedHistories.sort((a: any, b: any) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  transformedResponse.timesheetHistory = combinedHistories;

  return transformedResponse;
}

function transformAndSerializeUserLocations(
  userLocations: any[],
  clockIn: any,
  clockOut: any,
  breaks: any[],
  editedBy: any
) {
  return userLocations.flatMap((userLocation: any) => {
    if (userLocation.project) {
      userLocation.geofenceProject = serializeProject(userLocation.project);
    }

    delete userLocation.project;
    userLocation = serializeUserLocation(userLocation);

    if (
      (userLocation.locationEvent === "CLOCK_IN" && userLocation.loggedAt !== clockIn) ||
      (userLocation.locationEvent === "CLOCK_OUT" && userLocation.loggedAt !== clockOut)
    ) {
      const editedLoggedAt = userLocation.locationEvent === "CLOCK_IN" ? clockIn : clockOut;
      const editedUserLocation = {
        ...userLocation,
        locationAddress: "Edited by " + editedBy,
        loggedAt: editedLoggedAt,
        isEdited: true,
      };

      userLocation.strikeout = true;
      delete userLocation.locationCoordinates;
      delete editedUserLocation.locationCoordinates;

      return [userLocation, editedUserLocation];
    } else if (userLocation.locationEvent === "BREAK_START" || userLocation.locationEvent === "BREAK_END") {
      const matchingBreak = breaks.find((breakEntry: any) => {
        return breakEntry.id === userLocation.breakId;
      });

      // if matchingBreak is null, return empty array
      // this could happen if the break was deleted
      if (!matchingBreak) {
        return [];
      }

      const breakStart = matchingBreak.rawStart ?? matchingBreak.start;
      const breakEnd = matchingBreak.rawEnd ?? matchingBreak.end;

      if (matchingBreak) {
        if (
          (userLocation.locationEvent === "BREAK_START" && userLocation.loggedAt !== breakStart) ||
          (userLocation.locationEvent === "BREAK_END" && userLocation.loggedAt !== breakEnd)
        ) {
          const editedLoggedAt = userLocation.locationEvent === "BREAK_START" ? breakStart : breakEnd;
          const editedUserLocation = {
            ...userLocation,
            locationAddress:
              "Edited by " + matchingBreak.editedByUser?.firstName + " " + matchingBreak.editedByUser?.lastName,
            loggedAt: editedLoggedAt,
            isEdited: true,
          };

          userLocation.strikeout = true;
          delete userLocation.locationCoordinates;
          delete editedUserLocation.locationCoordinates;

          return [userLocation, editedUserLocation];
        }

        return [userLocation];
      }

      return [];
    }

    return [userLocation];
  });
}

function addTimesheetAlertsToUserLocations(userLocations: any[], timesheetAlerts: any[], clockOut: string): any[] {
  const clockOutUserLocation = userLocations.find((userLocation) => userLocation.locationEvent === "CLOCK_OUT");

  const clockOutLocationOutsideAlert = timesheetAlerts.find(
    (alert) => alert.type === "CLOCK_OUT_LOCATION_OUTSIDE_GEOFENCE"
  );

  if (clockOutLocationOutsideAlert) {
    if (clockOutUserLocation) {
      clockOutUserLocation.timesheetAlert = clockOutLocationOutsideAlert;
    }
  } else {
    const clockOutLocationMissingAlert = timesheetAlerts.find((alert) => alert.type === "CLOCK_OUT_LOCATION_MISSING");

    if (clockOutLocationMissingAlert) {
      const userLocationId = Math.floor(Math.random() * 1000000);
      const newUserLocation = {
        id: userLocationId,
        loggedAt: clockOut,
        locationEvent: "CLOCK_OUT",
        timesheetAlert: clockOutLocationMissingAlert,
      };

      userLocations.push(newUserLocation);
    }
  }

  return userLocations;
}
