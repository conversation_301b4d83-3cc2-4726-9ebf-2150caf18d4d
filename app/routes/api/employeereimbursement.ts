import { Router } from "express";
import { setEmployeeReimbursement } from "@/controllers/employeereimbursement";
import { validatorMw } from "@/routes/middlewares/validation/validator";
import { employeeReimbursementSchema } from "@/routes/middlewares/validation/employeereimbursement";

const router = Router();

router.post("/employee-reimbursements", validatorMw(employeeReimbursementSchema), setEmployeeReimbursement);

export default router;
