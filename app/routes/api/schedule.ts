import { Router } from "express";
import { userIsAdmin<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/middlewares/authorization";
import { ScheduleController } from "@/controllers/schedule";

export const scheduleRouter = Router();
const scheduleController = new ScheduleController();

scheduleRouter.get("/schedule-events", scheduleController.get);

scheduleRouter.post("/schedule-events", scheduleController.create);

scheduleRouter.post(
  "/schedule-events/:id/send-update-notifications",

  scheduleController.sendUpdateNotifications
);

scheduleRouter.patch("/schedule-events/:id", scheduleController.update);

scheduleRouter.delete("/schedule-events/:id", [userIsAdminOrForeman], scheduleController.delete);

scheduleRouter.delete("/schedule-events", [userIsAdminOrForeman], scheduleController.deleteMany);
