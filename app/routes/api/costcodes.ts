import { Router } from "express";
import { CostCodesController } from "@/controllers/costcodes";

export const costCodesRouter = Router();
const costCodesController = new CostCodesController();

/* GET cost codes. */
// Note: The assignment operation below simulates loss of context - this is why we need to bind methods in controller
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_objects/Function/bind
costCodesRouter.get("/cost-codes", costCodesController.get);

/* POST cost codes */
costCodesRouter.post("/cost-codes", costCodesController.create);

/* PATCH cost codes */
costCodesRouter.patch("/cost-codes/:id", costCodesController.update);

costCodesRouter.patch("/cost-codes/:id/workers-comp-code", costCodesController.updateWorkersCompCode);
