import { ExtendedUser } from "@/models/user";
import { EarningRate, Organization, User } from "@/models";
import { DeviceTokensService } from "@/services/devicetoken";
import { sendSMS } from "@/util/sendSMS";
import { apiIsAuthenticated } from "@/middlewares/session";
import rateLimit from "express-rate-limit";
import * as Sentry from "@sentry/node";
import { getActiveEarningRate } from "@/util/userHelper";
import { NextFunction, Request, Response } from "express";
import { OrganizationsService } from "@/services/organizations";
import TimeTrackingSettings from "@/models/timetrackingsettings";

const otpGenerator = require("otp-generator");

const organizationsService = new OrganizationsService();
export const initAuthRoutes = (app: any, basePath: string) => {
  const {
    ENVIRONMENT,
    LOGIN_RATE_LIMIT_WINDOW_MS = "60000", // default 1 minute in ms
    LOGIN_RATE_LIMIT_MAX_REQUESTS = "2", // default 2 requests
  } = process.env;

  const authLimiter = rateLimit({
    windowMs: parseInt(LOGIN_RATE_LIMIT_WINDOW_MS, 10), // Parse as base 10
    max: parseInt(LOGIN_RATE_LIMIT_MAX_REQUESTS, 10), // Parse as base 10
  });

  app.post(
    `${basePath}/login`,
    authLimiter,
    apiIsAuthenticated,
    async (request: Request, response: Response, next: NextFunction) => {
      const SMS_COOLDOWN_SECONDS = process.env.SMS_COOLDOWN_SECONDS ?? "60";
      try {
        request.session.user = undefined;

        const ip = request.headers["x-forwarded-for"] || request.connection.remoteAddress;
        console.log(`IP: ${ip}`);

        const { phone } = request.body;

        if (phone && phone.length > 7) {
          const user = await User.findOne({ where: { phone } });

          if (!user) {
            if (ENVIRONMENT === "PRODUCTION") {
              Sentry.captureMessage(`Login attempt with unauthorized phone number: ${phone}`);
            }

            return response.status(404).json({
              error: "User not found.",
            });
          }

          const org = await organizationsService.findOne({ where: { id: user?.organizationId } });

          if (!org) {
            return response.status(404).json({
              error: "Organization not found.",
            });
          }

          if (org.isChurned) {
            return response.status(403).json({
              error: "Organization is not active.",
            });
          }

          if (user.isArchived) {
            return response.status(403).json({
              error: "User is not active.",
            });
          }

          let diff = +SMS_COOLDOWN_SECONDS + 1;
          if (user.otpCodeSentAt) {
            diff = (+new Date() - +new Date(user.otpCodeSentAt)) / 1000;
          }

          if (diff < +SMS_COOLDOWN_SECONDS + 1) {
            return response.status(400).json({
              error: `You have to wait ${SMS_COOLDOWN_SECONDS} seconds for another code.`,
            });
          }

          request.session.user = {
            id: user.id,
          } as ExtendedUser;

          try {
            const code = otpGenerator.generate(6, {
              lowerCaseAlphabets: false,
              upperCaseAlphabets: false,
              specialChars: false,
            });

            const smsBody = `Hammr: ${code} is your code for login.`;

            // App store review phone number
            // Don't send OTP via SMS but hardcode it in the database
            if (phone == "+11234567890") {
              await user.update({ otpSmsCode: "123456", otpCodeSentAt: new Date() });
            } else {
              await sendSMS(user.phone, smsBody);
              await user.update({ otpSmsCode: code, otpCodeSentAt: new Date() });
            }

            return response.json({});
          } catch (error) {
            console.log("something went wrong ", error);

            next(error);
          }
        } else {
          return response.status(400).json({
            error: "Missing or invalid phone number",
          });
        }
      } catch (error) {
        console.log("something went wrong ", error);

        next(error);
      }
    }
  );

  app.post(
    `${basePath}/verify-otp`,
    apiIsAuthenticated,
    async (request: Request, response: Response, next: NextFunction) => {
      const { code } = request.body;

      if (!request.session.user) {
        return response.status(401).json({
          error: "Missing Auth cookie",
        });
      }

      if (code && code.length === 6) {
        const { id } = request.session.user;
        const user = (await User.findOne({
          where: { id },
          attributes: [
            "id",
            "firstName",
            "lastName",
            "otpSmsCode",
            "organizationId",
            "role",
            "email",
            "checkEmployeeId",
            "checkContractorId",
            "position",
            "workerClassification",
            "phone",
          ],
          include: [
            {
              model: EarningRate,
              as: "earningRates",
              attributes: [
                "id",
                "name",
                "amount",
                "userId",
                "period",
                "type",
                "active",
                "startDate",
                "endDate",
                "metadata",
              ],
            },
            {
              model: Organization,
              as: "organization",
              attributes: ["id", "name", "checkCompanyId", "isRegisteredOnCheck"],
              include: [
                {
                  model: TimeTrackingSettings,
                  as: "timeTrackingSettings",
                },
              ],
            },
          ],
        })) as ExtendedUser & { organization: Organization };

        if (!user || code !== user.otpSmsCode) {
          return response.status(403).json({
            error: `Wrong OTP code ${code}`,
          });
        }

        try {
          await user.update({ otpSmsCode: null, otpCodeSentAt: null, status: "ONBOARDED" });

          request.session.user.isAuthenticated = true;
          request.session.user.organizationId = user.organizationId;
          request.session.user.role = user.role;
          const regUserEarningRates = getActiveEarningRate(user?.earningRates, "REG");

          return response.json({
            data: {
              id: user.id,
              firstName: user.firstName,
              lastName: user.lastName,
              organizationId: user.organizationId,
              organizationName: user.organization?.name,
              organizationCheckCompanyId: user.organization?.checkCompanyId || null,
              organizationIsRegisteredOnCheck: user.organization?.isRegisteredOnCheck || false,
              organizationAreBreaksPaid: user.organization?.timeTrackingSettings.areBreaksPaid || false,
              role: user.role,
              email: user.email,
              checkEmployeeId: user.checkEmployeeId,
              checkContractorId: user.checkContractorId,
              position: user.position,
              hourlyRate: regUserEarningRates ? parseFloat(regUserEarningRates.amount) : 0,
              workerClassification: user.workerClassification,
              phone: user.phone,
            },
          });
        } catch (error) {
          next(error);
        }
      } else {
        return response.status(200).json({
          error: "Param code is not valid",
        });
      }
    }
  );

  app.post(`${basePath}/logout`, async (request: Request, response: Response, next: NextFunction) => {
    const { deviceToken = null } = request.body;

    try {
      if (deviceToken !== null) {
        const deviceTokensService = new DeviceTokensService();
        await deviceTokensService.delete({
          token: deviceToken,
        });
      }

      request.session.user = undefined;

      return response.json({});
    } catch (error) {
      next(error);
    }
  });
};
