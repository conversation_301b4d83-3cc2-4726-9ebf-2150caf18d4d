import { Router } from "express";

import { ChatController } from "@/controllers/chat";

export const chatRouter = Router();
const chatController = new ChatController();

chatRouter.post("/chat/announcement", chatController.createAnnouncement);
chatRouter.get("/chat/announcement/:id", chatController.getAnnouncements);
chatRouter.patch("/chat/:channelUrl/hide", chatController.update);
chatRouter.patch("/chat/:channelUrl/unhide", chatController.update);

chatRouter.get("/chat/token", chatController.getToken);
