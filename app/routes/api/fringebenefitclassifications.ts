import { Router } from "express";
import { userIsAdminOr<PERSON><PERSON><PERSON> } from "@/middlewares/authorization";
import {
  postFringeBenefitClassificationSchema,
  updateFringeBenefitClassificationSchema,
} from "@/middlewares/validation/fringebenefits";
import { validatorMw } from "@/middlewares/validation/validator";

import { FringeBenefitClassificationController } from "@/controllers/fringebenefitclassification";

export const fringeBenefitClassificationsRouter = Router();
const fringeBenefitClassificationController = new FringeBenefitClassificationController();

/* GET fringe benefit classfications. */
fringeBenefitClassificationsRouter.get(
  "/fringe-benefit-classifications",
  fringeBenefitClassificationController.get
);

/* GET one fringe benefit classfication */
fringeBenefitClassificationsRouter.get(
  "/fringe-benefit-classifications/:id",
  fringeBenefitClassificationController.getOne
);

/* PATCH fringe benefit classfication */
fringeBenefitClassificationsRouter.patch(
  "/fringe-benefit-classifications/:id",
  [userIsAdminOr<PERSON><PERSON><PERSON>, validatorMw(updateFringeBenefitClassificationSchema)],
  fringeBenefitClassificationController.update
);

fringeBenefitClassificationsRouter.post(
  "/fringe-benefit-classifications",
  [userIsAdminOrForeman, validatorMw(postFringeBenefitClassificationSchema)],
  fringeBenefitClassificationController.create
);
