import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { TimeTrackingSettingsController } from "@/controllers/timetrackingsettings";
import { validatorMw } from "@/middlewares/validation/validator";
import { updateTimeTrackingSettingsSchema } from "@/middlewares/validation/timetrackingsettings";

export const timeTrackingSettingsRouter = Router();
const timeTrackingSettingsController = new TimeTrackingSettingsController();

timeTrackingSettingsRouter.patch(
  "/time-tracking-settings",
  [userIsAdmin, validatorMw(updateTimeTrackingSettingsSchema)],
  timeTrackingSettingsController.update
);
