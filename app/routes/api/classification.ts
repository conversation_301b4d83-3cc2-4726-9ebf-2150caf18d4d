import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { ClassificationController } from "@/controllers/classification";
import { classificationBelongsToOrg } from "@/middlewares/authorization/classification";
import { wageTableBelongsToOrg } from "@/middlewares/authorization/wagetable";
import {
  createClassificationSchema,
  updateClassificationSchema,
} from "@/middlewares/validation/classification";
import { validatorMw } from "@/middlewares/validation/validator";

export const classificationRouter = Router();
const classificationController = new ClassificationController();

classificationRouter.get("/classifications", classificationController.get);

classificationRouter.post(
  "/classifications",
  [userIsAdmin, validatorMw(createClassificationSchema), wageTableBelongsToOrg],
  classificationController.create
);

classificationRouter.patch(
  "/classifications/:id",
  [userIsAdmin, validatorMw(updateClassificationSchema), wageTableBelongsToOrg, classificationBelongsToOrg],
  classificationController.update
);
