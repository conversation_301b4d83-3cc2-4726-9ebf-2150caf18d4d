import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { integrationSettingSchema } from "@/middlewares/validation/integrations";
import { IntegrationSettingsController } from "@/controllers/integrations/settings";
import { validatorMw } from "@/middlewares/validation/validator";

export const integrationSettingsRouter = Router();
const controller = new IntegrationSettingsController();

integrationSettingsRouter.get("/integrations/:platform/settings", [userIsAdmin], controller.getSettings);

integrationSettingsRouter.post(
  "/integrations/:platform/settings",
  [userIsAdmin, validatorMw(integrationSettingSchema)],
  controller.updateSetting
);
