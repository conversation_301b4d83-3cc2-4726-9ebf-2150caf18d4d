import { Router } from "express";
import TimeOffPolicyController from "../../controllers/timeoffpolicy";
import {
  createTimeOffPolicySchema,
  enrollEmployeeSchema,
  listTimeOffPoliciesSchema,
  updateFullPolicySchema,
} from "@/middlewares/validation/timeoffpolicy";
import { userIsAdmin } from "@/middlewares/authorization";
import { validateQuery, validatorMw } from "@/middlewares/validation/validator";
import modelExistsAndIsPartOfOrg from "@/middlewares/modelExistsAndIsPartOfOrg";
import TimeOffPolicy from "@/models/timeoffpolicy";
import User from "@/models/user";

const timeOffPolicyRouter = Router();
const timeOffPolicyController = new TimeOffPolicyController();

timeOffPolicyRouter.post(
  "/time-off-policies",
  userIsAdmin,
  validatorMw(createTimeOffPolicySchema),
  timeOffPolicyController.create
);
timeOffPolicyRouter.patch(
  "/time-off-policies/:id",
  userIsAdmin,
  modelExistsAndIsPartOfOrg(TimeOffPolicy),
  validatorMw(updateFullPolicySchema),
  timeOffPolicyController.update
);
timeOffPolicyRouter.get(
  "/time-off-policies/:id",
  userIsAdmin,
  modelExistsAndIsPartOfOrg(TimeOffPolicy),
  timeOffPolicyController.get
);

timeOffPolicyRouter.get(
  "/time-off-policies",
  userIsAdmin,
  validateQuery(listTimeOffPoliciesSchema),
  timeOffPolicyController.list
);

timeOffPolicyRouter.get(
  "/users/:userId/time-off-policies",
  userIsAdmin,
  validateQuery(listTimeOffPoliciesSchema),
  timeOffPolicyController.userPolicies
);
timeOffPolicyRouter.get(
  "/my/time-off-policies",
  validateQuery(listTimeOffPoliciesSchema),
  timeOffPolicyController.myPolicies
);

timeOffPolicyRouter.get("/time-off-policies/balances/:year", userIsAdmin, timeOffPolicyController.timeOffBalances);

timeOffPolicyRouter.post(
  "/time-off-policies/:id/users",
  userIsAdmin,
  modelExistsAndIsPartOfOrg(TimeOffPolicy),
  validatorMw(enrollEmployeeSchema),
  timeOffPolicyController.enrollUsers
);

timeOffPolicyRouter.delete(
  "/time-off-policies/:id/users/:userId",
  userIsAdmin,
  modelExistsAndIsPartOfOrg(TimeOffPolicy),
  modelExistsAndIsPartOfOrg(User, "userId"),
  timeOffPolicyController.removeUserEnrollment
);

timeOffPolicyRouter.post(
  "/time-off-policies/:id/archive",
  userIsAdmin,
  modelExistsAndIsPartOfOrg(TimeOffPolicy),
  timeOffPolicyController.archive
);

timeOffPolicyRouter.post(
  "/time-off-policies/:id/unarchive",
  userIsAdmin,
  modelExistsAndIsPartOfOrg(TimeOffPolicy),
  timeOffPolicyController.unarchive
);

export default timeOffPolicyRouter;
