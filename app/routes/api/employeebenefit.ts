import { Router } from "express";
import { userIsAdminOr<PERSON>oreman } from "@/middlewares/authorization";
import {
  createEmployeeBenefitSchema,
  updateEmployeeBenefitSchema,
  deactivateEmployeeBenefitSchema,
  validateNoPreExistingDynamicBenefit,
} from "@/middlewares/validation/benefits";
import { employeeBenefitBelongsToOrg, companyBenefitBelongsToOrg } from "@/middlewares/authorization/benefit";
import { checkResourceBelongsToOrg } from "@/middlewares/authorization/helpers";
import { fetchTargetedCompanyBenefit, fetchTargetedEmployeeBenefit } from "@/middlewares/targeted-resource";
import { validatorMw } from "@/middlewares/validation/validator";

import { EmployeeBenefitController } from "@/controllers/employeebenefit";

export const employeeBenefitsRouter = Router();
const employeeBenefitsController = new EmployeeBenefitController();

/* GET employee benefits. */
employeeBenefitsRouter.get("/employee-benefits", employeeBenefitsController.get);

/* GET one employee benefit */
employeeBenefitsRouter.get("/employee-benefits/:id", employeeBenefitsController.getOne);

/* POST employee benefit */
employeeBenefitsRouter.post(
  "/employee-benefits",
  [
    userIsAdminOrForeman,
    fetchTargetedCompanyBenefit,
    validatorMw(createEmployeeBenefitSchema),
    companyBenefitBelongsToOrg,
    checkResourceBelongsToOrg,
    validateNoPreExistingDynamicBenefit,
  ],
  employeeBenefitsController.create
);

/* PATCH employee benefit */
employeeBenefitsRouter.patch(
  "/employee-benefits/:id",
  [
    userIsAdminOrForeman,
    fetchTargetedEmployeeBenefit,
    employeeBenefitBelongsToOrg,
    validatorMw(updateEmployeeBenefitSchema),
    checkResourceBelongsToOrg,
  ],
  employeeBenefitsController.update
);

/* PATCH - deactivate employee benefit */
// prefer more robust authorization checks like targeted employee benefit is owned by user that is part of the org that owns the company benefit - TBD on this
employeeBenefitsRouter.patch(
  "/employee-benefits/:id/deactivate",
  [userIsAdminOrForeman, fetchTargetedEmployeeBenefit, validatorMw(deactivateEmployeeBenefitSchema)],
  employeeBenefitsController.deactivate
);
