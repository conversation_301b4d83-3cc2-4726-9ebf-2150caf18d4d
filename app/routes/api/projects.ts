import { Router } from "express";
import { userIsAdmin<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/middlewares/authorization";
import { ProjectsController } from "@/controllers/projects";
import { createProjectSchema, updateProjectSchema } from "@/middlewares/validation/projects";
import { projectBelongsToOrg } from "@/middlewares/authorization/project";
import { validatorMw } from "@/middlewares/validation/validator";

export const projectsRouter = Router();
const projectsController = new ProjectsController();

projectsRouter.get("/projects", [], projectsController.get);
projectsRouter.get("/projects/:id", [projectBelongsToOrg], projectsController.getOne);
projectsRouter.get("/projects/:id/project-actuals", [projectBelongsToOrg], projectsController.getOneProjectActuals);

projectsRouter.post("/projects", [userIsAdminOr<PERSON><PERSON><PERSON>, validatorMw(createProjectSchema)], projectsController.create);

projectsRouter.patch(
  "/projects/:id",
  [validatorMw(updateProjectSchema), projectBelongsToOrg],
  projectsController.update
);
