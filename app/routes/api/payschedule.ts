import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { PayScheduleController } from "@/controllers/payschedule";
import { createPayScheduleSchema } from "@/middlewares/validation/payschedule";
import { validatorMw } from "@/middlewares/validation/validator";

export const payScheduleRouter = Router();
const payScheduleController = new PayScheduleController();

payScheduleRouter.get("/pay-schedule", [userIsAdmin], payScheduleController.get);

payScheduleRouter.post(
  "/pay-schedule",
  [userIsAdmin, validatorMw(createPayScheduleSchema)],
  payScheduleController.update
);
