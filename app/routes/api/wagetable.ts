import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { WageTableController } from "@/controllers/wagetable";
import { wageTableBelongsToOrg } from "@/middlewares/authorization/wagetable";
import { createWageTableSchema, updateWageTableSchema } from "@/middlewares/validation/wagetable";
import { validatorMw } from "@/middlewares/validation/validator";

export const wageTableRouter = Router();
const wageTableController = new WageTableController();

/* GET wage tables. */
wageTableRouter.get("/wage-tables", wageTableController.get);

/* GET one wage table */
wageTableRouter.get("/wage-tables/:id", wageTableController.getOne);

/* Get wage table benefit statement */
wageTableRouter.get(
  "/wage-tables/:id/benefit-statement",
  [userIsAdmin, wageTableBelongsToOrg],
  wageTableController.getBenefitsStatement
);

/* Get wage table benefit report */
wageTableRouter.get(
  "/wage-tables/:id/benefit-report",
  [userIsAdmin, wageTableBelongsToOrg],
  wageTableController.getBenefitsReport
);

/* POST wage table */
wageTableRouter.post(
  "/wage-tables",
  [userIsAdmin, validatorMw(createWageTableSchema)],
  wageTableController.create
);

/* PATCH wage table */
wageTableRouter.patch(
  "/wage-tables/:id",
  [userIsAdmin, wageTableBelongsToOrg, validatorMw(updateWageTableSchema)],
  wageTableController.update
);
