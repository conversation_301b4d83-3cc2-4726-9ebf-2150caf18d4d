import { Router } from "express";

import { checkPayrollResourceBelongsToOrg } from "@/middlewares/authorization/check";
import { orgGrantedPayrollAccess } from "@/middlewares/authorization/payrolls";
import { userIsAdmin } from "@/middlewares/authorization";
import { validatorMw } from "@/middlewares/validation/validator";
import { createPayrollSchema } from "@/middlewares/validation/payrolls";

import { PayrollsController } from "@/controllers/payrolls";

export const payrollsRouter = Router();
const payrollsController = new PayrollsController();

payrollsRouter.get(
  "/payrolls/:id",
  [userIsAdmin, orgGrantedPayrollAccess, checkPayrollResourceBelongsToOrg],
  payrollsController.getOne
);

payrollsRouter.post(
  "/payrolls",
  [userIsAdmin, orgGrantedPayrollAccess, validatorMw(createPayrollSchema)],
  payrollsController.create
);

payrollsRouter.patch(
  "/payrolls/:id",
  [userIsAdmin, orgGrantedPayrollAccess, checkPayrollResourceBelongsToOrg],
  payrollsController.update
);

payrollsRouter.post(
  "/payrolls/:id/approve",
  [userIsAdmin, orgGrantedPayrollAccess, checkPayrollResourceBelongsToOrg],
  payrollsController.approve
);
