import { Router } from "express";
import { userIsAdminOr<PERSON><PERSON><PERSON> } from "@/middlewares/authorization";
import { validateCreateEarningRate, validateGetAllEarningRates } from "@/middlewares/validation/earningrate";
import { earningRateBelongsToOrg } from "@/middlewares/authorization/earningrate";
import { checkResourceBelongsToOrg } from "@/middlewares/authorization/helpers";

import { EarningRatesController } from "@/controllers/earningrate";

export const earningRatesRouter = Router();
const earningRatesController = new EarningRatesController();

/* GET earning rates. */
earningRatesRouter.get("/earning-rates", [validateGetAllEarningRates], earningRatesController.get);

/* GET one earning rate */
earningRatesRouter.get("/earning-rates/:id", [earningRateBelongsToOrg], earningRatesController.getOne);

/* POST earning rate */
earningRatesRouter.post(
  "/earning-rates",
  [
    userIsAdminOr<PERSON><PERSON>man, // permissions check
    checkResourceBelongsToOrg, // make sure employee belongs to org
    earningRateBelongsToOrg, // make sure earning rate belongs to org
    validateCreateEarningRate,
  ],
  earningRatesController.create
);
