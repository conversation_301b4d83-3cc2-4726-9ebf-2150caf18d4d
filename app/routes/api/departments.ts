import { Router } from "express";
import { DepartmentsController } from "@/controllers/departments";
import { validatorMw } from "@/middlewares/validation/validator";
import {
  departmentSchema,
  updateDepartmentMemberSchema,
  updateDepartmentSchema,
  bulkAssignDepartmentMembersSchema,
} from "@/middlewares/validation/departments";
import modelExistsAndIsPartOfOrg from "@/middlewares/modelExistsAndIsPartOfOrg";
import { Department } from "@/models/index";

export const departmentsRouter = Router();
const departmentsController = new DepartmentsController();

departmentsRouter.get("/departments", departmentsController.get);
departmentsRouter.post("/departments", [validatorMw(departmentSchema)], departmentsController.create);
departmentsRouter.patch("/departments/:id", [validatorMw(updateDepartmentSchema)], departmentsController.update);
departmentsRouter.delete("/departments/:id", [modelExistsAndIsPartOfOrg(Department)], departmentsController.delete);
departmentsRouter.patch(
  "/department/members",
  [validatorMw(updateDepartmentMemberSchema)],
  departmentsController.updateDepartmentMember
);
departmentsRouter.patch(
  "/department/members/bulk-assign",
  [validatorMw(bulkAssignDepartmentMembersSchema)],
  departmentsController.bulkAssignDepartmentMembers
);
