import { Router } from "express";

import { userIsAdmin, userIsPartofOrg } from "@/middlewares/authorization";
import { FeatureFlagsController } from "@/controllers/featureflags";

export const featureFlagsRouter = Router();
const featureFlagsController = new FeatureFlagsController();

featureFlagsRouter.patch(
  "/organizations/:id/feature-flags",
  [userIsPartofOrg, userIsAdmin],
  featureFlagsController.update
);
