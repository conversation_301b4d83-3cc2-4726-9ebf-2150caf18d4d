import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { GlAccountMappingSettingsController } from "@/controllers/glaccountmappingsetting";

import { validatorMw } from "@/middlewares/validation/validator";
import {
  createGlAccountMappingSettingSchema,
  updateGlAccountMappingSettingSchema,
  validateGlAccountMappingSetting,
} from "@/middlewares/validation/glaccountmappingsetting";

export const glAccountMappingSettingsRouter = Router();
const glAccountMappingSettingsController = new GlAccountMappingSettingsController();

/* GET gl account mapping settings */
glAccountMappingSettingsRouter.get(
  "/gl-account-mapping-settings",
  [userIsAdmin],
  glAccountMappingSettingsController.get
);

/* Create new gl account mapping setting */
glAccountMappingSettingsRouter.post(
  "/gl-account-mapping-settings",
  [userIsAdmin, validatorMw(createGlAccountMappingSettingSchema), validateGlAccountMappingSetting],
  glAccountMappingSettingsController.create
);

/* Update gl account mapping setting */
glAccountMappingSettingsRouter.patch(
  "/gl-account-mapping-settings/:id",
  [userIsAdmin, validatorMw(updateGlAccountMappingSettingSchema), validateGlAccountMappingSetting],
  glAccountMappingSettingsController.update
);
