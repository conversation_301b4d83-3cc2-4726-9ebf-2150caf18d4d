import { Router } from "express";

import { EquipmentCategoryController } from "@/controllers/equipmentcategory";
import { userIsAdminOrForeman } from "@/middlewares/authorization";

export const equipmentCategoryRouter = Router();
const equipmentCategoryController = new EquipmentCategoryController();

/* GET all equipment categories for organization */
equipmentCategoryRouter.get("/equipment-categories", [userIsAdminOrForeman], equipmentCategoryController.get);
