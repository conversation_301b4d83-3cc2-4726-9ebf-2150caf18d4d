import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { IntegrationSyncHistoryController } from "@/controllers/integrationsynchistory";

export const integrationSyncHistoryRouter = Router();
const integrationSyncHistoryController = new IntegrationSyncHistoryController();

/* GET integration sync history */
integrationSyncHistoryRouter.get(
  "/integration-sync-history/:integrationUserTokenId",
  [userIsAdmin],
  integrationSyncHistoryController.get
);
