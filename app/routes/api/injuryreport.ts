import { Router } from "express";
import { InjuryReportController } from "@/controllers/injuryreport";
import { validatorMw } from "@/routes/middlewares/validation/validator";
import { injuryReportSchema, injuryReportUpdateSchema } from "@/routes/middlewares/validation/injuryreport";

export const injuryReportRouter = Router();
const injuryReportController = new InjuryReportController();

injuryReportRouter.get("/injury-reports", injuryReportController.get);
injuryReportRouter.get("/injury-reports/list", injuryReportController.list);
injuryReportRouter.post("/injury-reports", [validatorMw(injuryReportSchema)], injuryReportController.create);
injuryReportRouter.patch("/injury-reports/:id", [validatorMw(injuryReportUpdateSchema)], injuryReportController.update);
