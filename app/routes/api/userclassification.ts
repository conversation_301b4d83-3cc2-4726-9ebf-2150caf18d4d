import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { UserClassificationController } from "@/controllers/userclassification";
import {
  classificationBelongsToOrg,
  userClassificationsBelongsToOrg,
} from "@/middlewares/authorization/classification";
import {
  userClassificationSchema,
  unassignUserClassificationSchema,
  updateUserClassificationSchema,
} from "@/middlewares/validation/userclassification";
import { validatorMw } from "@/middlewares/validation/validator";
import { usersBelongsToOrg } from "@/middlewares/authorization/user";

export const userClassificationRouter = Router();
const userClassificationController = new UserClassificationController();

userClassificationRouter.get("/user-classifications", userClassificationController.get);

userClassificationRouter.post(
  "/user-classifications/assign",
  [userIsAdmin, validatorMw(userClassificationSchema), classificationBelongsToOrg, usersBelongsToOrg],
  userClassificationController.assign
);

userClassificationRouter.post(
  "/user-classifications/unassign",
  [userIsAdmin, validatorMw(unassignUserClassificationSchema), userClassificationsBelongsToOrg],
  userClassificationController.unassign
);

userClassificationRouter.patch(
  "/user-classifications",
  [userIsAdmin, validatorMw(updateUserClassificationSchema)],
  userClassificationController.update
);
