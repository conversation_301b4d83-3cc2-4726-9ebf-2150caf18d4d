import { Router } from "express";

import { OrganizationsController } from "@/controllers/organizations";
import { userIsAdmin, userIsPartofOrg } from "@/middlewares/authorization";
import { conditionalCheckReqMiddlware } from "@/middlewares/evaluate-check-req-mw";

export const organizationsRouter = Router();
const organizationsController = new OrganizationsController();

/* GET one organization. */
// Note: The assignment operation below simulates loss of context - this is why we need to bind methods in controller
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_objects/Function/bind
organizationsRouter.get("/organizations/:id", [userIsPartofOrg], organizationsController.getOne);

organizationsRouter.patch(
  "/organizations/:id",
  [userIsPartofOrg, userIsAdmin, conditionalCheckReqMiddlware],
  organizationsController.update
);
