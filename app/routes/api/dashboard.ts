import dayjs from "dayjs";
import { Op } from "sequelize";
import { NextFunction, Request, Response } from "express";
import { Break, EarningRate, Project, TimeSheet, User } from "@/models";
import { calculateUsersEarnings, calculateWagesByProject } from "@/util/financial";
import { userIsAdminOrForeman } from "@/middlewares/authorization";
import { TimesheetsService } from "@/services/timesheets";
import { ExtendedTimesheet } from "@/models/timesheet";
import { OrganizationsService } from "@/services/organizations";

const organizationsService = new OrganizationsService();

export const initDashboardRoutes = (app: any, basePath: string) => {
  const BASE_RESOURCE = "dashboard";
  const timesheetsService = new TimesheetsService();

  app.get(
    `${basePath}/${BASE_RESOURCE}/employees-hours-wages`,
    [userIsAdminOrForeman],
    async (request: Request, response: Response, next: NextFunction) => {
      const organizationId = request.session.user.organizationId;

      const from = parseInt(request.query.from as string);
      const to = parseInt(request.query.to as string);

      if (!from || !to) {
        return response.status(400).json({
          error: "Missing params",
        });
      }

      const fromDate = from && dayjs(from).toISOString();
      const toDate = to && dayjs(to).toISOString();

      try {
        const users = await User.findAll({
          where: {
            organizationId,
          },
          raw: false, //DO NOT SET IT TO TRUE: https://github.com/sequelize/sequelize/issues/3885
          nest: true,
          attributes: ["id", "firstName", "lastName", "checkEmployeeId", "checkContractorId"],
          include: [
            {
              model: TimeSheet,
              where: {
                organizationId,
                clockIn: {
                  [Op.between]: [fromDate, toDate],
                },
                isDeleted: false,
              },
              attributes: ["clockIn", "clockOut", "breakDuration"],
              required: true,
              include: [
                {
                  model: Break,
                  where: {
                    isDeleted: false,
                  },
                  attributes: ["id", "start", "end", "timesheetId", "isManual"],
                  required: false,
                },
              ],
            },
            {
              model: EarningRate,
              as: "earningRates",
              where: {
                organizationId,
                active: true,
                type: "REG",
              },
              required: false,
            },
          ],
        });

        const organization = await organizationsService.findOne({
          where: { id: organizationId },
        });

        const { totalMinutes, totalWages, employees } = calculateUsersEarnings(users, organization, {
          expand: null,
        });

        return response.json({
          data: {
            totalMinutes: totalMinutes,
            totalWages: Math.ceil(totalWages),
            employees,
          },
        });
      } catch (error) {
        next(error);
      }
    }
  );

  /**
   * This endpoint is used by the mobile apps.
   */
  app.get(`${basePath}/${BASE_RESOURCE}/projects`, [userIsAdminOrForeman], async (request: any, response: any) => {
    const organizationId = request.session.user.organizationId;

    const from = parseInt(request.query.from);
    const to = parseInt(request.query.to);

    if (!from || !to) {
      return response.status(400).json({
        error: "Missing params",
      });
    }

    const fromDate = from && dayjs(from).toISOString();
    const toDate = to && dayjs(to).toISOString();

    const projects = await Project.findAll({
      where: {
        organizationId,
      },
      raw: false,
      nest: true,
      attributes: ["id", "name", "projectNumber", "address"],
      include: [
        {
          model: TimeSheet,
          where: {
            organizationId,
            clockIn: {
              [Op.between]: [fromDate, toDate],
            },
            isDeleted: false,
          },
          attributes: ["clockIn", "clockOut", "breakDuration"],
          required: true,
          include: [
            {
              model: User,
              include: [
                {
                  model: EarningRate,
                  as: "earningRates",
                  where: {
                    organizationId,
                    active: true,
                    type: "REG",
                  },
                  required: false,
                },
              ],
            },
            {
              model: Break,
              where: {
                isDeleted: false,
              },
              attributes: ["id", "start", "end", "timesheetId", "isManual"],
              required: false,
            },
          ],
        },
      ],
    });

    let totalMinutes = 0;
    let totalWages = 0;
    const projectsResponse: any = [];

    const organization = await organizationsService.findOne({
      where: { id: organizationId },
    });

    projects.forEach((project: any) => {
      let totalProjectWages = 0;
      let totalProjectMinutes = 0;
      project.timesheets.forEach((timesheet: any) => {
        const { projectWages, projectMinutes } = calculateWagesByProject([timesheet], organization, timesheet.user);
        totalProjectMinutes += projectMinutes;
        totalProjectWages += projectWages;
      });

      totalMinutes += totalProjectMinutes;
      totalWages += totalProjectWages;

      projectsResponse.push({
        id: project.id,
        name: project.name,
        totalMinutes: totalProjectMinutes,
        totalWages: Math.ceil(totalProjectWages),
      });
    });

    return response.json({
      data: {
        totalMinutes: totalMinutes,
        totalWages: Math.ceil(totalWages),
        projects: projectsResponse,
      },
    });
  });

  app.get(
    `${basePath}/${BASE_RESOURCE}/project-activity`,
    [userIsAdminOrForeman],
    async (request: Request, response: Response, next: NextFunction) => {
      try {
        const from = parseInt(request.query.from as string);
        const to = parseInt(request.query.to as string);

        if (!from || !to) {
          return response.status(400).json({
            error: "The 'from' and 'to' params are required",
          });
        }

        const timesheets = await timesheetsService.list(
          request.organization,
          {
            from,
            to,
          },
          false
        );

        const projects = getProjectActivity(timesheets.timesheets);

        return response.json({
          data: projects.slice(0, 5), // Get top 5 projects
        });
      } catch (error) {
        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/notifications`,
    [userIsAdminOrForeman],
    async (request: Request, response: Response, next: NextFunction) => {
      try {
        const unresolvedTimesheets = await timesheetsService
          .list(request.organization, {
            isResolved: false,
          })
          .then((response) => response.timesheets);

        const unapprovedTimesheets = await timesheetsService
          .list(request.organization, {
            status: "SUBMITTED",
          })
          .then((response) => response.timesheets);

        const oldestSubmittedTimesheet = unapprovedTimesheets.reduce((oldest, current) => {
          return !oldest || current.clockIn < oldest.clockIn ? current : oldest;
        }, undefined);

        const newestSubmittedTimesheet = unapprovedTimesheets.reduce((newest, current) => {
          return !newest || current.clockIn > newest.clockIn ? current : newest;
        }, undefined);

        const oldestTimesheetWithAlerts = unresolvedTimesheets.reduce((oldest, current) => {
          return !oldest || current.clockIn < oldest.clockIn ? current : oldest;
        }, undefined);

        const newestTimesheetWithAlerts = unresolvedTimesheets.reduce((newest, current) => {
          return !newest || current.clockIn > newest.clockIn ? current : newest;
        }, undefined);

        return response.json({
          data: {
            unresolvedTimesheets: {
              count: unresolvedTimesheets.length,
              from: oldestTimesheetWithAlerts ? oldestTimesheetWithAlerts.clockIn : null,
              to: newestTimesheetWithAlerts ? newestTimesheetWithAlerts.clockOut : null,
            },
            unapprovedTimesheets: {
              count: unapprovedTimesheets.length,
              from: oldestSubmittedTimesheet ? oldestSubmittedTimesheet.clockIn : null,
              to: newestSubmittedTimesheet ? newestSubmittedTimesheet.clockOut : null,
            },
          },
        });
      } catch (error) {
        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/projects/:projectId`,
    [userIsAdminOrForeman],
    async (request: Request, response: Response, next: NextFunction) => {
      const organizationId = request.session.user.organizationId;

      const from = parseInt(request.query.from as string);
      const to = parseInt(request.query.to as string);

      if (!from || !to) {
        return response.status(400).json({
          error: "Missing params",
        });
      }

      const fromDate = from && dayjs(from).toISOString();
      const toDate = to && dayjs(to).toISOString();

      try {
        const users: any = await User.findAll({
          where: {
            organizationId,
          },
          raw: false,
          nest: true,
          attributes: ["id", "firstName", "lastName"],
          include: [
            {
              model: TimeSheet,
              where: {
                organizationId,
                clockIn: {
                  [Op.between]: [fromDate, toDate],
                },
                isDeleted: false,
              },
              attributes: ["clockIn", "clockOut", "breakDuration"],
              required: true,
              include: [
                {
                  model: Project,
                  where: {
                    organizationId,
                    id: request.params.projectId,
                  },
                  attributes: ["id", "name", "projectNumber", "address"],
                  required: true,
                },
                {
                  model: Break,
                  where: {
                    isDeleted: false,
                  },
                  attributes: ["id", "start", "end", "timesheetId", "isManual"],
                  required: false,
                },
              ],
            },
            {
              model: EarningRate,
              as: "earningRates",
              where: {
                organizationId,
                active: true,
                type: "REG",
              },
              required: false,
            },
          ],
        });

        // this will happen if there's not timesheets for the given project
        if (users.length === 0) {
          const project = await Project.findOne({
            where: {
              organizationId,
              id: request.params.projectId,
            },
            raw: false,
            nest: true,
            attributes: ["id", "name"],
          });

          if (project) {
            return response.json({
              data: {
                id: project.id,
                name: project.name,
                totalMinutes: 0,
                totalWages: 0,
                employees: [],
              },
            });
          } else {
            return response.status(400).json({
              error: "Project not found",
            });
          }
        }

        const organization = await organizationsService.findOne({
          where: { id: organizationId },
        });

        const { totalMinutes, totalWages, employees } = calculateUsersEarnings(users, organization, {
          expand: null,
        });

        const projectName = users[0].timesheets[0].project.name;
        const projectId = users[0].timesheets[0].project.id;

        return response.json({
          data: {
            id: projectId,
            name: projectName,
            totalMinutes: totalMinutes,
            totalWages: Math.ceil(totalWages),
            employees: employees,
          },
        });
      } catch (error) {
        next(error);
      }
    }
  );
};

function getProjectActivity(timesheets: Partial<ExtendedTimesheet>[]) {
  const projectMap = new Map<
    number,
    {
      id: number;
      name: string;
      regularMinutes: number;
      overtimeMinutes: number;
    }
  >();

  timesheets.forEach((timesheet) => {
    if (!timesheet.project) return;

    const projectId = timesheet.project.id;
    const existingProject = projectMap.get(projectId);

    if (existingProject) {
      existingProject.regularMinutes += timesheet.regularMinutes || 0;
      existingProject.overtimeMinutes += timesheet.overtimeMinutes || 0;
    } else {
      projectMap.set(projectId, {
        id: projectId,
        name: timesheet.project.name,
        regularMinutes: timesheet.regularMinutes || 0,
        overtimeMinutes: timesheet.overtimeMinutes || 0,
      });
    }
  });

  // sort by total minutes (regular + overtime)
  return Array.from(projectMap.values()).sort((a, b) => {
    const totalMinutesA = a.regularMinutes + a.overtimeMinutes;
    const totalMinutesB = b.regularMinutes + b.overtimeMinutes;

    return totalMinutesB - totalMinutesA;
  });
}
