import { Router } from "express";
import TimeOffRequestController from "@/controllers/timeoffrequest";
import {
  createTimeOffRequestSchema,
  declineTimeOffRequestSchema,
  listTimeOffRequestsSchema,
  updateTimeOffRequestSchema,
} from "@/middlewares/validation/timeoffrequest";
import { validateQuery, validatorMw } from "@/middlewares/validation/validator";
import { userIsAdmin, userIsAdminOrForeman } from "@/middlewares/authorization";
import { timeOffRequestBelongsToOrg } from "@/middlewares/authorization/timeoffrequest";

const timeOffRequestRouter = Router();
const timeOffRequestController = new TimeOffRequestController();

timeOffRequestRouter.post(
  "/time-off-requests/",
  validatorMw(createTimeOffRequestSchema),
  timeOffRequestController.create
);

timeOffRequestRouter.get(
  "/my/time-off-requests",
  validateQuery(listTimeOffRequestsSchema),
  timeOffRequestController.getMyRequests
);

timeOffRequestRouter.put(
  "/time-off-requests/:id",
  userIsAdmin,
  timeOffRequestBelongsToOrg,
  validatorMw(updateTimeOffRequestSchema),
  timeOffRequestController.update
);

timeOffRequestRouter.get(
  "/time-off-requests/:id",
  userIsAdmin,
  timeOffRequestBelongsToOrg,
  timeOffRequestController.get
);

timeOffRequestRouter.get(
  "/time-off-requests/",
  userIsAdminOrForeman,
  validateQuery(listTimeOffRequestsSchema),
  timeOffRequestController.list
);

timeOffRequestRouter.get(
  "/users/:userId/time-off-requests",
  userIsAdmin,
  validateQuery(listTimeOffRequestsSchema),
  timeOffRequestController.getUserRequests
);

timeOffRequestRouter.post(
  "/time-off-requests/:id/approve",
  userIsAdmin,
  timeOffRequestBelongsToOrg,
  timeOffRequestController.approve
);

timeOffRequestRouter.post(
  "/time-off-requests/:id/decline",
  userIsAdmin,
  timeOffRequestBelongsToOrg,
  validatorMw(declineTimeOffRequestSchema),
  timeOffRequestController.decline
);

timeOffRequestRouter.delete(
  "/time-off-requests/:id",
  userIsAdmin,
  timeOffRequestBelongsToOrg,
  timeOffRequestController.delete
);

export default timeOffRequestRouter;
