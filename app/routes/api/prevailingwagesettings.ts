import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { PrevailingWageSettingsController } from "@/controllers/prevailingwagesettings";
import { validatorMw } from "@/middlewares/validation/validator";
import { updatePrevailingWageSettingsSchema } from "@/middlewares/validation/prevailingwagesettings";

export const prevailingWageSettingsRouter = Router();
const prevailingWageSettingsController = new PrevailingWageSettingsController();

prevailingWageSettingsRouter.patch(
  "/prevailing-wage-settings",
  [userIsAdmin, validatorMw(updatePrevailingWageSettingsSchema)],
  prevailingWageSettingsController.update
);
