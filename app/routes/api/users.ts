import { Router } from "express";

import { userIsAdmin } from "@/middlewares/authorization";
import { conditionalCheckReqMiddlware } from "@/middlewares/evaluate-check-req-mw";
import { UsersController } from "@/controllers/users";
import { validatorMw } from "@/middlewares/validation/validator";
import { sendOnboardingLinkSchema } from "@/middlewares/validation/user";

export const usersRouter = Router();
const usersController = new UsersController();

/* GET users. */
// Note: The assignment operation below simulates loss of context - this is why we need to bind methods in controller
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_objects/Function/bind
usersRouter.get("/users", usersController.get);

usersRouter.get("/users/schedule", usersController.getWithSchedule);
usersRouter.get("/users/locations", [userIsAdmin], usersController.getUsersLocations);

/* GET one user */
usersRouter.get("/users/:id", usersController.getOne);

/* POST user */
usersRouter.post("/users", [userIsAdmin], conditionalCheckReqMiddlware, usersController.create);

/* UPDATE user */
usersRouter.patch("/users/:id", [conditionalCheckReqMiddlware], usersController.update);

/* Resend invite to user */
usersRouter.post("/users/:id/resend-invite", usersController.resendInvite);

usersRouter.post(
  "/users/:id/send-onboarding-link",
  [userIsAdmin, validatorMw(sendOnboardingLinkSchema)],
  usersController.sendOnboardingLink
);

/* Create employee or contractor or check */
usersRouter.post(`/users/:id/add-to-payroll`, [conditionalCheckReqMiddlware], usersController.addToPayroll);

/* GET user's employee benefits */
usersRouter.get("/users/:id/employee-benefits", [userIsAdmin], usersController.getEmployeeBenefits);
