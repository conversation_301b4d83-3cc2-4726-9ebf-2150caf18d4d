import { Router } from "express";
import { userIsAdmin } from "@/middlewares/authorization";
import { GlAccountMappingsController } from "@/controllers/glaccountmapping";
import {
  createGlAccountMappingSchema,
  updateGlAccountMappingSchema,
  departmentMappingsSchema,
} from "@/middlewares/validation/glaccountmapping";
import { validatorMw } from "@/middlewares/validation/validator";

export const glAccountMappingsRouter = Router();
const glAccountMappingsController = new GlAccountMappingsController();

/* GET all gl account mappings */
glAccountMappingsRouter.get("/gl-account-mappings", [userIsAdmin], glAccountMappingsController.get);

/* GET all departments that have custom mappings */
glAccountMappingsRouter.get(
  "/gl-account-mappings/departments",
  [userIsAdmin],
  glAccountMappingsController.getDepartmentsWithCustomMappings
);

/* Upsert gl account mappings for a department */
glAccountMappingsRouter.post(
  "/gl-account-mappings/departments/:departmentId",
  [userIsAdmin, validatorMw(departmentMappingsSchema)],
  glAccountMappingsController.upsertDepartmentMappings
);

/* DELETE all gl account mappings for a department */
glAccountMappingsRouter.delete(
  "/gl-account-mappings/departments/:departmentId",
  [userIsAdmin],
  glAccountMappingsController.deleteDepartmentMappings
);

/* Trigger incremental sync */
glAccountMappingsRouter.post(
  "/gl-account-mappings/trigger-sync",
  [userIsAdmin /* Add validation if needed */],
  glAccountMappingsController.triggerIncrementalSync
);

/* Create new gl account mapping */
glAccountMappingsRouter.post(
  "/gl-account-mappings",
  [userIsAdmin, validatorMw(createGlAccountMappingSchema)],
  glAccountMappingsController.create
);

/* Update gl account mapping */
glAccountMappingsRouter.patch(
  "/gl-account-mappings/:id",
  [userIsAdmin, validatorMw(updateGlAccountMappingSchema)],
  glAccountMappingsController.update
);
