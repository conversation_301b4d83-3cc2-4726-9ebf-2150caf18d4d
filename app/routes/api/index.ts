import * as Sentry from "@sentry/node";
import { nodeProfilingIntegration } from "@sentry/profiling-node";
import cors, { CorsOptions } from "cors";
import express, { NextFunction, Request, RequestHandler, Response } from "express";
import cookieParser from "cookie-parser";
import bodyParser from "body-parser";
import { has } from "lodash";
import { apiIsAuthenticated, appVersionIsSupported, expressSession, userIsAuthenticated } from "@/middlewares/session";
import { checkProxyRouter } from "@/middlewares/check-proxy";

import { initAuthRoutes } from "@/routes/api/authentication";
import { initTimesheetsRoutes } from "@/routes/api/timesheets";
import { initMigrationRoutes } from "@/routes/api/csvRawImport";
import { initDashboardRoutes } from "@/routes/api/dashboard";
import { initReportRoutes } from "@/routes/api/reports";

import { initDailyStats } from "@/crons/dailyStats";

import { usersRouter } from "@/routes/api/users";
import { costCodesRouter } from "@/routes/api/costcodes";
import { projectsRouter } from "@/routes/api/projects";
import { organizationsRouter } from "@/routes/api/organizations";
import { scheduleRouter } from "@/routes/api/schedule";

import { organizationMiddleware } from "@/middlewares/organization";

import { initSchedulingNotifications } from "@/crons/scheduleNotifications";
import { projectPhotosRouter } from "./projectphotos";
import { projectPhotosCollectionRouter } from "./projectphotoscollection";
import { stsRouter } from "./sts";
import { deviceTokenRouter } from "@/routes/api/devicetoken";
import { crewsRouter } from "./crews";
import { featureFlagsRouter } from "./featureflags";
import { initStripeIntegration } from "@/crons/stripe";
import { initQuickBooksProjectsSync } from "@/crons/quickBooksProjectsSync";
import { stripeRouter } from "./stripe";
import { projectDocumentRouter } from "./projectdocuments";
import { chatRouter } from "./chat";
import { userLocationRouter } from "./userlocation";
import { timesheetAlertRouter } from "./timesheetalert";
import { earningRatesRouter } from "./earningrate";
import { companyBenefitsRouter } from "./companybenefit";
import { employeeBenefitsRouter } from "./employeebenefit";
import { wageTableRouter } from "./wagetable";
import { classificationRouter } from "./classification";
import { userClassificationRouter } from "./userclassification";
import { fringeBenefitsRouter } from "./fringebenefits";
import { fringeBenefitClassificationsRouter } from "./fringebenefitclassifications";
import { payrollsRouter } from "./payrolls";
import { integrationUserTokensRouter } from "./integrationusertoken";
import { userAccessIsAllowed, refreshUserRoleIfNeeded } from "@/middlewares/authorization";
import { glAccountMappingsRouter } from "./glaccountmapping";
import { glAccountMappingSettingsRouter } from "./glaccountmappingsetting";
import { integrationSyncHistoryRouter } from "./integrationsynchistory";
import { webhooksRouter } from "./webhooks";
import { payScheduleRouter } from "./payschedule";
import { timeTrackingSettingsRouter } from "./timetrackingsettings";
import { overtimeSettingsRouter } from "./overtimesettings";
import { prevailingWageSettingsRouter } from "./prevailingwagesettings";
import { workersCompCodeRouter } from "@/routes/api/workerscompcode";
import { employeeDocumentRouter } from "./employeedocuments";
import { employeeCertificationRouter } from "./employeecertifications";
import HTTPError from "../../errors/HTTPError";
import timeOffPolicyRouter from "@/routes/api/timeoffpolicy";
import timeOffRequestRouter from "@/routes/api/timeoffrequest";
import { dailyReportRouter } from "./dailyreports";
import employeeReimbursementRouter from "./employeereimbursement";
import { departmentsRouter } from "./departments";
import { injuryReportRouter } from "./injuryreport";
import { equipmentRouter } from "./equipment";
import { equipmentCategoryRouter } from "./equipmentcategory";

import { integrationSettingsRouter } from "@/routes/api/integrations/settings";
import { serve } from "inngest/express";
import { basePath } from "@/util/env";
import { inngest } from "../../queueing/client";
import { syncTimesheets } from "../../queueing/functions/integrations/syncTimesheets";
import { createEmployee } from "../../queueing/functions/integrations/createEmployee";
import { updateEmployee } from "../../queueing/functions/integrations/updateEmployee";
import { syncEmployees } from "../../queueing/functions/integrations/syncEmployees";
import { initWorkmansDashboardProjectsSync } from "@/crons/workmansDashboardProjectsSync";

const allowedHosts = process.env.ALLOWED_ORIGINS?.split(",") ?? [];

// maybe differentiate between deployed allowedOrigins and local/dev allowedOrigins
const allowedOrigins = (env: string) => {
  switch (env) {
    case "DEVELOPMENT":
      return [
        "http://localhost:3000",
        "http://localhost:3001",
        "https://app.hammr.com",
        "https://api.hammr.com",
        ...allowedHosts,
      ];
    case "STAGING":
      // may have to rethink this one for staging
      return [
        /^https:\/\/.*\.vercel\.app$/,
        "https://appstaging.hammr.com",
        "https://punchout.hammr.com",
        "https://api.hammr.com",
      ];
    case "PRODUCTION":
      return ["https://app.hammr.com", "https://api.hammr.com"];
    default:
      return [
        "http://localhost:3000",
        "http://localhost:3001",
        "https://app.hammr.com",
        "https://api.hammr.com",
        ...allowedHosts,
      ];
  }
};

if (!process.env.ENVIRONMENT) {
  throw new Error("process.env.ENVIRONMENT is not defined");
}

const corsOptions: CorsOptions = {
  origin: allowedOrigins(process.env.ENVIRONMENT),
  exposedHeaders: ["Content-Disposition"],
  allowedHeaders: ["Content-Type", "Authorization", "X-API-KEY", "platform", "app-version", "sentry-trace", "baggage"],
  credentials: true,
};

export const startAPI = (middleware?: RequestHandler, startServer = true) => {
  const app = express();

  Sentry.init({
    dsn: "https://<EMAIL>/4505677747191808",
    integrations: [
      // Add our Profiling integration
      nodeProfilingIntegration(),
    ],
    environment: process.env.ENVIRONMENT,
    enabled: process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "STAGING",
    // Add these configurations
    tracesSampleRate: 1.0,
    attachStacktrace: true,
    // Adjust this in production to avoid capturing too much data
    sampleRate: 1.0,
    // Enable source maps for accurate stack traces
    enableTracing: true,
    // Set sampling rate for profiling
    // This is relative to tracesSampleRate
    profilesSampleRate: 1.0,
  });

  if (middleware) {
    app.use(middleware);
  }

  app.use(cors(corsOptions));

  app.use(cookieParser());
  app.use(bodyParser.json());
  app.use(
    bodyParser.urlencoded({
      extended: true,
    })
  );

  app.use(
    `${basePath}/inngest`,
    serve({
      client: inngest,
      functions: [syncTimesheets, createEmployee, updateEmployee, syncEmployees],
    })
  );

  app.use(expressSession);
  app.use(appVersionIsSupported);
  app.use(apiIsAuthenticated);

  initAuthRoutes(app, basePath);

  // webhooks must come first before all the middleware
  app.use(basePath, webhooksRouter);

  // move some universal middleware checks here
  // userIsAuthenticated and check for userAccessIsAllowed
  app.use(userIsAuthenticated);
  // org mw after auth for protected routes
  app.use(organizationMiddleware);
  app.use(refreshUserRoleIfNeeded);
  app.use(userAccessIsAllowed);

  // routers for refactored endpoints
  app.use(basePath, usersRouter);
  app.use(basePath, projectsRouter);
  app.use(basePath, costCodesRouter);
  app.use(basePath, organizationsRouter);
  app.use(basePath, scheduleRouter);
  app.use(basePath, projectPhotosRouter);
  app.use(basePath, projectPhotosCollectionRouter);
  app.use(basePath, stsRouter);
  app.use(basePath, deviceTokenRouter);
  app.use(basePath, crewsRouter);
  app.use(basePath, featureFlagsRouter);
  app.use(basePath, stripeRouter);
  app.use(basePath, projectDocumentRouter);
  app.use(basePath, chatRouter);
  app.use(basePath, userLocationRouter);
  app.use(basePath, timesheetAlertRouter);
  app.use(basePath, earningRatesRouter);
  app.use(basePath, companyBenefitsRouter);
  app.use(basePath, employeeBenefitsRouter);
  app.use(basePath, wageTableRouter);
  app.use(basePath, classificationRouter);
  app.use(basePath, userClassificationRouter);
  app.use(basePath, fringeBenefitsRouter);
  app.use(basePath, fringeBenefitClassificationsRouter);
  app.use(basePath, payrollsRouter);
  app.use(basePath, integrationUserTokensRouter);
  app.use(basePath, integrationSyncHistoryRouter);
  app.use(basePath, glAccountMappingsRouter);
  app.use(basePath, glAccountMappingSettingsRouter);
  app.use(basePath, payScheduleRouter);
  app.use(basePath, timeTrackingSettingsRouter);
  app.use(basePath, overtimeSettingsRouter);
  app.use(basePath, prevailingWageSettingsRouter);
  app.use(basePath, workersCompCodeRouter);
  app.use(basePath, employeeDocumentRouter);
  app.use(basePath, employeeCertificationRouter);
  app.use(basePath, timeOffPolicyRouter);
  app.use(basePath, timeOffRequestRouter);
  app.use(basePath, dailyReportRouter);
  app.use(basePath, employeeReimbursementRouter);
  app.use(basePath, departmentsRouter);
  app.use(basePath, injuryReportRouter);
  app.use(basePath, integrationSettingsRouter);
  app.use(basePath, equipmentRouter);
  app.use(basePath, equipmentCategoryRouter);

  initTimesheetsRoutes(app, basePath);
  initDashboardRoutes(app, basePath);
  initReportRoutes(app, basePath);

  // check proxy request and validation
  app.use("/api/check-proxy", checkProxyRouter);

  initMigrationRoutes(app, "/_migrationAPI");
  initDailyStats();
  initSchedulingNotifications();
  initStripeIntegration();
  initQuickBooksProjectsSync();
  initWorkmansDashboardProjectsSync();

  app.set("trust proxy", true);
  app.get("/ip", (request, response) => response.send(request.ip));

  // Add this after all routes,
  // but before any and other error-handling middlewares are defined
  Sentry.setupExpressErrorHandler(app);

  // Optional fallthrough error handler
  app.use(function onError(err: Error, _req: Request, res: Response, _next: NextFunction) {
    // Check responds with useful validation error data. we should preserve this and pass to client to use if available
    if (has(err, "type")) {
      return res.status(400).json({
        error: err,
      });
    }

    const shouldShowErrorDetails = process.env.ENVIRONMENT === "DEVELOPMENT" || process.env.ENVIRONMENT === "TEST";
    if (err instanceof HTTPError) {
      return res.status(err.statusCode).json({
        ...(shouldShowErrorDetails ? err : undefined),
        context: err.context,
        error: err.message,
      });
    }

    res.statusCode = 500;
    res.json({
      ...(shouldShowErrorDetails ? { ...err, stack: err.stack.split("\n") } : undefined),
      error: "Internal server error",
    });
  });

  if (startServer) {
    // tests don't need a server
    app.listen(process.env.API_PORT);
    console.log(`Server listening at port ${process.env.API_PORT}`);
  }

  return app;
};
