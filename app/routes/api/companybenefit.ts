import { Router } from "express";
import { userIsAdminOr<PERSON>oreman } from "@/middlewares/authorization";
import { createCompanyBenefitSchema, updateCompanyBenefitSchema } from "@/middlewares/validation/benefits";
import { companyBenefitBelongsToOrg } from "@/middlewares/authorization/benefit";

import { CompanyBenefitController } from "@/controllers/companybenefit";
import { validatorMw } from "@/middlewares/validation/validator";

export const companyBenefitsRouter = Router();
const companyBenefitsController = new CompanyBenefitController();

/* GET company benefits. */
companyBenefitsRouter.get("/company-benefits", companyBenefitsController.get);

/* GET one company benefit */
companyBenefitsRouter.get("/company-benefits/:id", companyBenefitsController.getOne);

/* POST company benefit */
companyBenefitsRouter.post(
  "/company-benefits",
  [
    userIsAdminOrForeman, // permissions check
    validatorMw(createCompanyBenefitSchema),
  ],
  companyBenefitsController.create
);

/* PATCH company benefit */
companyBenefitsRouter.patch(
  "/company-benefits/:id",
  [userIsAdminOrForeman, companyBenefitBelongsToOrg, validatorMw(updateCompanyBenefitSchema)],
  companyBenefitsController.update
);
