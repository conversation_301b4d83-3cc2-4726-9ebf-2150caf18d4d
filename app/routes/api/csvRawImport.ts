import { CostCode, Organization, Project, User } from "@/models";
import { migrationAPIIsAuthenticated } from "@/middlewares/session";

export const initMigrationRoutes = (app: any, basePath: string) => {
  const BASE_RESOURCE = "csvRawImport";

  app.post(
    `${basePath}/${BASE_RESOURCE}/organization`,
    migrationAPIIsAuthenticated,
    async (request: any, response: any) => {
      request.session = null;

      const { name } = request.body;

      const newOrg = await Organization.create({
        name,
      });

      return response.json({
        data: {
          orgId: newOrg.id,
        },
      });
    }
  );

  app.post(`${basePath}/${BASE_RESOURCE}/employee`, async (request: any, response: any) => {
    request.session = null;

    const { name, role, employeeId, phoneNumber, hourlyRate, organizationId } = request.body;

    const nameTokens = name.split(" ");
    const firstName = nameTokens[0],
      lastName = nameTokens[1] || " ";

    const newUser = await User.create({
      firstName,
      lastName,
      role: role.toUpperCase(),
      employeeId,
      hourlyRate,
      phone: "+1" + phoneNumber,
      organizationId,
    });

    return response.json({
      data: {
        userId: newUser.id,
      },
    });
  });

  app.post(`${basePath}/${BASE_RESOURCE}/projects`, async (request: any, response: any) => {
    request.session = null;

    const { organizationId, name, address, projectNumber, foremanId, customerName } = request.body;

    await Project.create({
      foremanId,
      organizationId,
      name,
      address,
      projectNumber,
      customerName,
    });

    return response.json({
      data: {},
    });
  });

  app.post(`${basePath}/${BASE_RESOURCE}/costCodes`, async (request: any, response: any) => {
    request.session = null;

    const { name, number, organizationId } = request.body;

    await CostCode.create({
      name,
      number,
      organizationId,
    });

    return response.json({});
  });
};
