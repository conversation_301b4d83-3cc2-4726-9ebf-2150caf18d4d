import { Router } from "express";
import { DailyReportController } from "@/controllers/dailyreport";
import { validatorMw } from "@/routes/middlewares/validation/validator";
import { createDailyReportSchema } from "@/routes/middlewares/validation/dailyreports";

export const dailyReportRouter = Router();
const dailyReportController = new DailyReportController();

dailyReportRouter.post("/daily-reports", validatorMw(createDailyReportSchema), dailyReportController.create);

dailyReportRouter.get("/daily-reports", dailyReportController.list);
