import dayjs from "dayjs";
import { Op } from "sequelize";
import { Request, Response, NextFunction } from "express";
import { stringify } from "csv-stringify/sync";
import { userIsAdmin } from "@/middlewares/authorization";
import { CostCode, Project, TimeSheet, User, Break, EarningRate } from "@/models";
import { calculateUsersEarnings, calculateWagesByProject, employeeTimesheetsPerDay } from "@/util/financial";
import { formatProjectHoursCost, formattedMinutes } from "@/util/reportFormatter";
import { getActiveEarningRate } from "@/util/userHelper";

import { getCertifiedPayrollProjectSchema } from "@/middlewares/validation/reports";
import { validatorMw } from "@/middlewares/validation/validator";
import {
  fetchTargetedProject,
  fetchTargetedPayroll,
  fetchTargetedTimesheetForProjectStart,
} from "@/middlewares/targeted-resource";
import { ReportsController } from "@/controllers/reports";
import { aa202Schema } from "@/middlewares/validation/reports";
import { OrganizationsService } from "@/services/organizations";

const reportsController = new ReportsController();
const organizationsService = new OrganizationsService();

export const initReportRoutes = (app: any, basePath: string) => {
  const BASE_RESOURCE = "report";

  // certified payroll report
  app.post(
    `${basePath}/report/certified-payroll`,
    [
      userIsAdmin,
      validatorMw(getCertifiedPayrollProjectSchema),
      fetchTargetedProject,
      // note: there is a req.targetedPayroll depenedency in fetchTargetedTimesheetForProjectStart so ordering matters
      fetchTargetedPayroll,
      fetchTargetedTimesheetForProjectStart,
    ],
    reportsController.generateCertifiedPayrollReport
  );

  // 401k report
  app.post(`${basePath}/report/401k`, [userIsAdmin], reportsController.generate401kReport);

  // AA-202 report route
  app.post(`${basePath}/report/aa202`, [userIsAdmin, validatorMw(aa202Schema)], reportsController.AA202);

  app.get(
    `${basePath}/${BASE_RESOURCE}/employees-hours-wages`,
    [userIsAdmin],
    async (request: Request, response: Response, next: NextFunction) => {
      try {
        const organizationId = request.session.user.organizationId;
        const from = parseInt(request.query.from as string);
        const to = parseInt(request.query.to as string);

        if (!from || !to) {
          return response.status(400).json({
            error: "Missing params",
          });
        }

        const fromDate = from && dayjs(from).toISOString();
        const toDate = to && dayjs(to).toISOString();

        const users = await User.findAll({
          where: {
            organizationId,
          },
          raw: false, //DO NOT SET IT TO TRUE: https://github.com/sequelize/sequelize/issues/3885
          nest: true,
          attributes: ["id", "firstName", "lastName"],
          include: [
            {
              model: TimeSheet,
              where: {
                organizationId,
                clockIn: {
                  [Op.between]: [fromDate, toDate],
                },
                isDeleted: false,
              },
              attributes: ["clockIn", "clockOut", "breakDuration"],
              required: true,
              include: [
                {
                  model: Break,
                  where: {
                    isDeleted: false,
                  },
                  attributes: ["id", "start", "end", "timesheetId", "isManual"],
                  required: false,
                },
              ],
            },
            {
              model: EarningRate,
              as: "earningRates",
              where: {
                organizationId,
                active: true,
                type: "REG",
              },
              required: false,
            },
          ],
          order: [
            ["firstName", "ASC"],
            ["lastName", "ASC"],
          ],
        });

        const organization = await organizationsService.findOne({
          where: { id: organizationId },
        });

        const { employees, totalWages } = calculateUsersEarnings(users, organization, { expand: null });

        let header = `${organization.name} Employee Timesheet Report ${dayjs(from).format("MM/DD/YYYY")} - ${dayjs(
          to
        ).format("MM/DD/YYYY")}\n`;

        if (organization.timeTrackingSettings.areBreaksPaid) {
          header += "Employee Name,Hourly Wage,Hours Worked,Paid Break,Earnings\n";
        } else {
          header += "Employee Name,Hourly Wage,Hours Worked,Break,Earnings\n";
        }

        const report = employees.map(({ firstName, lastName, hourlyWage, totalMinutes, breakMinutes, totalWages }) => ({
          name: `${firstName} ${lastName}`,
          hourlyWage,
          hoursWorked: formattedMinutes(totalMinutes, organization.timeTrackingSettings.useDecimalHours),
          break: formattedMinutes(breakMinutes, organization.timeTrackingSettings.useDecimalHours),
          earnings: totalWages.toLocaleString("en-us", {
            style: "currency",
            currency: "USD",
          }),
        }));
        const body = stringify(report);
        const totalAmount = totalWages.toLocaleString("en-us", {
          style: "currency",
          currency: "USD",
        });

        const footer = stringify([{ 0: "Total", 1: "", 2: "", 3: "", 4: totalAmount }]);

        response.attachment("company-report.csv").send(header + body + footer);
      } catch (error) {
        console.error("Error generating employees hours wages report:", error);

        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/employees-hours-by-day`,
    [userIsAdmin],
    async (request: Request, response: Response, next: NextFunction) => {
      try {
        const organizationId = request.session.user.organizationId;
        const from = parseInt(request.query.from as string);
        const to = parseInt(request.query.to as string);

        if (!from || !to) {
          return response.status(400).json({
            error: "Missing params",
          });
        }

        const fromDate = from && dayjs(from).toISOString();
        const toDate = to && dayjs(to).toISOString();

        const users = await User.findAll({
          where: {
            organizationId,
          },
          raw: false, //DO NOT SET IT TO TRUE: https://github.com/sequelize/sequelize/issues/3885
          nest: true,
          attributes: ["id", "firstName", "lastName"],
          include: [
            {
              model: TimeSheet,
              where: {
                organizationId,
                clockIn: {
                  [Op.between]: [fromDate, toDate],
                },
                isDeleted: false,
              },
              attributes: ["clockIn", "clockOut", "breakDuration", "description"],
              required: true,
              include: [
                {
                  model: Project,
                  required: true,
                },
                {
                  model: CostCode,
                  required: false,
                },
                {
                  model: Break,
                  where: {
                    isDeleted: false,
                  },
                  attributes: ["id", "start", "end", "timesheetId", "isManual"],
                  required: false,
                },
              ],
            },
            {
              model: EarningRate,
              as: "earningRates",
              where: {
                organizationId,
                active: true,
                type: "REG",
              },
              required: false,
            },
          ],
          order: [
            ["firstName", "ASC"],
            ["lastName", "ASC"],
            [TimeSheet, "clockIn", "ASC"],
          ],
        });

        const organization = await organizationsService.findOne({
          where: { id: organizationId },
        });

        const timesheetRows = employeeTimesheetsPerDay(users, organization);

        let header = `${organization.name} Employee Timesheet Report ${dayjs(from).format("MM/DD/YYYY")} - ${dayjs(
          to
        ).format("MM/DD/YYYY")}\n`;

        if (organization.timeTrackingSettings.areBreaksPaid) {
          header +=
            "Employee Name,Project Name,Project Number,Cost Code,Date,Clock In,Clock Out,Hours Worked,Overtime Hours,Paid Break,Hourly Wage,Earnings,Description\n";
        } else {
          header +=
            "Employee Name,Project Name,Project Number,Cost Code,Date,Clock In,Clock Out,Hours Worked,Overtime Hours,Break,Hourly Wage,Earnings,Description\n";
        }

        const report = timesheetRows.map(
          ({
            firstName,
            lastName,
            hourlyWage,
            projectName,
            projectNumber,
            costCodeName,
            formattedDate,
            clockIn,
            clockOut,
            workedMinutes,
            overtimeMinutes,
            breakMinutes,
            wages,
            description,
            isTotalRow,
          }) => {
            const formattedHoursWorked = workedMinutes
              ? formattedMinutes(workedMinutes, organization.timeTrackingSettings.useDecimalHours)
              : "";
            const formattedOvertime = overtimeMinutes
              ? formattedMinutes(overtimeMinutes, organization.timeTrackingSettings.useDecimalHours)
              : "";
            const formattedBreak = breakMinutes
              ? formattedMinutes(breakMinutes, organization.timeTrackingSettings.useDecimalHours)
              : "";
            if (isTotalRow) {
              return {
                name: `${firstName} ${lastName} — Total`,
                projectName: "",
                projectNumber: "",
                costCodeName: "",
                date: "",
                clockIn: "",
                clockOut: "",
                hoursWorked: formattedHoursWorked,
                overtime: formattedOvertime,
                break: formattedBreak,
                hourlyWage: "",
                earnings: wages.toLocaleString("en-us", {
                  style: "currency",
                  currency: "USD",
                }),
                description: "",
              };
            } else {
              return {
                name: `${firstName} ${lastName}`,
                projectName,
                projectNumber,
                costCodeName,
                formattedDate,
                clockIn,
                clockOut,
                hoursWorked: formattedHoursWorked,
                overtime: formattedOvertime,
                break: formattedBreak,
                hourlyWage,
                earnings: "",
                description,
              };
            }
          }
        );

        const body = stringify(report);

        response.attachment("company-report.csv").send(header + body);
      } catch (error) {
        console.error("Error generating employees hours by day report:", error);

        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/projects-hours-cost`,
    [userIsAdmin],
    async (request: Request, response: Response, next: NextFunction) => {
      try {
        const organizationId = request.session.user.organizationId;
        const expand = request.query.expand;

        if (expand !== undefined && expand !== "day") {
          return response.status(400).json({
            error: "Invalid expand option",
          });
        }

        const from = parseInt(request.query.from as string);
        const to = parseInt(request.query.to as string);

        if (!from || !to) {
          return response.status(400).json({
            error: "Missing params",
          });
        }

        const fromDate = from && dayjs(from).toISOString();
        const toDate = to && dayjs(to).toISOString();

        const projects: any = await Project.findAll({
          where: {
            organizationId,
          },
          raw: false, //DO NOT SET IT TO TRUE: https://github.com/sequelize/sequelize/issues/3885
          nest: true,
          attributes: ["name"],
          include: [
            {
              model: TimeSheet,
              where: {
                organizationId,
                clockIn: {
                  [Op.between]: [fromDate, toDate],
                },
                isDeleted: false,
              },
              attributes: ["workerId", "clockIn", "clockOut", "breakDuration", "description"],
              required: true,
              include: [
                {
                  model: User,
                  where: {
                    organizationId,
                  },
                  attributes: ["id", "firstName", "lastName"],
                  required: true,
                  include: [
                    {
                      model: EarningRate,
                      as: "earningRates",
                      where: {
                        organizationId,
                        active: true,
                        type: "REG",
                      },
                    },
                  ],
                },
                {
                  model: Break,
                  where: {
                    isDeleted: false,
                  },
                  attributes: ["id", "start", "end", "timesheetId", "isManual"],
                  required: false,
                },
              ],
            },
          ],
        });

        const organization = await organizationsService.findOne({
          where: { id: organizationId },
        });

        let header = `${organization.name} Project Report ${dayjs(from).format("MM/DD/YYYY")} - ${dayjs(to).format(
          "MM/DD/YYYY"
        )}\n`;

        if (organization.timeTrackingSettings.areBreaksPaid) {
          header += "Project Name,Hourly Wage,Hours Worked,Paid Break,Wages";
        } else {
          header += "Project Name,Hourly Wage,Hours Worked,Break,Wages";
        }

        // add description if showing daily breakdown
        if (expand === "day") {
          header += ",Description\n";
        } else {
          header += "\n";
        }

        header += ",,,,,\n";

        let body = "";

        projects.forEach((project: any) => {
          const users: any = {};

          body += stringify([{ 0: project.name, 1: "", 2: "", 3: "" }]);
          project.timesheets?.forEach((timesheet: any) => {
            if (!users[timesheet.workerId]) {
              users[timesheet.workerId] = { user: undefined, timesheets: [] };
            }

            users[timesheet.workerId].timesheets.push({
              ...timesheet.dataValues,
            });

            users[timesheet.workerId].user = timesheet.user.dataValues;
          });

          //Since users are not directly related to a project, we invert the data to the calculateUsersEarnings input
          const usersSorted = [];

          for (const userId in users) {
            usersSorted.push({
              ...users[userId].user,
              timesheets: users[userId].timesheets,
            });
          }

          const { employees, totalWages } = calculateUsersEarnings(usersSorted, organization, {
            expand: expand,
          });

          body += formatProjectHoursCost(
            employees,
            totalWages,
            organization.timeTrackingSettings.useDecimalHours,
            expand as string
          );
        });

        response.attachment("company-report.csv").send(header + body);
      } catch (error) {
        console.error("Error generating projects hours cost report:", error);

        next(error);
      }
    }
  );

  app.get(
    `${basePath}/${BASE_RESOURCE}/employee-hours-by-project`,
    [userIsAdmin],
    async (request: Request, response: Response, next: NextFunction) => {
      try {
        const organizationId = request.session.user.organizationId;
        const from = parseInt(request.query.from as string);
        const to = parseInt(request.query.to as string);

        if (!from || !to) {
          return response.status(400).json({
            error: "Missing params",
          });
        }

        const fromDate = from && dayjs(from).toISOString();
        const toDate = to && dayjs(to).toISOString();

        // change query to emphasize user top level
        const users: any = await User.findAll({
          where: {
            organizationId,
          },
          raw: false, //DO NOT SET IT TO TRUE: https://github.com/sequelize/sequelize/issues/3885
          nest: true,
          attributes: ["id", "firstName", "lastName", "organizationId"],
          include: [
            {
              model: TimeSheet,
              where: {
                organizationId,
                workerId: {
                  [Op.col]: "user.id",
                },
                clockIn: {
                  [Op.between]: [fromDate, toDate],
                },
                isDeleted: false,
              },
              attributes: ["workerId", "clockIn", "clockOut", "breakDuration"],
              required: true,
              include: [
                {
                  model: Project,
                  where: {
                    organizationId,
                  },
                  attributes: ["id", "name"],
                  required: true,
                },
                {
                  model: Break,
                  where: {
                    isDeleted: false,
                  },
                  attributes: ["id", "start", "end", "timesheetId", "isManual"],
                  required: false,
                },
              ],
            },
            {
              model: EarningRate,
              as: "earningRates",
              where: {
                organizationId,
                active: true,
                type: "REG",
              },
              required: false,
            },
          ],
          order: [
            ["firstName", "ASC"],
            ["lastName", "ASC"],
          ],
        });

        const organization = await organizationsService.findOne({
          where: { id: organizationId },
        });

        let header = `${organization.name} Employee Timesheet Job Report ${dayjs(from).format("MM/DD/YYYY")} - ${dayjs(
          to
        ).format("MM/DD/YYYY")}\n`;

        if (organization.timeTrackingSettings.areBreaksPaid) {
          header += "Employee Name,Hourly Wage,Hours Worked,Paid Break,Wages\n";
        } else {
          header += "Employee Name,Hourly Wage,Hours Worked,Break,Wages\n";
        }
        header += ",,,,,\n";

        let body = "";

        users.forEach((user: any) => {
          const regUserEarningRates = getActiveEarningRate(user?.earningRates, "REG");
          const projects: any = {};
          const userHourlyRate = regUserEarningRates ? parseFloat(regUserEarningRates.amount) : 0;

          body += stringify([
            {
              0: `${user.firstName} ${user.lastName}`,
              1: `${userHourlyRate ? userHourlyRate : 0}`,
              2: "",
              3: "",
            },
          ]);

          // maniplate timesheets into rows of aggregated unique projects
          user.timesheets.forEach((timesheet: any) => {
            if (!projects[timesheet.project.name]) {
              projects[timesheet.project.name] = { user: undefined, project: undefined, timesheets: [] };
            }

            projects[timesheet.project.name].timesheets.push({
              ...timesheet.dataValues,
            });

            projects[timesheet.project.name].project = timesheet.project.dataValues;
            projects[timesheet.project.name].user = {
              id: user.id,
              firstName: user.firstName,
              lastName: user.lastName,
              hourlyRate: userHourlyRate,
              organizationId,
            };
          });

          // following above example, seems like convert projects from map/obj to list/collection
          const projectsSorted = [];

          for (const projectName in projects) {
            projectsSorted.push({
              ...projects[projectName].project,
              user: projects[projectName].user,
              timesheets: projects[projectName].timesheets,
            });
          }

          let userTotalWages: any = 0;
          let userTotalMinutes: any = 0;
          let userBreakMinutes: any = 0;

          // loop through projects (already nested under user) to generate a row in report
          projectsSorted.forEach((projectData) => {
            const { projectWages, projectMinutes, projectBreakMinutes } = calculateWagesByProject(
              projectData.timesheets,
              organization,
              user
            );
            userTotalWages += projectWages;
            userTotalMinutes += projectMinutes;
            userBreakMinutes += projectBreakMinutes;

            const report = [
              {
                projectName: projectData.name,
                hourlyRage: "",
                totalMinutes: formattedMinutes(projectMinutes, organization.timeTrackingSettings.useDecimalHours),
                breakMinutes: formattedMinutes(projectBreakMinutes, organization.timeTrackingSettings.useDecimalHours),
                totalWages: projectWages.toLocaleString("en-us", {
                  style: "currency",
                  currency: "USD",
                }),
              },
            ];

            body += stringify(report);
          });

          userTotalWages = userTotalWages.toLocaleString("en-us", {
            style: "currency",
            currency: "USD",
          });

          // after looping through project rows, add a total
          body += stringify([
            {
              0: "Total",
              1: "",
              2: formattedMinutes(userTotalMinutes, organization.timeTrackingSettings.useDecimalHours),
              3: formattedMinutes(userBreakMinutes, organization.timeTrackingSettings.useDecimalHours),
              4: userTotalWages,
            },
          ]);
          body += ",,,,,\n";
        });

        response.attachment("company-report.csv").send(header + body);
      } catch (error) {
        console.error("Error generating employee hours by project report:", error);
        next(error);
      }
    }
  );
};
