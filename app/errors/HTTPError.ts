export default class HTTPError extends Error {
  constructor(public statusCode: number, message: string, public context: Record<string, any> = {}) {
    super(message);
  }

  toJSON() {
    return {
      // we need to make up our mind about what property to use when sending it to the UI.
      // right now, the UI is expecting a message property.
      // But the API validator is returning and error property.
      error: this.message,
      message: this.message,
      ...this.context,
    };
  }

  toString() {
    return this.toJSON();
  }
}
