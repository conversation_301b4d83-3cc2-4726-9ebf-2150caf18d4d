import { CreateOptions, UpdateOptions } from "sequelize";
import Classification from "@/models/classification";

export class ClassificationService {
  private classification = Classification;

  async create(classificationObj: Partial<any>, options: CreateOptions): Promise<Classification> {
    const classification: any = await this.classification.create(classificationObj, options);

    return classification;
  }

  async findAll(options: object): Promise<Classification[]> {
    const extendedOptions = {
      ...options,
    };

    return await this.classification.findAll(extendedOptions);
  }

  async update(
    classificationUpdateObj: Partial<Classification>,
    options: UpdateOptions
  ): Promise<Array<number | any>> {
    return await this.classification.update(classificationUpdateObj, options);
  }
}
