import { CreateOptions, Transaction, UpdateOptions } from "sequelize";
import { EarningRate } from "@/models";
import { ExtendedUser } from "@/models/user";
import { UpdateEarningRate } from "@/types/earningRateDto";
import { ExtendedEarningRate } from "@/models/earningrate";

export class EarningRateService {
  private earningRate = EarningRate;

  async create(earningRateObj: Partial<any>, options: CreateOptions): Promise<EarningRate> {
    // ideally this has a standard dto and transforms data into expected orm/data access shape
    const earningRate: any = await this.earningRate.create(earningRateObj, options);

    return earningRate;
  }

  async findAll(options: object): Promise<EarningRate[]> {
    const extendedOptions = {
      ...options,
      attributes: [
        "id",
        "amount",
        "period",
        "name",
        "active",
        "type",
        "checkEarningRateId",
        "startDate",
        "endDate",
        "metadata",
        "createdAt",
      ],
      raw: true,
    };

    const earningRates = await this.earningRate.findAll(extendedOptions);

    return earningRates;
  }

  async findOne(options: object): Promise<EarningRate> {
    const extendedOptions = {
      ...options,
    };

    return await this.earningRate.findOne(extendedOptions);
  }

  // will not have client facing method in controller
  async update(earningRateUpdateObj: UpdateEarningRate, options: UpdateOptions) {
    return await this.earningRate.update(earningRateUpdateObj, options);
  }

  async deactivateCurrentRates(
    rateIds: number[],
    effectiveEndDate: number = new Date().valueOf(),
    transaction?: Transaction
  ) {
    // deactivate specified earning rates in your system using the provided IDs
    if (!rateIds || rateIds.length === 0) {
      throw new Error("No earning rate Ids provided for deactivation.");
    }

    return this.earningRate.update(
      { active: false, endDate: effectiveEndDate },
      { where: { id: rateIds }, transaction: transaction ? transaction : undefined }
    );
  }

  async createNewRates(user: ExtendedUser, ratesDetails: Partial<ExtendedEarningRate>[], transaction?: Transaction) {
    // create new earning rates in your system
    return this.earningRate.bulkCreate(
      ratesDetails.map((rate) => ({
        ...rate,
        active: true,
        period: "HOURLY",
        userId: user.id,
        organizationId: user.organization.id,
        checkEmployeeId: user.checkEmployeeId ? user.checkEmployeeId : null,
        startDate: rate.effectiveStartDate,
      })),
      { transaction: transaction ? transaction : undefined }
    );
  }

  async createNewSalaryRate(
    user: ExtendedUser,
    salaryDetails: Partial<ExtendedEarningRate>,
    transaction?: Transaction
  ) {
    return this.earningRate.create(
      {
        name: salaryDetails.name,
        amount: salaryDetails.amount,
        type: salaryDetails.type || "REG",
        period: "ANNUALLY",
        active: true,
        userId: user.id,
        organizationId: user.organization.id,
        checkEmployeeId: user.checkEmployeeId ? user.checkEmployeeId : null,
        startDate: salaryDetails.effectiveStartDate,
        weeklyHours: salaryDetails.weeklyHours,
      },
      { transaction: transaction ? transaction : undefined }
    );
  }

  async updateCheckEarningRateIds(earningRateIds: number[], checkIds: string[], transaction?: Transaction) {
    if (!earningRateIds || earningRateIds.length === 0) {
      throw new Error("No earning rate Ids provided for update.");
    }

    // Update your system with the Check earning rate IDs
    await Promise.all(
      earningRateIds.map((id, index) => {
        return this.earningRate.update(
          { checkEarningRateId: checkIds[index] },
          { where: { id }, transaction: transaction ? transaction : undefined }
        );
      })
    );
  }
}
