import { Transaction, WhereOptions } from "sequelize";
import WorkersCompCode from "@/models/workersCompCode";
import CostCode from "@/models/costcode";
import User from "@/models/user";
import HTTPError from "../errors/HTTPError";
import { CheckService } from "@/services/check";
import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import { CheckEmployee, Workplace } from "@/types/check";

export class WorkersCompCodeService {
  private costCode = CostCode;
  private checkService: CheckService;

  constructor() {
    this.checkService = new CheckService();
  }

  async findAll(organizationId: number, includeArchived = false): Promise<WorkersCompCode[]> {
    const where: WhereOptions<WorkersCompCode> = {
      organizationId,
    };

    if (!includeArchived) {
      where.isArchived = false;
    }

    return await WorkersCompCode.findAll({
      where,
      order: [["name", "ASC"]],
      include: [
        {
          model: CostCode,
          as: "costCodes",
          required: false,
        },
      ],
    });
  }

  async create(data: { name: string; code: string; organizationId: number }): Promise<WorkersCompCode> {
    return await WorkersCompCode.create(data);
  }

  async update(
    id: number,
    organizationId: number,
    data: {
      name?: string;
      code?: string;
      isArchived?: boolean;
    }
  ): Promise<WorkersCompCode> {
    const transaction = await sequelize.transaction();

    try {
      const workersCompCode = await WorkersCompCode.findOne({
        where: { id, organizationId },
        transaction,
      });

      if (!workersCompCode) {
        throw new HTTPError(404, "Worker comp code not found");
      }

      await workersCompCode.update(data, { transaction });

      if (data.isArchived) {
        await this.cleanAssignedWorkersComp(id, organizationId, transaction);
      }

      await transaction.commit();

      return workersCompCode;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  private async cleanAssignedWorkersComp(id: number, organizationId: number, transaction: Transaction) {
    await User.update(
      { workersCompCodeId: null },
      {
        where: {
          workersCompCodeId: id,
          organizationId,
        },
        transaction,
      }
    );

    await CostCode.update(
      { workersCompCodeId: null },
      {
        where: {
          workersCompCodeId: id,
          organizationId,
        },
        transaction,
      }
    );
  }

  async assignCostCodes(id: number, organizationId: number, costCodeIds: number[]): Promise<WorkersCompCode> {
    const workersCompCode = await WorkersCompCode.findOne({
      where: { id, organizationId },
    });

    if (!workersCompCode) {
      throw new HTTPError(404, "Worker comp code not found");
    }

    // First, unset workersCompCode from all cost codes that have this workersCompCode
    await this.costCode.update(
      { workersCompCodeId: null },
      {
        where: {
          workersCompCodeId: id,
          organizationId,
        },
      }
    );

    // If costCodeIds is empty, we're done - all associations have been removed
    if (costCodeIds.length === 0) {
      return workersCompCode;
    }

    // Update the provided cost codes with the new workersCompCode
    await this.costCode.update(
      { workersCompCodeId: id },
      {
        where: { id: costCodeIds },
      }
    );

    return workersCompCode;
  }

  async assignWorkers(
    organizationId: number,
    assignments: { userId: number; workersCompCodeId: string | number | null }[]
  ): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // Get unique worker comp code IDs (excluding null)
      const workersCompCodeIds = [
        ...new Set(assignments.map((item) => item.workersCompCodeId).filter((id) => id !== null)),
      ];

      // Verify all worker comp codes exist and belong to organization
      let workersCompCodes: WorkersCompCode[] = [];
      if (workersCompCodeIds.length > 0) {
        workersCompCodes = await WorkersCompCode.findAll({
          where: {
            id: workersCompCodeIds,
            organizationId,
          },
          transaction,
        });

        if (workersCompCodes.length !== workersCompCodeIds.length) {
          throw new HTTPError(400, "One or more worker comp codes not found");
        }
      }

      const userIds = assignments.map((user) => user.userId);

      const users = await User.findAll({
        where: {
          id: userIds,
          organizationId,
        },
        transaction,
      });

      if (users.length !== userIds.length) {
        throw new HTTPError(400, "One or more users not found");
      }

      const organization = await Organization.findByPk(organizationId);

      // Get all Check employees to get their workplace information
      const checkEmployeeIds = users.map((user) => user.checkEmployeeId).filter(Boolean);
      const employees = await this.checkService
        .loadAllPaginatedData<CheckEmployee>(`/employees?company=${organization.checkCompanyId}`)
        .then((employees) => employees.filter((employee) => checkEmployeeIds.includes(employee.id)));

      // Update each user's workersCompCodeId and Check risk class code if in WA
      for (const assignment of assignments) {
        // First update the user in our database
        await User.update(
          { workersCompCodeId: assignment.workersCompCodeId },
          {
            where: {
              id: assignment.userId,
              organizationId,
            },
            transaction,
          }
        );

        const user = users.find((user) => user.id === assignment.userId);
        const workersCompCode = workersCompCodes.find((w) => w.id === Number(assignment.workersCompCodeId));

        if (user?.checkEmployeeId && workersCompCode) {
          // Update the risk class code in Check

          const workplaceIds = employees.find((employee) => employee.id === user.checkEmployeeId)?.workplaces ?? [];

          const workplaces = await this.checkService
            .loadAllPaginatedData<Workplace>(`/workplaces?company=${organization.checkCompanyId}`)
            .then((workplaces) => workplaces.filter((workplace) => Array.from(workplaceIds).includes(workplace.id)));

          const hasWAStateWorkplace = workplaces.some((workplace) => workplace.address.state === "WA");

          if (hasWAStateWorkplace) {
            await this.checkService.patch(`/employees/${user.checkEmployeeId}/company_defined_attributes`, {
              company_defined_attributes: [{ name: "wa_risk_class_code", value: workersCompCode.code }],
            });
          }
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
