import { CostCode } from "@/models";
import { FindOptions, UpdateOptions } from "sequelize";
import WorkersCompCode from "@/models/workersCompCode";

export class CostCodesService {
  private costCode = CostCode;

  async create(costCodeObj: Partial<CostCode>): Promise<CostCode> {
    const costCode = await this.costCode.create(costCodeObj);

    return costCode;
  }

  async findAll(options: FindOptions<CostCode>): Promise<CostCode[]> {
    const costCodes = await this.costCode.findAll({
      ...options,
      attributes: ["id", "name", "number"],
      include: [
        {
          model: WorkersCompCode.unscoped(),
          as: "workersCompCode",
        },
      ],
    });

    return costCodes;
  }

  async update(costCodeUpdateObj: Partial<CostCode>, options: UpdateOptions) {
    return await this.costCode.update(costCodeUpdateObj, options);
  }

  async updateWorkersCompCode(costCodeId: number, workersCompCodeId: number | null): Promise<number[]> {
    return await this.costCode.update(
      { workersCompCodeId },
      {
        where: {
          id: costCodeId,
        },
      }
    );
  }
}
