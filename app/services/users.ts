import { isUserCompanyAdmin } from "@/util/validationHelpers";
import {
  User,
  TimeSheet,
  Project,
  Organization,
  Break,
  Crew,
  CrewMember,
  ProjectDocument,
  EarningRate,
  Department,
  FeatureFlags,
} from "@/models";
import { UpdateOptions, Op, CreateOptions } from "sequelize";
import { Request } from "express";
import { appVersionIsAboveOrEqual, isMobileRequest } from "@/middlewares/validatation";
import { roundTimesheet, serializeBreaks, serializeTimesheet } from "@/util/timesheetHelper";
import { TimesheetsService } from "./timesheets";
import { toEpoch } from "@/util/dateHelper";
import { ExtendedUser } from "@/models/user";
import { calculateTotalWorkedMinutes } from "@/util/timesheetHelper";
import { getActiveEarningRate } from "@/util/userHelper";
import WorkersCompCode from "@/models/workersCompCode";
import EmployeeReimbursement from "@/models/employeereimbursement";
import TimeTrackingSettings from "@/models/timetrackingsettings";
import PrevailingWageSettings from "@/models/prevailingwagesettings";
import PaySchedule from "@/models/payschedule";
import OvertimeSettings from "@/models/overtimesettings";
const USER_ATTRIBUTES = [
  "id",
  "firstName",
  "lastName",
  "role",
  "clockInReminderAt",
  "clockOutReminderAt",
  "isClockInReminderEnabled",
  "isClockOutReminderEnabled",
  "email",
  "gender",
  "ethnicity",
  "veteranStatus",
  "employeeId",
  "checkEmployeeId",
  "checkContractorId",
  "position",
  "workerClassification",
  "phone",
  "managerId",
  "profilePhotoObjectId",
  "isArchived",
  "emergencyContactFirstName",
  "emergencyContactLastName",
  "emergencyContactRelationship",
  "emergencyContactPhone",
];
const ORGANIZATION_ATTRIBUTES = ["name", "id", "checkCompanyId", "isRegisteredOnCheck"];
const TIMESHEET_ATTRIBUTES = ["id", "clockIn", "description"];
const PROJECT_ATTRIBUTES = ["id", "name"];
const BREAK_ATTRIBUTES = ["id", "start", "end", "timesheetId", "isManual"];
const EARNING_RATE_ATTRIBUTES = [
  "id",
  "name",
  "type",
  "period",
  "amount",
  "active",
  "startDate",
  "endDate",
  "checkEarningRateId",
  "weeklyHours",
];

export class UsersService {
  private user = User;
  private timesheetsService: TimesheetsService;

  constructor() {
    this.timesheetsService = new TimesheetsService();
  }

  async findOne(options: any, req?: Request): Promise<ExtendedUser> {
    const { organizationId } = options.where;
    const { simple = false } = options;
    const { forceRawResponse = null } = options;

    const raw = forceRawResponse ?? shouldUseRaw(req);

    const extendedOptions = {
      ...options,
      raw: raw,
      attributes: USER_ATTRIBUTES,
      include: [
        {
          model: WorkersCompCode,
          as: "workersCompCode",
          required: false,
        },
        {
          model: Organization,
          where: {
            id: organizationId,
          },
          attributes: ORGANIZATION_ATTRIBUTES,
          include: [
            {
              model: TimeTrackingSettings,
              as: "timeTrackingSettings",
              required: true,
            },
            {
              model: PrevailingWageSettings,
              as: "prevailingWageSettings",
              required: true,
            },
            {
              model: PaySchedule,
              as: "paySchedule",
              where: { isActive: true },
              required: false,
            },
            {
              model: FeatureFlags,
              required: true,
              attributes: ["isSchedulingEnabled", "isPayrollEnabled", "isMessagingEnabled"],
            },
            {
              model: OvertimeSettings,
              as: "overtimeSettings",
              required: true,
              where: {
                isDefault: true,
              },
            },
          ],
        },
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "manager",
        },
        {
          model: EarningRate,
          as: "earningRates", // return active earning rates
          where: {
            organizationId,
            active: true,
          },
          required: false,
          attributes: EARNING_RATE_ATTRIBUTES,
        },
        {
          model: Department,
          as: "department",
          required: false,
          attributes: ["id", "name"],
        },

        {
          model: EmployeeReimbursement,
          as: "employeeReimbursement",
          required: false,
        },
      ],
    };

    let user: any = await this.user.findOne(extendedOptions);
    const regUserEarningRates = getActiveEarningRate(user?.earningRates, "REG");

    if (!user) {
      return null;
    }

    if (!raw) {
      user = user.toJSON();
    }

    user.organizationName = user["organization.name"];
    user.organizationId = user["organization.id"];
    user.organizationCheckCompanyId = user["organization.checkCompanyId"];
    user.organizationIsRegisteredOnCheck = user["organization.isRegisteredOnCheck"];
    user.isCompanyAdmin = isUserCompanyAdmin(user);
    delete user["organization.name"];
    delete user["organization.id"];
    delete user["organization.checkCompanyId"];
    delete user["organization.isRegisteredOnCheck"];

    // override the hourlyRate
    user.hourlyRate = regUserEarningRates ? parseFloat(regUserEarningRates.amount) : 0;
    user.earningRateId = user["earningRates.id"];
    user.earningRateName = user["earningRates.name"];
    user.earningRateAmount = user["earningRates.amount"];
    user.earningRateType = user["earningRates.type"];
    user.earningRatePeriod = user["earningRates.period"];
    user.earningRateStartDate = user["earningRates.startDate"];
    user.earningRateEndDate = user["earningRates.endDate"];
    user.earningRateActive = user["earningRates.active"];
    delete user["earningRates.id"];
    delete user["earningRates.amount"];
    delete user["earningRates.name"];
    delete user["earningRates.type"];
    delete user["earningRates.startDate"];
    delete user["earningRates.endDate"];
    delete user["earningRates.active"];

    if (simple === false) {
      const currentTimesheet: any = await this.timesheetsService.findOne({
        where: {
          organizationId,
          workerId: user.id,
          clockOut: null,
          isDeleted: false,
        },
        raw: false,
        attributes: TIMESHEET_ATTRIBUTES,
        include: [
          {
            model: Project,
            where: {
              organizationId,
            },
            attributes: ["id", "name"],
          },
        ],
      });

      if (currentTimesheet) {
        // Sequelize limitation on queries again. Making separate queries for breaks and project documents
        const currentTimesheetBreaks = await Break.findAll({
          where: {
            timesheetId: currentTimesheet.id,
            isDeleted: false,
          },
          attributes: BREAK_ATTRIBUTES,
          raw: false,
        });

        const currentTimesheetProjectDocuments = await ProjectDocument.findAll({
          where: {
            projectId: currentTimesheet.project.id,
            isDeleted: false,
          },
          attributes: ["id", "objectId"],
          raw: false,
        });

        // Attach the data to the Sequelize model (needed for both raw and non-raw paths)
        currentTimesheet.breaks = currentTimesheetBreaks.length > 0 ? currentTimesheetBreaks : [];
        if (currentTimesheet.project) {
          currentTimesheet.project.projectDocuments =
            currentTimesheetProjectDocuments.length > 0 ? currentTimesheetProjectDocuments : [];
        }

        if (raw) {
          user.timesheetId = currentTimesheet.id;
          user.clockIn = toEpoch(currentTimesheet.clockIn);
          user.description = currentTimesheet.description;
          user.project = {
            id: currentTimesheet.project.id,
            name: currentTimesheet.project.name,
          };

          if (currentTimesheet.breaks && currentTimesheet.breaks.length > 0) {
            const plainBreaks = currentTimesheet.breaks.map((breakItem: any) => breakItem.get({ plain: true }));
            user.breaks = serializeBreaks({ breaks: plainBreaks });
          }
        } else {
          // Convert to plain object while preserving the manually attached properties
          const timesheetPlain = currentTimesheet.toJSON();

          // Copy the Sequelize array instances to the plain object, converting them to JSON
          if (currentTimesheet.breaks) {
            timesheetPlain.breaks = currentTimesheet.breaks.map((breakItem: any) =>
              breakItem.toJSON ? breakItem.toJSON() : breakItem
            );
          }

          if (currentTimesheet.project && currentTimesheet.project.projectDocuments) {
            timesheetPlain.project.projectDocuments = currentTimesheet.project.projectDocuments.map((doc: any) =>
              doc.toJSON ? doc.toJSON() : doc
            );
          }

          user.timesheet = serializeTimesheet(timesheetPlain);
        }
      }

      let fromDate: number = null;
      if (options?.timesheetOptions?.fromDate !== null) {
        fromDate = options?.timesheetOptions?.fromDate;
      }

      if (fromDate && !Number.isNaN(fromDate)) {
        // we assume that clients send 12:00:00 AM in the user's timezone in `fromDate`
        const startOfDay = new Date(fromDate);
        const endOfDay = new Date(fromDate);
        endOfDay.setTime(startOfDay.getTime() + 24 * 60 * 60 * 1000);

        // since this is used for `totalMinutesToday` that mobile relies on - we use the original clocked values
        const timesheetsFromToday: any = await TimeSheet.findAll({
          where: {
            organizationId,
            workerId: user.id,
            isDeleted: false,
            clockIn: { [Op.between]: [startOfDay, endOfDay] },
          },
          attributes: ["id", "clockIn", "clockOut", "breakDuration", "clockedOutBySwitch"],
          include: [
            {
              model: Project,
              where: {
                organizationId,
              },
              attributes: ["id", "name"],
            },
            {
              model: Break,
              where: {
                isDeleted: false,
              },
              attributes: ["id", "start", "end"],
              required: false,
            },
          ],
        });

        const totalMinutesToday = timesheetsFromToday.reduce((totalMinutes: number, timesheet: any) => {
          if (timesheet.id === currentTimesheet?.id) {
            return totalMinutes;
          }

          return totalMinutes + calculateTotalWorkedMinutes(timesheet);
        }, 0);

        user.totalMinutesToday = Math.floor(totalMinutesToday);

        // only send timesheetsForSwitch if the user has a current timesheet
        // since the user can only switch when clocking out from a current timesheet
        if (currentTimesheet) {
          user.timesheetsForSwitch = timesheetsFromToday
            .filter((timesheet: any) => {
              // only filter timesheets that were clocked out by switching and don't have a break
              // or the current timesheet
              if (timesheet.clockedOutBySwitch || timesheet.id === currentTimesheet.id) {
                if (timesheet.breakDuration == 0 || timesheet.breakDuration == null) {
                  return true;
                }
              }

              return false;
            })
            .map((timesheet: any) => {
              const updatedTimesheet = {
                id: timesheet.id,
                project: timesheet.project,
                clockIn: toEpoch(timesheet.clockIn),
                clockOut: toEpoch(timesheet.clockOut),
              };

              return updatedTimesheet;
            });
        }
      }
    }

    return user;
  }

  async findAll(options: any, req: Request): Promise<User[]> {
    const { organizationId } = options.where;

    const { simple = false } = options;

    const raw = shouldUseRaw(req);

    let userInclude: any = [];

    // only send data about timesheets, crews, and crew members if the client has asked for it
    if (!simple) {
      const timeSheetInclude: any = [
        {
          model: Project,
          where: {
            organizationId,
          },
          attributes: PROJECT_ATTRIBUTES,
        },
      ];

      if (!raw) {
        timeSheetInclude.push({
          model: Break,
          where: {
            isDeleted: false,
          },
          attributes: BREAK_ATTRIBUTES,
          required: false,
        });
      }

      userInclude = [
        {
          model: TimeSheet,
          where: {
            organizationId,
            clockOut: null,
            isDeleted: false,
          },
          attributes: TIMESHEET_ATTRIBUTES,
          required: false,
          include: timeSheetInclude,
        },
        {
          model: CrewMember,
          as: "crewMemberUser",
          attributes: ["id"],
          required: false,
          include: [
            {
              model: Crew,
              where: {
                organizationId,
              },
              attributes: ["id", "name"],
              required: true,
            },
          ],
        },
        {
          model: Crew,
          foreighKey: "crewLead",
          as: "crewLeadUser",
          where: {
            organizationId,
          },
          attributes: ["id", "name"],
          required: false,
        },
        {
          model: EarningRate,
          as: "earningRates", // return active earning rates only
          where: {
            organizationId,
            active: true,
          },
          required: false,
          attributes: EARNING_RATE_ATTRIBUTES,
        },
        {
          model: WorkersCompCode,
          as: "workersCompCode",
          required: false,
        },
        {
          model: Department,
          as: "department",
          required: false,
          attributes: ["id", "name"],
        },
      ];
    } else {
      userInclude = [
        {
          model: EarningRate,
          as: "earningRates",
          where: {
            organizationId,
            active: true,
          },
          required: false,
          attributes: EARNING_RATE_ATTRIBUTES,
        },
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "manager",
        },
        {
          model: WorkersCompCode,
          as: "workersCompCode",
          required: false,
        },
        {
          model: Department,
          as: "department",
          required: false,
          attributes: ["id", "name"],
        },
      ];
    }

    const extendedOptions = {
      ...options,
      raw: raw,
      attributes: USER_ATTRIBUTES,
      include: userInclude,
      order: [
        ["firstName", "ASC"],
        ["lastName", "ASC"],
      ],
    };

    let users: any = await this.user.findAll(extendedOptions);
    const timeTrackingSettings = req.organization.timeTrackingSettings;
    // remap user.hourlyRate to user.earningRate.amount
    users = users.map((user: ExtendedUser) => {
      if (user?.timesheets) {
        // round timesheets
        user.timesheets = user.timesheets.map((timesheet: TimeSheet) =>
          roundTimesheet(timesheet, timeTrackingSettings)
        );
      }

      // convert to plain object
      const plainUser = user.get({ plain: true });
      const regUserEarningRates = getActiveEarningRate(plainUser?.earningRates, "REG");

      return {
        ...plainUser,
        hourlyRate: regUserEarningRates ? parseFloat(regUserEarningRates.amount) : 0,
      };
    });

    if (raw) {
      users = processRawUsers(users);
    } else {
      users = JSON.parse(JSON.stringify(users));
      users = processNonRawUsers(users);
    }

    return users;
  }

  async create(userObj: Partial<User>, options: CreateOptions = {}): Promise<User> {
    const user = await this.user.create(userObj, options);

    return user;
  }

  async update(userUpdateObj: Partial<User>, options: UpdateOptions): Promise<Array<number | any>> {
    const usersUpdated = await this.user.update(userUpdateObj, options);

    return usersUpdated;
  }
}

// we introduced real-time breaks in version 4.6, which is when we started parsing the new
// structure of the user object in the mobile apps
function shouldUseRaw(req: Request) {
  return isMobileRequest(req) && !appVersionIsAboveOrEqual("4.6", req);
}

function processRawUsers(users: any[]) {
  return users.map((user) => {
    user.timesheetId = user["timesheets.id"];
    if (user["timesheets.project.id"] && user["timesheets.project.name"]) {
      user.project = {
        id: user["timesheets.project.id"],
        name: user["timesheets.project.name"],
      };
    }

    user.clockIn = user["timesheets.clockIn"] ? user["timesheets.clockIn"].getTime() : undefined;
    user.isCompanyAdmin = isUserCompanyAdmin(user);
    user.description = user["timesheets.description"];
    if (user["timesheets.breaks.id"]) {
      user.breaks = [
        {
          id: user["timesheets.breaks.id"],
          start: user["timesheets.breaks.start"].getTime(),
          end: user["timesheets.breaks.end"] ? user["timesheets.breaks.end"].getTime() : undefined,
        },
      ];
    }

    delete user["timesheets.breaks.id"];
    delete user["timesheets.breaks.start"];
    delete user["timesheets.breaks.end"];
    delete user["timesheets.id"];
    delete user["timesheets.clockIn"];
    delete user["timesheets.clockOut"];
    delete user["timesheets.project.id"];
    delete user["timesheets.project.name"];
    delete user["timesheets.description"];
    delete user["timesheets.breaks.timesheetId"];
    delete user["timesheets.breaks.isManual"];

    return user;
  });
}

// Process the users when the raw option is false
function processNonRawUsers(users: any[]) {
  return users.map((user) => {
    user.isCompanyAdmin = isUserCompanyAdmin(user);
    if (user.timesheets && user.timesheets.length > 0) {
      user.timesheet = serializeTimesheet(user.timesheets[0]);
    }

    delete user["timesheets"];

    return user;
  });
}
