import { Project, ProjectPhoto, ProjectPhotosCollection, User } from "@/models";
import { serializeProjectPhotosCollection } from "@/util/projectPhotoHelper";
import { FindOptions } from "sequelize";

export class ProjectPhotosCollectionService {
  private projectPhotosCollection = ProjectPhotosCollection;
  private projectPhoto = ProjectPhoto;

  async create(projectPhotosCollectionObj: Partial<ProjectPhotosCollection>): Promise<ProjectPhotosCollection> {
    const projectPhotosCollection = await this.projectPhotosCollection.create(projectPhotosCollectionObj);

    return serializeProjectPhotosCollection(projectPhotosCollection);
  }

  async findAll(options: any, requireProjectPhotos: boolean): Promise<ProjectPhotosCollection[]> {
    const extendedOptions: FindOptions<ProjectPhotosCollection> = {
      ...options,
      attributes: ["id", "note", "timesheetId", "createdAt"],
      include: [
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          required: true,
          subQuery: false,
        },
        {
          model: Project,
          attributes: ["id", "name"],
          required: true,
        },
        {
          model: this.projectPhoto,
          attributes: ["id", "objectId", "createdAt"],
          required: requireProjectPhotos,
          where: {
            isDeleted: false,
          },
        },
      ],
    };

    let projectPhotosCollections = await this.projectPhotosCollection.findAll(extendedOptions);
    projectPhotosCollections = projectPhotosCollections.map((projectPhotosCollection) => {
      return serializeProjectPhotosCollection(projectPhotosCollection);
    });

    return projectPhotosCollections;
  }

  async count({
    orgId,
    projectIds,
    userIds,
    projectArchived,
  }: {
    orgId: number;
    projectIds: number[];
    userIds: number[];
    projectArchived?: boolean | null;
  }): Promise<number> {
    const whereClause = [];
    if (orgId) {
      whereClause.push(`projectPhotosCollection.organization_id = ${orgId}`);
    }
    if (projectIds.length) {
      whereClause.push(`projectPhotosCollection.project_id IN (${projectIds.join(", ")})`);
    }
    if (userIds.length) {
      whereClause.push(`projectPhotosCollection.created_by IN (${userIds.join(", ")})`);
    }

    if (typeof projectArchived === "boolean") {
      whereClause.push(`project.is_archived = ${projectArchived}`);
    }

    whereClause.push(`projectPhoto.is_deleted = false`);

    const whereString = whereClause.length > 0 ? `WHERE ${whereClause.join(" AND ")}` : "";

    const query = `
        SELECT COUNT(DISTINCT projectPhotosCollection.id)
        FROM "ProjectPhotosCollections" AS projectPhotosCollection
        INNER JOIN "ProjectPhotos" AS projectPhoto ON projectPhoto.collection_id = projectPhotosCollection.id
        INNER JOIN "Projects" AS project ON projectPhotosCollection.project_id = project.id
        ${whereString}
    `;

    const [results]: any = await this.projectPhotosCollection.sequelize.query(query);

    return parseInt(results[0].count, 10);
  }
}
