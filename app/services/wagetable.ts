import { CreateOptions, UpdateOptions } from "sequelize";
import { Project, WageTable, UserClassification } from "@/models";
import { ExtendedUserClassification } from "@/models/userclassification";

import { attributeHourlyFringeContribution, calculateCashFringeDiff } from "@/util/financial";
import { shouldCashFringeBeSetToZero } from "@/util/timesheetHelper";

export class WageTableService {
  private wageTable = WageTable;

  async create(wageTableObj: Partial<any>, options: CreateOptions): Promise<WageTable> {
    const wageTable: any = await this.wageTable.create(wageTableObj, options);

    return wageTable;
  }

  async findAll(options: object): Promise<WageTable[]> {
    const extendedOptions = {
      ...options,
      include: [
        {
          model: Project,
          attributes: ["id", "name", "startDate"],
          required: false,
        },
      ],
    };

    const wageTables = await this.wageTable.findAll(extendedOptions);

    return wageTables;
  }

  async findOne(options: object): Promise<WageTable> {
    const extendedOptions = {
      ...options,
      include: [
        {
          model: Project,
          attributes: ["id", "name", "startDate"],
          required: false,
        },
      ],
    };

    const wageTable = await this.wageTable.findOne(extendedOptions);

    return wageTable;
  }

  async update(wageUpdateObj: Partial<WageTable>, options: UpdateOptions): Promise<Array<number | any>> {
    return await this.wageTable.update(wageUpdateObj, options);
  }

  extendWageTableData(
    userClassificationsData: ExtendedUserClassification[],
    otMultiplier = 1.5,
    payFrequency: string
  ): any[] {
    const transformedWageTableData = userClassificationsData.map((userClass) => {
      if (userClass instanceof UserClassification) {
        userClass = userClass.toJSON();
      }

      // what we want to do is re-order the benefits so that amount benefits are first, then percentage benefits
      // this is so that we can calculate the cash fringe correctly
      const attributedBenefits = attributeHourlyFringeContribution(
        userClass,
        userClass.user.employeeBenefits,
        userClass?.classification?.fringeBenefitClassifications,
        payFrequency
      );

      const transformedUserClass = {
        id: userClass.id,
        basePay: userClass.basePay,
        fringePay: userClass.fringePay,
        wageTableId: userClass.wageTableId,
        userId: userClass.userId,
        classificationId: userClass.classificationId,
        startDate: userClass.startDate,
        endDate: userClass.endDate,
        regRateOfPay: 0,
        otRateOfPay: 0,
        dotRateOfPay: 0,
        sumOfFringeBenefits: 0,
        sumOfEmployeeBenefits: 0,
        user: {
          ...userClass.user,
        },
        classification: {
          ...userClass.classification,
        },
        fringeBenefits: userClass?.classification?.fringeBenefitClassifications.map((fbc) => ({
          id: fbc.fringeBenefit.id,
          name: fbc.fringeBenefit.name,
          amount: fbc.amount,
          category: fbc.fringeBenefit?.category || (fbc.fringeBenefit as any)?.categ || null,
          startDate: fbc.startDate,
          endDate: fbc.endDate,
        })),
        cashFringe: "0",
        employeeBenefits: attributedBenefits,
      };

      // at the end we need to calculate the cash fringe which is the userClass.classification.fringePay minus sum of all fringe benefits and minus the sum of all employee benefits
      const sumOfFringeBenefits = transformedUserClass?.fringeBenefits?.reduce(
        (acc, curr) => acc + parseFloat(curr.amount),
        0
      );
      const sumOfEmployeeBenefits = transformedUserClass?.employeeBenefits?.reduce((acc, curr) => {
        return acc + curr.fringeHourlyContribution;
      }, 0);

      const cashFringeDiff = calculateCashFringeDiff(
        transformedUserClass?.fringePay,
        transformedUserClass?.fringeBenefits,
        transformedUserClass?.employeeBenefits
      );

      // if any of the employee benefits are contributionType: "DYNAMIC" then set the cashFringe to 0
      const cashFringeShouldBeZero = shouldCashFringeBeSetToZero(transformedUserClass.employeeBenefits);

      // round the cashFringe
      const calculatedCashFringe = cashFringeShouldBeZero ? 0 : cashFringeDiff;
      const basePay =
        typeof transformedUserClass.basePay === "string"
          ? parseFloat(transformedUserClass.basePay)
          : transformedUserClass.basePay;

      const calculatedRegRateOfPay = basePay + calculatedCashFringe;
      const calculatedOtRateOfPay = basePay * otMultiplier + calculatedCashFringe;
      const calculatedDotRateOfPay = basePay * 2 + calculatedCashFringe;

      transformedUserClass.sumOfFringeBenefits = sumOfFringeBenefits;
      transformedUserClass.sumOfEmployeeBenefits = sumOfEmployeeBenefits;
      transformedUserClass.cashFringe = calculatedCashFringe.toFixed(2);
      transformedUserClass.regRateOfPay = calculatedRegRateOfPay;
      transformedUserClass.otRateOfPay = calculatedOtRateOfPay;
      transformedUserClass.dotRateOfPay = calculatedDotRateOfPay;

      return transformedUserClass;
    });

    return transformedWageTableData;
  }
}
