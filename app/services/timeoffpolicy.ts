import TimeOffPolicy, { Acc<PERSON><PERSON><PERSON><PERSON><PERSON>, TimeOffPolicyType } from "../models/timeoffpolicy";
import TimeOffPolicyEnrollment from "../models/timeoffpolicyenrollment";
import { InferAttributes, Op, WhereOptions } from "sequelize";
import User from "@/models/user";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import { CheckEmployee } from "@/types/check";
import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import { CheckService } from "@/services/check";
import PaySchedule from "@/models/payschedule";
import { getActiveEarningRate } from "@/util/userHelper";
import { EarningRate } from "../models";
import { calculateTotalWorkedMinutes } from "@/util/timesheetHelper";
import Timesheet from "@/models/timesheet";
import HTTPError from "../errors/HTTPError";

// Register the plugins
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

interface UserTimeOffBalance {
  id: number;
  firstName: string;
  lastName: string;
  policies: {
    id: number;
    name: string;
    type: TimeOffPolicyType;
    accruedHours: number | null;
    usedHours: number;
    availableHours: number | null;
  }[];
}

export default class TimeOffPolicyService {
  checkService = new CheckService();
  cachedUserStartDates = new Map<string, { startDate: string; expiry: number }>();

  async create(data: InferAttributes<TimeOffPolicy>) {
    return TimeOffPolicy.create(data);
  }

  async update(id: number, data: InferAttributes<TimeOffPolicy>) {
    const policy = await TimeOffPolicy.findByPk(id);

    // Don't allow updating isLimited
    delete data.isLimited;

    await policy.update(data);

    return policy;
  }

  async get(id: number) {
    return TimeOffPolicy.findByPk(id, {
      include: [
        {
          association: "users",
          through: {
            as: "timeOffPolicyEnrollment",
          },
        },
      ],
    });
  }

  async list(organizationId: number, includeArchived?: boolean) {
    const where: WhereOptions<TimeOffPolicy> = {
      organizationId,
    };

    if (!includeArchived) {
      where.endDate = null;
    }

    return TimeOffPolicy.findAll({
      include: ["users"],
      order: [["createdAt", "DESC"]],
      where,
    });
  }

  async enrollUsers(policyId: number, userIds: number[], startingBalance: number) {
    const existingUsers = await User.findAll({
      where: {
        id: userIds,
      },
    });

    if (existingUsers.length !== userIds.length) {
      throw new HTTPError(400, "Some of the assigned users don't exist");
    }

    return TimeOffPolicyEnrollment.bulkCreate(
      userIds.map((userId) => ({
        timeOffPolicyId: policyId,
        startDate: new Date(),
        startingBalance,
        userId,
      }))
    );
  }

  async removeUserEnrollment(policyId: number, userId: number) {
    await TimeOffPolicyEnrollment.destroy({
      where: {
        timeOffPolicyId: policyId,
        userId,
      },
    });
  }

  async archive(id: number) {
    const policy = await TimeOffPolicy.findByPk(id);
    await policy.update({ endDate: new Date() });

    return policy;
  }

  async unarchive(id: number) {
    const policy = await TimeOffPolicy.findByPk(id);
    await policy.update({ endDate: null });

    return policy;
  }

  async userPolicies(userId: number, includeArchived?: boolean): Promise<InferAttributes<TimeOffPolicy>[]> {
    const where: WhereOptions<TimeOffPolicy> = {};

    if (!includeArchived) {
      where.endDate = null;
    }

    const policies = await TimeOffPolicy.findAll({
      where,
      include: [
        {
          model: User,
          as: "users",
          where: {
            id: userId,
          },
          required: true,
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    return await Promise.all(
      policies.map(async (policy) => {
        const timeOffSummary = await this.getTimeOffSummary(policy.id, userId);

        return {
          ...policy.toJSON(),
          availableHours: timeOffSummary.available,
          accruedHours: timeOffSummary.accrued,
          accruedHoursBasedOnAccrualLimit: timeOffSummary.accruedBasedOnAccrualLimit,
          usedHours: timeOffSummary.used,
          carryoverHours: timeOffSummary.carryover,
        };
      })
    );
  }

  async getTimeOffSummary(policyId: number, userId: number) {
    const policy = await TimeOffPolicy.findByPk(policyId);
    const user = await User.findByPk(userId);

    if (!policy) {
      throw new Error("TimeOff Policy doesn't exist");
    }

    const policyResetDate = await this.getPolicyResetDate(policy, user);
    const enrollmentStartDate = await this.getEnrollmentStartDate(policy.id, policy.name, user.id);

    let accruedHours = await this.calculateAccruedHours(policy, user, policyResetDate, enrollmentStartDate);

    const enrollment = await TimeOffPolicyEnrollment.findOne({
      where: {
        timeOffPolicyId: policyId,
        userId,
      },
    });

    // if user was enrolled in this year, add starting balance to accrued hours
    const startingBalance = enrollment?.startingBalance ?? 0;
    if (enrollment?.startDate && dayjs(enrollment.startDate).isSameOrAfter(policyResetDate, "day")) {
      accruedHours += startingBalance;
    }

    // Get used hours from approved and paid time-off requests
    const usedHours = await this.getUsedHours(userId, policy.id, policyResetDate);

    // Apply carryover limits if applicable
    const carryoverHours = await this.getCarryoverHours(
      policy,
      user,
      policyResetDate.subtract(1, "year"),
      enrollmentStartDate
    );

    const fullAvailableHours = Math.min(accruedHours + carryoverHours, policy.accrualLimit ?? Infinity);

    return {
      policy,
      accrued: accruedHours,
      carryover: carryoverHours,
      used: usedHours,
      available: Math.max(0, fullAvailableHours - usedHours),

      // This is mostly used on the UI.
      // Is to make sense for users when adding up the values in the UI
      accruedBasedOnAccrualLimit: accruedHours - (accruedHours + carryoverHours - fullAvailableHours),
    };
  }

  private async getUsedHours(userId: number, policyId: number, startDate: dayjs.Dayjs) {
    return (
      (await TimeOffRequest.sum("totalHours", {
        where: {
          userId,
          status: {
            [Op.or]: [TimeOffRequestStatus.PAID, TimeOffRequestStatus.APPROVED],
          },
          timeOffPolicyId: policyId,
          startDate: {
            [Op.between]: [startDate.toDate(), startDate.add(1, "year").toDate()],
          },
        },
      })) ?? 0
    );
  }

  private async calculateAccruedHours(
    policy: TimeOffPolicy,
    user: User,
    policyResetDate: dayjs.Dayjs,
    enrollmentStartDate: dayjs.Dayjs
  ): Promise<number> {
    const policyAccrualEndDate = policyResetDate.add(1, "year");

    if (!policy.isLimited || policy.type === TimeOffPolicyType.UNPAID) {
      return 0;
    }

    if (!user.checkEmployeeId) {
      throw new HTTPError(500, `Employee '${user.firstName} ${user.lastName}' not correctly enrolled in Check.`);
    }

    // cap userStartDate to enrollmentStartDate if needed
    const userStartDate = enrollmentStartDate.isAfter(policyResetDate) ? enrollmentStartDate : policyResetDate;

    // A policy's policyAccrualEndDate is set when it's archived.
    const policyEndDate = dayjs(policy.endDate);
    let userEndDate =
      policyEndDate.isValid() && policyEndDate.isBefore(policyAccrualEndDate) ? policyEndDate : policyAccrualEndDate;

    // ensure policyAccrualEndDate does not exceed the current date
    if (userEndDate.isAfter(dayjs())) {
      userEndDate = dayjs();
    }

    // When calculating carryover, it may happen that `userStartDate` doesn't fall under the last accruing period.
    // This usually happens because the enrollment happened in the current year
    // We cap the `userStartDate` above to the enrollment date,
    //    which means we also need to cap `userEndDate` too, so userEndDate is never before userStartDate
    if (userEndDate.isBefore(userStartDate)) {
      userEndDate = userStartDate;
    }

    switch (policy.accrualMethod) {
      case AccrualMethod.FIXED:
        return await this.calculateFixedAccrual(policy, policyResetDate, userStartDate);

      case AccrualMethod.ACCRUED:
        return await this.calculateAccruedAccrual(policy, policyResetDate, userStartDate, userEndDate);

      case AccrualMethod.HOURS_WORKED:
        return await this.calculateHoursWorkedAccrual(policy, user, userStartDate, userEndDate);
    }
  }

  private async calculateFixedAccrual(
    policy: TimeOffPolicy,
    policyResetDate: dayjs.Dayjs,
    userStartDate: dayjs.Dayjs
  ): Promise<number> {
    // if this is the first year of the policy, we should return 0 because the user will not be eligible for accrual
    // and will be granted the starting balance instead
    if (userStartDate.isAfter(policyResetDate, "day")) {
      return 0;
    }

    const totalHoursInYear = policyResetDate.add(1, "year").diff(policyResetDate, "hours");
    const policyAccrualRate = policy.accrualHoursRate;

    const elapsedHoursSinceEnrollment = userStartDate.isAfter(policyResetDate)
      ? totalHoursInYear - userStartDate.diff(policyResetDate, "hours")
      : totalHoursInYear;

    return Math.max(
      0,
      Math.min((policyAccrualRate * elapsedHoursSinceEnrollment) / totalHoursInYear, policyAccrualRate)
    );
  }

  private async calculateAccruedAccrual(
    policy: TimeOffPolicy,
    policyResetDate: dayjs.Dayjs,
    effectiveStartDate: dayjs.Dayjs,
    effectiveEndDate: dayjs.Dayjs
  ): Promise<number> {
    const paySchedule = await PaySchedule.findOne({
      where: {
        organizationId: policy.organizationId,
        isActive: true,
      },
    });

    if (!paySchedule) {
      throw new Error("No active pay schedule found for organization");
    }

    let periodsPerYear: number;
    let periodsPassed = 0;
    let periodsInYear: number;

    switch (paySchedule.payFrequency) {
      case "weekly":
        periodsPerYear = 52;
        periodsPassed = effectiveEndDate.diff(effectiveStartDate, "weeks");
        periodsInYear = effectiveEndDate.diff(policyResetDate, "weeks");
        break;
      case "biweekly":
        periodsPerYear = 26;
        periodsPassed = Math.floor(effectiveEndDate.diff(effectiveStartDate, "weeks") / 2);
        periodsInYear = Math.floor(effectiveEndDate.diff(policyResetDate, "weeks") / 2);
        break;
      case "semimonthly":
        periodsPerYear = 24;
        periodsPassed = effectiveEndDate.diff(effectiveStartDate, "months") * 2;
        periodsInYear = effectiveEndDate.diff(policyResetDate, "months") * 2;
        break;
      case "monthly":
        periodsPerYear = 12;
        periodsPassed = effectiveEndDate.diff(effectiveStartDate, "months");
        periodsInYear = effectiveEndDate.diff(policyResetDate, "months");
        break;
      case "quarterly":
        periodsPerYear = 4;
        periodsPassed = effectiveEndDate.diff(effectiveStartDate, "quarter");
        periodsInYear = effectiveEndDate.diff(policyResetDate, "quarter");
        break;
      case "annually":
        periodsPerYear = 1;
        periodsPassed = effectiveEndDate.diff(effectiveStartDate, "year");
        periodsInYear = effectiveEndDate.diff(policyResetDate, "year");
        break;
    }

    const policyAccrualRate = policy.accrualHoursRate;

    return Math.min(
      (policyAccrualRate / periodsPerYear) * periodsPassed,
      policyAccrualRate * (periodsPassed / periodsInYear) || 0
    );
  }

  private async calculateHoursWorkedAccrual(
    policy: TimeOffPolicy,
    user: User,
    effectiveStartDate: dayjs.Dayjs,
    effectiveEndDate: dayjs.Dayjs
  ): Promise<number> {
    await user.reload({
      include: [
        {
          model: EarningRate,
          as: "earningRates",
        },
      ],
    });

    const activeEarningRate = getActiveEarningRate(user.earningRates);
    const isHourly = activeEarningRate.period === "HOURLY";
    let workedHours: number;

    if (isHourly) {
      const timesheets = await Timesheet.findAll({
        where: {
          workerId: user.id,
          status: {
            [Op.or]: ["APPROVED", "PAID"],
          },
          clockIn: { [Op.gte]: effectiveStartDate.toDate() },
          clockOut: { [Op.lte]: effectiveEndDate.toDate() },
        },
      });

      workedHours = timesheets.reduce((minutes, timesheet) => minutes + calculateTotalWorkedMinutes(timesheet), 0) / 60;
    } else {
      // For salaried employees, use active earning rate's weekly hours
      const weeksWorked = effectiveEndDate.diff(effectiveStartDate, "weeks");
      workedHours = weeksWorked * (activeEarningRate?.weeklyHours ?? 40);
    }

    // We are pretty much guaranteed to have `accrualHoursInterval` set, but just to make TS happy, I added a default
    const workingHoursInAYear = 2080; // This means the interval is the entire year
    const accrualHoursInterval = policy.accrualHoursInterval ?? workingHoursInAYear;

    const accrualRatePerCycle = policy.accrualHoursRate / accrualHoursInterval;

    return workedHours * accrualRatePerCycle;
  }

  private async getCarryoverHours(
    policy: TimeOffPolicy,
    user: User,
    startDate: dayjs.Dayjs,
    enrollmentStartDate: dayjs.Dayjs
  ): Promise<number> {
    let accruedHours = await this.calculateAccruedHours(policy, user, startDate, enrollmentStartDate);
    const enrollment = await TimeOffPolicyEnrollment.findOne({
      where: {
        timeOffPolicyId: policy.id,
        userId: user.id,
      },
    });

    // if user was enrolled in this policy last year, add starting balance to accrued hours
    const startingBalance = enrollment?.startingBalance ?? 0;
    if (
      enrollment?.startDate &&
      dayjs(enrollment.startDate).isSameOrAfter(startDate, "day") &&
      dayjs(enrollment.startDate).isSameOrBefore(startDate.add(1, "year"), "day")
    ) {
      accruedHours += startingBalance;
    }

    // we need to recursively get the carryover hours from the previous year
    // because for any year, the total accrued hours is the sum of the carryover hours from the previous year
    // and the accrued hours from the current year
    if (enrollmentStartDate.isBefore(startDate, "day")) {
      const previousYearStartDate = startDate.subtract(1, "year");
      const carryoverHoursFromPreviousYear = await this.getCarryoverHours(
        policy,
        user,
        previousYearStartDate,
        enrollmentStartDate
      );
      accruedHours += carryoverHoursFromPreviousYear;
    }

    const usedHoursLastYear = await this.getUsedHours(user.id, policy.id, startDate);

    const unusedHours = Math.max(0, accruedHours - usedHoursLastYear);

    return Math.min(unusedHours, policy.carryoverLimit || 0);
  }

  async timeOffBalances(organizationId: number, checkCompanyId: string, year: number): Promise<UserTimeOffBalance[]> {
    const startDate = dayjs().year(year).startOf("year");

    // Get all active users with their policies
    const users = await User.findAll({
      where: {
        organizationId,
        checkEmployeeId: {
          [Op.ne]: null,
        },
      },
      include: [
        {
          model: TimeOffPolicy,
          as: "timeOffPolicies",
          required: false,
          where: {
            [Op.or]: [
              {
                endDate: null,
              },
              { endDate: { [Op.gte]: startDate.toDate() } },
            ],
          },
        },
      ],
    });

    // load the employees' startDates from Check before calculating time-off balances
    // this will improve the loading times since we will call Check once or twice
    //    instead of calling it once for each employee
    await this.loadUsersStartDatesFromCheck(
      checkCompanyId,
      users.map((user) => user.checkEmployeeId)
    );

    // Process all users in parallel
    const balancesPromises = users.map(async (user) => {
      const policiesPromises = user.timeOffPolicies.map(async (policy) => {
        // Get enrollment to check for starting balance
        const enrollment = await TimeOffPolicyEnrollment.findOne({
          where: {
            timeOffPolicyId: policy.id,
            userId: user.id,
          },
        });

        const [accruedHoursFromCalculation, carryOverHours, usedHours] = await Promise.all([
          this.calculateAccruedHours(policy, user, startDate, dayjs(enrollment?.startDate)),
          this.getCarryoverHours(policy, user, startDate.subtract(1, "year"), dayjs(enrollment?.startDate)),
          this.getUsedHours(user.id, policy.id, startDate),
        ]);

        // Add starting balance if user was enrolled in this year
        let accruedHoursInYear = accruedHoursFromCalculation;
        const startingBalance = enrollment?.startingBalance ?? 0;
        if (enrollment && dayjs(enrollment.startDate).isSameOrAfter(startDate, "day")) {
          accruedHoursInYear += startingBalance;
        }

        const accruedHours = policy.isLimited ? Math.min(accruedHoursInYear, policy.accrualLimit ?? Infinity) : null;

        return {
          id: policy.id,
          name: policy.name,
          type: policy.type,
          accruedHours: accruedHours + carryOverHours,
          usedHours: usedHours || 0,
          availableHours: policy.isLimited ? Math.max(0, accruedHours + carryOverHours - usedHours) : null,
        };
      });

      const policies = await Promise.all(policiesPromises);

      return {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        policies,
      };
    });

    return Promise.all(balancesPromises);
  }

  private async getUserStartDate(checkEmployeeId: string) {
    const cachedStartDate = this.cachedUserStartDates.get(checkEmployeeId);
    if (cachedStartDate?.expiry > Date.now()) {
      return cachedStartDate.startDate;
    }

    const checkEmployee = await this.checkService.get<CheckEmployee>(`/employees/${checkEmployeeId}`);

    if (!checkEmployee?.start_date) {
      throw new HTTPError(500, "Employee does not have a start date set.");
    }

    this.cachedUserStartDates.set(checkEmployeeId, {
      startDate: checkEmployee.start_date,
      expiry: Date.now() + 60 * 1000, // cache for 1 min
    });

    return checkEmployee.start_date;
  }

  private async loadUsersStartDatesFromCheck(checkCompanyId: string, checkEmployeeIds: string[]) {
    const results = await this.checkService.loadAllPaginatedData<CheckEmployee>(`/employees?company=${checkCompanyId}`);

    results?.forEach((employee) => {
      if (employee.id && employee.start_date && checkEmployeeIds.includes(employee.id)) {
        this.cachedUserStartDates.set(employee.id, {
          startDate: employee.start_date,
          expiry: Date.now() + 60 * 1000, // cache for 1 min
        });

        // Remove the processed employee ID from the list
        checkEmployeeIds = checkEmployeeIds.filter((id) => id !== employee.id);
      }
    });
  }

  private async getEnrollmentStartDate(policyId: number, policyName: string, userId: number) {
    const enrollment = await TimeOffPolicyEnrollment.findOne({
      where: {
        timeOffPolicyId: policyId,
        userId: userId,
      },
    });

    if (!enrollment) {
      throw new HTTPError(500, `User is not enrolled in the "${policyName}" time-off policy`, { policyId, userId });
    }

    const enrollmentStartDate = dayjs(enrollment.startDate);

    if (!enrollmentStartDate.isValid()) {
      throw new HTTPError(500, `Please set an enrollment start date`, { policyId, userId });
    }

    return enrollmentStartDate;
  }

  /**
   * Policy's reset date can either be standard for all employees (usually 1st of January)
   *    or the employee's employment start date
   */
  private async getPolicyResetDate(policy: TimeOffPolicy, user: User) {
    const currentDate = dayjs();
    const currentYear = currentDate.get("year");

    // Get base start date from policy's accrual reset date if available
    let startDate: dayjs.Dayjs;
    if (policy.accrualResetDate) {
      startDate = dayjs(`${currentYear}-${policy.accrualResetDate}`);
    } else {
      // If no accrual reset date, use the user's employment date
      if (!user.checkEmployeeId) {
        throw new HTTPError(500, `Employee '${user.firstName} ${user.lastName}' not correctly enrolled in Check.`);
      }

      const employmentStartDate = dayjs(await this.getUserStartDate(user.checkEmployeeId));
      startDate = dayjs(`${currentYear}-${employmentStartDate.format("MM-DD")}`);
    }

    if (startDate.isAfter(currentDate)) {
      startDate = startDate.year(currentYear - 1);
    }

    return startDate;
  }
}
