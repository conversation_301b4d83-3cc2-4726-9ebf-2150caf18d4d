import { FindOptions, Op, Transaction, UpdateOptions, WhereOptions } from "sequelize";
import {
  Break,
  BreakHistory,
  Classification,
  CompanyBenefit,
  CostCode,
  EarningRate,
  EmployeeBenefit,
  FringeBenefit,
  FringeBenefitClassification,
  InjuryReport,
  Organization,
  Project,
  TimeSheet,
  TimesheetAlert,
  TimesheetHistory,
  User,
  UserClassification,
  WorkersCompCode,
  Equipment,
  EquipmentCategory,
} from "@/models";
import Timesheet, { ExtendedTimesheet, UserEarningsSummary } from "@/models/timesheet";
import { ExtendedFringeBenefitClassification } from "@/models/fringebenefitclassification";
import TimeTrackingSettings from "@/models/timetrackingsettings";
import OvertimeSettings from "@/models/overtimesettings";

import { flattenDeep } from "lodash";
import {
  determineValidEarningRatesByClockIn,
  generateUserTimesheetSubArrays,
  generateWeeklyTimesheetSubArrays,
  roundTimesheet,
  serializeTimesheet,
  summarizeUserTimesheetData,
} from "@/util/timesheetHelper";
import {
  attributeHourlyFringeContribution,
  manualAttributedWeeklyTimesheets,
  refinedAttributedWeeklyTimesheets,
} from "@/util/financial";
import dayjs from "dayjs";
import { convertDatesToTimestamps, getNearestFullWeekIntervals } from "@/util/dateHelper";
import { dynamicSort, SortCriteria } from "@/util/collectionHelpers";
import HTTPError from "@/errors/HTTPError";
import { EarningRateService } from "@/services/earningrate";
import { UserLocationData, UserLocationService } from "@/services/userlocation";
import sequelize from "@/lib/sequelize";
import { BreakData, BreaksService } from "@/services/breaks";

export interface TimesheetData {
  userId: string;
  projectId: string;
  costCodeId: unknown;
  userClassificationId: unknown;
  description: unknown;
  clockIn: string;
  clockOut: string;
  clockInLocation: UserLocationData;
  clockOutLocation: UserLocationData;
  breakDuration: string;
  clockedOutBySwitch: unknown;
  breaks: BreakData[];
  workersCompCodeId: unknown;
  isManual: unknown;
  otDuration?: string;
  dotDuration?: string;
  driveTimeDuration?: string;
  equipmentId: string;
}

interface TimesheetListParams {
  from?: number;
  to?: number;
  userId?: string;
  isResolved?: boolean;
  status?: string;
  projectId?: string;
  costCodeId?: string;
  offsetTimestamp?: number;
  offsetId?: number;
  pageSize?: number;
  equipmentId?: string;
}

export interface TimesheetListResponse {
  totalMinutes: number;
  totalWages: number;
  timesheets: Partial<ExtendedTimesheet>[];
  summary: UserEarningsSummary[];
  pagination?: {
    hasMore: boolean;
    offsetTimestamp?: number;
    offsetId?: number;
  };
}

export class TimesheetsService {
  private timeSheet = TimeSheet;
  private earningRateService: EarningRateService;
  private userLocationService: UserLocationService;
  private breaksService: BreaksService;

  constructor() {
    this.earningRateService = new EarningRateService();
    this.userLocationService = new UserLocationService();
    this.breaksService = new BreaksService();
  }

  /**
   * Removes redundant fields from an array of timesheets before sending to the client
   */
  private removeRedundantFields(timesheets: ExtendedTimesheet[], pruneEarnings = false) {
    return timesheets.map((timesheet) => {
      const timesheetCopy = { ...timesheet };

      // Delete organization and rate fields
      delete timesheetCopy.organization;
      delete timesheetCopy.regEarningRate;
      delete timesheetCopy.otEarningRate;
      delete timesheetCopy.companyDynamicFringeContributionRate;
      delete timesheetCopy.employeeDynamicFringeContributionPerecent;
      delete timesheetCopy.companyDynamicFringeAllocation;
      delete timesheetCopy.employeeDynamicFringeAllocation;

      if (pruneEarnings) {
        delete timesheetCopy.totalWages;
        delete timesheetCopy.hourlyWage;
        delete timesheetCopy.regularWages;
        delete timesheetCopy.otHourlyWage;
        delete timesheetCopy.overtimeWages;
        delete timesheetCopy.cashFringeRate;
        delete timesheetCopy.doubleOvertimeWages;
      }

      // Remove employee benefits from user
      if (timesheetCopy.user) {
        delete timesheetCopy.user.employeeBenefits;
      }

      return timesheetCopy;
    });
  }

  async create(
    organization: Organization,
    creator: User,
    timeSheetObj: TimesheetData,
    transaction: Transaction
  ): Promise<TimeSheet> {
    const {
      userId,
      projectId,
      costCodeId,
      userClassificationId = null,
      description,
      clockIn,
      clockOut,
      clockInLocation = null,
      clockOutLocation = null,
      breakDuration,
      clockedOutBySwitch,
      breaks,
      workersCompCodeId,
      isManual,
      otDuration,
      dotDuration,
      driveTimeDuration,
      equipmentId,
    } = timeSheetObj;

    const organizationId = organization.id;
    const userIdInt = parseInt(userId);

    let breakDurationInt = 0;
    if (breakDuration) {
      breakDurationInt = parseInt(breakDuration);
    }

    let otDurationInt = 0;
    if (otDuration) {
      otDurationInt = parseInt(otDuration);
    }

    let dotDurationInt = 0;
    if (dotDuration) {
      dotDurationInt = parseInt(dotDuration);
    }

    let driveTimeDurationInt = 0;
    if (driveTimeDuration) {
      driveTimeDurationInt = parseInt(driveTimeDuration);
    }

    let equipmentIdInt = null;
    if (equipmentId) {
      equipmentIdInt = parseInt(equipmentId);
    }

    const project = await Project.findOne({
      where: {
        id: projectId,
        organizationId,
      },
    });

    if (!project) {
      throw new HTTPError(400, "Project not found");
    }

    // keep this for now - dependency on pulling project data in timesheetService.create
    if (project.isPrevailingWage && userClassificationId === null) {
      throw new HTTPError(400, "Missing userClassificationId for prevailing wage project");
    }

    // keep status in outer scope
    let timesheetStatus = "CLOCKED_IN";

    // Manual timesheet flow
    if (clockIn && clockOut) {
      // with both clockIn and clockOut - status should now be "SUBMITTED"
      timesheetStatus = "SUBMITTED";
      // don't allow workers to add manual timesheets
      if (!organization.timeTrackingSettings.allowWorkersToAddEditTime && creator.role === "WORKER") {
        throw new HTTPError(403, "Adding manual timesheets is disabled. Ask a supervisor or admin to add it for you.");
      }

      const clockInInt = parseInt(clockIn);
      const clockOutInt = parseInt(clockOut);
      if (clockInInt >= clockOutInt) {
        throw new HTTPError(400, "Clock in cannot be after clock out.");
      }

      let isManualValue = true;
      if (isManual != null) {
        if (isManual !== true && isManual !== false) {
          throw new HTTPError(400, "isManual is not valid");
        } else {
          isManualValue = isManual;
        }
      }

      // grab earning rates
      const earningRates = await this.earningRateService.findAll({
        where: {
          user_id: userIdInt,
          organization_id: organizationId,
        },
      });
      // earningRatesToUse is a tuple where the first element is the reg earning rate to use and the second element is the ot earning rate to use
      const earningRatesToUse = determineValidEarningRatesByClockIn(earningRates, clockInInt);

      const newTimesheet = await TimeSheet.create(
        {
          organizationId,
          workerId: userIdInt,
          projectId: parseInt(projectId),
          costCodeId,
          userClassificationId,
          description,
          clockIn: clockInInt,
          clockOut: clockOutInt,
          isManual: isManualValue,
          status: timesheetStatus,
          workersCompCodeId,
          createdBy: creator.id,
          breakDuration: breakDurationInt,
          otDuration: otDurationInt,
          dotDuration: dotDurationInt,
          driveTimeDuration: driveTimeDurationInt,
          clockedOutBySwitch: clockedOutBySwitch || false,
          regEarningRateId: earningRatesToUse[0]?.id || null,
          otEarningRateId: earningRatesToUse[1]?.id || null,
          dotEarningRateId: earningRatesToUse[2]?.id || null,
          equipmentId: equipmentIdInt,
        },
        { transaction }
      );

      if (clockInLocation !== null) {
        await this.userLocationService.create(
          {
            ...clockInLocation,
            timesheetId: newTimesheet.id,
            organizationId,
          },
          transaction
        );
      }

      if (clockOutLocation !== null) {
        await this.userLocationService.create(
          {
            ...clockOutLocation,
            timesheetId: newTimesheet.id,
            organizationId,
          },
          transaction
        );
      }

      if (organization.timeTrackingSettings.areRealtimeBreaksEnabled && breaks) {
        try {
          await this.breaksService.createOrUpdate(creator.id, breaks, newTimesheet, true, organizationId, transaction);
        } catch (error: any) {
          if (error.name === "BreakValidationError") {
            throw new HTTPError(422, error.message);
          }
          throw error;
        }
      }

      return newTimesheet;
    } else {
      // Clock in flow
      if (!clockIn) {
        throw new HTTPError(400, "Missing clockIn param");
      }

      // grab earning rates
      const clockInInt = parseInt(clockIn);

      const earningRates = await this.earningRateService.findAll({
        where: {
          user_id: userIdInt,
          organization_id: organizationId,
        },
      });

      // earningRatesToUse is a tuple where the first element is the reg earning rate to use and the second element is the ot earning rate to use
      const earningRatesToUse = determineValidEarningRatesByClockIn(earningRates, clockInInt);

      const runningTimesheet = await this.findOne({
        where: {
          organizationId,
          workerId: userIdInt,
          clockOut: null,
          isDeleted: false,
        },
        transaction: transaction,
      });

      if (runningTimesheet) {
        throw new HTTPError(409, "User already clocked in");
      }

      const newTimesheet = await TimeSheet.create(
        {
          organizationId,
          workerId: userIdInt,
          projectId: parseInt(projectId),
          costCodeId,
          userClassificationId,
          description,
          clockIn: clockInInt,
          isManual: false,
          status: timesheetStatus,
          workersCompCodeId,
          createdBy: creator.id,
          regEarningRateId: earningRatesToUse[0]?.id,
          otEarningRateId: earningRatesToUse[1]?.id,
          dotEarningRateId: earningRatesToUse[2]?.id,
          driveTimeDuration: driveTimeDurationInt,
          equipmentId: equipmentIdInt,
        },
        { transaction }
      );

      if (clockInLocation !== null) {
        await this.userLocationService.create(
          {
            ...clockInLocation,
            timesheetId: newTimesheet.id,
            organizationId,
          },
          transaction
        );
      }

      if (organization.timeTrackingSettings.areRealtimeBreaksEnabled && breaks) {
        try {
          await this.breaksService.createOrUpdate(creator.id, breaks, newTimesheet, true, organizationId, transaction);
        } catch (error: any) {
          if (error.name === "BreakValidationError") {
            throw new HTTPError(422, error.message);
          }
          throw error;
        }
      }

      return newTimesheet;
    }
  }

  async findOne(options: FindOptions): Promise<TimeSheet> {
    const defaultInclude = [
      {
        model: Organization,
        include: [
          {
            model: TimeTrackingSettings,
            as: "timeTrackingSettings",
          },
        ],
      },
    ];

    // Merge includes, ensuring our required include is present
    const mergedOptions = {
      ...options,
      include: [...(Array.isArray(options.include) ? options.include : []), ...defaultInclude],
    };

    const timeSheet: ExtendedTimesheet = await this.timeSheet.findOne(mergedOptions);

    if (!timeSheet) {
      return null;
    }

    const timeTrackingSettings = timeSheet.organization?.timeTrackingSettings;
    const roundedTimeSheet = roundTimesheet(timeSheet, timeTrackingSettings);

    return roundedTimeSheet;
  }

  async findAll(options: FindOptions): Promise<TimeSheet[]> {
    const defaultInclude = [
      {
        model: Organization,
        include: [
          {
            model: TimeTrackingSettings,
            as: "timeTrackingSettings",
          },
        ],
      },
    ];

    // Merge includes
    const mergedOptions = {
      ...options,
      include: [...(Array.isArray(options.include) ? options.include : []), ...defaultInclude],
    };

    const timeSheets: ExtendedTimesheet[] = await this.timeSheet.findAll(mergedOptions);

    const timeTrackingSettings = timeSheets[0]?.organization?.timeTrackingSettings;
    const roundedTimeSheets = timeSheets.map((timeSheet) => roundTimesheet(timeSheet, timeTrackingSettings));

    return roundedTimeSheets;
  }

  async bulkUpdate(timesheetUpdates: Partial<ExtendedTimesheet>, options: UpdateOptions) {
    return await this.timeSheet.update(timesheetUpdates, options);
  }

  async update(timesheetUpdates: Partial<ExtendedTimesheet>, options: UpdateOptions) {
    return await this.timeSheet.update(timesheetUpdates, options);
  }

  // ExtendedTimesheets should also include classification data - reference `joinedTimesheetsData`
  enrichTimesheets(timesheets: Partial<ExtendedTimesheet>[], payFrequency: string): ExtendedTimesheet[] {
    const modifiedResults = timesheets.map((timesheet) => {
      // plain objs
      let plainTimesheet: any = timesheet;
      if (timesheet instanceof TimeSheet) {
        plainTimesheet = timesheet.get({ plain: true });
      }

      // calculate 'hasTimesheetHistory' by summing the lengths of timesheetHistories and breakHistories
      const hasTimesheetHistory =
        (plainTimesheet.timesheetHistories ? plainTimesheet.timesheetHistories.length > 0 : false) ||
        (plainTimesheet.breakHistories ? plainTimesheet.breakHistories.length > 0 : false);

      const hasUnresolvedAlerts = plainTimesheet.timesheetAlerts ? plainTimesheet.timesheetAlerts.length > 0 : false;

      // attribute fringeHourlyContribution if user.employeeBenefits exists and length > 0
      if (
        plainTimesheet.userClassification &&
        plainTimesheet.user.employeeBenefits &&
        plainTimesheet.user.employeeBenefits.length > 0
      ) {
        // important to note that this "enhances" the data with a fringeHourlyContribution property
        const attributedBenefits = attributeHourlyFringeContribution(
          plainTimesheet.userClassification,
          plainTimesheet.user.employeeBenefits,
          plainTimesheet.fringeBenefitClassifications,
          payFrequency
        );

        plainTimesheet.user.employeeBenefits = attributedBenefits;
      }

      return {
        ...plainTimesheet,
        clockInTimestamp: plainTimesheet.clockIn.getTime(),
        clockOutTimestamp: plainTimesheet.clockOut ? plainTimesheet.clockOut.getTime() : null,
        hasTimesheetHistory,
        hasUnresolvedAlerts,
      };
    });

    return modifiedResults;
  }

  async getFirstClockInDate(filterId: number, filterAttribute = "projectId"): Promise<Date | null> {
    try {
      const firstTimesheet = await TimeSheet.findOne({
        where: {
          [filterAttribute]: filterId,
          clockIn: {
            [Op.ne]: null, // Ensuring clockIn is not null
          },
        },
        order: [["clockIn", "ASC"]],
        attributes: ["clockIn"], // only fetching the clockIn field
      });

      if (firstTimesheet) {
        return firstTimesheet.clockIn;
      } else {
        return null;
      }
    } catch (error) {
      console.error("Error fetching first clockIn date:", error);
      throw error;
    }
  }

  // enrich timesheets with fringe benefit classifications
  enrichTimesheetsWithFringeBenefitClassifications(
    timesheets: ExtendedTimesheet[],
    fringeBenefitClassifications: ExtendedFringeBenefitClassification[]
  ) {
    const joinedTimesheetsData = timesheets.map((timesheet) => {
      // Check if timesheet is already a plain object
      const timesheetData = timesheet instanceof TimeSheet ? timesheet.toJSON() : timesheet;

      const classification = timesheetData.userClassification?.classification;
      if (classification) {
        const listOfFringeBenefitClassifications = (
          fringeBenefitClassifications as ExtendedFringeBenefitClassification[]
        )
          .filter((fbc) => {
            if (fbc.classification?.id !== classification.id) {
              return false;
            }

            return timesheetData.clockIn >= (fbc.startDate ?? -Infinity) && timesheetData.clockIn <= (fbc.endDate ?? Infinity);
          })
          .map((fbc) => ({
            id: fbc.fringeBenefit?.id,
            name: fbc.fringeBenefit?.name,
            amount: fbc.amount,
          }));

        return {
          ...timesheetData,
          fringeBenefitClassifications: listOfFringeBenefitClassifications,
        };
      }

      return timesheetData;
    });

    return joinedTimesheetsData;
  }

  generateFlattenedAttributedTimesheets(
    enrichedExtendedTimesheets: ExtendedTimesheet[],
    adjustedStartTimestamp: number | undefined,
    organization: Organization
  ) {
    let flattendAttributedTimesheets: Partial<ExtendedTimesheet>[] = [];
    const collectionOfUserTimesheets = generateUserTimesheetSubArrays(enrichedExtendedTimesheets);

    for (let i = 0; i < collectionOfUserTimesheets.length; i++) {
      const userTimesheets = collectionOfUserTimesheets[i]; // collection of weekly timesheets for a user

      // each userTimesheet needs to be broken out into weekly timesheets
      const userWeeklyTimesheetsGrouped = generateWeeklyTimesheetSubArrays(userTimesheets, adjustedStartTimestamp);

      // attribute the user weekly timesheets
      const attributedWeeklyTimesheets = userWeeklyTimesheetsGrouped.map((weeklyTimesheet) => {
        // branch the logic based on the overtime settings
        if (organization.overtimeSettings.automaticOvertimeCalculation) {
          return refinedAttributedWeeklyTimesheets(weeklyTimesheet, organization);
        } else {
          // manual ot calculation
          return manualAttributedWeeklyTimesheets(weeklyTimesheet, organization);
        }
      });

      const flattenedWeeklyTimesheets = flattenDeep(attributedWeeklyTimesheets);
      flattendAttributedTimesheets = flattendAttributedTimesheets.concat(flattenedWeeklyTimesheets);
    }

    return flattendAttributedTimesheets;
  }

  async list(
    organization: Organization,
    params: TimesheetListParams = {},
    pruneEarnings?: boolean
  ): Promise<TimesheetListResponse> {
    const { from, to, isResolved, offsetTimestamp = null, offsetId = null, pageSize: requestedPageSize = 200 } = params;

    const pageSize = Math.min(requestedPageSize, 200);

    let effectiveFrom: Date | undefined;
    let effectiveTo: Date | undefined;
    let nextOffsetTimestamp: number | null = null;
    let nextOffsetId: number | null = null;
    let hasMore = false;
    let paginationTimesheets: TimeSheet[] = [];

    // all our logic to calculate OT on timesheets and attribute them is based on from and to
    // we calculate adjusted dates based on from and to to account for work week start and end
    // therefore, if client has provided pagination params, we will use it to figure out the effective from and to
    // and then use that to query timesheets
    if (offsetTimestamp !== null) {
      const paginationTimesheetsPlusOne = await this.timeSheet.findAll({
        where: {
          organizationId: organization.id,
          isDeleted: false,
          [Op.or]: [
            { updatedAt: { [Op.gt]: new Date(offsetTimestamp) } },
            {
              updatedAt: { [Op.eq]: new Date(offsetTimestamp) },
              id: { [Op.gte]: offsetId },
            },
          ],
        },
        order: [
          ["updatedAt", "ASC"],
          ["id", "ASC"],
        ],
        limit: pageSize + 1,
      });

      if (paginationTimesheetsPlusOne.length === 0) {
        return {
          totalMinutes: 0,
          totalWages: 0,
          timesheets: [],
          summary: [],
          pagination: {
            hasMore: false,
            offsetTimestamp: null,
            offsetId: null,
          },
        };
      }

      hasMore = paginationTimesheetsPlusOne.length > pageSize;
      if (hasMore) {
        const lastTimesheet = paginationTimesheetsPlusOne[paginationTimesheetsPlusOne.length - 1];
        nextOffsetTimestamp = lastTimesheet.updatedAt.getTime();
        nextOffsetId = lastTimesheet.id;
      }

      paginationTimesheets = hasMore ? paginationTimesheetsPlusOne.slice(0, pageSize) : paginationTimesheetsPlusOne;

      if (paginationTimesheets.length > 0) {
        // sort paginationTimesheets by clockIn without mutating the original array
        const sortedTimesheets = [...paginationTimesheets].sort((a, b) => a.clockIn.getTime() - b.clockIn.getTime());
        effectiveFrom = sortedTimesheets[0].clockIn;
        effectiveTo = sortedTimesheets[sortedTimesheets.length - 1].clockIn;
      }
    }

    const fromDate = effectiveFrom ?? (from ? dayjs(from) : undefined);
    const toDate = effectiveTo ?? (to ? dayjs(to) : undefined);

    // requested projectId filters
    let projectIds: number[] = [];
    // requested costCodeId filters
    let costCodeIds: number[] = [];
    // requested equipmentId filters
    let equipmentIds: number[] = [];

    // find options
    const findOptions: FindOptions = {};

    // generate the where clause
    const whereClause: WhereOptions = {
      organizationId: organization.id,
      isDeleted: false,
    };

    // we can only apply userId filter on the whereClause if it is provided
    // any other filters should be applied after the timesheets are fetched
    // this is because we need to fetch all timesheets for each user and perform OT calculation first
    // if we apply any other filters, we will only fetch partial timesheets and OT calculation will be incorrect
    if (params.userId) {
      const userIds: number[] = params.userId.split(",").map((id: string) => parseInt(id));
      // if userIds contains any NaN - need to reject
      if (userIds.some((id) => isNaN(id))) {
        throw new Error("Invalid user ids");
      }

      whereClause.workerId = { [Op.in]: userIds };
    }

    if (params.projectId) {
      projectIds = params.projectId.split(",").map((id: string) => parseInt(id));
      // if projectIds contains any NaN - need to reject
      if (projectIds.some((id) => isNaN(id))) {
        throw new Error("Invalid project ids");
      }
    }

    if (params.equipmentId) {
      equipmentIds = params.equipmentId.split(",").map((id: string) => parseInt(id));
      // if equipmentIds contains any NaN - need to reject
      if (equipmentIds.some((id) => isNaN(id))) {
        throw new Error("Invalid equipment ids");
      }
    }

    let includeUnassignedCostCodes = false;
    if (params.costCodeId) {
      costCodeIds = params.costCodeId.split(",").map((id: string) => parseInt(id));
      // if costCodeIds contains any NaN - need to reject
      if (costCodeIds.some((id) => isNaN(id))) {
        throw new Error("Invalid cost code ids");
      }

      if (costCodeIds?.includes(-1)) {
        includeUnassignedCostCodes = true;
      }
    }

    // joins
    findOptions.include = timesheetsReportInclude(fromDate?.toISOString(), toDate?.toISOString(), { isResolved });
    // order
    findOptions.order = timesheetsReportOrder();

    const startDayOfWeek =
      organization.paySchedule.payFrequency === "weekly" ? organization.overtimeSettings.weekStartDay : "MONDAY";

    const adjustedDates = getNearestFullWeekIntervals(fromDate?.valueOf(), toDate?.valueOf(), startDayOfWeek);
    whereClause.clockIn = {
      [Op.between]: [adjustedDates.start.toISOString(), adjustedDates.end.toISOString()],
    };

    findOptions.where = whereClause;

    // for all export types
    const results: ExtendedTimesheet[] = await this.findAll(findOptions);

    const fringeBenefitData = await FringeBenefitClassification.findAll({
      where: {
        organizationId: organization.id,
      },
      include: [
        {
          model: Classification,
          required: true,
        },
        {
          model: FringeBenefit,
          required: true,
        },
      ],
    });

    // sequencing this, we need to enrich the timesheets with fringe benefit classifications then do additional enrichment for histories/fringeHourlyContribution/etc
    const joinedTimesheetsData = this.enrichTimesheetsWithFringeBenefitClassifications(results, fringeBenefitData);

    // modify each timesheet to include 'hasTimesheetHistory' and `hasUnresolvedAlerts`
    const modifiedResults = this.enrichTimesheets(joinedTimesheetsData, organization.paySchedule.payFrequency);

    const flattendAttributedTimesheets: Partial<ExtendedTimesheet>[] = this.generateFlattenedAttributedTimesheets(
      modifiedResults,
      adjustedDates.start.valueOf(),
      organization
    );

    // then stitch the results back together and slice cut out the timesheets that are not in the date range
    let eligibleAttributedTimesheets: Partial<ExtendedTimesheet>[] = flattendAttributedTimesheets;
    if (fromDate && toDate) {
      eligibleAttributedTimesheets = eligibleAttributedTimesheets.filter((timesheet) => {
        if (offsetTimestamp !== null) {
          return paginationTimesheets.some((t) => t.id === timesheet.id);
        }

        return timesheet.clockInTimestamp >= fromDate?.valueOf() && timesheet.clockInTimestamp <= toDate?.valueOf();
      });
    }

    // now filter out any timesheets not matching any projectIds or costCodeIds if specified
    if (projectIds.length > 0) {
      eligibleAttributedTimesheets = eligibleAttributedTimesheets.filter((timesheet) => {
        return projectIds.includes(timesheet.projectId);
      });
    }

    // now filter out any timesheets not matching any cost code ids if specified or include unassigned cost codes if includeUnassignedCostCodes is true
    if (costCodeIds.length > 0) {
      eligibleAttributedTimesheets = eligibleAttributedTimesheets.filter((timesheet) => {
        return (
          costCodeIds.includes(timesheet.costCodeId) || (includeUnassignedCostCodes && timesheet.costCodeId === null)
        );
      });
    }

    // if params.status is provided, filter the timesheets by status
    if (params.status) {
      eligibleAttributedTimesheets = eligibleAttributedTimesheets.filter((timesheet) => {
        return timesheet.status === params.status;
      });
    }

    if (equipmentIds.length > 0) {
      eligibleAttributedTimesheets = eligibleAttributedTimesheets.filter((timesheet) => {
        return equipmentIds.includes(timesheet.equipmentId);
      });
    }

    const sortCriteria: SortCriteria[] = [
      { path: ["user", "firstName"], order: "asc" },
      { path: ["user", "lastName"], order: "asc" },
      { path: ["clockIn"], order: "asc" },
    ];

    const sortedTimesheets = dynamicSort(eligibleAttributedTimesheets, sortCriteria);

    // aggregate - mins
    const totalMinutes = sortedTimesheets.reduce((acc, timesheet) => {
      return acc + timesheet.regularMinutes + timesheet.overtimeMinutes + timesheet.driveTimeMinutes;
    }, 0);

    // convert dates to timestamps
    const sortedTimesheetsWithTimestamps = convertDatesToTimestamps(sortedTimesheets);

    // summarize user timesheet data - should try to mimic `calculateUserEarnings` functionality
    const userEarningsSummaries: UserEarningsSummary[] = summarizeUserTimesheetData(
      sortedTimesheetsWithTimestamps,
      pruneEarnings
    );

    // Process entire list at once
    const timesheetsResponse = this.removeRedundantFields(sortedTimesheetsWithTimestamps, pruneEarnings);

    // aggregate wages
    const wagesEarned = pruneEarnings
      ? undefined
      : timesheetsResponse.reduce((acc, timesheet) => {
          return acc + timesheet.totalWages;
        }, 0);

    return {
      totalMinutes: totalMinutes,
      totalWages: Math.ceil(wagesEarned),
      timesheets: timesheetsResponse,
      summary: userEarningsSummaries,
      pagination: {
        hasMore,
        offsetTimestamp: nextOffsetTimestamp,
        offsetId: nextOffsetId,
      },
    };
  }

  async createBulk(
    organization: Organization,
    creator: User,
    timesheetDataArray: TimesheetData[]
  ): Promise<ExtendedTimesheet[]> {
    const transaction = await sequelize.transaction();

    let newTimesheets: Timesheet[];
    try {
      newTimesheets = await Promise.all(
        timesheetDataArray.map(async (timesheetData) => this.create(organization, creator, timesheetData, transaction))
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return Promise.all(
      newTimesheets.map((timesheet) => createOrUpdateTimesheetResponse(timesheet.id, organization.id))
    );
  }

  /**
   * Validates that the effective date for a new rate is after the latest paid timesheet
   * @param userId The ID of the user whose timesheets to check
   * @param organizationId The organization ID
   * @param effectiveDate The proposed effective date for the new rate
   * @param timezone The organization's timezone
   * @param transaction The current database transaction
   * @returns Error message if validation fails, null if validation passes
   */
  async validateEffectiveDate(
    userId: number,
    organizationId: number,
    effectiveDate: Date,
    timezone = "America/Los_Angeles",
    transaction?: Transaction
  ): Promise<string | null> {
    const timesheetData = await this.findAll({
      where: {
        status: "PAID",
        workerId: userId,
        organizationId,
        clockIn: {
          [Op.gte]: effectiveDate,
        },
      },
      order: [["clockIn", "DESC"]],
      transaction,
    });

    // If there is any timesheet data, return an error message
    if (timesheetData.length > 0) {
      const date = dayjs(timesheetData[0].clockIn).tz(timezone).format("MMM D, YYYY");
      throw new HTTPError(
        400,
        `Effective Date cannot be earlier than ${date}, as the employee has been paid at the old rate until this date. Please select a later date.`
      );
    }

    return null;
  }
}

export const TIMESHEET_ATTRIBUTES = [
  "id",
  "clockIn",
  "clockOut",
  "isManual",
  "description",
  "breakDuration",
  "hideGeofenceWarning",
  "clockedOutBySwitch",
  "status",
  "otDuration",
  "dotDuration",
  "driveTimeDuration",
];

// returns a serialized timesheet object that is sent back to the client after creating or updating a timesheet
export async function createOrUpdateTimesheetResponse(
  timesheetId: number,
  organizationId: number,
  transaction?: Transaction
): Promise<any> {
  const timesheetsService = new TimesheetsService();

  const timesheet: any = await timesheetsService.findOne({
    where: { id: timesheetId },
    attributes: TIMESHEET_ATTRIBUTES,
    include: [
      {
        model: Organization,
        where: {
          id: organizationId,
        },
        attributes: ["id", "name"],
      },
      {
        model: Project,
        where: {
          organizationId,
        },
        attributes: ["id", "name", "isPrevailingWage", "wageTableId"],
      },
      {
        model: CostCode,
        where: {
          organizationId,
        },
        attributes: ["id", "name"],
        required: false,
      },
      {
        model: UserClassification,
        as: "userClassification",
        required: false,
        include: [
          {
            model: Classification,
            required: false,
          },
        ],
      },
      {
        model: User,
        attributes: ["firstName", "lastName"],
        as: "createdByUser",
      },
      {
        model: Break,
        where: {
          isDeleted: false,
        },
        attributes: ["id", "start", "end", "timesheetId", "isManual"],
        required: false,
      },
      {
        model: WorkersCompCode,
        as: "workersCompCode",
        required: false,
      },
      {
        model: Equipment,
        as: "equipment",
        required: false,
        attributes: ["id", "name", "hourlyCost", "year"],
        include: [
          {
            model: EquipmentCategory,
            as: "category",
            attributes: ["id", "name"],
          },
        ],
      },
    ],
    transaction,
  });

  let timesheetData = timesheet.toJSON();
  timesheetData = serializeTimesheet(timesheetData);

  delete timesheetData.createdByUser;

  return timesheetData;
}

/**
 * This function generates the include options for the timesheets report. The `fromDate` and `toDate` are dayjs ISO strings that really only affect the Employee Benefits fetched and not the timesheets themselves
 * @param fromDate - (ISO date string) The fromDate to fetch benefits for
 * @param toDate - (ISO date string) The toDate to fetch benefits for
 * @param options
 *  onlyIncludeUsersWithHourlyEarningRates - (boolean) Whether to only include users with hourly earning rates
 *  isResolved - filters only resolved or unresolved timesheets
 * @returns The include options for the supplemental timesheets joins in queries
 */
export function timesheetsReportInclude(
  fromDate: string | undefined,
  toDate: string | undefined,
  options?: { onlyIncludeUsersWithHourlyEarningRates?: boolean; isResolved?: boolean; includeSalariedUsers?: boolean }
) {
  const { onlyIncludeUsersWithHourlyEarningRates = false, isResolved, includeSalariedUsers = false } = options;

  const shouldIncludeSalariedUsers = includeSalariedUsers && !onlyIncludeUsersWithHourlyEarningRates;

  const include: FindOptions<TimeSheet>["include"] = [
    {
      model: User,
      attributes: [
        "id",
        "firstName",
        "lastName",
        "position",
        "employeeId",
        "checkEmployeeId",
        "checkContractorId",
        "phone",
        "gender",
        "ethnicity",
        "workerClassification",
      ],
      include: [
        {
          model: EmployeeBenefit,
          required: false,
          where: {
            ...(fromDate
              ? {
                  benefitStartDate: {
                    [Op.lte]: fromDate,
                  },
                }
              : undefined),
            [Op.or]: [
              { benefitEndDate: null },
              toDate
                ? {
                    benefitEndDate: { [Op.gte]: toDate },
                  }
                : undefined,
            ],
          },
          include: [
            {
              model: CompanyBenefit,
              as: "companyBenefit",
              where: {
                // this is queried this way because only relevant for PW projects for cash fringe calculation
                is_approved_fringe: true,
              },
            },
          ],
        },
      ],
    },
    {
      model: EarningRate,
      as: "regEarningRate",
      required: onlyIncludeUsersWithHourlyEarningRates,
      where: !shouldIncludeSalariedUsers
        ? {
            period: {
              [Op.ne]: "ANNUALLY",
            },
          }
        : undefined,
    },
    {
      model: EarningRate,
      as: "otEarningRate",
      required: onlyIncludeUsersWithHourlyEarningRates,
      where: !shouldIncludeSalariedUsers
        ? {
            period: {
              [Op.ne]: "ANNUALLY",
            },
          }
        : undefined,
    },
    {
      model: EarningRate,
      as: "dotEarningRate",
      required: false,
      where: !shouldIncludeSalariedUsers
        ? {
            period: {
              [Op.ne]: "ANNUALLY",
            },
          }
        : undefined,
    },
    {
      model: Project,
      attributes: ["id", "name", "projectNumber", "isPrevailingWage", "wageTableId"],
      include: [
        {
          model: OvertimeSettings,
          as: "overtimeSettings",
        },
      ],
    },
    {
      model: CostCode,
      attributes: ["id", "name", "number"],
    },
    {
      model: UserClassification,
      as: "userClassification",
      required: false,
      include: [
        {
          model: Classification,
          required: false,
        },
      ],
    },
    {
      model: Break,
      where: {
        isDeleted: false,
      },
      attributes: ["id", "start", "end", "timesheetId", "isManual"],
      required: false,
    },
    // include timesheet history and break history
    {
      model: TimesheetHistory,
      separate: true,
      attributes: ["id", "type", "history", "createdAt", "createdBy"],
    },
    {
      model: BreakHistory,
      separate: true,
      attributes: ["id", "type", "history", "createdAt", "createdBy"],
    },
    {
      model: WorkersCompCode,
      as: "workersCompCode",
      required: false,
    },
    {
      model: InjuryReport,
      as: "injuryReport",
      required: false,
    },
    {
      model: Equipment,
      as: "equipment",
      required: false,
      attributes: ["id", "name", "hourlyCost", "year"],
      include: [
        {
          model: EquipmentCategory,
          as: "category",
          attributes: ["id", "name"],
        },
      ],
    },
  ];

  // Filtering timesheets based on `isResolved`:
  // - If `isResolved` is not provided (is `undefined`): return all timesheets, regardless of alerts.
  // - If `isResolved` is `true` or `false`: return timesheets that HAVE at least one alert (resolved or unresolved).
  //   - This is mainly useful for getting timesheets with unresolved alerts
  //        (e.g., in the dashboard) by setting `isResolved = false`.
  //   - No clear use case yet for fetching timesheets with only resolved alerts.
  if (isResolved === undefined) {
    include.push({
      model: TimesheetAlert,
      required: false,
      separate: true,
      where: {
        isResolved: false,
      },
    });
  } else {
    include.push({
      model: TimesheetAlert,
      required: true,
      where: { isResolved },
    });
  }

  return include;
}

export function timesheetsReportOrder(order?: any) {
  return (
    order || [
      [User, "id", "ASC"],
      ["clockIn", "ASC"],
    ]
  );
}
