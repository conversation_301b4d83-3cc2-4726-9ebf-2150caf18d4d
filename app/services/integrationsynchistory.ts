import * as Sen<PERSON> from "@sentry/node";
import { IntegrationSyncHistory } from "@/models";

export class IntegrationSyncHistoryService {
  private integrationSyncHistory = IntegrationSyncHistory;

  async findAll(integrationUserTokenId: number): Promise<IntegrationSyncHistory[]> {
    const syncHistories = await this.integrationSyncHistory.findAll({
      where: {
        integrationUserTokenId,
      },
    });

    return syncHistories;
  }

  async create(integrationUserTokenId: number, payrollId: string) {
    return await this.integrationSyncHistory.create({
      integrationUserTokenId,
      syncedAt: new Date(),
      payrollId,
    });
  }

  async createFailedSyncHistoryEntry(integrationUserTokenId: number, payrollId: string) {
    Sentry.captureException(
      `Failed to create journal entry for integrationUserTokenId: ${integrationUserTokenId} and payrollId: ${payrollId}`
    );

    return await this.integrationSyncHistory.create({
      integrationUserTokenId,
      failedAt: new Date(),
      payrollId,
    });
  }
}
