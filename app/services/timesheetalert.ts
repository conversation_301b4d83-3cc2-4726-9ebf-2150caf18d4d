import { CreateOptions, Transaction, UpdateOptions } from "sequelize";
import { Project, TimesheetAlert } from "../models";
import { isWithinGeofence, createPoint } from "@/util/locationHelper";

export class TimesheetAlertService {
  private timesheetAlert = TimesheetAlert;

  async create(timesheetAlertObj: Partial<TimesheetAlert>, options: CreateOptions): Promise<TimesheetAlert> {
    return await this.timesheetAlert.create(timesheetAlertObj, options);
  }

  async update(
    timesheetAlertObj: Partial<TimesheetAlert>,
    options: Omit<UpdateOptions<any>, "returning">
  ): Promise<number[]> {
    return await this.timesheetAlert.update(timesheetAlertObj, options);
  }

  /**
   * - When user clocks out, create a timesheet alert if:
   *   - Clock out location is not captured OR
   *   - Clock out location is captured but it is outside the geofence with accuracy taken into account
   */
  async createClockoutLocationAlertIfNeeded(
    clockOutLocation: any,
    timesheet: any,
    locationBreadcrumbingEnabled: boolean,
    transaction: Transaction
  ) {
    const project = await Project.findOne({
      where: { id: timesheet.projectId },
      attributes: ["isGeofenced", "location", "geofenceRadius"],
      transaction,
    });

    // timesheet alert for clock out location not captured
    if (clockOutLocation === null && (project.isGeofenced || locationBreadcrumbingEnabled)) {
      const timesheetAlertObj = { timesheetId: timesheet.id, type: "CLOCK_OUT_LOCATION_MISSING" };
      await this.create(timesheetAlertObj, { transaction });
    } else {
      if (project && project.isGeofenced && project.location) {
        const accuracyVariableInFeet = (clockOutLocation.horizontalAccuracy ?? 0) * 3.28084;
        const clockOutCoordinates = createPoint(clockOutLocation.long, clockOutLocation.lat);
        const isInsideGeofence = isWithinGeofence(
          project.location,
          clockOutCoordinates,
          project.geofenceRadius + accuracyVariableInFeet
        );

        if (!isInsideGeofence) {
          const timesheetAlertObj = {
            timesheetId: timesheet.id,
            type: "CLOCK_OUT_LOCATION_OUTSIDE_GEOFENCE",
          };

          await this.create(timesheetAlertObj, { transaction });
        }
      }
    }
  }
}
