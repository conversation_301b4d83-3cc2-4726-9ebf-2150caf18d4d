import { Equipment } from "@/models";
import { UpdateOptions } from "sequelize";

export class EquipmentService {
  private equipment = Equipment;

  async create(equipmentObj: Partial<Equipment>): Promise<Equipment> {
    return await this.equipment.create(equipmentObj);
  }

  async findAll(options: object): Promise<Equipment[]> {
    return await this.equipment.findAll(options);
  }

  async findOne(options: object): Promise<Equipment> {
    return await this.equipment.findOne(options);
  }

  async update(
    equipmentUpdateObj: Partial<Equipment>,
    options: Omit<UpdateOptions<any>, "returning">
  ): Promise<number[]> {
    return await this.equipment.update(equipmentUpdateObj, options);
  }

  async delete(options: object): Promise<number> {
    return await this.equipment.destroy(options);
  }
}
