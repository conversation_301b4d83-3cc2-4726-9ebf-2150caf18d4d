import { EarningRate } from "@/models";
import { CheckService, UpdateResourceObj } from "@/services/check";

export class CheckEarningRateManager {
  checkService: CheckService;

  constructor() {
    this.checkService = new CheckService();
  }

  async synchronizeEarningRates(checkEmployeeId: string, newRates: Partial<UpdateResourceObj>[], oldRateIds: string[]) {
    // remap newRates to be data values instead of instances
    newRates = newRates.map((rate: EarningRate) => {
      return {
        ...rate.dataValues,
        checkEmployeeId,
        period: rate?.period?.toLowerCase() || "hourly",
      };
    });

    // set old rates in Check to inactive
    const deactivationPromises = oldRateIds.map((id: string) =>
      this.checkService.patch(`/earning_rates/${id}`, { active: false })
    );

    await Promise.all(deactivationPromises);

    // convert new rates to Check format
    const formattedNewRates = newRates.map((rate: any) => {
      return this.checkService.handleCreateCheckPayload(rate, "earning_rates", "POST");
    });

    // Create new rates in Check and collect new IDs
    const creationPromises = formattedNewRates.map((rate: any) => this.checkService.post("/earning_rates", rate));

    const newRateResponses = await Promise.all(creationPromises);

    return newRateResponses.map((response) => response.id);
  }

  async deactivateEarningRate(checkEarningRateId: string) {
    return this.checkService.patch(`/earning_rates/${checkEarningRateId}`, { active: false });
  }
}
