import OvertimeSettings from "@/models/overtimesettings";
import { FindOptions, CreateOptions, UpdateOptions, Op } from "sequelize";

export class OvertimeSettingsService {
  private overtimeSettings = OvertimeSettings;

  async findByPk(id: number): Promise<OvertimeSettings | null> {
    return await this.overtimeSettings.findByPk(id);
  }

  async findOne(options: FindOptions): Promise<OvertimeSettings | null> {
    return await this.overtimeSettings.findOne(options);
  }

  async findAll(options: FindOptions): Promise<OvertimeSettings[]> {
    return await this.overtimeSettings.findAll(options);
  }

  async create(overtimeSettingsObj: Partial<OvertimeSettings>, options?: CreateOptions): Promise<OvertimeSettings> {
    const overtimeSettings = await this.overtimeSettings.create(overtimeSettingsObj, options);

    return overtimeSettings;
  }

  async update(
    id: number,
    overtimeSettingsObj: Partial<OvertimeSettings>,
    organizationId: number,
    _options?: UpdateOptions
  ): Promise<OvertimeSettings | null> {
    const overtimeSettings = await this.overtimeSettings.findOne({
      where: {
        id,
        organizationId,
      },
    });

    if (!overtimeSettings) {
      return null;
    }

    // Set properties then save
    overtimeSettings.set(overtimeSettingsObj);
    await overtimeSettings.save();

    // If this is the default settings, cascade global settings to all other settings
    // in the same organization
    if (overtimeSettings.isDefault) {
      const globalSettings: Partial<OvertimeSettings> = {};

      // Only include global properties that were updated
      if ("overtimeDistribution" in overtimeSettingsObj) {
        globalSettings.overtimeDistribution = overtimeSettings.overtimeDistribution;
      }

      // Add automaticOvertimeCalculation to global settings
      if ("automaticOvertimeCalculation" in overtimeSettingsObj) {
        globalSettings.automaticOvertimeCalculation = overtimeSettings.automaticOvertimeCalculation;
      }

      if ("weekStartDay" in overtimeSettingsObj) {
        globalSettings.weekStartDay = overtimeSettings.weekStartDay;
      }

      // If any global settings were updated, cascade them
      if (Object.keys(globalSettings).length > 0) {
        await this.overtimeSettings.update(globalSettings as any, {
          where: {
            organizationId: overtimeSettings.organizationId,
            name: {
              [Op.ne]: "default",
            },
          },
        });
      }
    }

    return overtimeSettings;
  }

  async delete(id: number, organizationId: number): Promise<boolean> {
    const result = await this.overtimeSettings.destroy({
      where: { id, organizationId },
    });

    return result > 0; // Returns true if at least one record was deleted
  }
}
