import { ProjectPhoto } from "@/models";
import { serializeProjectPhoto } from "@/util/projectPhotoHelper";

export class ProjectPhotosService {
  private projectPhoto = ProjectPhoto;

  async findAll(options: object): Promise<ProjectPhoto[]> {
    return await this.projectPhoto.findAll(options);
  }

  async create(projectPhotoObj: Partial<ProjectPhoto>): Promise<ProjectPhoto> {
    const projectPhoto = await this.projectPhoto.create(projectPhotoObj);

    return serializeProjectPhoto(projectPhoto);
  }

  async delete(id: number): Promise<void> {
    const projectPhoto = await this.projectPhoto.findByPk(id);
    if (!projectPhoto) {
      throw new Error("Project photo not found");
    }

    await projectPhoto.update({ isDeleted: true });
  }
}
