<!doctype html>
<html lang="en">
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>Time-Off Request</title>
  <style media="all" type="text/css">
    @media all {
      .btn-primary table td:hover {
        background-color: #ee6023 !important;
      }

      .btn-primary a:hover {
        background-color: #ee6023 !important;
        border-color: #ee6023 !important;
      }
    }
    @media only screen and (max-width: 640px) {
      .main p,
      .main td,
      .main span {
        font-size: 16px !important;
      }

      .wrapper {
        padding: 8px !important;
      }

      .content {
        padding: 0 !important;
      }

      .container {
        padding: 0 !important;
        padding-top: 8px !important;
        width: 100% !important;
      }

      .main {
        border-left-width: 0 !important;
        border-radius: 0 !important;
        border-right-width: 0 !important;
      }

      .btn table {
        max-width: 100% !important;
        width: 100% !important;
      }

      .btn a {
        font-size: 16px !important;
        max-width: 100% !important;
        width: 100% !important;
      }
    }
    @media all {
      .ExternalClass {
        width: 100%;
      }

      .ExternalClass,
      .ExternalClass p,
      .ExternalClass span,
      .ExternalClass font,
      .ExternalClass td,
      .ExternalClass div {
        line-height: 100%;
      }

      .apple-link a {
        color: inherit !important;
        font-family: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
        text-decoration: none !important;
      }

      #MessageViewBody a {
        color: inherit;
        text-decoration: none;
        font-size: inherit;
        font-family: inherit;
        font-weight: inherit;
        line-height: inherit;
      }
    }

    .logo {
      width: 120px;
      height: auto;
      margin-bottom: 32px;
    }

    .title {
      font-size: 32px;
      font-weight: bold;
      color: #1a1a1a;
      margin-bottom: 32px;
    }

    .review-button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #3366FF;
      color: #ffffff !important;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 500;
      margin: 24px 0;
    }

    .review-button:hover {
      background-color: #2952CC;
    }

    .main-content {
      padding: 40px;
      background: #ffffff;
      border-radius: 16px;
    }

    p {
      color: #4a4a4a;
      line-height: 1.5;
      margin-bottom: 16px;
    }
  </style>
</head>
<body style="font-family: Helvetica, sans-serif; -webkit-font-smoothing: antialiased; font-size: 16px; line-height: 1.3; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background-color: #f4f5f6; margin: 0; padding: 0;">
  <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f4f5f6; width: 100%;" width="100%" bgcolor="#f4f5f6">
    <tr>
      <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
      <td class="container" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; max-width: 600px; padding: 0; padding-top: 24px; width: 600px; margin: 0 auto;" width="600" valign="top">
        <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 600px; padding: 0;">
          <table role="presentation" class="main" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border-radius: 16px; width: 100%;" width="100%">
            <tr>
              <td class="main-content">
                <img src="https://appstaging.hammr.com/img/hammr-logo.png" alt="Hammr Logo" class="logo">
                <h1 class="title">Time off request</h1>
                <p>Dear {{manager}},</p>
                <p>{{employee}} has requested time off from {{start}} to {{end}}.</p>
                <p>Please review the request <a href="{{host}}/time-off-requests" >here</a></p>

              </td>
            </tr>
          </table>

          <!-- START FOOTER -->
          <div class="footer" style="clear: both; padding-top: 24px; text-align: center; width: 100%;">
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
              <tr>
                <td class="content-block" style="font-family: Helvetica, sans-serif; vertical-align: top; color: #9a9ea6; font-size: 16px; text-align: center;" valign="top" align="center">
                  <span class="apple-link" style="color: #9a9ea6; font-size: 16px; text-align: center;">Hammr Inc</span>
                  <!--                  <br> Don't like these emails? <a href="http://htmlemail.io/blog" style="text-decoration: underline; color: #9a9ea6; font-size: 16px; text-align: center;">Unsubscribe</a>.-->
                </td>
              </tr>
            </table>
          </div>

          <!-- END FOOTER -->
        </div>
      </td>
      <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
    </tr>
  </table>
</body>
</html>
