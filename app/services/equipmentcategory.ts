import { EquipmentCategory } from "@/models";

export class EquipmentCategoryService {
  private equipmentCategory = EquipmentCategory;

  async findAll(options: object): Promise<EquipmentCategory[]> {
    return await this.equipmentCategory.findAll(options);
  }

  async findOne(options: object): Promise<EquipmentCategory> {
    return await this.equipmentCategory.findOne(options);
  }

  async create(categoryObj: Partial<EquipmentCategory>): Promise<EquipmentCategory> {
    return await this.equipmentCategory.create(categoryObj);
  }

  async findOrCreate(name: string, organizationId: number): Promise<EquipmentCategory> {
    const [category] = await this.equipmentCategory.findOrCreate({
      where: {
        name,
        organizationId,
      },
      defaults: {
        name,
        organizationId,
      },
    });

    return category;
  }
}
