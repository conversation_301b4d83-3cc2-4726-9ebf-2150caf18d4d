import { toEpoch } from "@/util/dateHelper";
import { EmployeeCertification, User } from "../models";
import { Op, WhereOptions } from "sequelize";
import dayjs from "dayjs";

export class EmployeeCertificationService {
  private employeeCertification = EmployeeCertification;

  async create(employeeCertificationObj: Partial<EmployeeCertification>): Promise<EmployeeCertification> {
    const employeeCertification = await this.employeeCertification.create(employeeCertificationObj);

    const createdEmployeeCertification = await this.employeeCertification.findOne({
      where: { id: employeeCertification.id },
    });

    return serializeEmployeeCertification(createdEmployeeCertification);
  }

  async markAsArchived(options: WhereOptions<EmployeeCertification>): Promise<number[]> {
    return this.employeeCertification.update(
      {
        isArchived: true,
      },
      {
        where: options,
      }
    );
  }

  async findAll(
    organizationId: number,
    userId?: number,
    expiredOrExpiring?: boolean
  ): Promise<EmployeeCertification[]> {
    const where: WhereOptions<EmployeeCertification> = {
      organizationId,
      isArchived: false,
    };

    if (userId) {
      where["userId"] = userId;
    }

    if (expiredOrExpiring) {
      where["expirationDate"] = {
        [Op.lte]: dayjs().add(30, "days").toDate(),
      };
    }

    const result = await this.employeeCertification.findAll({
      where,
      include: [{ model: User, as: "user", attributes: ["id", "firstName", "lastName", "profilePhotoObjectId"] }],
      order: [["expirationDate", "ASC"]],
    });

    return result.map((employeeCertification) => serializeEmployeeCertification(employeeCertification));
  }
}

export function serializeEmployeeCertification(employeeCertification: EmployeeCertification): EmployeeCertification {
  const employeeCertificationJSON = employeeCertification.toJSON();
  employeeCertificationJSON.createdAt = toEpoch(employeeCertificationJSON.createdAt);
  employeeCertificationJSON.updatedAt = toEpoch(employeeCertificationJSON.updatedAt);

  return employeeCertificationJSON;
}
