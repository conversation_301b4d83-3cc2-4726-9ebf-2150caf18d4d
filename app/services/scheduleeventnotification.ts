import { ScheduleEventNotification } from "@/models";
import { UpdateOptions } from "sequelize";
import { Op } from "sequelize";

import { ScheduleService } from "@/services/schedule";
import { UserScheduleEventService } from "@/services/userscheduleevent";
import { ScheduleEvent } from "@/models";

function getScheduledAtOneMinuteAgoQuery(): { scheduledAt: { [Op.gte]: Date } } {
  const oneMinuteAgo = new Date(Date.now() - 60 * 1000);

  return {
    scheduledAt: {
      [Op.gte]: oneMinuteAgo,
    },
  };
}

export class ScheduleEventNotificationService {
  private scheduleEventNotification = ScheduleEventNotification;
  private scheduleService = new ScheduleService();
  private userScheduleEventService = new UserScheduleEventService();

  async create(
    scheduleEventNotificationObj: Partial<ScheduleEventNotification>
  ): Promise<ScheduleEventNotification> {
    const scheduleEventNotification = await this.scheduleEventNotification.create(
      scheduleEventNotificationObj
    );

    return scheduleEventNotification;
  }

  async findAll(options: object): Promise<ScheduleEventNotification[]> {
    const extendedOptions = { ...options, raw: true };

    return await this.scheduleEventNotification.findAll(extendedOptions);
  }

  async findOne(options: object): Promise<ScheduleEventNotification> {
    const extendedOptions = {
      ...options,
    };

    return await this.scheduleEventNotification.findOne(extendedOptions);
  }

  async delete(userId: number, scheduleEventId: number): Promise<number> {
    return await this.scheduleEventNotification.destroy({
      where: {
        userId: userId,
        scheduleEventId: scheduleEventId,
      },
    });
  }

  async deletePendingNotifications(scheduleEventId: number): Promise<number> {
    return await this.scheduleEventNotification.destroy({
      where: {
        scheduleEventId: scheduleEventId,
        ...getScheduledAtOneMinuteAgoQuery(),
      },
    });
  }

  async update(
    scheduleEventUpdateObj: Partial<ScheduleEventNotification>,
    options: Omit<UpdateOptions<any>, "returning">
  ): Promise<number[]> {
    return await this.scheduleEventNotification.update(scheduleEventUpdateObj, options);
  }

  // TO DO: the likely scenario is we extend this to handle multiday updates by telling it it is a multiday update
  // maybe pass linkedEventId and then downstream during the event fetch, we can apply the date shifts?
  async handleScheduledNotificationsForEventUpdate(
    scheduleEventId: number,
    newlyAddedUserIds: number[],
    newlyRemovedUserIds: number[],
    notificationScheduledAtDate?: Date,
    notifyViaText?: any,
    notifyViaPush?: any
  ) {
    // `notificationScheduledAtDate` null indicates the user removed the notification from the event on the web app
    if (notificationScheduledAtDate === null) {
      await this.deletePendingNotifications(scheduleEventId);

      return;
    }

    // handle changes that affect the users notified
    const areUsersUpdated = newlyAddedUserIds.length > 0 || newlyRemovedUserIds.length > 0;
    if (areUsersUpdated) {
      await this.handleScheduledNotificationsForUserUpdate(
        scheduleEventId,
        newlyAddedUserIds,
        newlyRemovedUserIds
      );
    }

    // handle changes to the scheduled notification itself
    await this.handleUpdatesToScheduledNotifications(
      scheduleEventId,
      notificationScheduledAtDate,
      notifyViaText,
      notifyViaPush
    );
  }

  async handleScheduledNotificationsForUserUpdate(
    scheduleEventId: number,
    newlyAddedUserIds: number[],
    newlyRemovedUserIds: number[]
  ) {
    // if there is a pending notification, just update the user ids in that notification
    const pendingNotification = await this.fetchPendingNotification(scheduleEventId);
    if (pendingNotification) {
      const scheduleUserIds = await this.fetchScheduleUserIds(scheduleEventId);
      await this.update({ userIds: scheduleUserIds }, { where: { id: pendingNotification.id } });
    } else {
      // if there is no pending notification, create a new notification for newly added or newly removed users
      // if the event is in the future

      const scheduleEvent = await ScheduleEvent.findOne({ where: { id: scheduleEventId } });
      if (!scheduleEvent || scheduleEvent.startTime < new Date()) {
        return;
      }

      const latestPastNotification = await this.fetchLatestPastNotification(scheduleEventId);

      if (latestPastNotification !== null) {
        if (newlyAddedUserIds.length > 0) {
          await this.create({
            scheduleEventId: scheduleEventId,
            notifyViaText: latestPastNotification.notifyViaText,
            notifyViaPush: latestPastNotification.notifyViaPush,
            userIds: newlyAddedUserIds,
            scheduledAt: new Date(),
            notificationType: "ADDED_USERS",
          });
        }

        if (newlyRemovedUserIds.length > 0) {
          await this.create({
            scheduleEventId: scheduleEventId,
            notifyViaText: latestPastNotification.notifyViaText,
            notifyViaPush: latestPastNotification.notifyViaPush,
            userIds: newlyRemovedUserIds,
            scheduledAt: new Date(),
            notificationType: "REMOVED_USERS",
          });
        }
      }
    }
  }

  async handleUpdatesToScheduledNotifications(
    scheduleEventId: number,
    notificationScheduledAtDate?: Date,
    notifyViaText?: any,
    notifyViaPush?: any
  ) {
    // If there is an existing pending notification for the event, update the fields of that notification
    const pendingNotification = await this.fetchPendingNotification(scheduleEventId);
    if (pendingNotification) {
      const updateData: any = {};
      if (notificationScheduledAtDate !== undefined && notificationScheduledAtDate !== null) {
        updateData.scheduledAt = notificationScheduledAtDate;
      }

      if (notifyViaText !== undefined && notifyViaText !== null) {
        updateData.notifyViaText = notifyViaText;
      }

      if (notifyViaPush !== undefined && notifyViaPush !== null) {
        updateData.notifyViaPush = notifyViaPush;
      }

      await this.update(updateData, {
        where: { id: pendingNotification.id },
      });
    } else {
      // If there is no existing notification, create a new one
      // This will happen if the user created an event without a notification and then added a notification later by editing the event
      if (notificationScheduledAtDate !== undefined && !isNaN(notificationScheduledAtDate.getTime())) {
        const scheduleUserIds = await this.fetchScheduleUserIds(scheduleEventId);
        await this.create({
          scheduleEventId: scheduleEventId,
          notifyViaText: notifyViaText ?? false,
          notifyViaPush: notifyViaPush ?? false,
          userIds: scheduleUserIds,
          scheduledAt: notificationScheduledAtDate,
          notificationType: "CREATED",
        });
      }
    }
  }

  async handleScheduledNotificationsForEventDelete(scheduleEventId: number) {
    // if there is a pending notification for the event, delete it
    const pendingNotification = await this.fetchPendingNotification(scheduleEventId);
    if (pendingNotification) {
      await this.deletePendingNotifications(scheduleEventId);
    } else {
      // if there is no pending notification, create a new notification based on the latest past notification
      await this.createNotificationBasedOnPastNotification(scheduleEventId, "CANCELLED");
    }
  }

  // Creates a new notification related to the update or deletion of a schedule event
  async createNotificationBasedOnPastNotification(scheduleEventId: number, notificationType: string) {
    const latestPastNotification = await this.fetchLatestPastNotification(scheduleEventId);
    // if users were never notified of this event, there is no need to send a notification when the event is updated or deleted
    if (latestPastNotification === null) {
      return;
    }

    const scheduleUserIds = await this.fetchScheduleUserIds(scheduleEventId);

    await this.create({
      scheduleEventId: scheduleEventId,
      notifyViaText: latestPastNotification.notifyViaText,
      notifyViaPush: latestPastNotification.notifyViaPush,
      userIds: scheduleUserIds,
      scheduledAt: new Date(),
      notificationType: notificationType,
    });
  }

  // Helper functions
  /**
   * notificationScheduleAt can be a valid timestamp, null, or undefined (not passed in the request body)
   * if it's null, we want to set it to null in the database
   * if it's undefined, we don't want to update the notificationScheduledAt field in the database
   */
  async parseNotificationScheduledAt(notificationScheduledAt: any): Promise<Date | null | undefined> {
    if (notificationScheduledAt !== undefined) {
      if (notificationScheduledAt === null) {
        return null;
      } else if (typeof notificationScheduledAt === "string") {
        const notificationScheduledAtDate = new Date(parseInt(notificationScheduledAt));
        if (isNaN(notificationScheduledAtDate.getTime())) {
          throw new Error("Invalid type for notificationScheduledAt");
        }

        return notificationScheduledAtDate;
      } else if (typeof notificationScheduledAt === "number") {
        const notificationScheduledAtDate = new Date(notificationScheduledAt);
        if (isNaN(notificationScheduledAtDate.getTime())) {
          throw new Error("Invalid type for notificationScheduledAt");
        }

        return notificationScheduledAtDate;
      }
    }

    return undefined;
  }

  /**
   *
   * @param scheduleEventId
   * @returns the latest pending notification for the schedule event, or null if there are no pending notifications
   * pending notification is defined as a notification that has not been sent yet, has a `scheduledAt` within the last 1 minute
   * since the notification cron job runs every 1 minute and we don't want to send duplicate notifications
   */
  async fetchPendingNotification(scheduleEventId: number): Promise<ScheduleEventNotification | null> {
    const oneMinuteAgo = new Date();
    oneMinuteAgo.setMinutes(oneMinuteAgo.getMinutes() - 1);

    return await this.findOne({
      where: {
        scheduleEventId: scheduleEventId,
        scheduledAt: {
          [Op.gte]: oneMinuteAgo,
        },
        sentAt: null,
      },
      order: [["scheduledAt", "DESC"]],
    });
  }

  async fetchLatestPastNotification(scheduleEventId: number): Promise<ScheduleEventNotification | null> {
    return await this.findOne({
      where: {
        scheduleEventId: scheduleEventId,
        scheduledAt: {
          [Op.lt]: new Date(),
        },
      },
      order: [["scheduledAt", "DESC"]],
    });
  }

  async fetchScheduleUserIds(scheduleEventId: number) {
    const userScheduleEvents = await this.userScheduleEventService.findAll({
      where: {
        scheduleEventId: scheduleEventId,
      },
    });

    return userScheduleEvents.map((event) => event.userId);
  }
}
