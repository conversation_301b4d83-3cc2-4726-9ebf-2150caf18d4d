import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import User from "@/models/user";
import TimeOffPolicy from "@/models/timeoffpolicy";
import { FindOptions, Op, UpdateOptions, WhereOptions } from "sequelize";
import { sendSMS } from "@/util/sendSMS";
import dayjs from "dayjs";
import AWS from "aws-sdk";
import fs from "fs";
import { join } from "node:path";

import Handlebars from "handlebars";
import TimeOffPolicyService from "@/services/timeoffpolicy";
import HTTPError from "../errors/HTTPError";
import * as Sentry from "@sentry/node";

export interface ListTimeOffRequestsInput {
  userId?: number[];
  status?: TimeOffRequestStatus[];
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

export interface TimeOffConflict {
  userId: number;
  firstName: string;
  lastName: string;
  startDate: Date;
  endDate: Date;
}

export default class TimeOffRequestService {
  timeOffPolicyService = new TimeOffPolicyService();

  async create(creator: User, data: TimeOffRequest) {
    if (creator.role !== "ADMIN" && data.userId) {
      throw new HTTPError(400, "You cannot create time-off requests for other users");
    }

    data.userId = data.userId ?? creator.id; // if there isn't a userId sent, use the authed user

    // Check if there are enough hours available in the policy
    const policy = await TimeOffPolicy.findByPk(data.timeOffPolicyId);
    if (policy.isLimited) {
      const timeOffSummary = await this.timeOffPolicyService.getTimeOffSummary(policy.id, data.userId);
      if (data.totalHours > timeOffSummary.available) {
        throw new HTTPError(400, "Not enough hours available in the selected time off policy");
      }
    }

    const approveInfo: Partial<TimeOffRequest> =
      creator.role === "ADMIN"
        ? {
            reviewedBy: creator.id,
          }
        : {
            // if it's not an admin, it will create a timerequest for himself
            userId: creator.id,
          };

    const timeOffRequest = await TimeOffRequest.create({
      ...data,
      ...approveInfo,
    });

    await timeOffRequest.reload({ include: [{ model: User, as: "user" }] });

    if (timeOffRequest?.status !== TimeOffRequestStatus.APPROVED) {
      await this.sendTimeOffRequestCreatedEmailNotification(timeOffRequest);
    }

    return this.get(timeOffRequest.id);
  }

  async update(id: number, data: Partial<TimeOffRequest>) {
    await TimeOffRequest.update(data, { where: { id } });

    return this.get(id);
  }

  async findAll(options: FindOptions<TimeOffRequest>): Promise<TimeOffRequest[]> {
    return await TimeOffRequest.findAll(options);
  }

  async staticUpdate(timeOffRequestObj: Partial<TimeOffRequest>, options: UpdateOptions) {
    return await TimeOffRequest.update(timeOffRequestObj, options);
  }

  async get(id: number): Promise<TimeOffRequest> {
    return await TimeOffRequest.findByPk(id, {
      include: [
        { model: User, as: "user" },
        { model: User, as: "reviewer" },
        { model: TimeOffPolicy, as: "timeOffPolicy" },
      ],
    });
  }

  async list(
    organizationId: number,
    data: ListTimeOffRequestsInput = {}
  ): Promise<{ timeOffRequests: TimeOffRequest[]; total: number }> {
    const { userId, status, startDate, endDate, page = 1, limit = 20 } = data;
    const filterConditions: WhereOptions<TimeOffRequest> = {};

    if (userId) {
      filterConditions.userId = { [Op.in]: userId };
    }

    const otherFilterConditions: WhereOptions<TimeOffRequest> = [];
    if (status) {
      otherFilterConditions.push({
        [Op.or]: status.map((status) => ({ status })),
      });
    }

    if (startDate && endDate) {
      otherFilterConditions.push({
        [Op.or]: [
          { startDate: { [Op.between]: [startDate, endDate] } },
          { endDate: { [Op.between]: [startDate, endDate] } },
        ],
      });
    } else {
      if (startDate) {
        filterConditions.startDate = {
          [Op.gte]: startDate,
        };
      }
      if (endDate) {
        filterConditions.endDate = {
          [Op.lte]: endDate,
        };
      }
    }

    const { rows, count } = await TimeOffRequest.findAndCountAll({
      where: { ...filterConditions, ...(otherFilterConditions.length ? { [Op.and]: otherFilterConditions } : {}) },
      subQuery: false,
      include: [
        { model: User, as: "user" },
        { model: User, as: "reviewer" },
        { model: TimeOffPolicy, as: "timeOffPolicy", where: { organizationId } },
      ],
      order: [["startDate", "DESC"]],
      limit: limit || undefined, // `undefined` is used to return everything when pagination is not needed
      offset: (page - 1) * limit,
    });

    return {
      timeOffRequests: rows,
      total: count,
    };
  }

  async approve(id: number, reviewedBy: number) {
    const request = await TimeOffRequest.findByPk(id, {
      include: [{ model: User, as: "user" }],
    });

    const updatedRequest = await request.update({
      reviewedBy,
    });

    try {
      await this.sendStatusUpdateSMS(request, "approved");
    } catch (error) {
      Sentry.captureException(error);
    }

    return updatedRequest;
  }

  async decline(id: number, reviewedBy: number, declineNotes: string) {
    const request = await TimeOffRequest.findByPk(id, {
      include: [{ model: User, as: "user" }],
    });

    const updatedRequest = await request.update({
      reviewedBy,
      declineNotes,
    });

    await this.sendStatusUpdateSMS(request, "declined");

    return updatedRequest;
  }

  async delete(id: number) {
    return TimeOffRequest.destroy({ where: { id } });
  }

  async sendStatusUpdateSMS(request: TimeOffRequest, status: "approved" | "declined") {
    if (request.user?.phone) {
      const formattedStartDate = dayjs(request.startDate).format("MMM DD, YYYY");
      const formattedEndDate = dayjs(request.endDate).format("MMM DD, YYYY");

      await sendSMS(
        request.user.phone,
        `Your time off request from ${formattedStartDate} to ${formattedEndDate} has been ${status}.`
      );
    }
  }

  async sendTimeOffRequestCreatedEmailNotification(timeOffRequest: TimeOffRequest) {
    const formattedStartDate = dayjs(timeOffRequest.startDate).format("MMM DD, YYYY");
    const formattedEndDate = dayjs(timeOffRequest.endDate).format("MMM DD, YYYY");

    // Get all admin users from the same company to notify them over email
    const adminUsers = await User.findAll({
      where: {
        organizationId: timeOffRequest.user.organizationId,
        role: "ADMIN",
      },
    });

    if (!adminUsers.length) {
      return;
    }

    const emailTemplate = fs.readFileSync(join(__dirname, "time-off", "time-off-requested.handlebars"), "utf8");

    const emailPromises = adminUsers
      .filter((user) => user.email)
      .map((admin) => {
        const data = {
          manager: `${admin.firstName} ${admin.lastName}`,
          employee: `${timeOffRequest.user.firstName} ${timeOffRequest.user.lastName}`,
          start: formattedStartDate,
          end: formattedEndDate,
          host: process.env.APP_URL as string,
        };

        const email = Handlebars.compile(emailTemplate)(data);

        return new AWS.SES({ apiVersion: "2010-12-01" })
          .sendEmail({
            Destination: {
              ToAddresses: [admin.email],
            },
            Message: {
              Body: {
                Html: {
                  Charset: "UTF-8",
                  Data: email,
                },
                Text: {
                  Charset: "UTF-8",
                  Data: `
                Dear ${data.manager},
                  ${data.employee} has requested time off from ${data.start} to ${data.end}. 
                  Please review the request [here](${data.host}/time-off-requests).
                `,
                },
              },
              Subject: {
                Charset: "UTF-8",
                Data: `Time-Off Request from ${data.employee}`,
              },
            },
            Source: "<EMAIL>",
          })
          .promise();
      });

    return Promise.all(emailPromises);
  }

  async getTimeOffConflicts(userIds: number[], startDate: Date, endDate: Date): Promise<TimeOffConflict[]> {
    const timeOffRequests = await TimeOffRequest.findAll({
      where: {
        userId: {
          [Op.in]: userIds,
        },
        status: {
          [Op.in]: [TimeOffRequestStatus.APPROVED, TimeOffRequestStatus.PAID],
        },
        [Op.or]: [
          {
            startDate: {
              [Op.between]: [startDate, endDate],
            },
          },
          {
            endDate: {
              [Op.between]: [startDate, endDate],
            },
          },
          {
            [Op.and]: [
              {
                startDate: {
                  [Op.lte]: startDate,
                },
              },
              {
                endDate: {
                  [Op.gte]: endDate,
                },
              },
            ],
          },
        ],
      },
      include: [
        {
          model: User,
          as: "user",
        },
      ],
    });

    return timeOffRequests.map((request) => ({
      userId: request.userId,
      firstName: request.user.firstName,
      lastName: request.user.lastName,
      startDate: request.startDate,
      endDate: request.endDate,
    }));
  }
}
