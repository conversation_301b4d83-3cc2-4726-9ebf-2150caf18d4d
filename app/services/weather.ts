import fetch from "node-fetch";
import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import dayjsPluginUTC from "dayjs/plugin/utc";
import dayjsPluginTimezone from "dayjs/plugin/timezone";

dayjs.extend(advancedFormat);
dayjs.extend(dayjsPluginUTC);
dayjs.extend(dayjsPluginTimezone);

const RAPID_API_KEY = process.env.RAPID_API_KEY;
const RAPID_API_HOST = process.env.RAPID_API_HOST;
const METEOSTAT_BASE_URL = "https://meteostat.p.rapidapi.com";

export interface WeatherDataPoint {
  icon: string;
  temperature: number;
  time: string;
  wind: number;
  precipitation: number;
  humidity: number;
}

/**
 * Properties returned from a weather station observation
 * Contains current conditions and measurements including:
 * - Temperature, wind, pressure readings
 * - Precipitation amounts
 * - Cloud cover and visibility
 * - Various calculated indices (heat index, wind chill)
 * Each measurement includes units, values and quality control flags
 */

const SUN_ICON =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2LjAwMjYgMjRDMTEuNTg0MyAyNCA4LjAwMjYgMjAuNDE4MyA4LjAwMjYgMTZDOC4wMDI2IDExLjU4MTggMTEuNTg0MyA4LjAwMDA0IDE2LjAwMjYgOC4wMDAwNEMyMC40MjA5IDguMDAwMDQgMjQuMDAyNiAxMS41ODE4IDI0LjAwMjYgMTZDMjQuMDAyNiAyMC40MTgzIDIwLjQyMDkgMjQgMTYuMDAyNiAyNFpNMTYuMDAyNiAyMS4zMzM0QzE4Ljk0ODEgMjEuMzMzNCAyMS4zMzU5IDE4Ljk0NTUgMjEuMzM1OSAxNkMyMS4zMzU5IDEzLjA1NDUgMTguOTQ4MSAxMC42NjY3IDE2LjAwMjYgMTAuNjY2N0MxMy4wNTcxIDEwLjY2NjcgMTAuNjY5MyAxMy4wNTQ1IDEwLjY2OTMgMTZDMTAuNjY5MyAxOC45NDU1IDEzLjA1NzEgMjEuMzMzNCAxNi4wMDI2IDIxLjMzMzRaTTE0LjY2OTMgMS4zMzMzN0gxNy4zMzU5VjUuMzMzMzdIMTQuNjY5M1YxLjMzMzM3Wk0xNC42NjkzIDI2LjY2NjdIMTcuMzM1OVYzMC42NjY3SDE0LjY2OTNWMjYuNjY2N1pNNC42ODg5IDYuNTcxOTVMNi41NzQ1MSA0LjY4NjMzTDkuNDAyOTQgNy41MTQ3Nkw3LjUxNzMyIDkuNDAwMzdMNC42ODg5IDYuNTcxOTVaTTIyLjYwMjIgMjQuNDg1NEwyNC40ODc5IDIyLjU5OTZMMjcuMzE2MyAyNS40MjgyTDI1LjQzMDcgMjcuMzEzOEwyMi42MDIyIDI0LjQ4NTRaTTI1LjQzMDcgNC42ODYzM0wyNy4zMTYzIDYuNTcxOTVMMjQuNDg3OSA5LjQwMDM3TDIyLjYwMjIgNy41MTQ3NkwyNS40MzA3IDQuNjg2MzNaTTcuNTE3MzIgMjIuNTk5Nkw5LjQwMjk0IDI0LjQ4NTRMNi41NzQ1MSAyNy4zMTM4TDQuNjg4OSAyNS40MjgyTDcuNTE3MzIgMjIuNTk5NlpNMzAuNjY5MyAxNC42NjY3VjE3LjMzMzRIMjYuNjY5M1YxNC42NjY3SDMwLjY2OTNaTTUuMzM1OTQgMTQuNjY2N1YxNy4zMzM0SDEuMzM1OTRWMTQuNjY2N0g1LjMzNTk0WiIgZmlsbD0iI0Y2QjUxRSIvPgo8L3N2Zz4K";
const CLOUD_ICON =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjY2OTMgOC4wMDAwNEM3Ljg4MjggOC4wMDAwNCA0LjAwMjYgMTEuODgwMiA0LjAwMjYgMTYuNjY2N0M0LjAwMjYgMjEuNDUzMiA3Ljg4MjggMjUuMzMzNCAxMi42NjkzIDI1LjMzMzRIMjIuMDAyNkMyNS4zMTYzIDI1LjMzMzQgMjguMDAyNiAyMi42NDcxIDI4LjAwMjYgMTkuMzMzNEMyOC4wMDI2IDE2LjAxOTYgMjUuMzE2MyAxMy4zMzM0IDIyLjAwMjYgMTMuMzMzNEMyMS41NjQ5IDEzLjMzMzQgMjEuMTM4MSAxMy4zODAzIDIwLjcyNyAxMy40NjkyQzE5LjQ1NDYgMTAuMjY1MyAxNi4zMjY1IDguMDAwMDQgMTIuNjY5MyA4LjAwMDA0Wk0yMi4wMDI2IDI4SDEyLjY2OTNDNi40MTAwNCAyOCAxLjMzNTk0IDIyLjkyNTkgMS4zMzU5NCAxNi42NjY3QzEuMzM1OTQgMTAuNDA3NSA2LjQxMDA0IDUuMzMzMzcgMTIuNjY5MyA1LjMzMzM3QzE2LjcyNyA1LjMzMzM3IDIwLjI4NjcgNy40NjU4OSAyMi4yODg3IDEwLjY3MTNDMjYuOTQyNyAxMC44MjIzIDMwLjY2OTMgMTQuNjQyNyAzMC42NjkzIDE5LjMzMzRDMzAuNjY5MyAyNC4xMTk5IDI2Ljc4OTEgMjggMjIuMDAyNiAyOFoiIGZpbGw9IiNDQUNGRDgiLz4KPC9zdmc+Cg==";
const RAIN_ICON =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYuNjY5MjcgMjIuNTcyOUMzLjQ4MDk5IDIwLjcyODYgMS4zMzU5NCAxNy4yODE0IDEuMzM1OTQgMTMuMzMzM0MxLjMzNTk0IDcuNDQyMjUgNi4xMTE1NiAyLjY2NjYzIDEyLjAwMjYgMi42NjY2M0MxNi41MjkxIDIuNjY2NjMgMjAuMzk3MSA1LjQ4NjE3IDIxLjk0NjEgOS40NjQ4MUMyMi4zOTYxIDkuMzc4NDkgMjIuODYwNyA5LjMzMzI5IDIzLjMzNTkgOS4zMzMyOUMyNy4zODYxIDkuMzMzMjkgMzAuNjY5MyAxMi42MTY1IDMwLjY2OTMgMTYuNjY2NkMzMC42NjkzIDIwLjAyMzQgMjguNDEzOSAyMi44NTMzIDI1LjMzNTkgMjMuNzI0VjIwLjg4NDJDMjYuOTEyNiAyMC4xMzUzIDI4LjAwMjYgMTguNTI4MiAyOC4wMDI2IDE2LjY2NjZDMjguMDAyNiAxNC4wODkzIDI1LjkxMzMgMTIgMjMuMzM1OSAxMkMyMi4wMjk5IDEyIDIwLjg0OTQgMTIuNTM2NCAyMC4wMDIzIDEzLjQwMDlDMjAuMDAyNSAxMy4zNzg0IDIwLjAwMjYgMTMuMzU1OCAyMC4wMDI2IDEzLjMzMzNDMjAuMDAyNiA4LjkxNTAxIDE2LjQyMDkgNS4zMzMyOSAxMi4wMDI2IDUuMzMzMjlDNy41ODQzMiA1LjMzMzI5IDQuMDAyNiA4LjkxNTAxIDQuMDAyNiAxMy4zMzMzQzQuMDAyNiAxNS43MDI2IDUuMDMyNiAxNy44MzE0IDYuNjY5MjcgMTkuMjk2MlYyMi41NzI5WiIgZmlsbD0iI0NBQ0ZEOCIvPgo8cGF0aCBkPSJNMTEuOTk0OCAyMS4zMzM1SDkuMzI4MTJWMjYuNjY2OEgxMS45OTQ4VjIxLjMzMzVaIiBmaWxsPSIjMzM1Q0ZGIi8+CjxwYXRoIGQ9Ik0yMi42NjE1IDIxLjMzMzVIMTkuOTk0OFYyNi42NjY4SDIyLjY2MTVWMjEuMzMzNVoiIGZpbGw9IiMzMzVDRkYiLz4KPHBhdGggZD0iTTE3LjMyODEgMjUuMzMzNUgxNC42NjE1VjMwLjY2NjhIMTcuMzI4MVYyNS4zMzM1WiIgZmlsbD0iIzMzNUNGRiIvPgo8L3N2Zz4K";
const SNOW_ICON =
  "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNCAyNCI+CiAgPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI5LjIuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDIuMS4wIEJ1aWxkIDExNikgIC0tPgogIDxkZWZzPgogICAgPHN0eWxlPgogICAgICAuc3QwIHsKICAgICAgICBmaWxsOiAjY2FjZmQ4OwogICAgICB9CgogICAgICAuc3QxIHsKICAgICAgICBmaWxsOiAjMzM1Y2ZmOwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMTcsMTh2LTJoLjVjMS45LDAsMy41LTEuNiwzLjUtMy41cy0xLjYtMy41LTMuNS0zLjUtMS45LjQtMi41LDEuMWMwLDAsMCwwLDAsMCwwLTMuMy0yLjctNi02LTZzLTYsMi43LTYsNiwxLjcsNC44LDQsNS43djIuMWMtMy41LS45LTYtNC02LTcuN1M0LjYsMiw5LDJzNi4zLDIuMSw3LjUsNS4xYy4zLDAsLjcsMCwxLDAsMywwLDUuNSwyLjUsNS41LDUuNXMtMi41LDUuNS01LjUsNS41aC0uNVoiLz4KICA8cG9seWdvbiBjbGFzcz0ic3QxIiBwb2ludHM9IjEzIDE2LjMgMTUgMTUuMSAxNiAxNi45IDE0IDE4IDE2IDE5LjEgMTUgMjAuOSAxMyAxOS43IDEzIDIyIDExIDIyIDExIDE5LjcgOSAyMC45IDggMTkuMSAxMCAxOCA4IDE2LjkgOSAxNS4xIDExIDE2LjMgMTEgMTQgMTMgMTQgMTMgMTYuMyIvPgo8L3N2Zz4=";
const TARGET_HOURS = ["8 AM", "12 PM", "4 PM"];

interface MeteostatPoint {
  time: string;
  temp: number;
  dwpt: number;
  rhum: number;
  prcp: number;
  snow: number;
  wdir: number;
  wspd: number;
  wpgt: number;
  pres: number;
  tsun: number;
  coco: number;
}

interface StationMetadata {
  id: string;
  name: {
    [key: string]: string;
  };
  country: string;
  region: string;
  identifiers: {
    national: string;
    wmo: string;
    icao: string;
  };
  location: {
    latitude: number;
    longitude: number;
    elevation: number;
  };
  timezone: string;
  inventory: {
    model: {
      start: string;
      end: string;
    };
    hourly: {
      start: string;
      end: string;
    };
    daily: {
      start: string;
      end: string;
    };
    monthly: {
      start: string;
      end: string;
    };
    normals: {
      start: number;
      end: number;
    };
  };
  // field added by us
  fallbackStationId?: string;
}

interface StationProps {
  address: { coordinates: { lat: number; long: number }; streetAddress: string };
}

interface WeatherProps {
  stationMetadata: StationMetadata;
  timezone: string;
  date: string;
}

export class WeatherService {
  async getStationData({ address }: StationProps) {
    const { lat, long } = await this.getCoordinates(address);

    const stationResponse = await fetch(`${METEOSTAT_BASE_URL}/stations/nearby?` + `lat=${lat}` + `&lon=${long}`, {
      headers: {
        "x-rapidapi-host": RAPID_API_HOST,
        "x-rapidapi-key": RAPID_API_KEY,
      },
    });

    const stationData = await stationResponse.json();

    // Get the two closest stations
    const closestStations = stationData.data?.slice(0, 2) || [];

    if (!closestStations.length) {
      return null;
    }

    //t Get metadata for the primary station
    const stationMetadata = await fetch(`${METEOSTAT_BASE_URL}/stations/meta?id=${closestStations[0].id}`, {
      headers: {
        "x-rapidapi-host": RAPID_API_HOST,
        "x-rapidapi-key": RAPID_API_KEY,
      },
    });

    const stationMetadataData = await stationMetadata.json();

    // Add the fallback station ID if available
    if (closestStations.length > 1) {
      stationMetadataData.data.fallbackStationId = closestStations[1].id;
    }

    return stationMetadataData.data;
  }

  async getWeatherData({
    stationMetadata,
    date = dayjs().format("YYYY-MM-DD"),
    timezone = "America/Los_Angeles",
  }: WeatherProps): Promise<WeatherDataPoint[]> {
    if (!stationMetadata) {
      return [];
    }

    try {
      // Try with the primary station first
      let weatherData = await this.fetchStationWeatherData(stationMetadata.id, date, timezone);

      // If primary station has no data and we have a fallback, try the fallback
      if ((!weatherData.data || !weatherData.data.length) && stationMetadata.fallbackStationId) {
        weatherData = await this.fetchStationWeatherData(stationMetadata.fallbackStationId, date, timezone);
      }

      if (!weatherData.data || !weatherData.data.length) {
        return [];
      }

      // Transform the data into our WeatherDataPoint format
      return TARGET_HOURS.map((targetHour) => {
        // Use the station's timezone when parsing times
        const hour = parseInt(dayjs(targetHour, "h A").tz(timezone).format("H"), 10);
        const hourData =
          weatherData.data.find((d: MeteostatPoint) => {
            // Parse API time with the correct timezone
            return dayjs(d.time).tz(timezone).hour() === hour;
          }) || {};

        return {
          // Format the display time in the local timezone
          time: targetHour,
          temperature: typeof hourData.temp === "number" ? Math.round(this.celsiusToFahrenheit(hourData.temp)) : null,
          wind: typeof hourData.wspd === "number" ? Math.round(hourData.wspd) : null,
          precipitation: typeof hourData.prcp === "number" ? hourData.prcp : null,
          humidity: typeof hourData.rhum === "number" ? Math.round(hourData.rhum) : null,
          icon: this.determineIcon(hourData),
        };
      });
    } catch (error) {
      console.error("Error fetching weather data:", error);

      return [];
    }
  }

  // New helper method to fetch weather data for a specific station
  private async fetchStationWeatherData(stationId: string, date: string, timezone: string) {
    const response = await fetch(
      `${METEOSTAT_BASE_URL}/stations/hourly?` +
        `station=${stationId}` +
        `&start=${date}` +
        `&end=${date}` +
        `&tz=${timezone}`,
      {
        headers: {
          "x-rapidapi-host": RAPID_API_HOST,
          "x-rapidapi-key": RAPID_API_KEY,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Meteostat API error: ${response.status}`);
    }

    return await response.json();
  }

  private celsiusToFahrenheit(celsius: number): number {
    return (celsius * 9) / 5 + 32;
  }

  private determineIcon(data: MeteostatPoint): string {
    // Check for actual snow data first if available
    if (data.snow > 0) {
      return SNOW_ICON;
    }

    // 14	Light Snowfall
    // 15	Snowfall
    // 16	Heavy Snowfall
    const SNOW_CODES = [14, 15, 16];

    if (SNOW_CODES.includes(data.coco)) {
      return SNOW_ICON;
    }

    if (data.prcp > 0) {
      return RAIN_ICON;
    }

    // Cloud cover (coco) 8 base scale:
    // 0 = Clear sky (0-1)
    // 1 = Fair (1-3)
    // 2 = Partly cloudy (3-5)
    // 3 = Mostly cloudy (5-7)
    // 4 = Overcast (7-8)
    switch (data.coco) {
      case 0:
      case 1:
        return SUN_ICON;
      default:
        return CLOUD_ICON;
    }
  }

  private async getCoordinates(address: { coordinates?: { lat: number; long: number }; streetAddress?: string }) {
    if (address.coordinates?.lat && address.coordinates?.long) {
      return {
        lat: address.coordinates.lat,
        long: address.coordinates.long,
      };
    }
    throw new Error("Geocoding not implemented");
  }
}
