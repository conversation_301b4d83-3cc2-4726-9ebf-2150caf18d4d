import { User, Crew, CrewM<PERSON>ber } from "@/models";
import { UpdateOptions } from "sequelize";

export class CrewsService {
  private crew = Crew;

  async findAll(organizationId: number): Promise<Crew[]> {
    return await this.crew.findAll({
      where: {
        organizationId,
      },
      include: [
        {
          model: CrewM<PERSON>ber,
          attributes: ["id"],
          include: [
            {
              model: User,
              as: "crewMemberUser",
              attributes: ["firstName", "lastName", "id"],
              where: { isArchived: false },
            },
          ],
        },
        {
          model: User,
          as: "crewLeadUser",
          attributes: ["firstName", "lastName", "id"],
        },
      ],
    });
  }

  async create(crewObj: Partial<Crew>): Promise<Crew> {
    return await this.crew.create(crewObj);
  }

  async update(crewObj: Partial<Crew>, options: Omit<UpdateOptions<any>, "returning">): Promise<number[]> {
    return await this.crew.update(crewObj, options);
  }

  async delete(options: any): Promise<number> {
    return this.crew.destroy({ where: options });
  }
}
