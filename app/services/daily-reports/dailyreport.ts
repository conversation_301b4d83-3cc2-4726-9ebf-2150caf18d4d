import DailyReport from "@/models/dailyreport";
import Project from "@/models/project";
import { toEpoch } from "@/util/dateHelper";
import { User } from "../../models";
import Organization from "@/models/organization";
import { join } from "path";
import fs from "fs";
import AWS from "aws-sdk";
import { bucket, getSignedUrl } from "@/controllers/sts";
import { ExtendedTimesheet } from "@/models/timesheet";
import dayjs from "dayjs";
import { convertMinutesToHoursWorked } from "@/util/timeHelper";
import { WeatherDataPoint } from "../weather";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { getJsReportService, releaseJsReportService } from "@/services/reports/JsReportService";

dayjs.extend(utc);
dayjs.extend(timezone);

interface DailyReportTemplateData {
  date: string;
  imageUrls: (string | null)[];
  companyName: string;
  projectName: string;
  projectAddress: string;
  timesheets: {
    name: string;
    clockIn: string;
    clockOut: string;
    regHours: string;
    otHours: string;
    totalHours: string;
  }[];
  signOffDate: string;
  signOffDateWithTime: string;
  employeesOnSite: number;
  totalHours: string;
  weatherData: WeatherDataPoint[];
}

export class DailyReportService {
  private dailyReport = DailyReport;
  private jsReportOptions = {
    httpPort: 5001,
  };

  async count(options: any): Promise<number> {
    const result = await this.dailyReport.count(options);
    // If result is an array (GroupedCountResultItem[]), sum up the counts
    if (Array.isArray(result)) {
      return result.reduce((sum, item) => sum + Number(item.count), 0);
    }

    // Otherwise return as is (it's already a number)
    return result;
  }

  async create(dailyReportObj: Partial<DailyReport>): Promise<DailyReport> {
    const dailyReport = await this.dailyReport.create(dailyReportObj);

    const createdDailyReport = await this.dailyReport.findOne({
      where: { id: dailyReport.id },
      include: [
        {
          model: Project,
          attributes: ["id", "name"],
          required: true,
        },
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          required: true,
        },
      ],
    });

    return serializeDailyReport(createdDailyReport);
  }

  async findAll(options: any): Promise<DailyReport[]> {
    const extendedOptions = extendOptions(options);
    let dailyReports = await this.dailyReport.findAll({
      ...extendedOptions,
    });
    dailyReports = dailyReports.map((dailyReport) => {
      return serializeDailyReport(dailyReport);
    });

    return dailyReports;
  }

  async generatePdf({
    dailyReport,
    organization,
    selectedPhotoIds,
    project,
    timesheets,
    totalHours,
    weatherData,
    timezone,
  }: {
    dailyReport: Partial<DailyReport>;
    organization: Organization;
    selectedPhotoIds: string[];
    project: Project;
    timesheets: Partial<ExtendedTimesheet>[];
    totalHours: string;
    weatherData: WeatherDataPoint[];
    timezone: string;
  }) {
    let jsReportService = null;
    try {
      // get a fresh instance for this specific report generation
      jsReportService = getJsReportService(this.jsReportOptions);

      const organizationId = organization.id;

      // Transform Sequelize models into plain objects
      const plainTimesheets = timesheets.map((timesheet) => ({
        name: timesheet.user.firstName + " " + timesheet.user.lastName,
        clockIn: dayjs(timesheet.clockIn).tz(timezone).format("hh:mm A"),
        clockOut: timesheet.clockOut ? dayjs(timesheet.clockOut).tz(timezone).format("hh:mm A") : "-",
        regHours: convertMinutesToHoursWorked(timesheet.regularMinutes),
        otHours: convertMinutesToHoursWorked(timesheet.overtimeMinutes),
        totalHours: convertMinutesToHoursWorked(timesheet.regularMinutes + timesheet.overtimeMinutes),
      }));

      const imageUrls = await Promise.all(
        selectedPhotoIds.map(async (objectId) => {
          const params = {
            Bucket: `${bucket}/${organizationId}/project-photos`,
            Key: objectId,
            Expires: 21600,
          };

          const s3 = new AWS.S3();

          try {
            await s3.headObject({ Bucket: params.Bucket, Key: params.Key }).promise();
            const url = await getSignedUrl(params);

            return url;
          } catch (err) {
            try {
              const url = await getSignedUrl({
                ...params,
                Bucket: `${bucket}/${organizationId}/daily-report-photos`,
              });

              return url;
            } catch (_err) {
              return null;
            }
          }
        })
      );

      const dailyReportTemplate = fs.readFileSync(join(__dirname, "daily-report.handlebars"), "utf8");
      const pdf = await jsReportService.render<DailyReportTemplateData>(
        {
          content: dailyReportTemplate,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            marginTop: "20px",
            marginBottom: "20px",
            marginLeft: "20px",
            marginRight: "20px",
            format: "A4",
            printBackground: true,
            displayHeaderFooter: false,
            pdfOptions: {
              preferCSSPageSize: true,
              printBackground: true,
              // the metadata won't show up in the browser pdf viewer - we'd have to trigger a download of the document
              // whenever we start supporting downloading the pdf directly from the client this will be useful
              metadata: {
                title: `Daily Report - ${project.name} - ${dayjs(dailyReport.date).format("MMMM D, YYYY")}`,
                author: organization.name,
                producer: "Hammr",
                creator: "Hammr Web App",
                creationDate: new Date().toISOString(),
              },
            },
          },
        },
        {
          ...dailyReport,
          date: dayjs(dailyReport.date).format("MMMM D, YYYY"),
          imageUrls: imageUrls as (string | null)[],
          companyName: organization.name,
          projectName: project.name,
          projectAddress: project.address,
          timesheets: plainTimesheets,
          signOffDate: dayjs().tz(organization.timezone).format("MM/DD/YYYY"),
          signOffDateWithTime: dayjs().tz(organization.timezone).format("MM/DD/YYYY hh:mm A z"),
          employeesOnSite: timesheets.length,
          totalHours,
          weatherData,
        }
      );

      return pdf;
    } finally {
      // only release the service instance we created for this specific call
      if (jsReportService) {
        releaseJsReportService(this.jsReportOptions);
      }
    }
  }
}

function extendOptions(options: any) {
  const whereClause = options.where || {};

  return {
    ...options,
    where: whereClause,
    include: [
      {
        model: Project,
        attributes: ["id", "name"],
        required: true,
      },
      {
        model: User,
        attributes: ["id", "firstName", "lastName"],
        required: true,
      },
    ],
  };
}

export function serializeDailyReport(dailyReport: DailyReport): any {
  const dailyReportJSON = dailyReport.toJSON();
  dailyReportJSON.createdAt = toEpoch(dailyReportJSON.createdAt);
  dailyReportJSON.updatedAt = toEpoch(dailyReportJSON.updatedAt);
  dailyReportJSON.date = toEpoch(dailyReportJSON.date);

  return dailyReportJSON;
}
