</html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400..700&display=swap" rel="stylesheet" />

  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
    }


    .root {
      section {
        background: white;
        padding: 16px;
        border-radius: 16px;
        border: 1px solid #E1E4EA;
        margin-top: 12px;

        h3 {
          background: #EE6023;
          color: white;
          border-radius: 6px;
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }

    .font-signature {
      font-family: "Caveat", cursive;
    }

    .text-muted {
      color: #525866;
      font-size: 7px;
    }

    .text-small {
      font-size: 10px;
    }

    .black {
      color: #0E121B;
    }

    .root>section>div:first-of-type {
      margin-top: 16px;
    }

    table {
      width: 100%;
      margin-top: 8px;
      border-collapse: collapse;
    }

    th {
      background: #F5F7FA;
      color: #525866;
      font-size: 7px;
      font-weight: 400;
      text-align: left;
      padding: 8px;
      border-radius: 6px;
    }

    td {
      font-size: 7px;
      color: #0E121B;
      font-weight: 400;
      padding: 8px;
    }

    th:first-child,
    td:first-child {
      min-width: 130px;
    }

    th:not(:first-child),
    td:not(:first-child) {
      flex-grow: 1;
    }
  </style>

</head>

<body>
  <div class="root">

    <div class="flex justify-between">

      <div>
        <h2 class="font-medium">
          {{projectName}}
        </h2>

        <div class="text-muted">
          {{projectAddress}}
        </div>
      </div>

      <div>
        <h2 class="font-medium">
          {{date}}
        </h2>
        <div class="text-muted">
          {{companyName}}
        </div>
      </div>
    </div>

    <section>
      <h3>Weather</h3>

      <div class="grid grid-cols-3 gap-4">
        {{#each weatherData}}
        <div class="flex flex-col items-center">
          <div class="flex items-center gap-2">
            <img src="{{this.icon}}" alt="Weather icon" class="w-12 h-12" />
            <div class="text-2xl font-medium mt-2">{{this.temperature}}°</div>
          </div>
          <div class="flex items-center gap-2.5 mt-2.5">
            <div class="text-sm mt-1 text-[#0E121B]">{{this.time}}</div>
            <div class="text-muted">
              <div>
                <span>
                  Wind:
                </span>
                <span>{{this.wind}} mph</span>
              </div>
              <div>
                Precipitation:
                <span>{{this.precipitation}}"</span>
              </div>
              <div>
                Humidity:
                <span>{{this.humidity}}%</span>
              </div>
            </div>
          </div>
        </div>
        {{/each}}
      </div>

      {{#if weatherData.length}}
      <div class="text-muted" style="font-size: 6px; margin-top: 8px; text-align: center;">
        Source: meteostat.net
      </div>
      {{/if}}
    </section>

    <section>
      <h3>Summary</h3>

      <div class="text-[10px] text-[#0E121B]">
        {{summary}}
      </div>

      <div class="grid grid-cols-4 gap-[5px] mt-4">
        {{#each imageUrls}}
        <img src="{{this}}" alt="Image" class="h-[172px] aspect-square object-cover rounded-[6px]" />
        {{/each}}
      </div>
    </section>

    <section>
      <h3>Project Data</h3>

      <div class="flex w-full gap-3">
        <div class="border-[0.5px] border-[#E1E4EA] rounded-[6px] p-2 flex-grow">
          <h5 class="text-small black font-medium">Employees On Site</h5>
          <div class="font-medium text-center text-[#0E121B]">
            {{employeesOnSite}}
          </div>
        </div>

        <div class="border-[0.5px] border-[#E1E4EA] rounded-[6px] p-2 flex-grow">
          <h5 class="text-small black font-medium">Total Hours</h5>
          <div class="font-medium text-center text-[#0E121B]">
            {{totalHours}}
          </div>
        </div>
      </div>

      <h4 class="text-small black font-medium mt-4">Employees Summary</h4>

      <table>
        <thead>
          <tr>
            <th>Employee</th>
            <th>Clock In</th>
            <th>Clock Out</th>
            <th>Regular Hours</th>
            <th>Overtime Hours</th>
            <th>Total Hours</th>
          </tr>
        </thead>

        <tbody>
          {{#each timesheets}}
          <tr>
            <td>{{this.name}}</td>
            <td>{{this.clockIn}}</td>
            <td>{{this.clockOut}}</td>
            <td>{{this.regHours}}</td>
            <td>{{this.otHours}}</td>
            <td>{{this.totalHours}}</td>
          </tr>
          {{/each}}
        </tbody>
      </table>
    </section>

    <section>
      <h3>Injury Notes</h3>

      <div class="text-[10px] text-[#0E121B]">
        {{injuryNotes}}
      </div>
    </section>

    <section>
      <h3>Equipment Notes</h3>

      <div class="text-[10px] text-[#0E121B]">
        {{equipmentNotes}}
      </div>
    </section>


    <section>
      <h3>Material Notes</h3>

      <div class="text-[10px] text-[#0E121B]">
        {{materialNotes}}
      </div>
    </section>


    <section>
      <h3>Sign Off</h3>

      <div class="text-[10px] text-[#0E121B]">
        <p>
          I certify that this report is correct to the best of my knowledge.
        </p>
        <div class="max-w-[200px] mt-3">
          <div class="font-signature border-b border-[#E1E4EA] pb-2 text-xl font-bold">
            {{signOffName}}
          </div>
          <div class="flex justify-between mt-1">
            <div>
              {{signOffName}}
            </div>

            <div>
              {{signOffDate}}
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="mt-3 flex justify-between">
      <div class="text-muted">
        <div>Prepared by: {{signOffName}}</div>
        <div class="mt-2">{{signOffDateWithTime}}</div>

      </div>

      <div>

        <div class="text-muted">Generated by:</div>
        <img style="height: 14px"
          src="data:image/png;base64,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" />
      </div>

    </div>



  </div>
</body>

</html>