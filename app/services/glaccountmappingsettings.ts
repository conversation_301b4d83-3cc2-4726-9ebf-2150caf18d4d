import { IntegrationUserToken } from "@/models";
import { GL_ACCOUNT_MAPPING_SETTINGS } from "@/models/glaccountmappingsetting";
import GlAccountMappingSetting from "@/models/glaccountmappingsetting";

interface GlAccountMappingSettingsData {
  glAccountMappingSettings: GlAccountMappingSetting;
  glAccountMappingSettingTypes: typeof GL_ACCOUNT_MAPPING_SETTINGS;
}

export class GlAccountMappingSettingsService {
  private glAccountMappingSetting = GlAccountMappingSetting;

  async getOne(organizationId: number, integrationUserTokenId: number): Promise<GlAccountMappingSettingsData> {
    const foundIntegrationUserToken = await IntegrationUserToken.findOne({
      where: {
        organizationId,
        id: integrationUserTokenId,
      },
    });

    const glAccountMappingSettings = await this.glAccountMappingSetting.findOne({
      where: {
        organizationId,
        integrationUserTokenId: foundIntegrationUserToken.id,
      },
    });

    const combinedData = {
      glAccountMappingSettings,
      glAccountMappingSettingTypes: GL_ACCOUNT_MAPPING_SETTINGS,
    };

    return combinedData;
  }

  async create(
    body: Partial<GlAccountMappingSetting & { integrationUserTokenId: number }>,
    userId: number,
    organizationId: number
  ): Promise<GlAccountMappingSetting | null> {
    const foundIntegrationUserToken = await IntegrationUserToken.findOne({
      where: {
        organizationId,
        id: body.integrationUserTokenId,
      },
    });

    if (!foundIntegrationUserToken) {
      return null;
    }

    // create a new mapping
    return await this.glAccountMappingSetting.create({
      ...body,
      createdBy: userId,
      integrationUserTokenId: foundIntegrationUserToken.id,
      organizationId,
    });
  }

  async update(
    body: Partial<GlAccountMappingSetting & { integrationUserTokenId: number }>,
    userId: number,
    glAccountMappingSettingId: number,
    organizationId: number
  ): Promise<GlAccountMappingSetting | null> {
    // find the mapping
    const mapping = await this.glAccountMappingSetting.findOne({
      where: {
        id: glAccountMappingSettingId,
        organizationId,
      },
    });

    // if mapping is not found, throw an error
    if (!mapping) {
      return null;
    }

    // update the mapping
    return await mapping.update(body);
  }
}
