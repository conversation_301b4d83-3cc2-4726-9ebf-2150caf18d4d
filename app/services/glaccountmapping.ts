import { GlAccountMapping, Organization, IntegrationUserToken, CompanyBenefit, Department } from "@/models";
import { getAccountingAccounts } from "@/util/rutterHelper";
import { PAYROLL_CATEGORY_TYPES_MAPPING, PAYROLL_CATEGORY_GROUPS } from "@/models/glaccountmapping";
import { PROVIDERS_MAPPING } from "@/models/integrationusertoken";
import { RutterAccount } from "@/util/rutterHelper";
import { formatCategoryKey, formatDisplayName, formatBenefitKey } from "@/util/accountingUtil";
import sequelize from "@/lib/sequelize";
import { Op, WhereOptions } from "sequelize";

const CategoryKeys = {
  // Expenses Group
  WAGES_AND_SALARIES: PAYROLL_CATEGORY_GROUPS.EXPENSES[0],
  CONTRACTOR_PAYMENTS: PAYROLL_CATEGORY_GROUPS.EXPENSES[1],
  EMPLOYER_TAX_EXPENSE: PAYROLL_CATEGORY_GROUPS.EXPENSES[2],
  EMPLOYER_BENEFITS_EXPENSE: PAYROLL_CATEGORY_GROUPS.EXPENSES[3],
  EXPENSE_REIMBURSEMENTS: PAYROLL_CATEGORY_GROUPS.EXPENSES[4],
  // Liabilities Group
  EMPLOYEE_BENEFITS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[0],
  EMPLOYER_BENEFITS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[1],
  GARNISHMENTS_PAYABLE: PAYROLL_CATEGORY_GROUPS.LIABILITIES[2],
  MANUAL_PAYMENTS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[3],
  // Assets Group
  BANK_ACCOUNT: PAYROLL_CATEGORY_GROUPS.ASSETS[0],
};

type MappingKey = keyof typeof PAYROLL_CATEGORY_TYPES_MAPPING;

interface GlAccountMappingData {
  payrollCategoryTypes: { id: string; name: string; isHeader: boolean }[];
  payrollCategoryTypesMapping: { [key: string]: string };
  glAccounts: { id: string; name: string }[];
  glAccountMappings: GlAccountMapping[];
}

interface DepartmentMappingAccountData {
  accountId: string;
  accountName: string;
  accountType: string;
  accountCategory: string;
}

export class GlAccountMappingsService {
  private glAccountMapping = GlAccountMapping;
  private department = Department;
  /**
   * Retrieves all GL account mappings with organized category structure
   *
   * @param organizationId - The organization ID
   * @param platform - The integration platform ID (e.g., "quickbooks")
   * @param storeUniqueId - The store unique ID (e.g., "**********")
   * @param workplaces - All company workplaces used to determine state tax jurisdictions
   * @param companyBenefits - All company benefits to include in mappings
   *
   * @returns Structured GL account mapping data with categories organized into:
   *   - Salaries (Wages, Contractor Payments, Reimbursements)
   *   - Tax Liabilities (Federal + Dynamic State Taxes)
   *   - Employee Benefits (Dynamic based on company benefits)
   *   - Others (Bank Account, Payroll Taxes, Other Liabilities, Garnishments)
   *
   * Each category is either a header (isHeader=true) or a mappable item.
   * Returns null if accounting accounts are not yet available.
   */
  async findAll(
    organizationId: number,
    platform: string,
    integrationUserTokenId: number,
    companyBenefits: CompanyBenefit[],
    departmentId: string | number | null
  ): Promise<GlAccountMappingData> {
    const foundIntegrationUserToken = await IntegrationUserToken.findOne({
      where: {
        organizationId,
        platform,
        id: integrationUserTokenId,
      },
    });

    if (!foundIntegrationUserToken) {
      throw new Error("Integration user token not found");
    }

    const whereClause: WhereOptions = {
      integrationUserTokenId: foundIntegrationUserToken.id,
    };

    if (departmentId === "ALL_CUSTOM_MAPPINGS") {
      // Sequelize operator for 'IS NOT NULL'
      whereClause.departmentId = { [Op.ne]: null };
    } else {
      // This handles a specific department ID (number) or default mappings (null)
      whereClause.departmentId = departmentId;
    }

    const glAccountMappings = await this.glAccountMapping.findAll({
      where: whereClause,
    });

    let glAccounts: RutterAccount[] = [];

    if (foundIntegrationUserToken.provider === PROVIDERS_MAPPING.RUTTER) {
      glAccounts = await getAccountingAccounts(foundIntegrationUserToken.accessToken);
    } else {
      glAccounts = [];
    }

    if (!glAccounts) {
      // need to return here as accounts are not ready
      return null;
    }

    const refinedGlAccounts = glAccounts.map((glAccount: any) => {
      return {
        id: glAccount.id,
        name: glAccount.name,
        accountType: glAccount.account_type,
        accountCategory: glAccount.category,
        platform_id: glAccount.platform_id,
      };
    });

    const getDisplayName = (key: string): string => {
      return PAYROLL_CATEGORY_TYPES_MAPPING[key as MappingKey] || formatDisplayName(key);
    };

    const payrollCategoryTypes = [
      { id: "header_salaries", name: "SALARIES", isHeader: true },
      {
        id: CategoryKeys.WAGES_AND_SALARIES,
        name: getDisplayName(CategoryKeys.WAGES_AND_SALARIES),
        isHeader: false,
      },
      {
        id: CategoryKeys.CONTRACTOR_PAYMENTS,
        name: getDisplayName(CategoryKeys.CONTRACTOR_PAYMENTS),
        isHeader: false,
      },
      {
        id: CategoryKeys.EXPENSE_REIMBURSEMENTS,
        name: getDisplayName(CategoryKeys.EXPENSE_REIMBURSEMENTS),
        isHeader: false,
      },

      { id: "header_benefits", name: "EMPLOYEE BENEFITS", isHeader: true },
      ...companyBenefits.flatMap((benefit) => [
        {
          id: formatBenefitKey(benefit.name, "ER_EXPENSE"),
          name: `Employer Contribution Expense: ${benefit.name}`,
          isHeader: false,
        },
        {
          id: formatBenefitKey(benefit.name, "ER_LIABILITY"),
          name: `Employer Contribution Liability: ${benefit.name}`,
          isHeader: false,
        },
        {
          id: formatBenefitKey(benefit.name, "EE_LIABILITY"),
          name: `Employee Deduction Liability: ${benefit.name}`,
          isHeader: false,
        },
      ]),

      { id: "header_others", name: "OTHERS", isHeader: true },
      {
        id: CategoryKeys.EMPLOYER_TAX_EXPENSE,
        name: getDisplayName(CategoryKeys.EMPLOYER_TAX_EXPENSE),
        isHeader: false,
      },
      {
        id: CategoryKeys.GARNISHMENTS_PAYABLE,
        name: getDisplayName(CategoryKeys.GARNISHMENTS_PAYABLE),
        isHeader: false,
      },
      {
        id: CategoryKeys.MANUAL_PAYMENTS_LIABILITY,
        name: getDisplayName(CategoryKeys.MANUAL_PAYMENTS_LIABILITY),
        isHeader: false,
      },
      {
        id: CategoryKeys.BANK_ACCOUNT,
        name: getDisplayName(CategoryKeys.BANK_ACCOUNT),
        isHeader: false,
      },
    ];

    const dynamicPayrollCategoryTypesMapping = {
      ...PAYROLL_CATEGORY_TYPES_MAPPING,
      ...Object.fromEntries(
        companyBenefits.flatMap((benefit) => [
          [formatBenefitKey(benefit.name, "ER_EXPENSE"), `Employer Contribution Expense: ${benefit.name}`],
          [formatBenefitKey(benefit.name, "ER_LIABILITY"), `Employer Contribution Liability: ${benefit.name}`],
          [formatBenefitKey(benefit.name, "EE_LIABILITY"), `Employee Deduction Liability: ${benefit.name}`],
        ])
      ),
    };

    const combinedData = {
      payrollCategoryTypes,
      payrollCategoryTypesMapping: dynamicPayrollCategoryTypesMapping,
      glAccounts: refinedGlAccounts,
      glAccountMappings,
    };

    return combinedData;
  }

  async getDepartmentsWithCustomMappings(
    organizationId: number,
    platform: string,
    integrationUserTokenId: number
  ): Promise<{ id: number; name: string; hasCustomMappings: boolean }[]> {
    const departments = await this.department.findAll({
      where: { organizationId },
      attributes: ["id", "name"],
    });

    const foundIntegrationUserToken = await IntegrationUserToken.findOne({
      where: {
        organizationId,
        platform,
        id: integrationUserTokenId,
      },
      include: [
        {
          model: Organization,
          attributes: ["name"],
        },
      ],
    });

    if (!foundIntegrationUserToken) {
      throw new Error("Integration user token not found");
    }

    const glAccountMappings = await this.glAccountMapping.findAll({
      where: {
        integrationUserTokenId: integrationUserTokenId,
      },
      attributes: ["departmentId"],
    });

    const mappedDepartmentIds = new Set(glAccountMappings.map((m) => m.departmentId));

    return departments.map((dept) => ({
      id: dept.id,
      name: dept.name,
      hasCustomMappings: mappedDepartmentIds.has(dept.id),
    }));
  }

  async create(
    body: Partial<GlAccountMapping & { integrationUserTokenId: number }>,
    userId: number,
    organizationId: number
  ): Promise<void> {
    const foundIntegrationUserToken = await IntegrationUserToken.findOne({
      where: {
        organizationId,
        id: body.integrationUserTokenId,
      },
    });

    if (!foundIntegrationUserToken) {
      throw new Error("Integration user token not found");
    }

    // Standardize payroll category format when creating
    let payrollCategory = body.payrollCategory;
    if (payrollCategory) {
      // If it's a benefit, use benefit key formatting
      if (payrollCategory.includes("contribution") || payrollCategory.includes("deduction")) {
        // Extract benefit name and type
        let benefitName = "";
        let type = "";

        if (payrollCategory.includes("Employer Contribution Expense")) {
          benefitName = payrollCategory.replace("Employer Contribution Expense: ", "");
          type = "ER_EXPENSE";
        } else if (payrollCategory.includes("Employer Contribution Liability")) {
          benefitName = payrollCategory.replace("Employer Contribution Liability: ", "");
          type = "ER_LIABILITY";
        } else if (payrollCategory.includes("Employee Deduction Liability")) {
          benefitName = payrollCategory.replace("Employee Deduction Liability: ", "");
          type = "EE_LIABILITY";
        }

        if (benefitName && type) {
          payrollCategory = formatBenefitKey(benefitName, type as any);
        } else {
          // If extraction failed, use standard formatting
          payrollCategory = formatCategoryKey(payrollCategory);
        }
      } else {
        // For non-benefit categories, use standard formatting
        payrollCategory = formatCategoryKey(payrollCategory);
      }
    }

    await this.glAccountMapping.create({
      accountId: body.accountId,
      accountName: body.accountName,
      platformId: body.platformId,
      payrollCategory: payrollCategory,
      accountType: body.accountType,
      accountCategory: body.accountCategory,
      accountCostClass: body.accountCostClass,
      createdBy: userId,
      integrationUserTokenId: foundIntegrationUserToken.id,
      organizationId,
    });
  }

  async update(body: any, glAccountMappingId: number): Promise<GlAccountMapping | null> {
    const mapping = await this.glAccountMapping.findOne({
      where: {
        id: glAccountMappingId,
      },
    });

    if (!mapping) {
      return null;
    }

    // Standardize payroll category format when updating
    let payrollCategory = body.payrollCategory;
    if (payrollCategory) {
      // If it's a benefit, use benefit key formatting
      if (payrollCategory.includes("contribution") || payrollCategory.includes("deduction")) {
        // Extract benefit name and type
        let benefitName = "";
        let type = "";

        if (payrollCategory.includes("Employer Contribution Expense")) {
          benefitName = payrollCategory.replace("Employer Contribution Expense: ", "");
          type = "ER_EXPENSE";
        } else if (payrollCategory.includes("Employer Contribution Liability")) {
          benefitName = payrollCategory.replace("Employer Contribution Liability: ", "");
          type = "ER_LIABILITY";
        } else if (payrollCategory.includes("Employee Deduction Liability")) {
          benefitName = payrollCategory.replace("Employee Deduction Liability: ", "");
          type = "EE_LIABILITY";
        }

        if (benefitName && type) {
          payrollCategory = formatBenefitKey(benefitName, type as any);
        } else {
          // If extraction failed, use standard formatting
          payrollCategory = formatCategoryKey(payrollCategory);
        }
      } else {
        // For non-benefit categories, use standard formatting
        payrollCategory = formatCategoryKey(payrollCategory);
      }
    }

    return await mapping.update({
      accountId: body.accountId,
      accountName: body.accountName,
      platformId: body.platformId,
      payrollCategory: payrollCategory,
      accountType: body.accountType,
      accountCategory: body.accountCategory,
      accountCostClass: body.accountCostClass,
    });
  }

  async upsertDepartmentMappings(
    mappings: Record<string, DepartmentMappingAccountData>,
    departmentId: number,
    platformId: string,
    integrationUserTokenId: number,
    userId: number,
    organizationId: number
  ): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // Get existing mappings for this department
      const existingMappings = await this.glAccountMapping.findAll({
        where: {
          departmentId,
          platformId,
          integrationUserTokenId,
          organizationId,
        },
        transaction,
      });

      // Create a map of existing mappings for quick lookup
      const existingMappingsMap = new Map(existingMappings.map((mapping) => [mapping.payrollCategory, mapping]));

      // Prepare arrays for bulk operations
      const toCreate: any[] = [];
      const toUpdate: any[] = [];
      const toDelete: number[] = [];

      // Process each mapping
      for (const [payrollCategory, accountData] of Object.entries(mappings)) {
        const existingMapping = existingMappingsMap.get(payrollCategory);

        if (!accountData?.accountId) {
          // If no accountId provided, delete the mapping if it exists
          if (existingMapping) {
            toDelete.push(existingMapping.id);
          }
          continue;
        }

        const mappingData = {
          accountId: accountData.accountId,
          accountName: accountData.accountName,
          platformId,
          payrollCategory,
          accountType: accountData.accountType,
          accountCategory: accountData.accountCategory,
          createdBy: userId,
          integrationUserTokenId,
          organizationId,
          departmentId,
        };

        if (existingMapping) {
          toUpdate.push({
            ...mappingData,
            id: existingMapping.id,
          });
        } else {
          toCreate.push(mappingData);
        }
      }

      // Execute bulk operations
      if (toDelete.length > 0) {
        await this.glAccountMapping.destroy({
          where: { id: toDelete },
          transaction,
        });
      }

      if (toCreate.length > 0) {
        await this.glAccountMapping.bulkCreate(toCreate, { transaction });
      }

      if (toUpdate.length > 0) {
        await Promise.all(
          toUpdate.map((mapping) =>
            this.glAccountMapping.update(mapping, {
              where: { id: mapping.id },
              transaction,
            })
          )
        );
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async deleteDepartmentMappings(departmentId: number, organizationId: number): Promise<void> {
    const transaction = await sequelize.transaction();
    try {
      await this.glAccountMapping.destroy({
        where: { departmentId, organizationId },
        transaction,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
