import {
  GlAccountMapping,
  IntegrationUserToken,
  User,
  Department,
  CompanyBenefit,
  EmployeeBenefit,
  Project,
  FringeBenefitClassification,
  Classification,
  FringeBenefit,
  Organization,
  UserClassification,
} from "@/models";
import { JournalEntryResponse, JournalEntryLineItem, Garnishment } from "@/types/journalEntry";
import GlAccountMappingSetting from "@/models/glaccountmappingsetting";
import {
  createBaseJournalEntry,
  validateAndBalanceJournalEntry,
  processPayrollByProject,
  sortJournalEntryLineItemsByDescription,
} from "@/util/journalEntryHelper";
import { formatCurrency, formatCategoryKey } from "@/util/accountingUtil";
import { processTaxesForPayroll, processBenefitsForPayroll, processPayrollItem } from "@/util/journalEntryCalculation";
import { getNearestFullWeekIntervals } from "@/util/dateHelper";
import { Payroll, PayrollItem } from "@/types/check";
import { ExtendedPayrollItemForCalc, ExtendedContractorPayment } from "@/types/journalEntryCalculation";
import { Op } from "sequelize";
import { TimesheetsService } from "@/services/timesheets";
import IntegrationObjectSetting from "@/models/integrationobjectsetting";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import IntegrationMapping, { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";
import { PAYROLL_CATEGORY_GROUPS } from "@/models/glaccountmapping";

dayjs.extend(timezone);
dayjs.extend(utc);

const StandardCategories = {
  // Assets
  BANK_ACCOUNT: PAYROLL_CATEGORY_GROUPS.ASSETS[0],
  // Expenses
  WAGES_EXPENSE: PAYROLL_CATEGORY_GROUPS.EXPENSES[0],
  EMPLOYER_TAX_EXPENSE: PAYROLL_CATEGORY_GROUPS.EXPENSES[2],
  REIMBURSEMENT_EXPENSE: PAYROLL_CATEGORY_GROUPS.EXPENSES[4],
  // Liabilities
  EMPLOYEE_BENEFITS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[0],
  EMPLOYER_BENEFITS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[1],
  GARNISHMENTS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[2],
  MANUAL_PAYMENTS_LIABILITY: PAYROLL_CATEGORY_GROUPS.LIABILITIES[3],
};

interface DepartmentTotalsData {
  name: string; // To store department name or "No Department"
  wages: number;
  contractorPayments: number;
  reimbursements: number;
  employerTaxes: number;
  employerBenefitsExpenses: number;
}

export class IntegrationUserTokensService {
  private integrationUserToken = IntegrationUserToken;
  private timesheetsService = new TimesheetsService();

  /**
   * Creates a standard journal entry summarizing payroll costs and liabilities.
   * Total Debits = Gross Pay + Employer Taxes + Employer Benefit Contributions.
   * Assumes GL mappings exist for the categories defined in StandardCategories and dynamic benefit/tax keys.
   */
  async mapPayrollToJournalEntry(
    payroll: Payroll,
    glAccountMappings: GlAccountMapping[],
    companyBenefits: CompanyBenefit[],
    employeeBenefits: EmployeeBenefit[]
  ): Promise<JournalEntryResponse> {
    const journalEntry = createBaseJournalEntry(payroll);
    const totals = payroll.totals;

    const reimbursements =
      parseFloat(totals.employee_reimbursements || "0") + parseFloat(totals.contractor_reimbursements || "0");

    const categorizedEmployerTaxes = processTaxesForPayroll(payroll);
    const totalEmployerTaxExpense = Object.values(categorizedEmployerTaxes).reduce((sum, amount) => sum + amount, 0);
    const employeeTaxes = parseFloat(totals.employee_taxes || "0");

    const { categorizedBenefits } = processBenefitsForPayroll(payroll, companyBenefits, employeeBenefits);

    let directDepositNetPay = 0;
    let manualPaymentsNetPay = 0;
    let managedPostTaxDeductions = 0;

    payroll.items.forEach((item: PayrollItem) => {
      if (item.payment_method === "direct_deposit") {
        directDepositNetPay += parseFloat(item.net_pay || "0");
      } else {
        manualPaymentsNetPay += parseFloat(item.net_pay || "0");
      }
      if (item.post_tax_deductions) {
        item.post_tax_deductions.forEach((deduction) => {
          if (deduction.managed === true) {
            managedPostTaxDeductions += parseFloat(deduction.amount || "0");
          }
        });
      }
      item.earnings?.forEach((earning) => {
        if (earning.type === "2_percent_shareholder_benefits" || earning.type === "2_percent_shareholder_hsa") {
          manualPaymentsNetPay += parseFloat(earning.amount || "0");
        }
      });
    });

    payroll.contractor_payments?.forEach((payment) => {
      if (payment.payment_method === "direct_deposit") {
        directDepositNetPay += parseFloat(payment.net_pay || "0");
      } else {
        manualPaymentsNetPay += parseFloat(payment.net_pay || "0");
      }
    });

    directDepositNetPay = formatCurrency(directDepositNetPay);
    manualPaymentsNetPay = formatCurrency(manualPaymentsNetPay);
    managedPostTaxDeductions = formatCurrency(managedPostTaxDeductions);

    let totalGarnishmentsLiability = 0;
    payroll.items.forEach((item: PayrollItem) => {
      const extendedItem = item as ExtendedPayrollItemForCalc;
      if (extendedItem.garnishments) {
        extendedItem.garnishments.forEach((garnish: Garnishment) => {
          totalGarnishmentsLiability += parseFloat(garnish.amount || "0");
        });
      }
      if (item.post_tax_deductions) {
        item.post_tax_deductions.forEach((deduction) => {
          if (deduction.managed !== true) {
            totalGarnishmentsLiability += parseFloat(deduction.amount || "0");
          }
        });
      }
    });
    payroll.contractor_payments?.forEach((payment) => {
      const extendedPayment = payment as ExtendedContractorPayment;
      if (extendedPayment.garnishments) {
        extendedPayment.garnishments.forEach((garnish: Garnishment) => {
          totalGarnishmentsLiability += parseFloat(garnish.amount || "0");
        });
      }
    });
    totalGarnishmentsLiability = formatCurrency(totalGarnishmentsLiability);

    const accountMap: Record<string, string> = {};
    glAccountMappings.forEach((mapping) => {
      if (mapping.payrollCategory) {
        accountMap[mapping.payrollCategory] = mapping.accountId;
      }
    });

    const getMappedAccountId = (category: string, isOptional = false): string | null => {
      const accountId = accountMap[category] || accountMap[formatCategoryKey(category)];
      if (!accountId && !isOptional) {
        throw new Error(`Missing required GL account mapping for category: ${category}`);
      }

      return accountId || null;
    };

    const lineItems: JournalEntryLineItem[] = [];

    const employeeGrossWages = parseFloat(totals.employee_gross || "0");
    if (employeeGrossWages > 0) {
      lineItems.push({
        account_id: getMappedAccountId(StandardCategories.WAGES_EXPENSE),
        total_amount: formatCurrency(employeeGrossWages),
        description: "Wages and Salaries",
      });
    }

    const contractorGrossPayments = parseFloat(totals.contractor_gross || "0");
    if (contractorGrossPayments > 0) {
      const contractorAccountId =
        getMappedAccountId("CONTRACTOR_PAYMENTS", true) || getMappedAccountId(StandardCategories.WAGES_EXPENSE);
      if (!contractorAccountId) {
        throw new Error(`Missing required GL account mapping for Contractor Payments or Wages Expense`);
      }
      lineItems.push({
        account_id: contractorAccountId,
        total_amount: formatCurrency(contractorGrossPayments),
        description: "Contractor Payments",
      });
    }

    if (totalEmployerTaxExpense > 0) {
      lineItems.push({
        account_id: getMappedAccountId(StandardCategories.EMPLOYER_TAX_EXPENSE),
        total_amount: formatCurrency(totalEmployerTaxExpense),
        description: "Employer Taxes",
      });
    }

    Object.entries(categorizedBenefits).forEach(([categoryKey, amount]) => {
      if (categoryKey.endsWith("_ER") && amount > 0) {
        const benefitName = categoryKey.replace("_ER", "");
        const expenseCategoryKey = formatCategoryKey(`${benefitName}_ER_EXPENSE`);
        const accountId = getMappedAccountId(expenseCategoryKey);
        if (accountId) {
          lineItems.push({
            account_id: accountId,
            total_amount: formatCurrency(amount),
            description: `Employer Contribution Expense: ${benefitName}`,
          });
        }
      }
    });

    if (reimbursements > 0) {
      lineItems.push({
        account_id: getMappedAccountId(StandardCategories.REIMBURSEMENT_EXPENSE),
        total_amount: formatCurrency(reimbursements),
        description: "Expense Reimbursements",
      });
    }

    // Liabilities (Credits)
    Object.entries(categorizedBenefits).forEach(([categoryKey, amount]) => {
      if (categoryKey.endsWith("_EE") && amount > 0) {
        const benefitName = categoryKey.replace("_EE", "");
        const liabilityCategoryKey = formatCategoryKey(`${benefitName}_EE_LIABILITY`);
        const accountId = getMappedAccountId(liabilityCategoryKey);
        if (accountId) {
          lineItems.push({
            account_id: accountId,
            total_amount: -formatCurrency(amount),
            description: `Employee Deduction Liability: ${benefitName}`,
          });
        }
      }
      if (categoryKey.endsWith("_ER") && amount > 0) {
        const benefitName = categoryKey.replace("_ER", "");
        const liabilityCategoryKey = formatCategoryKey(`${benefitName}_ER_LIABILITY`);
        const accountId = getMappedAccountId(liabilityCategoryKey);
        if (accountId) {
          lineItems.push({
            account_id: accountId,
            total_amount: -formatCurrency(amount),
            description: `Employer Contribution Liability: ${benefitName}`,
          });
        }
      }
    });

    if (totalGarnishmentsLiability > 0) {
      lineItems.push({
        account_id: getMappedAccountId(StandardCategories.GARNISHMENTS_LIABILITY),
        total_amount: -formatCurrency(totalGarnishmentsLiability),
        description: "Garnishments & Post-Tax Deductions Payable",
      });
    }

    if (manualPaymentsNetPay > 0) {
      lineItems.push({
        account_id: getMappedAccountId(StandardCategories.MANUAL_PAYMENTS_LIABILITY),
        total_amount: -formatCurrency(manualPaymentsNetPay),
        description: "Manual Payments Liability",
      });
    }

    const cashOutAmount = formatCurrency(
      directDepositNetPay + employeeTaxes + totalEmployerTaxExpense + managedPostTaxDeductions
    );
    if (cashOutAmount !== 0) {
      // Avoid adding zero amount lines
      lineItems.push({
        account_id: getMappedAccountId(StandardCategories.BANK_ACCOUNT),
        total_amount: -cashOutAmount,
        description: "Cash Out (Direct Deposit)",
      });
    }

    journalEntry.line_items = lineItems.filter((line) => Math.abs(line.total_amount) >= 0.01);

    if (!validateAndBalanceJournalEntry(journalEntry)) {
      throw new Error("Default journal entry generation resulted in unbalanced debits and credits.");
    }

    return { journal_entry: journalEntry };
  }

  async mapPayrollToJournalEntryPerDepartment(
    payroll: Payroll,
    glAccountMappings: GlAccountMapping[],
    users: (User & { department?: Department })[],
    companyBenefits: CompanyBenefit[],
    employeeBenefits: EmployeeBenefit[]
  ): Promise<JournalEntryResponse> {
    const journalEntry = createBaseJournalEntry(payroll);
    const globalTotals = payroll.totals;

    const categorizedEmployerTaxes = processTaxesForPayroll(payroll);
    const totalGlobalEmployerTaxExpense = Object.values(categorizedEmployerTaxes).reduce(
      (sum, amount) => sum + amount,
      0
    );
    const employeeTaxes = parseFloat(globalTotals.employee_taxes || "0");

    const { categorizedBenefits } = processBenefitsForPayroll(payroll, companyBenefits, employeeBenefits);

    let directDepositNetPay = 0;
    let manualPaymentsNetPay = 0;
    let managedPostTaxDeductions = 0;
    let totalGarnishmentsLiability = 0;

    payroll.items.forEach((item: PayrollItem) => {
      const extendedItem = item as ExtendedPayrollItemForCalc;
      if (item.payment_method === "direct_deposit") {
        directDepositNetPay += parseFloat(item.net_pay || "0");
      } else {
        manualPaymentsNetPay += parseFloat(item.net_pay || "0");
      }
      if (item.post_tax_deductions) {
        item.post_tax_deductions.forEach((deduction) => {
          if (deduction.managed === true) {
            managedPostTaxDeductions += parseFloat(deduction.amount || "0");
          } else {
            totalGarnishmentsLiability += parseFloat(deduction.amount || "0");
          }
        });
      }
      if (extendedItem.garnishments) {
        extendedItem.garnishments.forEach((garnish: Garnishment) => {
          totalGarnishmentsLiability += parseFloat(garnish.amount || "0");
        });
      }
      item.earnings?.forEach((earning) => {
        if (earning.type === "2_percent_shareholder_benefits" || earning.type === "2_percent_shareholder_hsa") {
          manualPaymentsNetPay += parseFloat(earning.amount || "0");
        }
      });
    });

    payroll.contractor_payments?.forEach((payment) => {
      const extendedPayment = payment as ExtendedContractorPayment;
      if (payment.payment_method === "direct_deposit") {
        directDepositNetPay += parseFloat(payment.net_pay || "0");
      } else {
        manualPaymentsNetPay += parseFloat(payment.net_pay || "0");
      }
      if (extendedPayment.garnishments) {
        extendedPayment.garnishments.forEach((garnish: Garnishment) => {
          totalGarnishmentsLiability += parseFloat(garnish.amount || "0");
        });
      }
    });

    directDepositNetPay = formatCurrency(directDepositNetPay);
    manualPaymentsNetPay = formatCurrency(manualPaymentsNetPay);
    managedPostTaxDeductions = formatCurrency(managedPostTaxDeductions);
    totalGarnishmentsLiability = formatCurrency(totalGarnishmentsLiability);

    const getResolvedAccountId = (
      category: string,
      targetDepartmentId: number | null,
      isOptional = false
    ): string | null => {
      let accountId: string | undefined;

      accountId = glAccountMappings.find(
        (m) => m.payrollCategory === category && m.departmentId === targetDepartmentId
      )?.accountId;

      if (!accountId) {
        accountId = glAccountMappings.find((m) => m.payrollCategory === category && m.departmentId === null)?.accountId;
      }

      if (!accountId) {
        const formattedCat = formatCategoryKey(category);
        accountId = glAccountMappings.find(
          (m) => m.payrollCategory === formattedCat && m.departmentId === targetDepartmentId
        )?.accountId;
      }

      if (!accountId) {
        const formattedCat = formatCategoryKey(category);
        accountId = glAccountMappings.find(
          (m) => m.payrollCategory === formattedCat && m.departmentId === null
        )?.accountId;
      }

      if (!accountId && !isOptional) {
        throw new Error(
          `Missing GL account mapping for category: ${category} (Dept: ${
            targetDepartmentId === null ? "Default" : targetDepartmentId
          })`
        );
      }

      return accountId || null;
    };

    const employeeMap = new Map<string, User & { department?: Department }>();
    const contractorMap = new Map<string, User & { department?: Department }>();
    users.forEach((user) => {
      if (user.checkEmployeeId) employeeMap.set(user.checkEmployeeId, user);
      if (user.checkContractorId) contractorMap.set(user.checkContractorId, user);
    });

    const departmentTotals = new Map<number | null, DepartmentTotalsData>();

    const initializeDeptTotals = (departmentId: number | null, departmentName: string) => {
      if (!departmentTotals.has(departmentId)) {
        departmentTotals.set(departmentId, {
          name: departmentName,
          wages: 0,
          contractorPayments: 0,
          reimbursements: 0,
          employerTaxes: 0,
          employerBenefitsExpenses: 0,
        });
      }
    };

    payroll.items.forEach((item: PayrollItem) => {
      const processedItem = processPayrollItem(item as ExtendedPayrollItemForCalc);
      const user = employeeMap.get(item.employee);
      const departmentId = user?.department?.id ?? null;
      const departmentName = user?.department?.name ?? "No Department";
      initializeDeptTotals(departmentId, departmentName);

      const currentDeptTotals = departmentTotals.get(departmentId);
      if (!currentDeptTotals) {
        throw new Error(`Unexpectedly missing department totals for department ID: ${departmentId}`);
      }
      currentDeptTotals.wages += processedItem.grossWages;
      currentDeptTotals.reimbursements += processedItem.reimbursements;
      currentDeptTotals.employerTaxes += processedItem.employerTaxes;
      currentDeptTotals.employerBenefitsExpenses += processedItem.employerBenefits;
    });

    payroll.contractor_payments?.forEach((contractorPayment) => {
      const user = contractorMap.get(contractorPayment.contractor);
      const departmentId = user?.department?.id ?? null;
      const departmentName = user?.department?.name ?? "No Department";
      initializeDeptTotals(departmentId, departmentName);

      const grossPay = parseFloat(contractorPayment.amount || "0");
      const reimbursements = parseFloat(contractorPayment.reimbursement_amount || "0");
      const currentDeptTotals = departmentTotals.get(departmentId);
      if (!currentDeptTotals) {
        throw new Error(`Unexpectedly missing department totals for department ID: ${departmentId}`);
      }
      currentDeptTotals.contractorPayments += grossPay;
      currentDeptTotals.reimbursements += reimbursements;
    });

    const lineItems: JournalEntryLineItem[] = [];

    departmentTotals.forEach((totalsData, departmentId) => {
      const deptName = totalsData.name;
      const deptSuffix = deptName === "No Department" ? "" : ` - ${deptName}`;
      const currentDepartmentId = departmentId;
      const totals = totalsData;

      if (totals.wages > 0) {
        lineItems.push({
          account_id: getResolvedAccountId(StandardCategories.WAGES_EXPENSE, currentDepartmentId),
          total_amount: formatCurrency(totals.wages),
          description: `Wages and Salaries${deptSuffix}`,
        });
      }
      if (totals.contractorPayments > 0) {
        const contractorExpenseAccountId = getResolvedAccountId(StandardCategories.WAGES_EXPENSE, currentDepartmentId);

        if (!contractorExpenseAccountId) {
          throw new Error(
            `Missing required GL account mapping for Contractor Payments (mapped as Wages or specific category) (Dept: ${
              currentDepartmentId === null ? "Default" : currentDepartmentId
            })`
          );
        }
        lineItems.push({
          account_id: contractorExpenseAccountId,
          total_amount: formatCurrency(totals.contractorPayments),
          description: `Contractor Payments${deptSuffix}`,
        });
      }
      if (totals.reimbursements > 0) {
        lineItems.push({
          account_id: getResolvedAccountId(StandardCategories.REIMBURSEMENT_EXPENSE, currentDepartmentId),
          total_amount: formatCurrency(totals.reimbursements),
          description: `Expense Reimbursements${deptSuffix}`,
        });
      }
      if (totals.employerTaxes > 0) {
        lineItems.push({
          account_id: getResolvedAccountId(StandardCategories.EMPLOYER_TAX_EXPENSE, currentDepartmentId),
          total_amount: formatCurrency(totals.employerTaxes),
          description: `Employer Taxes${deptSuffix}`,
        });
      }

      const totalOverallEmployerBenefitExpenses = Array.from(departmentTotals.values()).reduce(
        (sum, dept) => sum + dept.employerBenefitsExpenses,
        0
      );

      Object.entries(categorizedBenefits).forEach(([categoryKey, globalAmount]) => {
        if (categoryKey.endsWith("_ER") && globalAmount > 0) {
          const benefitName = categoryKey.replace("_ER", "");
          const expenseCategoryKey = formatCategoryKey(`${benefitName}_ER_EXPENSE`);

          const allocationRatio =
            totalOverallEmployerBenefitExpenses > 0
              ? totals.employerBenefitsExpenses / totalOverallEmployerBenefitExpenses
              : 0;
          const allocatedAmount = formatCurrency(globalAmount * allocationRatio);

          if (allocatedAmount > 0.009) {
            const accountId = getResolvedAccountId(expenseCategoryKey, currentDepartmentId);
            if (accountId) {
              lineItems.push({
                account_id: accountId,
                total_amount: allocatedAmount,
                description: `Employer Contribution Expense: ${benefitName}${deptSuffix}`,
              });
            } else {
              throw new Error(
                `Missing GL mapping for Employer Benefit Expense: ${expenseCategoryKey} (Dept: ${
                  currentDepartmentId === null ? "Default" : currentDepartmentId
                })`
              );
            }
          }
        }
      });
    });

    Object.entries(categorizedBenefits).forEach(([categoryKey, amount]) => {
      if (amount <= 0) return;

      if (categoryKey.endsWith("_EE")) {
        const benefitName = categoryKey.replace("_EE", "");
        const liabilityCategoryKey = formatCategoryKey(`${benefitName}_EE_LIABILITY`);
        const accountId = getResolvedAccountId(liabilityCategoryKey, null);
        if (accountId) {
          lineItems.push({
            account_id: accountId,
            total_amount: -formatCurrency(amount),
            description: `Employee Deduction Liability: ${benefitName}`,
          });
        }
      } else if (categoryKey.endsWith("_ER")) {
        const benefitName = categoryKey.replace("_ER", "");
        const liabilityCategoryKey = formatCategoryKey(`${benefitName}_ER_LIABILITY`);
        const accountId = getResolvedAccountId(liabilityCategoryKey, null);
        if (accountId) {
          lineItems.push({
            account_id: accountId,
            total_amount: -formatCurrency(amount),
            description: `Employer Contribution Liability: ${benefitName}`,
          });
        }
      }
    });

    if (totalGarnishmentsLiability > 0) {
      lineItems.push({
        account_id: getResolvedAccountId(StandardCategories.GARNISHMENTS_LIABILITY, null),
        total_amount: -formatCurrency(totalGarnishmentsLiability),
        description: "Garnishments & Post-Tax Deductions Payable",
      });
    }

    if (manualPaymentsNetPay > 0) {
      lineItems.push({
        account_id: getResolvedAccountId(StandardCategories.MANUAL_PAYMENTS_LIABILITY, null),
        total_amount: -formatCurrency(manualPaymentsNetPay),
        description: "Manual Payments Liability",
      });
    }

    const cashOutAmount = formatCurrency(
      directDepositNetPay + employeeTaxes + totalGlobalEmployerTaxExpense + managedPostTaxDeductions
    );

    if (cashOutAmount !== 0) {
      lineItems.push({
        account_id: getResolvedAccountId(StandardCategories.BANK_ACCOUNT, null),
        total_amount: -cashOutAmount,
        description: "Cash Out (Direct Deposit)",
      });
    }

    journalEntry.line_items = lineItems.filter((item) => Math.abs(item.total_amount) >= 0.01);

    sortJournalEntryLineItemsByDescription(journalEntry);

    if (!validateAndBalanceJournalEntry(journalEntry)) {
      throw new Error("Per-department journal entry generation resulted in unbalanced debits and credits.");
    }

    return { journal_entry: journalEntry };
  }

  async mapJournalEntryForFoundation(
    payroll: Payroll,
    glAccountMappings: GlAccountMapping[],
    consolidateByEmployee: boolean,
    users: User[],
    companyBenefits: CompanyBenefit[],
    employeeBenefits: EmployeeBenefit[]
  ): Promise<JournalEntryResponse> {
    const result = await this.mapPayrollToJournalEntry(payroll, glAccountMappings, companyBenefits, employeeBenefits);

    const { journal_entry } = result;

    // Add foundationCostClass to each line item based on the account mapping
    journal_entry.line_items = journal_entry.line_items.map((item) => {
      const match = glAccountMappings.find((m) => m.accountId === item.account_id);

      return {
        ...item,
        foundationCostClass: match?.accountCostClass || "", // Add cost class or empty string
      };
    });

    return { journal_entry };
  }

  async getByAccessToken(accessToken: string): Promise<IntegrationUserToken | null> {
    return await this.integrationUserToken.findOne({
      where: {
        accessToken,
      },
    });
  }

  async disable(integrationUserTokenId: number, organizationId: number): Promise<void> {
    const foundIntegrationUserToken = await this.integrationUserToken.findOne({
      where: {
        id: integrationUserTokenId,
        organizationId,
      },
    });

    if (!foundIntegrationUserToken) {
      throw new Error("Integration token not found or does not belong to the organization.");
    }

    await foundIntegrationUserToken.update({ isEnabled: false });

    // Cascade delete related mappings and settings
    await GlAccountMapping.destroy({ where: { integrationUserTokenId } });
    await GlAccountMappingSetting.destroy({ where: { integrationUserTokenId } });
    await IntegrationObjectSetting.destroy({ where: { integrationUserTokenId } });
    await IntegrationMapping.destroy({ where: { integrationUserTokenId } }); // Also clear object mappings
  }

  async getById(integrationUserTokenId: number): Promise<IntegrationUserToken | null> {
    return await this.integrationUserToken.findOne({
      where: {
        id: integrationUserTokenId,
      },
    });
  }

  /** Maps payroll data to journal entries attributed by project using timesheet data. */
  async mapPayrollToJournalEntryPerProject(
    payroll: Payroll,
    glAccountMappings: GlAccountMapping[],
    users: User[],
    companyBenefits: CompanyBenefit[],
    employeeBenefits: EmployeeBenefit[],
    completeOrganization?: Organization,
    integrationUserTokenId?: number
  ): Promise<JournalEntryResponse> {
    const timesheets = await this.getPayrollTimesheets(payroll, users, completeOrganization);

    let projectMappings: Array<{ hammrId: number; externalId: string }> = [];
    if (integrationUserTokenId) {
      const validProjectIds = timesheets
        .map((timesheet) => timesheet.projectId)
        .filter((id): id is number => typeof id === "number");

      if (validProjectIds.length > 0) {
        const mappings = await IntegrationMapping.findAll({
          where: {
            hammrId: { [Op.in]: validProjectIds },
            objectType: INTEGRATION_OBJECT_TYPE.PROJECT,
            integrationUserTokenId,
          },
        });
        projectMappings = mappings.map((mapping) => ({ hammrId: mapping.hammrId, externalId: mapping.externalId }));
      }
    }

    const journalEntry = await processPayrollByProject(
      payroll,
      glAccountMappings,
      users,
      companyBenefits,
      employeeBenefits,
      timesheets,
      projectMappings
    );

    return { journal_entry: journalEntry };
  }

  /** Retrieves and prepares timesheets relevant to a specific payroll period. */
  private async getPayrollTimesheets(payroll: Payroll, _users: User[], completeOrganization: Organization) {
    const organizationTimezone = completeOrganization?.timezone || "UTC";

    const payrollPeriodStart = dayjs.tz(payroll.period_start, organizationTimezone).startOf("day");
    const payrollPeriodEnd = dayjs.tz(payroll.period_end, organizationTimezone).endOf("day");
    const weekStartDay = completeOrganization?.overtimeSettings?.weekStartDay || "monday";

    const adjustedDates = getNearestFullWeekIntervals(
      payrollPeriodStart.valueOf(),
      payrollPeriodEnd.valueOf(),
      weekStartDay
    );

    const payrollTimesheets = await this.timesheetsService.findAll({
      where: {
        clockIn: {
          [Op.between]: [adjustedDates.start.toDate(), adjustedDates.end.toDate()],
        },
        checkPayrollId: payroll.id,
        isDeleted: false,
      },
      include: [
        { model: Project, required: true },
        { model: User, required: true },
        {
          model: UserClassification,
          required: false,
          include: [{ model: Classification, required: false }],
        },
      ],
    });

    const enrichedTimesheets = this.timesheetsService.enrichTimesheets(
      payrollTimesheets,
      completeOrganization?.paySchedule?.payFrequency || "weekly"
    );

    const fringeBenefitData = await FringeBenefitClassification.findAll({
      where: { organizationId: completeOrganization.id },
      include: [
        { model: Classification, required: true },
        { model: FringeBenefit, required: true },
      ],
    });
    const joinedTimesheetsData = this.timesheetsService.enrichTimesheetsWithFringeBenefitClassifications(
      enrichedTimesheets,
      fringeBenefitData
    );

    // Ensure data safety for calculations
    const timesheetsWithSafeData = joinedTimesheetsData.map((timesheet) => {
      if (timesheet.project?.isPrevailingWage && timesheet.userClassificationId && !timesheet.userClassification) {
        timesheet.userClassification = { fringePay: "0", basePay: "0" };
      }
      timesheet.fringeBenefitClassifications = timesheet.fringeBenefitClassifications || [];
      if (timesheet.user && !timesheet.user.employeeBenefits) {
        timesheet.user.employeeBenefits = [];
      }

      return timesheet;
    });

    const attributedTimesheets = this.timesheetsService.generateFlattenedAttributedTimesheets(
      timesheetsWithSafeData,
      adjustedDates.start.valueOf(),
      completeOrganization
    );

    // Filter down to only timesheets starting within the actual payroll period
    const timesheetsFromTimestamp = payrollPeriodStart.valueOf();
    const timesheetsToTimestamp = payrollPeriodEnd.valueOf();

    const eligibleAttributedTimesheets = attributedTimesheets.filter((timesheet) => {
      return (
        timesheet.clockInTimestamp >= timesheetsFromTimestamp && timesheet.clockInTimestamp <= timesheetsToTimestamp
      );
    });

    return eligibleAttributedTimesheets;
  }
}
