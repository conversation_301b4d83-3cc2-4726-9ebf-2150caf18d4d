import { Op, UpdateOptions } from "sequelize";
import {
  CostCode,
  Project,
  ScheduleEvent,
  ScheduleEventNotification,
  User,
  UserScheduleEvent,
  Equipment,
  EquipmentCategory,
} from "@/models";
import { serializeScheduleEvent } from "@/util/scheduleEventHelper";
import { MultiUpdateScheduleDto } from "@/types/scheduleDto";
import sequelize from "@/lib/sequelize";
import TimeOffRequestService from "./timeoffrequest";
import HTTPError from "@/errors/HTTPError";

const staticExtendedOptions = {
  attributes: ["id", "startTime", "endTime", "note", "linkedEventId"],
  include: [
    {
      model: User,
      through: { attributes: [] as string[] },
      attributes: ["id", "firstName", "lastName", "email", "position", "phone"],
      required: true,
    },
    {
      model: Project,
      attributes: [
        "id",
        "name",
        "address",
        "location",
        "isGeofenced",
        "geofenceRadius",
        "isPrevailingWage",
        "wageTableId",
      ],
      required: true,
    },
    {
      model: CostCode,
      attributes: ["id", "name"],
    },
    {
      model: ScheduleEventNotification,
      attributes: ["id", "sentAt", "scheduledAt", "notifyViaText", "notifyViaPush"],
    },
    {
      model: Equipment,
      through: { attributes: [] as string[] },
      attributes: ["id", "name", "hourlyCost", "year", "categoryId"],
      required: false,
      include: [
        {
          model: EquipmentCategory,
          as: "category",
          attributes: ["id", "name"],
          required: false,
        },
      ],
    },
  ],
};

export class ScheduleService {
  private scheduleEvent = ScheduleEvent;
  private timeOffRequestService = new TimeOffRequestService();

  async create(scheduleEventObj: Partial<ScheduleEvent>, userIds: number[]): Promise<ScheduleEvent> {
    // If userIds are provided, check for time off conflicts
    if (userIds.length) {
      await this.checkForTimeOffConflicts(userIds, scheduleEventObj.startTime, scheduleEventObj.endTime);
    }

    return this.scheduleEvent.create(scheduleEventObj);
  }

  async findOne(options: object): Promise<ScheduleEvent> {
    const extendedOptions = {
      ...staticExtendedOptions,
      ...options,
    };

    const scheduleEvent = await this.scheduleEvent.findOne(extendedOptions);

    if (!scheduleEvent) {
      return null;
    }

    return serializeScheduleEvent(scheduleEvent.toJSON());
  }

  async findAll(options: any): Promise<ScheduleEvent[]> {
    const extendedOptions = {
      ...staticExtendedOptions,
      ...options,
    };

    let scheduleEvents = await this.scheduleEvent.findAll(extendedOptions);

    scheduleEvents = scheduleEvents.map((scheduleEvent: any) => {
      return serializeScheduleEvent(scheduleEvent.toJSON());
    });

    return scheduleEvents;
  }

  async update(
    scheduleEventUpdateObj: Partial<ScheduleEvent>,
    userIds: number[],
    options: Omit<UpdateOptions<any>, "returning">
  ): Promise<number[]> {
    // If updating time or users, check for conflicts
    if ((scheduleEventUpdateObj.startTime || scheduleEventUpdateObj.endTime || userIds.length) && options.where) {
      const currentEvent = await this.scheduleEvent.findOne(options);

      const startTime = scheduleEventUpdateObj.startTime || currentEvent.startTime;
      const endTime = scheduleEventUpdateObj.endTime || currentEvent.endTime;

      if (userIds.length) {
        await this.checkForTimeOffConflicts(userIds, startTime, endTime);
      }
    }

    return await this.scheduleEvent.update(scheduleEventUpdateObj, options);
  }

  async delete(options: any): Promise<number> {
    const [affectedCount] = await this.scheduleEvent.update({ isDeleted: true }, options);

    return affectedCount;
  }

  async updateScheduleEventsAndUserScheduleEvents(payload: MultiUpdateScheduleDto) {
    const transaction = await sequelize.transaction();

    try {
      const { scheduleEventsToUpdate, usersToAdd, usersToRemove } = payload;
      const scheduleEventIdsToUpdate = scheduleEventsToUpdate.map((e) => e.id);

      // Update Schedule Events
      if (scheduleEventsToUpdate && scheduleEventsToUpdate.length > 0) {
        for (const event of scheduleEventsToUpdate) {
          await this.scheduleEvent.update(event, {
            where: { id: event.id },
            transaction,
          });
        }
      }

      // Add UserScheduleEvent rows for each user in usersToAdd
      if (usersToAdd && usersToAdd.length > 0) {
        for (const userId of usersToAdd) {
          // get user schedule event rows that match the schedule event id and user id
          const existingUserScheduleEvents = await UserScheduleEvent.findAll({
            // ...staticExtendedOptions,
            where: {
              userId,
              scheduleEventId: {
                [Op.in]: scheduleEventIdsToUpdate, // checks if scheduleEventId is in the list of IDs to add
              },
            },
            transaction,
          });

          // filter out existing rows and remove duplicates
          const existingUserScheduleEventIds = existingUserScheduleEvents.map((em) => em.scheduleEventId);
          const newUserScheduleEventIdsToAdd = scheduleEventIdsToUpdate.filter(
            (id: number) => !existingUserScheduleEventIds.includes(id)
          );

          // // bulk create for unique entries
          const newUserScheduleEventsToAdd = newUserScheduleEventIdsToAdd.map((id: number) => ({
            userId,
            scheduleEventId: id,
          }));

          if (newUserScheduleEventsToAdd.length > 0) {
            await UserScheduleEvent.bulkCreate(newUserScheduleEventsToAdd, { transaction });
          }
        }
      }

      // remove UserScheduleEvent rows for each user in usersToRemove
      if (usersToRemove && usersToRemove.length > 0) {
        for (const userId of usersToRemove) {
          await UserScheduleEvent.destroy({
            where: {
              userId: userId,
              scheduleEventId: {
                [Op.in]: scheduleEventIdsToUpdate,
              },
            },
            transaction,
          });
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  private async checkForTimeOffConflicts(userIds: number[], startDate: Date, endDate: Date) {
    const conflicts = await this.timeOffRequestService.getTimeOffConflicts(userIds, startDate, endDate);

    if (conflicts.length > 0) {
      const conflictingUsers = Array.from(new Set(conflicts.map((c) => `${c.firstName} ${c.lastName}`))).join(", ");
      throw new HTTPError(
        400,
        `Cannot schedule event: The following users have approved time off during this period: ${conflictingUsers}`
      );
    }
  }
}
