import { User, InjuryReport, Injury<PERSON>hoto, TimeSheet, Project } from "@/models";
import { Transaction } from "sequelize";
import { Op } from "sequelize";

export class InjuryReportService {
  private injuryReport = InjuryReport;
  private injuryPhoto = InjuryPhoto;

  async findByTimesheet(timesheetId: number): Promise<InjuryReport[]> {
    return await this.injuryReport.findAll({
      where: {
        timesheetId,
      },
      include: [
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "resolvedByUser",
        },
        {
          model: InjuryPhoto,
          where: { isDeleted: false },
          required: false,
        },
      ],
    });
  }

  async list(params: {
    organizationId: number;
    page?: number;
    limit?: number;
    search?: string;
    isResolved?: boolean;
    startDate?: Date;
    endDate?: Date;
    userId?: number;
  }) {
    const { organizationId, page = 1, limit = 10, search, isResolved, startDate, endDate, userId } = params;

    const offset = (page - 1) * limit;

    const where: any = {
      organizationId,
    };

    if (isResolved !== undefined) {
      where.isResolved = isResolved;
    }

    if (userId) {
      where.userId = userId;
    }

    if (search) {
      where[Op.or] = [{ note: { [Op.iLike]: `%${search}%` } }, { resolutionNote: { [Op.iLike]: `%${search}%` } }];
    }

    if (startDate || endDate) {
      const timesheetIds = await TimeSheet.findAll({
        where: {
          organizationId,
          clockIn: {
            [Op.between]: [startDate || new Date(0), endDate || new Date()],
          },
        },
        attributes: ["id"],
      }).then((timesheets) => timesheets.map((t) => t.id));

      where.timesheetId = {
        [Op.in]: timesheetIds,
      };
    }

    const count = await this.injuryReport.count({ where });

    const rows = await this.injuryReport.findAll({
      where,
      include: [
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "user",
        },
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "creator",
        },
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "resolvedByUser",
        },
        {
          model: TimeSheet,
          attributes: ["id", "clockIn", "clockOut"],
          as: "timesheet",
          include: [
            {
              model: Project,
              attributes: ["id", "name"],
              as: "project",
            },
          ],
        },
        {
          model: InjuryPhoto,
          where: { isDeleted: false },
          required: false,
          attributes: ["id", "objectId"],
        },
      ],
      order: [["createdAt", "DESC"]],
      offset,
      limit,
    });

    return {
      data: rows,
      pagination: {
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      },
    };
  }

  async create(injuryReportObj: Partial<InjuryReport>, transaction?: Transaction): Promise<InjuryReport> {
    return await this.injuryReport.create(injuryReportObj, { transaction });
  }

  async addPhoto(photoData: Partial<InjuryPhoto>, transaction?: Transaction): Promise<InjuryPhoto> {
    return await this.injuryPhoto.create(photoData, { transaction });
  }

  async update(injuryReportObj: Partial<InjuryReport>, transaction?: Transaction): Promise<InjuryReport> {
    await this.injuryReport.update(injuryReportObj, { where: { id: injuryReportObj.id }, transaction });

    return await this.injuryReport.findOne({
      where: { id: injuryReportObj.id },
      transaction,
      include: [
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "resolvedByUser",
        },
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
          as: "user",
        },
        {
          model: InjuryPhoto,
          where: { isDeleted: false },
          required: false,
        },
        {
          model: TimeSheet,
          attributes: ["id", "clockIn", "clockOut"],
          as: "timesheet",
          include: [
            {
              model: Project,
              attributes: ["id", "name"],
              as: "project",
            },
          ],
        },
      ],
    });
  }
}
