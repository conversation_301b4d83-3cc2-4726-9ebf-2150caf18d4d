import { CreateOptions, FindOptions, UpdateOptions } from "sequelize";
import { FringeBenefit, FringeBenefitClassification } from "@/models";

export class FringeBenefitsService {
  private fringeBenefit = FringeBenefit;

  async create(fringeBenefitObj: Partial<any>, options: CreateOptions): Promise<FringeBenefit> {
    const fringeBenefit: any = await this.fringeBenefit.create(fringeBenefitObj, options);

    return fringeBenefit;
  }

  async update(fringeBenefitObj: Partial<any>, options: UpdateOptions): Promise<FringeBenefit> {
    const fringeBenefit: any = await this.fringeBenefit.update(fringeBenefitObj, options);

    return fringeBenefit;
  }

  async findAll(options: FindOptions) {
    // check if include options are present in the provided options
    const includeOptions =
      options.include && (options.include as any).length
        ? options.include
        : [
            {
              model: FringeBenefitClassification,
              as: "fringeBenefitClassifications",
              required: false,
            },
          ];

    const extendedOptions: FindOptions = {
      ...options,
      include: includeOptions,
    };

    const fringeBenefits = await this.fringeBenefit.findAll(extendedOptions);

    return fringeBenefits;
  }

  async findOne(options: object) {
    const extendedOptions: FindOptions = {
      ...options,
      include: [
        {
          model: FringeBenefitClassification,
          as: "fringeBenefitClassifications",
          required: false,
        },
      ],
    };

    const fringeBenefit = await this.fringeBenefit.findOne(extendedOptions);

    return fringeBenefit;
  }
}
