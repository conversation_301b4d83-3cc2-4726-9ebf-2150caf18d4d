import { toEpoch } from "@/util/dateHelper";
import { EmployeeDocument, User } from "../models";
import { WhereOptions } from "sequelize";

export class EmployeeDocumentService {
  private employeeDocument = EmployeeDocument;

  async create(employeeDocumentObj: Partial<EmployeeDocument>): Promise<EmployeeDocument> {
    const employeeDocument = await this.employeeDocument.create(employeeDocumentObj);

    const createdEmployeeDocument = await this.employeeDocument.findOne({
      where: { id: employeeDocument.id },
    });

    return serializeEmployeeDocument(createdEmployeeDocument);
  }

  async markAsArchived(options: WhereOptions<EmployeeDocument>): Promise<number[]> {
    return this.employeeDocument.update(
      {
        isArchived: true,
      },
      {
        where: options,
      }
    );
  }

  async findAll(where: WhereOptions<EmployeeDocument>): Promise<EmployeeDocument[]> {
    const result = await this.employeeDocument.findAll({
      where,
      include: [{ model: User, attributes: ["id", "firstName", "lastName"] }],
      order: [["createdAt", "DESC"]],
    });

    return result.map((employeeDocument) => serializeEmployeeDocument(employeeDocument));
  }
}

export function serializeEmployeeDocument(employeeDocument: EmployeeDocument): EmployeeDocument {
  const employeeDocumentJSON = employeeDocument.toJSON();
  employeeDocumentJSON.createdAt = toEpoch(employeeDocumentJSON.createdAt);
  employeeDocumentJSON.updatedAt = toEpoch(employeeDocumentJSON.updatedAt);

  return employeeDocumentJSON;
}
