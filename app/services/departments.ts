import { User, Department } from "@/models";
import { UpdateOptions, Transaction, WhereOptions } from "sequelize";

export class DepartmentsService {
  private department = Department;
  private user = User;

  async findAll(organizationId: number): Promise<Department[]> {
    return await this.department.findAll({
      where: {
        organizationId,
      },
      include: [
        {
          model: User,
          attributes: ["id", "firstName", "lastName"],
        },
      ],
    });
  }

  async findOne(id: number): Promise<Department> {
    return await this.department.findByPk(id, {
      include: [{ model: User, attributes: ["id", "firstName", "lastName"] }],
    });
  }

  async getMembersFromDepartment(departmentId: number): Promise<User[]> {
    const users = await this.user.findAll({
      where: {
        departmentId,
      },
    });

    return users;
  }

  async create(departmentObj: Partial<Department>): Promise<Department> {
    return await this.department.create(departmentObj);
  }

  async update(
    departmentObj: Partial<Department>,
    options: Omit<UpdateOptions<Department>, "returning">
  ): Promise<number[]> {
    return await this.department.update(departmentObj, options);
  }

  async delete(options: WhereOptions<Department>, transaction?: Transaction): Promise<number> {
    return this.department.destroy({
      where: options,
      transaction,
    });
  }

  // New method to handle department member updates
  async updateMembers(departmentId: number | null, userIds: number[], transaction?: Transaction): Promise<number[]> {
    return await User.update(
      { departmentId },
      {
        where: { id: userIds },
        transaction,
      }
    );
  }
}
