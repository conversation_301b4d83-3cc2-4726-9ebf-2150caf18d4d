import { CreateOptions, FindOptions, UpdateOptions } from "sequelize";
import { FringeBenefitClassification, FringeBenefit, Classification } from "@/models";

export class FringeBenefitClassificationsService {
  private fringeBenefitClassification = FringeBenefitClassification;

  async create(
    fringeBenefitClassificationObj: Partial<any>,
    options: CreateOptions
  ): Promise<FringeBenefitClassification> {
    const fringeBenefitClassification: any = await this.fringeBenefitClassification.create(
      fringeBenefitClassificationObj,
      options
    );

    return fringeBenefitClassification;
  }

  async update(
    fringeBenefitClassificationObj: Partial<any>,
    options?: UpdateOptions
  ): Promise<FringeBenefitClassification> {
    const fringeBenefitClassification: any = await this.fringeBenefitClassification.update(
      fringeBenefitClassificationObj,
      options
    );

    return fringeBenefitClassification;
  }

  async findAll(options: FindOptions) {
    // check if include options are present in the provided options
    const includeOptions =
      options.include && (options.include as any).length
        ? options.include
        : [
            {
              model: FringeBenefit,
              required: true,
            },
            {
              model: Classification,
              required: true,
            },
          ];

    const extendedOptions: FindOptions = {
      ...options,
      include: includeOptions,
    };

    const fringeBenefitClassifications = await this.fringeBenefitClassification.findAll(extendedOptions);

    return fringeBenefitClassifications;
  }

  async findOne(options: FindOptions) {
    // check if include options are present in the provided options
    const includeOptions =
      options.include && (options.include as any).length
        ? options.include
        : [
            {
              model: FringeBenefit,
              required: true,
            },
            {
              model: Classification,
              required: true,
            },
          ];

    const extendedOptions: FindOptions = {
      ...options,
      include: includeOptions,
    };

    const fringeBenefitClassification = await this.fringeBenefitClassification.findOne(extendedOptions);

    return fringeBenefitClassification;
  }
}
