import { CrewMember } from "@/models";

export class CrewsMembersService {
  private crewMember = CrewMember;

  async create(crewMemberObj: Partial<CrewMember>): Promise<CrewMember> {
    return await this.crewMember.create(crewMemberObj);
  }

  async delete(options: any): Promise<number> {
    return this.crewMember.destroy({ where: options });
  }

  async findAll(options: object): Promise<CrewMember[]> {
    const extendedOptions = { ...options, raw: true };

    return await this.crewMember.findAll(extendedOptions);
  }
}
