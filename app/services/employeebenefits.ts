import { CreateOptions, Transaction, UpdateOptions } from "sequelize";
import { EmployeeBenefit, User } from "@/models";
import { UpdateEmployeeBenefit } from "@/types/employeeBenefitDto";

// condensed list to return
const EMPLOYEE_ATTRIBUTES = ["id", "firstName", "lastName", "email", "position", "phone", "role", "organizationId"];

export class EmployeeBenefitsService {
  private employeeBenefit = EmployeeBenefit;

  async create(employeeBenefitObj: Partial<any>, options: CreateOptions): Promise<EmployeeBenefit> {
    // ideally this has a standard dto and transforms data into expected orm/data access shape
    const employeeBenefit: any = await this.employeeBenefit.create(employeeBenefitObj, options);

    return employeeBenefit;
  }

  async findAll(options: any): Promise<EmployeeBenefit[]> {
    const extendedOptions = {
      include: [
        {
          model: User,
          attributes: EMPLOYEE_ATTRIBUTES,
        },
      ],
      ...options,
    };

    const employeeBenefits = await this.employeeBenefit.findAll(extendedOptions);

    return employeeBenefits;
  }

  async findOne(options: object): Promise<EmployeeBenefit> {
    const extendedOptions = {
      include: [
        {
          model: User,
          attributes: EMPLOYEE_ATTRIBUTES,
        },
      ],
      ...options,
    };

    return await this.employeeBenefit.findOne(extendedOptions);
  }

  async update(employeeBenefitUpdateObj: UpdateEmployeeBenefit, options: UpdateOptions) {
    return await this.employeeBenefit.update(employeeBenefitUpdateObj, options);
  }

  // how much precision do we need on the deactive or benefit end date?
  async deactivateCurrentBenefits(benefitIds: number[], transaction?: Transaction) {
    // deactivate specified employee benefits in your system using the provided IDs
    if (!benefitIds || benefitIds.length === 0) {
      throw new Error("No employee benefit Ids provided for deactivation.");
    }

    return this.employeeBenefit.update(
      { benefitEndDate: new Date().valueOf() },
      { where: { id: benefitIds }, transaction: transaction ? transaction : undefined }
    );
  }

  async updateCheckBenefitIds(benefitIds: number[], checkBenefitIds: string[], transaction?: Transaction) {
    // update the checkBenefitId for the specified employee benefits in your system using the provided IDs
    if (!benefitIds || benefitIds.length === 0) {
      throw new Error("No employee benefit Ids provided for update.");
    }

    await Promise.all(
      benefitIds.map((id, index) => {
        return this.employeeBenefit.update(
          { checkBenefitId: checkBenefitIds[index] },
          { where: { id }, transaction: transaction ? transaction : undefined }
        );
      })
    );
  }
}
