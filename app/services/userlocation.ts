import UserLocation from "@/models/userlocation";
import { toEpoch } from "@/util/dateHelper";
import { createPoint } from "@/util/locationHelper";
import { getAddressFromCoords } from "@/util/googlemaps";
import { Break, TimeSheet } from "@/models";
import { Transaction } from "sequelize";

export interface UserLocationData {
  lat: number;
  long: number;
  speed: number;
  horizontalAccuracy: number;
  verticalAccuracy: number;
  altitude: number;
  loggedAt: string;
  platform: string;
  appState: string;
  locationPermission: string;
  preciseLocationEnabled: boolean;
  locationEvent: string;
  userId: number;
  timesheetId: number;
  breakId: number | null;
  geofenceProjectId: number | null;
  organizationId: number | null;
}

export class UserLocationService {
  private userLocation = UserLocation;

  async create(userLocationData: UserLocationData, transaction?: Transaction): Promise<UserLocation | null> {
    try {
      const { lat, long, loggedAt, locationEvent } = userLocationData;
      const validationResult = await this.validateUserLocation(userLocationData, transaction);

      // Return null if validation indicates we should skip this update
      if (validationResult === null) {
        return null;
      }

      const loggedAtDate = new Date(parseInt(loggedAt));

      let locationAddress = null;

      // for all location events except LOCATION_CHANGE, get the address from the coordinates
      // so that clients can display the address without making another request
      if (locationEvent !== "LOCATION_CHANGE") {
        const address = await getAddressFromCoords(lat, long);
        locationAddress = address;
      }

      const locationCoordinates = createPoint(long, lat);

      const userLocation = await this.userLocation.create(
        {
          ...userLocationData,
          loggedAt: loggedAtDate,
          locationCoordinates,
          locationAddress,
        },
        { transaction }
      );

      return serializeUserLocation(userLocation.toJSON());
    } catch (err) {
      throw new Error("Failed to create user location");
    }
  }

  async validateUserLocation(userLocationData: UserLocationData, transaction: Transaction): Promise<void> {
    try {
      const {
        lat,
        long,
        speed,
        horizontalAccuracy,
        verticalAccuracy,
        loggedAt,
        platform,
        appState,
        locationPermission,
        preciseLocationEnabled,
        locationEvent,
        userId,
        timesheetId,
        breakId,
        organizationId,
      } = userLocationData;

      if (
        lat === undefined ||
        long === undefined ||
        loggedAt === undefined ||
        platform === undefined ||
        appState === undefined ||
        locationPermission === undefined ||
        preciseLocationEnabled === undefined ||
        locationEvent === undefined ||
        userId === undefined ||
        timesheetId === undefined ||
        organizationId === undefined
      ) {
        throw new Error("Missing required fields");
      }

      const timesheet: any = await TimeSheet.findOne({
        where: { id: timesheetId },
        include: [
          {
            model: Break,
          },
        ],
        transaction: transaction,
      });

      // if timesheet is not found and locationEvent is not CLOCK_IN, discard the update
      if (!timesheet && locationEvent !== "CLOCK_IN") {
        throw new Error("Timesheet not found");
      }

      // if user has already clocked out, discard the update
      if (locationEvent === "LOCATION_CHANGE" && timesheet && timesheet.clockOut) {
        return null;
      }

      // if event is LOCATION_CHANGE, verify user is clocked in and not on a break
      // before storing that event, to protect user privacy
      // we already check if user is clocked in above
      // check if they are on a break here
      if (locationEvent === "LOCATION_CHANGE" && timesheet.breaks && timesheet.breaks.length > 0) {
        // check if there's any break where break.end is null
        const activeBreak = timesheet.breaks.find((b: any) => !b.end);
        if (activeBreak) {
          throw new Error("User is on a break");
        }
      }

      const locationCoordinates = createPoint(long, lat);
      if (!locationCoordinates) {
        throw new Error("Invalid location");
      }

      if (speed < 0 || horizontalAccuracy < 0 || verticalAccuracy < 0) {
        throw new Error("Invalid speed, horizontalAccuracy, verticalAccuracy");
      }

      const loggedAtDate = new Date(parseInt(loggedAt));
      if (isNaN(loggedAtDate.getTime())) {
        throw new Error("Invalid loggedAt");
      }

      if (platform !== "IOS" && platform !== "ANDROID") {
        throw new Error("Invalid platform");
      }

      if (appState !== "BACKGROUND" && appState !== "FOREGROUND") {
        throw new Error("Invalid appState");
      }

      if (
        locationPermission !== "NOT_DETERMINED" &&
        locationPermission !== "WHEN_IN_USE" &&
        locationPermission !== "ALWAYS" &&
        locationPermission !== "DENIED" &&
        locationPermission !== "RESTRICTED" &&
        locationPermission !== "TURNED_OFF"
      ) {
        throw new Error("Invalid locationPermission");
      }

      if (locationEvent === "BREAK_START" || locationEvent === "BREAK_END") {
        if (breakId === undefined) {
          throw new Error("Missing breakId");
        }
      }
    } catch (err) {
      throw new Error(`Failed to validate user location: ${err.message}`);
    }
  }

  async findAll(options: any): Promise<UserLocation[]> {
    const userLocations = await this.userLocation.findAll(options);

    return userLocations.map(serializeUserLocation);
  }
}

export function serializeUserLocation(userLocationJSON: any): any {
  userLocationJSON.loggedAt = toEpoch(userLocationJSON.loggedAt);
  userLocationJSON.locationCoordinates = userLocationJSON.locationCoordinates.coordinates;

  if (userLocationJSON.createdAt) {
    userLocationJSON.createdAt = toEpoch(userLocationJSON.createdAt);
  }

  if (userLocationJSON.updatedAt) {
    userLocationJSON.updatedAt = toEpoch(userLocationJSON.updatedAt);
  }

  if (userLocationJSON.geofenceProjectId && userLocationJSON.project) {
    userLocationJSON.locationAddress = userLocationJSON.project.name;
  }

  return userLocationJSON;
}
