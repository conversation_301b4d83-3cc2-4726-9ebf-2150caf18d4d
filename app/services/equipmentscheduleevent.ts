import EquipmentScheduleEvent from "@/models/equipmentscheduleevent";

export class EquipmentScheduleEventService {
  private equipmentScheduleEvent = EquipmentScheduleEvent;

  async create(equipmentScheduleEventObj: Partial<EquipmentScheduleEvent>): Promise<EquipmentScheduleEvent> {
    const equipmentScheduleEvent = await this.equipmentScheduleEvent.create(equipmentScheduleEventObj);

    return equipmentScheduleEvent;
  }

  async findAll(options: object): Promise<EquipmentScheduleEvent[]> {
    const extendedOptions = { ...options, raw: true };

    return await this.equipmentScheduleEvent.findAll(extendedOptions);
  }

  async delete(equipmentId: number, scheduleEventId: number): Promise<number> {
    return await this.equipmentScheduleEvent.destroy({
      where: {
        equipmentId: equipmentId,
        scheduleEventId: scheduleEventId,
      },
    });
  }
  async deleteAllEquipmentsFromScheduleEvent({ scheduledEventId }: { scheduledEventId: number }): Promise<number> {
    const extendedOptions = {
      where: { scheduleEventId: scheduledEventId },
    };

    return await this.equipmentScheduleEvent.destroy(extendedOptions);
  }
}
