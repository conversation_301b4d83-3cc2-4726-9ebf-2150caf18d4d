import { CreateOptions, UpdateOptions, FindOptions, cast, literal, fn, col, Op } from "sequelize";
import { CompanyBenefit, EmployeeBenefit } from "@/models";

import { UpdateCompanyBenefit } from "@/types/companyBenefitDto";

export class CompanyBenefitsService {
  private companyBenefit = CompanyBenefit;

  async create(companyBenefitObj: Partial<any>, options: CreateOptions): Promise<CompanyBenefit> {
    // ideally this has a standard dto and transforms data into expected orm/data access shape
    const companyBenefit: any = await this.companyBenefit.create(companyBenefitObj, options);

    return companyBenefit;
  }

  async findAll(options: object): Promise<CompanyBenefit[]> {
    const extendedOptions: FindOptions = {
      ...options,
      raw: true,
      include: [
        {
          model: EmployeeBenefit,
          as: "employeeBenefits",
          attributes: [],
          where: {
            [Op.or]: [{ benefitEndDate: null }, { benefitEndDate: { [Op.gt]: new Date() } }],
          },
          required: false,
        },
      ],
      attributes: {
        include: [[cast(literal('COUNT(DISTINCT "employeeBenefits"."user_id")'), "integer"), "numberEnrolled"]],
      },
      group: ["companyBenefits.id"],
    };

    const companyBenefits = await this.companyBenefit.findAll(extendedOptions);

    return companyBenefits;
  }

  async findOne(options: object) {
    const extendedOptions: FindOptions = {
      ...options,
      raw: true,
      include: [
        {
          model: EmployeeBenefit,
          as: "employeeBenefits",
          attributes: [],
          required: false,
        },
      ],
      attributes: {
        include: [
          [cast(fn("COUNT", fn("DISTINCT", col('"employeeBenefits"."user_id"'))), "integer"), "numberEnrolled"],
        ],
      },
      group: ["companyBenefits.id"],
    };

    const companyBenefit = await this.companyBenefit.findOne(extendedOptions);

    return companyBenefit;
  }

  async update(companyBenefitUpdateObj: UpdateCompanyBenefit, options: UpdateOptions) {
    return await this.companyBenefit.update(companyBenefitUpdateObj, options);
  }
}
