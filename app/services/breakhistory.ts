import { CreateOptions } from "sequelize";
import { BreakHistory, Break } from "../models";

import { propertyExclusions } from "./timesheethistory";

interface BreakHistoryProps {
  [key: string]: {
    prev: any; // Replace 'any' with the appropriate type
    new: any; // Replace 'any' with the appropriate type
  };
}
export class BreakHistoryService {
  private breakHistory = BreakHistory;

  async create(breakHistoryObj: Partial<BreakHistory>, options: CreateOptions): Promise<BreakHistory> {
    const breakHistory = await this.breakHistory.create(breakHistoryObj, options);

    return breakHistory;
  }

  async findAll(options: object): Promise<BreakHistory[]> {
    return await this.breakHistory.findAll(options);
  }

  formatBreakHistory(
    existingBreakData: Partial<Break>,
    newBreakData: Partial<Break>,
    type: string,
    timesheetId: number,
    userId: number
  ) {
    const history: Partial<BreakHistory> = Object.keys(newBreakData).reduce((acc, key) => {
      const safeKey = key as keyof Break;

      const existingProp =
        existingBreakData[safeKey] instanceof Date === true
          ? existingBreakData[safeKey].getTime()
          : existingBreakData[safeKey];

      // Check if the value has changed and not part of excluded properties
      if (!(safeKey in propertyExclusions) && existingProp !== newBreakData[safeKey]) {
        // there is a special case where breaks are soft deleted - this is an update to a property instead of a call to delete
        if (safeKey === "isDeleted") {
          acc[safeKey] = {
            prev: existingBreakData,
            new: null,
          };
        } else {
          acc[safeKey] = {
            prev: existingProp,
            new: newBreakData[safeKey],
          };
        }

        // quick transform if previous or new is null
        if (acc[safeKey].prev === null || acc[safeKey].prev === undefined) {
          acc[safeKey].prev = "None";
        }

        if (acc[safeKey].new === null || acc[safeKey].prev === undefined) {
          acc[safeKey].new = "None";
        }

        // if type is create, combine break start and end into one object
        if (type === "CREATE" && safeKey === "start") {
          acc.breakCreated = {
            prev: "None",
            new: {
              start: newBreakData?.start || "None",
              end: newBreakData?.end || "None",
            },
          };
        }
      }

      // clean up
      if (acc?.breakCreated) {
        // clean up and delete
        delete acc?.start;
        delete acc?.end;
      }

      return acc;
    }, {} as Partial<BreakHistoryProps>);

    const breakHistoryObj = {
      breakId: existingBreakData.id,
      history: JSON.stringify(history),
      type,
      createdBy: userId,
      timesheetId,
    };

    return breakHistoryObj;
  }
}
