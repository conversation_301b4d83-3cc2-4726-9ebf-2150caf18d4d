import dayjs from "dayjs";
import { stringify } from "csv-stringify/sync";

import { Organization, Project, User } from "@/models";
import { ExtendedTimesheet } from "@/models/timesheet";
import { SimplifiedFringeBenefitClassification } from "@/models/fringebenefitclassification";
import { CheckBenefit, CheckCompany, CheckEmployee, Payroll, PayrollItem, PayrollItemEarning } from "@/types/check";
import { ExtendedUserClassification } from "@/models/userclassification";
import { REPORT_FORMAT } from "@/models/reports";
import { LCPTrackerReport } from "@/services/reports/LCPTrackerReport";
import { Report } from "@/services/reports/Report";
import { JSONReport } from "@/services/reports/JSONReport";
import { Response } from "express";
import FederalDepartmentOfLabor from "@/services/reports/FederalDepartmentOfLabor";
import NJDepartmentOfTransportation from "@/services/reports/NJDepartmentOfTransportation";
import { CADepartmentOfIndustrialRelations } from "@/services/reports/CADepartmentOfIndustrialRelations";
import { WADepartmentOfLaborAndIndustries } from "@/services/reports/WADepartmentOfLaborAndIndustries";
import NJDepartmentOfLaborAndWorkforce from "@/services/reports/NJDepartmentOfLaborAndWorkforce";
import DEDepartmentOfLabor from "@/services/reports/DEDepartmentOfLabor";
import { Address, PayFrequency } from "@/types/checkDto";
import ORDepartmentOfLaborAndIndustries from "@/services/reports/ORDepartmentOfLaborAndIndustries";
// HOLD to eventually move some logic here

export interface RequiredCertifiedPayrollData {
  signatoryName: string;
  signatoryTitle: string;
  company: CheckCompany;
  payroll: Payroll;
  project: Project;
  payrollNumber: string;
  organization?: Organization; // will need for timezone
  employees: CheckEmployee[];
  userClassifications: ExtendedUserClassification[];
  enrichedTimesheets: ExtendedTimesheet[];
  enrichedTimesheetsAllProjects: Partial<ExtendedTimesheet>[];
  noWorkWeek: boolean;
  payrollWeekEnding: string;
  remarks?: string;
  isLastCertifiedPayrollReport?: boolean;
}

export interface WeeklyCertifiedPayrollEmployeeData {
  jobClassification: string; // specified in Hammr data
  classificationId: number;
  userClassificationId: number;
  checkRegEarningCodeId: string;
  checkOtEarningCodeId: string;
  checkDotEarningCodeId: string;
  baseRate: string; // specified in Hammr data
  fringeRate: string; // specified in Hammr data
  cashFringe: string; // calculated from Hammr data
  regRateOfPay: string; // calculated from Hammr data
  otRateOfPay: string; // calculated from Hammr data
  dotRateOfPay: string; // calculated from Hammr data
  grossEarned: string; // calculated from Check payroll.items[x].earnings.amount - can be different from how hammr would calculate
  netWages: string; // check provides this in items[x].net_pay
  regHoursWorked?: string; // timesheets aggregated - allow decimal - round to 2 places
  otHoursWorked?: string; // timesheets aggregated - allow decimal - round to 2 places
  dotHoursWorked?: string; // timesheets aggregated - allow decimal - round to 2 places
  earnings: PayrollItemEarning[]; // comes from Check
  taxes: any[]; // comes from Check
  postTaxDeductions: any[]; // comes from Check
  postTaxDeductionsOverrides: any[];
  benefits: CheckBenefit[]; // comes from Check
  benefitsOverrides: any[]; // comes from Check
  payrollData?: PayrollItem; // comes from Check
  attributedTimesheets: ExtendedTimesheet[]; // filtered for this employee and userClassificationId
  allAttributedTimesheets: Partial<ExtendedTimesheet>[]; // all timesheets for this employee
  fringeBenefitClassifications?: SimplifiedFringeBenefitClassification[];
  paymentMethod?: string;
  paperCheckNumber?: string;
  payFrequency?: string;
  employee: {
    id: number;
    name: string;
    firstName: string;
    lastName: string;
    middleName?: string;
    checkEmployeeId: string;
    phone: string;
    position?: string;
    lastFourSSN?: string;
    hireDate?: string;
    status?: "active" | "inactive";
    residence?: Address;
    gender?: string;
    ethnicity?: string;
  };
}

export interface Deduction {
  name: string;
  amount: string;
  value?: string;
  type?: string;
}

export interface CertifiedPayrollData {
  companyName: string;
  company: {
    state: string;
    city: string;
    zip: string;
    line1: string;
    line2: string;
    fullStreet: string;
    fullAddress: string;
  };
  isSubcontractor: boolean;
  signatoryName: string;
  signatoryTitle: string;
  payrollNumber: string;
  areFringeBenefitsPaidToPlans: boolean;
  areFringeBenefitsPaidInCash: boolean;
  weekEnding: string;
  project: Project;
  employeesWeekData: Partial<WeeklyCertifiedPayrollEmployeeData>[];
  remarks?: string;
  payFrequency?: string;
  payday?: string;
  isLastCertifiedPayrollReport?: boolean;
}

export class ReportsService {
  reports: Partial<Record<keyof typeof REPORT_FORMAT, new (response: Response) => Report>> = {
    DepartmentOfLabor: FederalDepartmentOfLabor,
    LCPTracker: LCPTrackerReport,
    JSON: JSONReport,
    DepartmentOfIndustrialRelations: CADepartmentOfIndustrialRelations,
    DepartmentOfLaborAndIndustries: WADepartmentOfLaborAndIndustries,
    NJDepartmentOfTransportation: NJDepartmentOfTransportation,
    NJDepartmentOfLaborAndWorkforce: NJDepartmentOfLaborAndWorkforce,
    DEDepartmentOfLabor: DEDepartmentOfLabor,
    ORDepartmentOfLaborAndIndustries: ORDepartmentOfLaborAndIndustries,
  };

  // Custom sorting function

  stitchCertifiedPayrollData(
    data: RequiredCertifiedPayrollData,
    timezone = "America/Los_Angeles"
  ): CertifiedPayrollData {
    const {
      signatoryName,
      signatoryTitle,
      company,
      project,
      employees,
      organization,
      enrichedTimesheets,
      enrichedTimesheetsAllProjects,
      payroll,
      userClassifications,
      payrollNumber,
      noWorkWeek,
      payrollWeekEnding,
      remarks = "",
      isLastCertifiedPayrollReport = false,
    } = data;

    const result: Partial<CertifiedPayrollData> = {};

    // misc data and company info
    result.signatoryName = signatoryName;
    result.signatoryTitle = signatoryTitle;
    result.payrollNumber = noWorkWeek ? `${payrollNumber} (No Work Week)` : payrollNumber;
    result.payFrequency = payroll?.pay_frequency;
    result.payday = payroll?.payday;
    result.isSubcontractor = project.prevailingWageIsSubcontractor;
    result.remarks = remarks;
    result.isLastCertifiedPayrollReport = isLastCertifiedPayrollReport;

    result.areFringeBenefitsPaidToPlans = userClassifications.some(
      (uc) => (uc?.sumOfFringeBenefits || 0) > 0 || (uc?.sumOfEmployeeBenefits || 0) > 0
    );

    result.areFringeBenefitsPaidInCash = userClassifications.some((uc) => parseFloat(uc?.cashFringe || "0") > 0);

    // so weekEnding comes from data.payrolol.period_end which comes from check without timezone
    // based on this, we should want parse this by the organization's timezone
    result.weekEnding = dayjs.tz(payrollWeekEnding, timezone).format("MM-DD-YYYY");
    result.companyName = company.legal_name;

    result.company = {
      fullAddress: `${company.address.line1}${company.address.line2 ? ", " + company.address.line2 : ""}, ${
        company.address.city
      }, ${company.address.state} ${company.address.postal_code}, ${company.address.country}`,
      line1: company.address.line1,
      line2: company.address.line2,
      fullStreet: `${company.address.line1}${company.address.line2 ? ", " + company.address.line2 : ""}`,
      city: company.address.city,
      state: company.address.state,
      zip: company.address.postal_code,
    };
    result.project = project && project instanceof Project ? project.toJSON() : project;

    // to build employeeWeekData - we use the skeleton shape of userClassifications - the rationale is that this already breaksd own rows by employee and classification
    const transformedUserClassifications = userClassifications.map((userClass) => {
      const { checkEmployeeId } = userClass.user;

      const employeeCheckEarningsData = payroll.items.find((item) => item.employee === checkEmployeeId);

      // this is primarily only PW enriched timesheets where userClassification matches
      const eligibleEnrichedTimesheets = enrichedTimesheets.filter((ts) => {
        return ts.workerId === userClass.user.id && ts.userClassificationId === userClass.id;
      });

      const eligibleAllProjectsEnrichedTimesheets = enrichedTimesheetsAllProjects.filter((ts) => {
        return ts.workerId === userClass.user.id;
      });

      const checkEmployee = employees.find((emp) => emp.id === userClass.user.checkEmployeeId);

      // If organization settings enable prevailing wage rate override (overridePwIfBelowRegularRate),
      // use the higher of: (1) regular hourly rate or (2) prevailing wage rate (base pay + cash fringe)
      const basePay = parseFloat(userClass.basePay);
      const cashFringe = parseFloat(userClass.cashFringe);
      const hourlyRate = parseFloat(eligibleEnrichedTimesheets?.[0]?.regEarningRate?.amount || "0");
      const otRate = parseFloat(eligibleEnrichedTimesheets?.[0]?.otEarningRate?.amount || "0");
      const dotRate = parseFloat(eligibleEnrichedTimesheets?.[0]?.dotEarningRate?.amount || "0");
      const shouldUseHourlyRates =
        organization?.prevailingWageSettings?.overridePwIfBelowRegularRate && hourlyRate > basePay + cashFringe;

      const data: WeeklyCertifiedPayrollEmployeeData = {
        employee: {
          id: userClass.user.id,
          name: `${userClass.user.firstName} ${userClass.user.lastName}`,
          firstName: userClass.user.firstName,
          lastName: userClass.user.lastName,
          middleName: undefined,
          checkEmployeeId,
          position: userClass.user.position,
          phone: userClass.user.phone,
          lastFourSSN: checkEmployee?.ssn_last_four,
          hireDate: checkEmployee ? dayjs(checkEmployee.start_date).format("YYYY-MM-DD") : undefined,
          status: checkEmployee ? (checkEmployee.active ? "active" : "inactive") : undefined,
          residence: checkEmployee ? checkEmployee.residence : undefined,
          gender: userClass?.user?.gender,
          ethnicity: userClass?.user?.ethnicity,
        },
        fringeBenefitClassifications: userClass.fringeBenefits,
        jobClassification: userClass.classification.name,
        classificationId: userClass.classification.id,
        userClassificationId: userClass.id,
        checkRegEarningCodeId: userClass.classification.checkRegEarningCodeId,
        checkOtEarningCodeId: userClass.classification.checkOtEarningCodeId,
        checkDotEarningCodeId: userClass.classification.checkDotEarningCodeId,
        baseRate: shouldUseHourlyRates ? `${hourlyRate}` : `${basePay}`,
        fringeRate: shouldUseHourlyRates ? `0` : `${userClass.fringePay}`,
        cashFringe: shouldUseHourlyRates ? `0` : `${cashFringe}`,
        regRateOfPay: shouldUseHourlyRates ? `${hourlyRate}` : `${userClass?.regRateOfPay}`,
        otRateOfPay: shouldUseHourlyRates ? `${otRate}` : `${userClass?.otRateOfPay}`,
        dotRateOfPay: shouldUseHourlyRates ? `${dotRate}` : `${userClass?.dotRateOfPay}`,
        earnings: employeeCheckEarningsData?.earnings || [],
        grossEarned:
          employeeCheckEarningsData?.earnings?.length > 0
            ? employeeCheckEarningsData.earnings.reduce((acc, curr) => acc + parseFloat(curr.amount), 0).toFixed(2)
            : "0.00",
        netWages: employeeCheckEarningsData?.net_pay || "0.00",
        taxes: employeeCheckEarningsData?.taxes || [],
        postTaxDeductions: employeeCheckEarningsData?.post_tax_deductions || [],
        postTaxDeductionsOverrides: employeeCheckEarningsData?.post_tax_deduction_overrides || [],
        benefits: employeeCheckEarningsData?.benefits || [],
        benefitsOverrides: employeeCheckEarningsData?.benefit_overrides || [],
        attributedTimesheets: eligibleEnrichedTimesheets,
        paymentMethod: employeeCheckEarningsData?.payment_method,
        paperCheckNumber: employeeCheckEarningsData?.paper_check_number,
        allAttributedTimesheets: eligibleAllProjectsEnrichedTimesheets,
      };

      return data;
    });

    // filter out any userClassifications that have no attributed timesheets
    const filteredTransformedUserClassifications = transformedUserClassifications.filter((uc) => {
      if (uc.attributedTimesheets?.length > 0) {
        return true;
      }
    });

    result.employeesWeekData = filteredTransformedUserClassifications;

    return result as CertifiedPayrollData;
  }

  // used primarily for certified payroll, but technically could be used for any project
  determinePayrollNumber(
    firstProjectTimesheetDate: Date,
    payrollWeekEnding: string, // date string in YYYY-MM-DD format (assume non-timezone)
    payFrequency: PayFrequency = "weekly",
    timezone = "America/Los_Angeles",
    isFinalPayroll = false
  ): string {
    const firstTimesheetDate = dayjs(firstProjectTimesheetDate).tz(timezone);
    const weekEnding = dayjs.tz(payrollWeekEnding, timezone);
    const daysSinceFirstTimesheet = weekEnding.diff(firstTimesheetDate, "days");

    let payrollNumber: number;
    let monthsDiff: number;
    switch (payFrequency) {
      case "biweekly":
        payrollNumber = Math.ceil(daysSinceFirstTimesheet / 14) + 1;
        break;
      case "semimonthly":
        monthsDiff = weekEnding.diff(firstTimesheetDate, "month", true);
        payrollNumber = Math.ceil(monthsDiff * 2) + 1;
        break;
      case "monthly":
        payrollNumber = Math.ceil(weekEnding.diff(firstTimesheetDate, "month")) + 1;
        break;
      case "weekly":
      default:
        payrollNumber = Math.ceil(daysSinceFirstTimesheet / 7) + 1;
        break;
    }

    return `${payrollNumber} ${isFinalPayroll ? "(FINAL)" : ""}`;
  }

  // build 401k csv report
  build401kReport(checkPayroll: Payroll, organization: Organization, hammrUsers: User[]) {
    // Get all unique retirement benefit names
    const uniqueBenefitNames = new Set<string>();
    checkPayroll?.items?.forEach((item) => {
      item?.benefits?.forEach((benefit) => {
        if (benefit.benefit === "401k" || benefit.benefit === "roth_401k") {
          const benefitName = benefit?.description?.split("-")?.[0]?.trim() || benefit?.description || "401k";
          uniqueBenefitNames.add(benefitName);
        }
      });
    });
    const benefitNames = Array.from(uniqueBenefitNames);

    const reportHeader = stringify([
      ["Company Name:", `${organization.name}`],
      ["Pay Date:", `${dayjs(checkPayroll.period_end).format("MM/DD/YYYY")}`],
    ]);

    // Create headers for each benefit type
    const headers = [
      "Last Name",
      "First Name",
      "Total Hours",
      "Gross Pay",
      // Add employee and company contribution columns for each benefit
      ...benefitNames.flatMap((name) => [`${name} (employee)`, `${name} (company)`]),
      // Add loan payment as the last column
      "401k loan payment (employee)",
    ];

    const reportRowHeaders = stringify([headers]);

    const reportRows = stringify(
      checkPayroll.items.map((item) => {
        const hammrUser = hammrUsers.find((user) => user.checkEmployeeId === item.employee);

        return [
          hammrUser?.lastName,
          hammrUser?.firstName,
          item?.earnings?.reduce((a, b) => a + b.hours, 0),
          item?.earnings?.reduce((a, b) => a + parseFloat(b.amount), 0),
          // Add contribution amounts for each benefit
          ...benefitNames.flatMap((benefitName) => {
            const benefit = item?.benefits?.find(
              (b) =>
                (b.benefit === "401k" || b.benefit === "roth_401k") &&
                (b?.description?.split("-")?.[0]?.trim() === benefitName || b?.description === benefitName)
            );

            return [benefit?.employee_contribution_amount || "0.00", benefit?.company_contribution_amount || "0.00"];
          }),
          // Keep loan payment as the last column
          item?.post_tax_deductions?.find(
            (deduction) =>
              deduction.description === "401k loan repayment" || deduction.metadata?.["401k loan repayment"]
          )?.miscellaneous?.amount || "0.00",
        ];
      })
    );

    return `${reportHeader}\n${reportRowHeaders}\n${reportRows}`;
  }
}
