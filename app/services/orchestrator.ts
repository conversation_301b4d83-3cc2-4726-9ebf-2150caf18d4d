import { CheckService } from "@/services/check";

/* eslint-disable no-unused-vars */
export interface DownstreamServiceConfig {
  endpoint: string;
  method: string;
  transformData: (data: any) => any;
}

/* eslint-disable no-unused-vars */
export interface DownstreamService {
  post(endpoint: string, data: any): Promise<any>;
  patch(endpoint: string, data: any): Promise<any>;
  getForwardingConfiguration?: (config: Record<string, any>) => DownstreamServiceConfig;
}

export class ServiceOrchestrator {
  private services: Map<string, DownstreamService>;

  constructor() {
    this.services = new Map();
    // Initialize with instances of your services
    this.services.set("check", new CheckService());
    // Add other services as needed
  }

  // Method to add or register services dynamically
  registerService(serviceName: string, service: DownstreamService): void {
    this.services.set(serviceName, service);
  }

  // Method to forward a request to a specific service
  async forwardRequest(serviceName: string, method: string, endpoint: string, data: any) {
    const service = this.services.get(serviceName);

    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }

    // Ensure method name is lowercase to match the interface's method names
    method = method.toLowerCase();

    // Check if the service has the method (e.g., post, patch)
    if (typeof service[method as keyof DownstreamService] === "function") {
      return await service[method as keyof DownstreamService](endpoint as any, data);
    } else {
      throw new Error(`Method ${method} is not supported by service ${serviceName}`);
    }
  }

  async forwardToAll(data: any) {
    // Assuming getForwardingConfiguration is properly implemented in your service instances
    const serviceConfigs = Array.from(this.services.entries())
      .map(([serviceName, service]) => {
        const config = service.getForwardingConfiguration
          ? service.getForwardingConfiguration({
              endpoint: data.endpoint,
              method: data.method,
            })
          : null;

        return { serviceName, config };
      })
      .filter(({ config }) => config !== null);

    const promises = serviceConfigs.map(({ serviceName, config }) => {
      if (!config) return Promise.reject("Configuration not found");

      const service = this.services.get(serviceName);
      if (!service) return Promise.reject(`Service ${serviceName} not found`);

      const transformedData = config.transformData(data);
      const method = config.method.toLowerCase();

      // Ensure the service has the method and it's a function
      if (typeof service[method as keyof DownstreamService] === "function") {
        // Call the method (e.g., post, patch) on the service instance
        return service[method as keyof DownstreamService](config.endpoint as any, transformedData);
      } else {
        return Promise.reject(`Method ${method} is not supported by service ${serviceName}`);
      }
    });

    return Promise.allSettled(promises);
  }
}
