import { Break, TimeSheet } from "@/models";
import { UpdateOptions } from "sequelize";
import { BreakHistoryService } from "@/services/breakhistory";
import { UserLocationService } from "@/services/userlocation";
import { ExtendedTimesheet } from "@/models/timesheet";
import { Transaction } from "sequelize";

export interface BreakData {
  id: number | null;
  start: string;
  end: string | null;
  startLocation: any | null;
  endLocation: any | null;
  isManual: boolean;
  isDeleted: boolean | null;
}

type ValidatableBreak = {
  start: string;
  end: string | null;
  isDeleted: boolean | null;
};

class BreakValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "BreakValidationError";
  }
}

export class BreaksService {
  private breakInstance = Break;
  breakHistoryService: BreakHistoryService;
  userLocationService: UserLocationService;

  constructor() {
    this.breakHistoryService = new BreakHistoryService();
    this.userLocationService = new UserLocationService();
  }

  async create(
    breakObj: BreakData,
    timesheetId: number,
    createdBy: number,
    organizationId: number,
    transaction?: Transaction
  ) {
    const start = breakObj.start;
    const end = breakObj.end;

    const startInt = parseInt(start);
    const endInt = parseInt(end);

    if (!startInt) {
      throw new Error("break start is not valid");
    }

    if (end && !endInt) {
      throw new Error("break end is not valid");
    }

    const endDate = endInt ? new Date(endInt) : null;

    const createdBreak = await Break.create(
      {
        start: new Date(startInt),
        end: endDate,
        isManual: breakObj.isManual ?? false,
        createdBy,
        timesheetId,
        isDeleted: breakObj.isDeleted,
      },
      { transaction }
    );

    if (breakObj.startLocation !== undefined && createdBreak) {
      await this.userLocationService.create(
        {
          ...breakObj.startLocation,
          timesheetId: timesheetId,
          breakId: createdBreak.id,
          organizationId,
        },
        transaction
      );
    }

    if (breakObj.endLocation !== undefined && createdBreak) {
      await this.userLocationService.create(
        {
          ...breakObj.endLocation,
          timesheetId: timesheetId,
          breakId: createdBreak.id,
          organizationId,
        },
        transaction
      );
    }
  }

  async createOrUpdate(
    creatorId: number,
    breaks: BreakData[],
    timesheet: TimeSheet,
    initialTimesheetCreate: boolean,
    organizationId: number,
    transaction?: Transaction
  ) {
    if (breaks) {
      for (const breakObj of breaks) {
        validateBreak(breakObj, timesheet);
        const breakId = breakObj.id ?? null;
        const breakPayload = this.generateBreakPayload(creatorId, breakObj);

        // break create flow
        if (breakId === null) {
          // we want to avoid adding a history row when:
          // - break doesn't have an end time indicating user is starting a break from mobile
          // - timesheet is created for the first time with a break
          if (breakPayload.end !== undefined && initialTimesheetCreate === false) {
            const breakHistoryObj = this.breakHistoryService.formatBreakHistory(
              {},
              breakPayload,
              "CREATE",
              timesheet.id,
              creatorId
            );

            await this.breakHistoryService.create(breakHistoryObj, { transaction });
          }

          await this.create(breakObj, timesheet.id, creatorId, organizationId, transaction);
        } else {
          // generate the break payload
          // we want to fetch the break with id
          const existingBreak = await this.findOne({ where: { id: breakId }, transaction });

          // we want to avoid adding a history row when:
          // - existing break doesn't have an end i.e. user is ending a break from mobile
          if (existingBreak.end !== null) {
            const breakHistoryObj = this.breakHistoryService.formatBreakHistory(
              existingBreak,
              breakPayload,
              breakObj?.isDeleted === true ? "DELETE" : "UPDATE",
              timesheet.id,
              creatorId
            );

            // don't add empty row(s)
            if (breakHistoryObj.history && breakHistoryObj.history !== "{}") {
              await this.breakHistoryService.create(breakHistoryObj, { transaction });
            }
          }

          if (breakObj.endLocation !== undefined) {
            await this.userLocationService.create(
              {
                ...breakObj.endLocation,
                timesheetId: timesheet.id,
                breakId: breakId,
                organizationId,
              },
              transaction
            );
          }

          await this.update(breakPayload, { where: { id: breakId } }, transaction);
        }
      }
    }
  }

  async validateExistingBreaks(timesheet: TimeSheet, transaction: Transaction) {
    const breaks = await this.findAll({
      where: {
        timesheetId: timesheet.id,
      },
      transaction,
    });

    if (breaks) {
      for (const breakObj of breaks) {
        const validatableBreak: ValidatableBreak = {
          start: breakObj.start.getTime().toString(),
          end: breakObj.end ? breakObj.end.getTime().toString() : null,
          isDeleted: breakObj.isDeleted,
        };

        validateBreak(validatableBreak, timesheet);
      }
    }
  }

  generateBreakPayload(creatorId: number, breakObj: BreakData) {
    const patchUpdates: any = {};

    const start = breakObj.start;
    if (start && parseInt(start)) {
      patchUpdates.start = parseInt(start);
    }

    const end = breakObj.end;
    if (end && parseInt(end)) {
      patchUpdates.end = parseInt(end);
    }

    const isDeleted = breakObj.isDeleted ?? null;
    if (isDeleted !== null) {
      patchUpdates.isDeleted = isDeleted;
    }

    patchUpdates.editedBy = creatorId;

    return patchUpdates;
  }

  async findOne(options: object): Promise<Break> {
    return await this.breakInstance.findOne(options);
  }

  async findAll(options: object): Promise<Break[]> {
    const extendedOptions = { ...options, raw: true };
    const breaks = await this.breakInstance.findAll(extendedOptions);

    return breaks;
  }

  async update(
    data: Partial<Break>,
    options: Omit<UpdateOptions<Break>, "returning">,
    transaction?: Transaction
  ): Promise<number[]> {
    return await Break.update(data, {
      ...options,
      where: {
        ...options.where,
      },
      transaction,
    });
  }
}

function validateBreak(breakObj: ValidatableBreak, timesheet: ExtendedTimesheet) {
  if (breakObj.isDeleted) {
    return;
  }

  const start = breakObj.start;
  let startInt: number;
  if (start) {
    startInt = parseInt(start);
    if (!startInt) {
      throw new Error("break start is not valid");
    }

    const startDate = new Date(startInt);
    const clockIn = timesheet.dataValues.rawClockIn?.valueOf() ?? timesheet.clockIn?.valueOf();
    if (startDate.valueOf() < clockIn) {
      throw new BreakValidationError(
        "The break couldn’t be added because the break timing conflicts with the recorded clock-in or clock-out times. Please adjust the break timing slightly and try again."
      );
    }
  }

  const end = breakObj.end;
  if (end) {
    const endInt = parseInt(end);
    if (!endInt) {
      throw new Error("break end is not valid");
    }

    const endDate = new Date(endInt);
    const clockOut = timesheet.dataValues.rawClockOut?.valueOf() ?? timesheet.clockOut?.valueOf();
    if (clockOut && endDate.valueOf() > clockOut) {
      throw new BreakValidationError(
        "The break couldn’t be added because the break timing conflicts with the recorded clock-in or clock-out times. Please adjust the break timing slightly and try again."
      );
    }

    if (startInt && endInt && startInt > endInt) {
      throw new BreakValidationError("The break end time cannot be before the break start time.");
    }
  }
}
