import ProjectDocument from "@/models/projectdocument";
import Project from "@/models/project";
import { toEpoch } from "@/util/dateHelper";
import { User } from "../models";

export class ProjectDocumentService {
  private projectDocument = ProjectDocument;

  async create(projectDocumentObj: Partial<ProjectDocument>): Promise<ProjectDocument> {
    const projectDocument = await this.projectDocument.create(projectDocumentObj);

    const createdProjectDocument = await this.projectDocument.findOne({
      where: { id: projectDocument.id },
      include: [
        {
          model: Project,
          attributes: ["id", "name"],
          required: true,
        },
      ],
    });

    return serializeProjectDocument(createdProjectDocument);
  }

  async markAsDeleted(options: any): Promise<number[]> {
    return this.projectDocument.update(
      {
        isDeleted: true,
      },
      {
        where: options,
      }
    );
  }

  async findAll(options: any): Promise<ProjectDocument[]> {
    const extendedOptions = extendOptions(options);
    let projectDocuments = await this.projectDocument.findAll(extendedOptions);
    projectDocuments = projectDocuments.map((projectDocument) => {
      return serializeProjectDocument(projectDocument);
    });

    return projectDocuments;
  }

  async count(options: object): Promise<number> {
    const extendedOptions = extendOptions(options);

    try {
      const countResult = await this.projectDocument.count(extendedOptions);

      if (Array.isArray(countResult)) {
        // Sum up the counts from each group
        return countResult.reduce((total, item) => total + item.count, 0);
      } else {
        return countResult;
      }
    } catch (error) {
      console.error("Error in count method:", error);
      throw error;
    }
  }
}

function extendOptions(options: any) {
  const whereClause = options.where || {};

  return {
    ...options,
    where: whereClause,
    include: [
      {
        model: Project,
        attributes: ["id", "name"],
        required: true,
      },
      {
        model: User,
        attributes: ["id", "firstName", "lastName"],
      },
    ],
  };
}

export function serializeProjectDocument(projectDocument: ProjectDocument): any {
  const projectDocumentJSON = projectDocument.toJSON();
  projectDocumentJSON.createdAt = toEpoch(projectDocumentJSON.createdAt);
  projectDocumentJSON.updatedAt = toEpoch(projectDocumentJSON.updatedAt);

  return projectDocumentJSON;
}
