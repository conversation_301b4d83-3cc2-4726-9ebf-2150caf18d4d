import { TimeSheet, TimesheetHistory } from "../models";
import { Transaction } from "sequelize";

interface TimesheetHistoryProps {
  [key: string]: {
    prev: any; // Replace 'any' with the appropriate type
    new: any; // Replace 'any' with the appropriate type
  };
}

export const propertyExclusions = {
  editedBy: true,
  project: true,
  costCode: true,
  workersCompCode: true,
  userClassification: true,
  description: true,
  approvedAt: true,
};

export class TimesheetHistoryService {
  private timesheetHistory = TimesheetHistory;

  async create(timesheetHistoryObj: Partial<TimesheetHistory>, transaction?: Transaction): Promise<TimesheetHistory> {
    const timesheetHistory = await this.timesheetHistory.create(timesheetHistoryObj, { transaction });

    return timesheetHistory;
  }

  async findAll(options: object): Promise<TimesheetHistory[]> {
    return await this.timesheetHistory.findAll(options);
  }

  formatTimesheetHistory(
    existingTimesheetData: Partial<TimeSheet>,
    newTimesheetData: Partial<TimeSheet>,
    type: string,
    userId: number
  ): Partial<TimesheetHistory> {
    // essentially loop through each data property and create a new object with prev and new values
    const history: Partial<TimesheetHistoryProps> = Object.keys(newTimesheetData).reduce((acc, key) => {
      // Assure TypeScript that key is a string here, which is safe since Object.keys() returns string[]
      const safeKey = key as keyof TimeSheet;

      // if the type isnt a primitive, then we need to check if its a date - ignore other types for now
      const existingProp =
        existingTimesheetData[safeKey] instanceof Date
          ? (existingTimesheetData[safeKey] as Date).getTime()
          : existingTimesheetData[safeKey];

      if (!(safeKey in propertyExclusions) && existingProp !== newTimesheetData[safeKey]) {
        // if property is an id, it should fetch the data relevant so we can save/display name
        // not foolproof but should work for now
        const isIdKey = safeKey.slice(-2) === "Id";
        const idKeyProperty = safeKey.slice(0, -2);

        const prevValue = isIdKey ? existingTimesheetData[idKeyProperty as keyof TimeSheet]?.name : existingProp;
        const newValue = isIdKey ? newTimesheetData[idKeyProperty as keyof TimeSheet]?.name : newTimesheetData[safeKey];

        acc[safeKey] = {
          prev: prevValue,
          new: newValue,
        };

        // quick transform if previous or new is null
        if (acc[safeKey].prev === null || acc[safeKey].prev === undefined) {
          acc[safeKey].prev = "None";
        }

        if (acc[safeKey].new === null || acc[safeKey].prev === undefined) {
          acc[safeKey].new = "None";
        }

        if (safeKey === "breakDuration") {
          acc[safeKey].prev = `${existingTimesheetData[safeKey] / 60} mins`;
          acc[safeKey].new = `${newTimesheetData[safeKey] / 60} mins`;
        }

        if (safeKey === "otDuration") {
          acc[safeKey].prev = `${existingTimesheetData[safeKey] / 60} mins`;
          acc[safeKey].new = `${newTimesheetData[safeKey] / 60} mins`;
        }

        if (safeKey === "dotDuration") {
          acc[safeKey].prev = `${existingTimesheetData[safeKey] / 60} mins`;
          acc[safeKey].new = `${newTimesheetData[safeKey] / 60} mins`;
        }

        if (safeKey === "driveTimeDuration") {
          acc[safeKey].prev = `${existingTimesheetData[safeKey] / 60} mins`;
          acc[safeKey].new = `${newTimesheetData[safeKey] / 60} mins`;
        }

        // If both previous and current value are "None",
        //    it usually means something was passed as a string instead of a number or vice versa.
        // To prevent this, this check removes the history entries with "None" since we don't have a use for them.
        // Technically, we shouldn't have any of them if the correct data is passed from the controller/service.
        if (acc[safeKey].prev === "None" && acc[safeKey].new === "None") {
          delete acc[safeKey];
        }

        if (acc[safeKey].prev === "0 mins" && acc[safeKey].new === "0 mins") {
          delete acc[safeKey];
        }
      }

      // should remove these properties from the history object
      if (acc?.editedBy) {
        delete acc["editedBy"];
      }

      return acc;
    }, {} as Partial<TimesheetHistoryProps>);

    const timesheetHistoryObj = {
      timesheetId: existingTimesheetData.id,
      history: JSON.stringify(history),
      type: type,
      createdBy: userId,
    };

    return timesheetHistoryObj;
  }
}
