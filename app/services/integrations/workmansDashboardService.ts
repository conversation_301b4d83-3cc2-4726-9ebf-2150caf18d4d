import { WorkmansDashboard } from "@/lib/integrations/workmansDashboard";
import dayjs, { Dayjs } from "dayjs";
import IntegrationObjectSetting, { OBJECT_TYPES } from "@/models/integrationobjectsetting";
import IntegrationMapping, { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";
import Project from "@/models/project";
import IntegrationUserToken from "@/models/integrationusertoken";
import { Organization } from "@/models";

// sync jobs that have these statuses only
const VALID_PROJECT_STATUSES = ["contracted", "proceed", "scheduled", "completed", "invoicing", "paid"];

interface WorkmansDashboardJob {
  job_number: string;
  id: number;
  name: string;
  status: string;
  prevailing_wage: string;
  created_at: string;
  updated_at: string;
}

interface WorkmansDashboardResponse {
  job_count: number;
  jobs: WorkmansDashboardJob[];
}

export class WorkmansDashboardService {
  workmansDashboard: WorkmansDashboard;

  constructor() {
    this.workmansDashboard = new WorkmansDashboard();
  }

  projects(startDate: Dayjs, endDate: Dayjs): Promise<WorkmansDashboardResponse> {
    return this.workmansDashboard.projects(
      startDate.format("YYYY-MM-DD"),
      endDate.format("YYYY-MM-DD")
    ) as Promise<WorkmansDashboardResponse>;
  }

  async syncProjects(integrationId: number) {
    const integration = await IntegrationUserToken.findOne({
      where: {
        id: integrationId,
        isEnabled: true,
      },
      include: [
        {
          model: Organization,
          as: "organization",
        },
      ],
    });

    const integrationObjectSetting = await IntegrationObjectSetting.findOne({
      where: {
        integrationUserTokenId: integrationId,
        isEnabled: true,
        objectType: OBJECT_TYPES.PROJECTS,
        syncType: "PULL",
      },
    });

    if (!integration || !integrationObjectSetting) {
      return;
    }

    const organization = integration.organization as Organization;
    const lastRun = dayjs(integrationObjectSetting.lastSyncedAt ?? new Date()).tz(organization.timezone);
    const today = dayjs().tz(organization.timezone);

    const response = await this.projects(lastRun, today);

    await Promise.all(
      response.jobs
        .filter((job) => VALID_PROJECT_STATUSES.includes(job.status) && job.prevailing_wage === "None")
        .map(async (job) => {
          // Check if we already have this project mapped
          const existingMapping = await IntegrationMapping.findOne({
            where: {
              externalId: job.id.toString(),
              objectType: INTEGRATION_OBJECT_TYPE.PROJECT,
              integrationUserTokenId: integrationId,
            },
          });

          if (!existingMapping) {
            const hammrProject = await Project.create({
              name: job.name,
              projectNumber: job.job_number,
              organizationId: integration.organizationId,
              isPrevailingWage: false,
            });

            await IntegrationMapping.create({
              hammrId: hammrProject.id,
              externalId: job.id.toString(),
              objectType: INTEGRATION_OBJECT_TYPE.PROJECT,
              integrationUserTokenId: integrationId,
            });
          } else {
            await Project.update(
              {
                name: job.name,
                projectNumber: job.job_number,
              },
              {
                where: { id: existingMapping.hammrId },
              }
            );
          }
        })
    );

    await integrationObjectSetting.update({
      lastSyncedAt: new Date(),
    });
  }
}
