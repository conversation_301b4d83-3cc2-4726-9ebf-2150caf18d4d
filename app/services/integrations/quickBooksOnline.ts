import { RutterService } from "@/services/integrations/rutter";
import { QuickBooksOnline } from "@/lib/integrations/quickBooksOnline";
import { JournalEntry, JournalEntryLineItem } from "@/types/journalEntry";
import { GlAccountMapping } from "@/models";
import { QuickBooksJournalEntry, QuickBooksJournalEntryLine } from "@/lib/integrations/quickBooks.types";

export class QuickBooksOnlineService {
  rutter: RutterService;
  quickBooks: QuickBooksOnline;

  constructor() {
    this.rutter = new RutterService();
    this.quickBooks = new QuickBooksOnline();
  }

  async projects(organizationId: number) {
    const integration = await this.rutter.getIntegration(organizationId);

    const { authToken, realm } = await this.rutter.getPassThroughToken(integration.accessToken);

    return await this.quickBooks.projects(authToken, realm);
  }

  async employees(organizationId: number) {
    const integration = await this.rutter.getIntegration(organizationId);

    const { authToken, realm } = await this.rutter.getPassThroughToken(integration.accessToken);

    return await this.quickBooks.employees(authToken, realm);
  }

  /**
   * Converts our internal journal entry format to QuickBooks format
   *
   * @param journalEntry Our internal journal entry structure
   * @param glAccountMappings GL Account mappings to look up account names and platform IDs
   * @returns Journal entry formatted for QuickBooks API
   */
  private convertToQuickBooksJournalEntry(
    journalEntry: JournalEntry,
    glAccountMappings: GlAccountMapping[]
  ): QuickBooksJournalEntry {
    // Create maps for quick lookup of account names and platform IDs
    const accountNameMap = new Map(glAccountMappings.map((m) => [m.accountId, m.accountName]));
    const platformIdMap = new Map(glAccountMappings.map((m) => [m.accountId, m.platformId || m.accountId]));

    const qboJournalEntry: QuickBooksJournalEntry = {
      TxnDate: journalEntry.transaction_date.split("T")[0], // Format date as YYYY-MM-DD
      PrivateNote: journalEntry.memo,
      Line: [],
    };

    if (journalEntry.currency_code) {
      qboJournalEntry.CurrencyRef = {
        value: journalEntry.currency_code,
      };
    }

    // Convert line items
    journalEntry.line_items.forEach((item: JournalEntryLineItem) => {
      const accountName = accountNameMap.get(item.account_id);
      // Use the platformId (QuickBooks native ID) when available, fall back to accountId
      const accountId = platformIdMap.get(item.account_id);

      const line: QuickBooksJournalEntryLine = {
        Amount: Math.abs(item.total_amount),
        DetailType: "JournalEntryLineDetail",
        Description: item.description,
        JournalEntryLineDetail: {
          PostingType: item.total_amount > 0 ? "Debit" : "Credit",
          AccountRef: {
            value: accountId,
            name: accountName,
          },
          Entity: {
            Type: "Customer",
            EntityRef: {
              value: item.project_id,
            },
          },
        },
      };

      // Add project reference if available
      if (item.project_id) {
        line.ProjectRef = {
          value: item.project_id,
        };
      }

      qboJournalEntry.Line.push(line);
    });

    return qboJournalEntry;
  }

  /**
   * Creates a journal entry in QuickBooks Online using direct QBO API
   *
   * @param organizationId Organization ID to get QBO integration
   * @param journalEntry Journal entry data structured according to Hammr format
   * @param glAccountMappings GL Account mappings to look up account names
   * @returns Created journal entry from QuickBooks
   */
  async createJournalEntry(organizationId: number, journalEntry: JournalEntry, glAccountMappings: GlAccountMapping[]) {
    // Get QBO integration for this organization
    const integration = await this.rutter.getIntegration(organizationId);

    // Get authentication tokens
    const { authToken, realm } = await this.rutter.getPassThroughToken(integration.accessToken);

    // Convert our journal entry to QBO format
    const qboJournalEntry = this.convertToQuickBooksJournalEntry(journalEntry, glAccountMappings);

    // Create journal entry in QBO
    return await this.quickBooks.createJournalEntry(authToken, realm, qboJournalEntry);
  }
}
