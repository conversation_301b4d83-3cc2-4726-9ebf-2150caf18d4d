export interface RutterCustomer {
  id: string;
  email: string;
  phone: string;
  status: "active" | string;
  addresses: [];
  parent_id: string;
  created_at: string;
  tax_number: string;
  updated_at: string;
  platform_id: string;
  contact_name: string;
  platform_url: string;
  currency_code: string;
  customer_name: string;
  subsidiary_id: string;
  last_synced_at: string;
  payment_terms_id: string;
  registration_number: string;
}
