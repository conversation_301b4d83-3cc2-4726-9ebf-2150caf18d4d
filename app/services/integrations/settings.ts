import IntegrationObjectSetting, { OBJECT_TYPES, SYNC_TYPES } from "@/models/integrationobjectsetting";
import IntegrationUserToken from "@/models/integrationusertoken";
import HttpError from "@/errors/HTTPError";
import { RutterService } from "./rutter";
import IntegrationMapping, { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";
import * as Sentry from "@sentry/node";
import { QuickBooksOnlineService } from "@/services/integrations/quickBooksOnline";
import Timesheet from "@/models/timesheet";
import { Op, WhereOptions } from "sequelize";
import { inngest } from "../../queueing/client";
import Project from "@/models/project";
import { WorkmansDashboardService } from "@/services/integrations/workmansDashboardService";
import { chunk } from "lodash";

interface IntegrationSettingData {
  objectType: IntegrationObjectSetting["objectType"];
  isEnabled: boolean;
  syncType: IntegrationObjectSetting["syncType"];
}

export class IntegrationSettingsService {
  private rutterService: RutterService;
  private quickBooksOnlineService: QuickBooksOnlineService;
  private workmansDashboardService: WorkmansDashboardService;

  constructor() {
    this.rutterService = new RutterService();
    this.quickBooksOnlineService = new QuickBooksOnlineService();
    this.workmansDashboardService = new WorkmansDashboardService();
  }

  async getSettings(integrationId?: number) {
    const integration = await IntegrationUserToken.findByPk(integrationId, {
      include: [
        {
          model: IntegrationObjectSetting,
          as: "objectSettings",
        },
      ],
    });

    if (!integration) {
      throw new HttpError(404, "Integration not found");
    }

    return integration.objectSettings || [];
  }

  async createOrUpdateSetting(
    integrationId: number | undefined,
    settingData: IntegrationSettingData & {
      options?: Record<string, any>;
    }
  ) {
    const integration = await IntegrationUserToken.findByPk(integrationId);

    if (!integration) {
      throw new HttpError(404, "Integration not found");
    }

    if (!Object.values(OBJECT_TYPES).includes(settingData.objectType as keyof typeof OBJECT_TYPES)) {
      throw new HttpError(400, "Invalid object type");
    }

    if (!Object.values(SYNC_TYPES).includes(settingData.syncType)) {
      throw new HttpError(400, "Invalid sync type");
    }

    const [setting, created] = await IntegrationObjectSetting.findOrCreate({
      where: {
        integrationUserTokenId: integrationId,
        objectType: settingData.objectType,
      },
      defaults: settingData,
    });

    if (!created) {
      await setting.update({
        isEnabled: settingData.isEnabled,
        syncType: settingData.syncType,
      });
    }

    if (integration.isEnabled && setting.isEnabled) {
      if (setting.objectType === OBJECT_TYPES.EMPLOYEES) {
        await inngest.send({
          name: "integration/employees-sync",
          data: {
            organizationId: integration.organizationId,
          },
        });
      }

      // same as above, but for Projects
      if (setting.objectType === OBJECT_TYPES.PROJECTS) {
        if (integration.platform === "QUICKBOOKS") {
          await this.syncProjects(integration.organizationId);
          await setting.update({
            lastSyncedAt: new Date(),
          });
        }
        // TODO ionel: remove this in the SAGE Intacct implementation PR
        if (integration.platform === "WORKMANS_DASHBOARD") {
          await this.workmansDashboardService.syncProjects(integration.id);
        }
      }

      // same as above, but for Projects
      if (setting.objectType === OBJECT_TYPES.TIMESHEETS) {
        await this.syncTimesheets(integration.organizationId, settingData.options);
      }
    }

    return setting;
  }

  getIntegrationObjectSetting(
    organizationId: number,
    objectType: IntegrationObjectSetting["objectType"],
    platform = "QUICKBOOKS"
  ) {
    // For now, we only allow QUICKBOOKS syncing just so we can move faster when building this feature.
    // We should consider updating this so it syncs every integration/platform, not just QUICKBOOKS

    return IntegrationObjectSetting.findOne({
      where: {
        isEnabled: true,
        objectType,
      },
      include: [
        {
          model: IntegrationUserToken,
          as: "integration",
          required: true,
          where: {
            organizationId: organizationId,
            isEnabled: true,
            platform,
          },
        },
      ],
    });
  }

  async syncProjects(organizationId: number) {
    try {
      // Get the integration for this organization
      const integration = await this.rutterService.getIntegration(organizationId);

      // Get all Hammr projects for this organization
      const hammrProjects = await Project.findAll({
        where: {
          organizationId,
          isArchived: false,
        },
      });

      const qboProjects = (await this.quickBooksOnlineService.projects(organizationId)) ?? [];

      // Get existing project mappings
      const existingMappings = await IntegrationMapping.findAll({
        where: {
          integrationUserTokenId: integration.id,
          objectType: INTEGRATION_OBJECT_TYPE.PROJECT,
        },
      });

      const existingMappingsByQBProjectId = Object.fromEntries(
        existingMappings.map((mapping) => [mapping.externalId, mapping])
      ) as Record<string, IntegrationMapping>;

      // Process each Hammr project
      for (const qboProject of qboProjects) {
        // If the project already has a mapping, skip
        if (existingMappingsByQBProjectId[qboProject.Id]) {
          await Project.update(
            {
              name: qboProject.DisplayName,
              customerName: qboProject.CompanyName,
              organizationId,
            },
            {
              where: {
                id: existingMappingsByQBProjectId[qboProject.Id].hammrId,
              },
            }
          );

          continue;
        }

        // Find matching QBO project by name (case insensitive)
        let matchingHammrProject = hammrProjects.find(
          (hammrProject) => qboProject.DisplayName.toLowerCase() === hammrProject.name.toLowerCase()
        );

        if (!matchingHammrProject) {
          matchingHammrProject = await Project.create({
            name: qboProject.DisplayName,
            customerName: qboProject.CompanyName,
            organizationId,
          });
        }

        // Create a mapping between the Hammr project and QBO project
        await IntegrationMapping.create({
          hammrId: matchingHammrProject.id,
          objectType: INTEGRATION_OBJECT_TYPE.PROJECT,
          externalId: qboProject.Id,
          integrationUserTokenId: integration.id,
        });
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error syncing projects:", error);
    }
  }

  private async syncTimesheets(organizationId: number, options: Record<string, any>) {
    if (!options.all && !options.startingFrom) {
      return;
    }

    try {
      // Get the integration for this organization
      const integration = await this.rutterService.getIntegration(organizationId);

      const where: WhereOptions<Timesheet> = {
        organizationId,
        status: "PAID",
        isDeleted: false,
      };

      if (options.startingFrom) {
        where.clockIn = {
          [Op.gt]: options.startingFrom,
        };
      }

      const timesheets = await Timesheet.findAll({ where });

      // splitting timesheets in 100 items chunks when calling the `integration/timesheets-sync` Inngest event due to this Express JS error:
      // {"expected":136760,"length":136760,"limit":102400,"message":"request entity too large","type":"entity.too.large"}
      chunk(
        timesheets.map((timesheet) => timesheet.id),
        100
      ).forEach((chunk) => {
        inngest.send({
          name: "integration/timesheets-sync",
          data: {
            timesheetIds: chunk,
            organizationId: integration.organizationId,
          },
        });
      });
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error syncing employees:", error);
    }
  }
}
