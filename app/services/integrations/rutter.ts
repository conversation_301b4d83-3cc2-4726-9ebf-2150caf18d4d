import HttpError from "@/errors/HTTPError";
import {
  Break,
  CompanyBenefit,
  EmployeeBenefit,
  IntegrationUserToken,
  Organization,
  Project,
  TimeSheet,
  User,
} from "@/models";
import IntegrationMapping, { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";
import { QuickBooksOnline } from "@/lib/integrations/quickBooksOnline";
import { HTTPService } from "@/lib/integrations/httpService";
import dayjs from "dayjs";
import { calculateBreakMinutes } from "@/util/timesheetHelper";
import { QuickBooksRef } from "@/lib/integrations/quickBooks.types";
import { OBJECT_TYPES } from "@/models/integrationobjectsetting";
import { Department, EarningRate, GlAccountMapping } from "@/models";
import { Payroll } from "@/types/check";
import GlAccountMappingSetting, { CONSOLIDATE_BY } from "@/models/glaccountmappingsetting";
import { IntegrationUserTokensService } from "@/services/integrationusertokenservice";
import { JournalEntryResponse } from "@/types/journalEntry";
import IntegrationObjectSetting from "@/models/integrationobjectsetting";

const { RUTTER_API_URL, RUTTER_CLIENT_ID, RUTTER_CLIENT_SECRET, RUTTER_VERSION } = process.env;

export class RutterService extends HTTPService {
  quickBooksOnline: QuickBooksOnline;
  protected baseUrl = `${RUTTER_API_URL}/versioned`;

  constructor() {
    super();
    this.quickBooksOnline = new QuickBooksOnline();
  }

  protected getHeaders() {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");

    return {
      "Content-Type": "application/json",
      Authorization: `Basic ${encodedCredentials}`,
      "X-Rutter-Version": RUTTER_VERSION,
    };
  }

  async getPassThroughToken(accessToken: string) {
    const credentials = await this.get<{
      credential: { access_token: string; platform_specific_credentials: { realm: string } };
    }>("connections/credentials", { access_token: accessToken });

    // todo , cache for 1h
    return {
      authToken: credentials.credential.access_token,
      realm: credentials.credential.platform_specific_credentials.realm,
    };
  }

  // TODO "platform" is hardcoded for now
  async getIntegration(organizationId: number, platform = "QUICKBOOKS") {
    const integrationUserToken = await IntegrationUserToken.findOne({
      where: {
        organizationId: organizationId,
        isEnabled: true,
        platform,
      },
    });

    if (!integrationUserToken) {
      throw new HttpError(400, "Integration not activated");
    }

    return integrationUserToken;
  }

  async getEmployeeExternalId(hammrId: number) {
    const user = await User.findByPk(hammrId);
    const integration = await this.getIntegration(user.organizationId);

    let employeeExternalId = await IntegrationMapping.findOne({
      where: {
        hammrId: hammrId,
        objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
        integrationUserTokenId: integration.id,
      },
    }).then((integration) => integration?.externalId);

    if (!employeeExternalId) {
      // This might be an old user or it wasn't mapped or the mapping was deleted from the DB.
      // We need to find if the employee exists in QBO and use that, or add it to QBO if it doesn't exist

      const { authToken, realm } = await this.getPassThroughToken(integration.accessToken);
      employeeExternalId = await this.quickBooksOnline
        .getEmployeeByDisplayName(authToken, realm, `${user.firstName} ${user.lastName}`)
        .then((response) => response.Id);

      if (employeeExternalId) {
        // if we found an employee, let's add the mapping in our DB so we can find it next time
        await IntegrationMapping.create({
          hammrId,
          objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
          externalId: employeeExternalId,
          integrationUserTokenId: integration.id,
        });
      } else {
        employeeExternalId = await this.createEmployee(user).then((response) => response.Id);
      }
    }

    return employeeExternalId;
  }

  async createEmployee(employee: User) {
    const integration = await this.getIntegration(employee.organizationId);

    const { authToken, realm } = await this.getPassThroughToken(integration.accessToken);

    const quickBooksEmployee = await this.quickBooksOnline.createEmployee(authToken, realm, employee);

    await IntegrationMapping.create({
      hammrId: employee.id,
      objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
      externalId: quickBooksEmployee.Id,
      integrationUserTokenId: integration.id,
    });

    return quickBooksEmployee;
  }

  async updateEmployee(id: number, employee: User) {
    const integration = await this.getIntegration(employee.organizationId);

    const { authToken, realm } = await this.getPassThroughToken(integration.accessToken);

    let integrationMapping = await IntegrationMapping.findOne({
      where: {
        hammrId: id,
        objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
        integrationUserTokenId: integration.id,
      },
    });
    if (!integrationMapping) {
      const quickBooksEmployee = await this.createEmployee(employee);
      integrationMapping = await IntegrationMapping.create({
        hammrId: id,
        objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
        externalId: quickBooksEmployee.Id,
        integrationUserTokenId: integration.id,
      });
    }

    return this.quickBooksOnline.updateEmployee(authToken, realm, integrationMapping.externalId, employee);
  }

  async createTimesheet(organizationId: number, timesheet: TimeSheet) {
    const integration = await this.getIntegration(organizationId);

    // Create time activity in QuickBooks
    const { authToken, realm, timesheetData } = await this.prepareTimesheetData(organizationId, timesheet);
    const quickBooksTimeActivity = await this.quickBooksOnline.createTimeActivity(authToken, realm, timesheetData);

    // Create mapping for the time activity
    await IntegrationMapping.create({
      hammrId: timesheet.id,
      objectType: INTEGRATION_OBJECT_TYPE.TIMESHEET,
      externalId: quickBooksTimeActivity.Id,
      integrationUserTokenId: integration.id,
    });

    return quickBooksTimeActivity;
  }

  async updateTimesheet(organizationId: number, timesheet: TimeSheet) {
    const qboTimeActivityMapping = await IntegrationMapping.findOne({
      where: {
        hammrId: timesheet.id,
        objectType: OBJECT_TYPES.TIMESHEETS,
      },
    });

    if (!qboTimeActivityMapping) {
      return await this.createTimesheet(organizationId, timesheet);
    }

    const { authToken, realm, timesheetData } = await this.prepareTimesheetData(organizationId, timesheet);

    // Update time activity in QuickBooks
    await this.quickBooksOnline.updateTimeActivity(authToken, realm, qboTimeActivityMapping.externalId, timesheetData);

    return qboTimeActivityMapping;
  }

  private async prepareTimesheetData(organizationId: number, timesheet: TimeSheet) {
    const integration = await this.getIntegration(organizationId);
    const { authToken, realm } = await this.getPassThroughToken(integration.accessToken);
    const qboEmployeeId = await this.getEmployeeExternalId(timesheet.workerId);

    // Get project mapping
    const qboProjectMapping = await IntegrationMapping.findOne({
      where: {
        hammrId: timesheet.projectId,
        objectType: INTEGRATION_OBJECT_TYPE.PROJECT,
        integrationUserTokenId: integration.id,
      },
    });

    // this is needed to calculate the total break duration
    await timesheet.reload({
      include: [
        {
          model: Break,
        },
        {
          model: Project,
        },
        {
          model: User,
          foreignKey: "workerId",
        },
      ],
    });

    const organization = await Organization.findByPk(organizationId);

    const txnDate = dayjs(timesheet.clockIn).tz(organization.timezone).format("YYYY-MM-DD");
    const startTime = dayjs(timesheet.clockIn).tz(organization.timezone).format("YYYY-MM-DDTHH:mm:ssZ");
    const endTime = dayjs(timesheet.clockOut).tz(organization.timezone).format("YYYY-MM-DDTHH:mm:ssZ");
    const breakMinutes = calculateBreakMinutes(timesheet);

    const timesheetData = {
      startTime: startTime,
      endTime: endTime,
      txnDate: txnDate,
      breakHours: Math.floor(breakMinutes / 60),
      breakMinutes: breakMinutes % 60,
      employeeRef: {
        value: qboEmployeeId,
      },
      customerRef: undefined as QuickBooksRef,
    };

    if (qboProjectMapping) {
      timesheetData.customerRef = {
        value: qboProjectMapping.externalId,
      };
    }

    return { integration, authToken, realm, timesheetData };
  }

  /**
   * Generates a journal entry based on the consolidation type
   */
  async generateJournalEntry(
    payroll: Payroll,
    glAccountMappings: GlAccountMapping[],
    glAccountMappingSettings: GlAccountMappingSetting,
    integrationUserToken: IntegrationUserToken,
    organizationId: number,
    companyBenefits: CompanyBenefit[],
    employeeBenefits: EmployeeBenefit[],
    organization: Organization
  ): Promise<JournalEntryResponse> {
    const integrationUserTokensService = new IntegrationUserTokensService();

    const consolidationType = glAccountMappingSettings.consolidateJournalEntryBy;

    if (consolidationType === CONSOLIDATE_BY.DEPARTMENT) {
      const users = await User.findAll({
        where: { organizationId },
        include: [
          {
            model: Department,
            required: false,
          },
        ],
      });

      return await integrationUserTokensService.mapPayrollToJournalEntryPerDepartment(
        payroll,
        glAccountMappings,
        users,
        companyBenefits,
        employeeBenefits
      );
    } else if (consolidationType === CONSOLIDATE_BY.PROJECT) {
      const projectSyncSetting = await IntegrationObjectSetting.findOne({
        where: {
          integrationUserTokenId: integrationUserToken.id,
          objectType: OBJECT_TYPES.PROJECTS,
          isEnabled: true,
        },
      });

      if (!projectSyncSetting) {
        throw new Error("Please enable Projects syncing first");
      }

      const users = await User.findAll({
        where: { organizationId },
        include: [
          {
            model: EarningRate,
            as: "earningRates",
            required: false,
            where: {
              active: true,
            },
          },
        ],
      });

      const result = await integrationUserTokensService.mapPayrollToJournalEntryPerProject(
        payroll,
        glAccountMappings,
        users,
        companyBenefits,
        employeeBenefits,
        organization,
        integrationUserToken.id
      );

      return result;
    } else {
      return await integrationUserTokensService.mapPayrollToJournalEntry(
        payroll,
        glAccountMappings,
        companyBenefits,
        employeeBenefits
      );
    }
  }
}
