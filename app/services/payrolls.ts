import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { FindOptions, Op, col } from "sequelize";
import { startCase, toLower } from "lodash";

import { CheckService } from "@/services/check";
import { timesheetsReportInclude, timesheetsReportOrder, TimesheetsService } from "@/services/timesheets";
import TimeOffRequestService from "@/services/timeoffrequest";
import { FringeBenefitClassificationsService } from "@/services/fringebenefitclassifications";

import { Classification, EarningRate, FringeBenefit, Organization, User, TimeOffPolicy } from "@/models";
import { ExtendedTimesheet, ExtendedUserEarningsSummary, UserEarningsSummary, WageSummary } from "@/models/timesheet";
import { PayrollPeriodDates, PayrollProcessingData, UserToRemoveSummaryFrom } from "@/models/payroll";
import { ExtendedUserPartial } from "@/models/user";
import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import { EARNING_RATE_PERIODS, EARNING_RATE_TYPES } from "@/models/earningrate";
import { TimeOffPolicyType } from "@/models/timeoffpolicy";
import { salariedInclude } from "@/controllers/payrolls";

import { getHourlyEarningRate, getResourceType, summarizeSalariedEmployeeEarnings } from "@/util/userHelper";
import {
  categorizeUserEarningsSummaries,
  filterEligibleTimesheets,
  filterIneligibleTimeOffRequests,
  mapExistingPayrollToPayloadData,
} from "@/util/payrollHelpers";
import { convertDatesToTimestamps } from "@/util/dateHelper";
import { prefixCollectionValues } from "@/util/collectionHelpers";
import { summarizeUserTimesheetData } from "@/util/timesheetHelper";

import {
  CheckContractor,
  CheckEmployee,
  ContractorMetadata,
  ContractorPayment,
  Payroll,
  PayrollEarningType,
  PayrollItem,
  PayrollItemEarning,
} from "@/types/check";
import { PayFrequency } from "@/types/checkDto";
import EmployeeReimbursement from "@/models/employeereimbursement";
import { ExtendedEmployeeReimbursement } from "@/models/employeereimbursement";

dayjs.extend(utc);

export const PER_DIEM_REIMBURSEMENT_CODE = "per_diem";
export const CONTRACTOR_PAYMENT_CODE = "contractor_payment";

interface HandleCreatePayrollParams {
  payloadData: any;
  organization: Organization;
  checkEmployeeData: CheckEmployee[];
  checkContractorData: CheckContractor[];
  hourlyEmployeeSummaries: UserEarningsSummary[];
  contractorSummaries: UserEarningsSummary[];
  salariedEmployeeSummaries: UserEarningsSummary[];
  timeOffRequests: TimeOffRequest[];
  periodStartTimestamp: number;
  periodEndTimestamp: number;
}

interface HandleUpdatePayrollParams extends HandleCreatePayrollParams {
  payrollId: string;
  checkContractorData: CheckContractor[];
  existingPayrollData: Payroll;
  salariedEmployeesToSkip?: string[];
  paymentMethodOverride?: Record<string, string>;
  perDiemReimbursements?: ExtendedEmployeeReimbursement[];
}

export interface PayloadEarning {
  resourceId: string;
  description?: string;
  type?: string;
  amount?: string;
  hours?: number;
  earning_code?: string;
  earning_rate?: string;
  metadata?: any;
}

export interface PayloadReimbursement {
  resourceId: string;
  amount?: string;
  description?: string;
  code?: string;
}

interface GeneratePayrollItemsParams {
  payrollId: string;
  checkEmployeeData: CheckEmployee[];
  hourlyEmployeeSummaries: UserEarningsSummary[];
  salariedEmployeeSummaries: Partial<UserEarningsSummary>[];
  payloadEarnings: PayloadEarning[];
  payloadReimbursements: PayloadReimbursement[];
  periodStartTimestamp: number;
  periodEndTimestamp: number;
  timezone: string;
  hourlyUsersWithEarnings: ExtendedUserPartial[];
  timeOffRequests: TimeOffRequest[];
  paymentMethodOverride?: Record<string, string>;
  perDiemReimbursements?: ExtendedEmployeeReimbursement[];
}

interface GenerateContractorPaymentsParams {
  checkContractorData: CheckContractor[];
  contractorSummaries: UserEarningsSummary[];
  payloadReimbursements: PayloadReimbursement[];
  payrollId: string;
  paymentMethodOverride?: Record<string, string>;
  perDiemReimbursements?: ExtendedEmployeeReimbursement[];
  additionalContractorPayments?: PayloadEarning[];
}

export interface GenerateEmployeeReimbursementObjectsParams {
  checkEmployeeData: CheckEmployee;
  listOfReimbursements: PayloadReimbursement[];
  perDiemReimbursements?: ExtendedEmployeeReimbursement[];
}

export const PAY_FREQUENCY_HOURS_MAP: Record<PayFrequency, number> = {
  weekly: 40,
  biweekly: 80,
  semimonthly: 86.67,
  monthly: 173.33,
  quarterly: 520,
  annually: 2080,
};

// "salaried" is allowed now but we add specific checks to metadata to decide whether to include it
const EXCLUDED_PAYLOAD_EARNING_TYPES = ["hourly", "salaried", "overtime", "double_overtime"];

interface GenerateEmployeeEarningObjectsParams {
  checkEmployeeData: CheckEmployee;
  nonPWTimesheetsSummary: WageSummary[];
  pwTimesheetsSummary: WageSummary[];
  salariedEmployeeSummary: UserEarningsSummary | null;
  payloadEarnings: PayloadEarning[];
  periodStartTimestamp: number;
  periodEndTimestamp: number;
  timezone: string;
  hourlyUserWithEarnings: ExtendedUserPartial;
  timeOffRequests: TimeOffRequest[];
}

export class PayrollsService {
  private checkService: CheckService;
  private timesheetsService: TimesheetsService;
  private fringeBenefitClassificationsService: FringeBenefitClassificationsService;
  private timeOffRequestService: TimeOffRequestService;

  constructor() {
    this.checkService = new CheckService();
    this.timesheetsService = new TimesheetsService();
    this.fringeBenefitClassificationsService = new FringeBenefitClassificationsService();
    this.timeOffRequestService = new TimeOffRequestService();
  }

  /**
   * This function fetches timesheets for a payroll and overfetches depending on what gets picked up via `adjustedDates` - this overfetching allows "better" attribution downstream
   * @param organizationId - The ID of the organization for which the timesheets are being fetched.
   * @param periodDates - The period dates for the payroll.
   * @returns The timesheets for the payroll.
   */
  private async fetchTimesheetsForPayroll(
    organizationId: number,
    periodDates: PayrollPeriodDates,
    includeSalariedUsers = false
  ) {
    const findOptions: FindOptions = {
      where: {
        organizationId,
        isDeleted: false,
        clockIn: {
          [Op.between]: [periodDates.adjustedDates.start.toISOString(), periodDates.adjustedDates.end.toISOString()],
        },
      },
      include: timesheetsReportInclude(
        dayjs(periodDates.periodStartTimestamp).toISOString(),
        dayjs(periodDates.periodEndTimestamp).toISOString(),
        {
          onlyIncludeUsersWithHourlyEarningRates: includeSalariedUsers === false,
          includeSalariedUsers: includeSalariedUsers,
        }
      ),
      order: timesheetsReportOrder(),
    };

    return this.timesheetsService.findAll(findOptions);
  }

  public mergePaymentMethodOverrides(existingPayrollData: Payroll) {
    // Merge existing payment method overrides with new ones
    const existingManualPaymentMethodOverride1 = existingPayrollData?.metadata?.manualDepositOverrides1
      ? JSON.parse(existingPayrollData.metadata.manualDepositOverrides1)
      : [];
    const existingManualPaymentMethodOverride2 = existingPayrollData?.metadata?.manualDepositOverrides2
      ? JSON.parse(existingPayrollData.metadata.manualDepositOverrides2)
      : [];
    const existingManualPaymentMethodOverride3 = existingPayrollData?.metadata?.manualDepositOverrides3
      ? JSON.parse(existingPayrollData.metadata.manualDepositOverrides3)
      : [];
    const existingManualPaymentMethodOverride4 = existingPayrollData?.metadata?.manualDepositOverrides4
      ? JSON.parse(existingPayrollData.metadata.manualDepositOverrides4)
      : [];
    const existingManualPaymentMethodOverride5 = existingPayrollData?.metadata?.manualDepositOverrides5
      ? JSON.parse(existingPayrollData.metadata.manualDepositOverrides5)
      : [];

    const existingPaymentMethodOverride: Record<string, string> = {};

    [
      ...existingManualPaymentMethodOverride1,
      ...existingManualPaymentMethodOverride2,
      ...existingManualPaymentMethodOverride3,
      ...existingManualPaymentMethodOverride4,
      ...existingManualPaymentMethodOverride5,
    ]?.forEach((id: string) => {
      existingPaymentMethodOverride[id] = "manual";
    });

    return existingPaymentMethodOverride;
  }

  private async fetchTimeOffRequestsForPayroll(organizationId: number, periodDates: PayrollPeriodDates) {
    const requests = await this.timeOffRequestService.findAll({
      where: {
        status: TimeOffRequestStatus.APPROVED,
        startDate: {
          [Op.lte]: dayjs(periodDates.periodEndTimestamp).endOf("day").valueOf(),
        },
        endDate: {
          [Op.gte]: dayjs(periodDates.periodStartTimestamp).startOf("day").valueOf(),
        },
      },
      include: [
        {
          model: User,
          as: "user",
          where: {
            organizationId: organizationId,
          },
          include: [
            {
              model: EarningRate,
              as: "earningRates",
              where: {
                // remove active: true; technically, the active earning rate might not be the one that's active at the time of the time off request
                startDate: {
                  [Op.lte]: col("TimeOffRequest.start_date"),
                },
                endDate: {
                  [Op.or]: [{ [Op.gte]: col("TimeOffRequest.start_date") }, { [Op.is]: null }],
                },
                type: EARNING_RATE_TYPES.REG, // time off reqs only for reg earning rates
                period: EARNING_RATE_PERIODS.HOURLY, // time off reqs only for hourly earning rates
              },
              required: true,
            },
          ],
          required: true,
        },
        {
          model: TimeOffPolicy,
          as: "timeOffPolicy",
          where: {
            type: {
              [Op.ne]: TimeOffPolicyType.UNPAID,
            },
          },
          required: true,
        },
      ],
    });

    return requests;
  }

  private getCheckEmployeeIds({
    timesheets,
    salariedEmployees,
    earnings,
    reimbursements,
    timeOffRequests,
  }: {
    timesheets?: ExtendedTimesheet[];
    salariedEmployees?: ExtendedUserPartial[];
    earnings?: PayloadEarning[];
    reimbursements?: PayloadReimbursement[];
    timeOffRequests?: TimeOffRequest[];
  }): string[] {
    const checkEmployeeIds = new Set<string>();

    if (timesheets) {
      timesheets.forEach((ts) => {
        if (ts.user.checkEmployeeId) checkEmployeeIds.add(ts.user.checkEmployeeId);
      });
    }

    if (salariedEmployees) {
      salariedEmployees.forEach((employee) => {
        if (employee.checkEmployeeId) checkEmployeeIds.add(employee.checkEmployeeId);
      });
    }

    if (earnings) {
      earnings.forEach((earning) => {
        if (earning.resourceId && getResourceType(earning.resourceId) === "employee") {
          checkEmployeeIds.add(earning.resourceId);
        }
      });
    }

    if (reimbursements) {
      reimbursements.forEach((reimbursement) => {
        if (reimbursement.resourceId && getResourceType(reimbursement.resourceId) === "employee") {
          checkEmployeeIds.add(reimbursement.resourceId);
        }
      });
    }

    if (timeOffRequests) {
      timeOffRequests.forEach((request) => {
        if (request.user?.checkEmployeeId) checkEmployeeIds.add(request.user?.checkEmployeeId);
      });
    }

    return Array.from(checkEmployeeIds);
  }

  private getCheckContractorIds({
    timesheets,
    reimbursements,
    additionalContractorPayments,
  }: {
    timesheets: ExtendedTimesheet[];
    reimbursements: PayloadReimbursement[];
    additionalContractorPayments?: PayloadEarning[];
  }) {
    const checkContractorIds = new Set<string>();

    if (timesheets) {
      timesheets.forEach((ts) => {
        if (ts.user.checkContractorId) checkContractorIds.add(ts.user.checkContractorId);
      });
    }

    if (additionalContractorPayments) {
      additionalContractorPayments.forEach((payment) => {
        checkContractorIds.add(payment.resourceId);
      });
    }

    if (reimbursements) {
      reimbursements.forEach((reimbursement) => {
        if (reimbursement.resourceId && getResourceType(reimbursement.resourceId) === "contractor") {
          checkContractorIds.add(reimbursement.resourceId);
        }
      });
    }

    return Array.from(checkContractorIds);
  }

  private async enrichTimesheets(timesheets: ExtendedTimesheet[], organization: Organization) {
    const fringeBenefitClassifications = await this.fringeBenefitClassificationsService.findAll({
      where: { organizationId: organization.id },
      include: [
        { model: Classification, required: true },
        { model: FringeBenefit, required: true },
      ],
    });

    const timesheetsWithFringeBenefitClassifications =
      this.timesheetsService.enrichTimesheetsWithFringeBenefitClassifications(timesheets, fringeBenefitClassifications);

    return this.timesheetsService.enrichTimesheets(
      timesheetsWithFringeBenefitClassifications,
      organization.paySchedule.payFrequency
    );
  }

  private async fetchUsersPerDiemReimbursements({
    organizationId,
    salariedEmployeesToSkip,
  }: {
    organizationId: number;
    salariedEmployeesToSkip: string[];
  }) {
    const reimbursements = (await EmployeeReimbursement.findAll({
      where: { organizationId },
      include: [
        {
          model: User,
          as: "user",
          required: true,
          attributes: ["checkEmployeeId", "checkContractorId"],
        },
      ],
    })) as ExtendedEmployeeReimbursement[];

    const formattedReimbursements = reimbursements.map((reimbursement) => {
      const updatedReimbursement = {
        id: reimbursement.id,
        reimbursementAmount: reimbursement.reimbursementAmount,
        resourceId: reimbursement.user.checkEmployeeId ?? reimbursement.user.checkContractorId,
        code: PER_DIEM_REIMBURSEMENT_CODE,
      };

      return updatedReimbursement;
    });

    const filteredReimbursements = formattedReimbursements.filter((reimbursement) => {
      return !(salariedEmployeesToSkip || []).includes(reimbursement.resourceId);
    });

    return filteredReimbursements;
  }

  private async fetchSalariedEmployeeSummaries(
    periodDates: PayrollPeriodDates,
    organization: Organization,
    salariedEmployeesToSkip: string[]
  ) {
    const salariedEmployees = await User.findAll({
      where: { organizationId: organization.id, isArchived: false },
      include: salariedInclude(periodDates.periodStartTimestamp, periodDates.periodEndTimestamp),
      order: [["earningRates", "startDate", "DESC"]],
    });

    const paySchedule = organization.paySchedule;

    const salariedEmployeeSummaries = summarizeSalariedEmployeeEarnings(salariedEmployees, paySchedule.payFrequency);

    if (salariedEmployeesToSkip) {
      return salariedEmployeeSummaries.map((summary) => {
        if (salariedEmployeesToSkip.includes(summary.checkEmployeeId)) {
          return {
            ...summary,
            skip: true,
          };
        }

        return summary;
      });
    }

    return salariedEmployeeSummaries;
  }

  /**
   * This function processes timesheets for a payroll. It generates flattened attributed timesheets, filters eligible timesheets, and summarizes timesheet data.
   * @param enrichedTimesheets - The timesheets to process.
   * @param periodDates - The period dates for the payroll.
   * @param organization - The organization for which the payroll is being processed.
   * @returns The processed timesheet data.
   */
  private processTimesheets(
    enrichedTimesheets: ExtendedTimesheet[],
    periodDates: PayrollPeriodDates,
    organization: Organization
  ): ExtendedTimesheet[] {
    // we flatten the timesheets into weekly grouped timesheets to apply attribution to them
    // at this stage it might include timesheets from beyond the specified dates in order to attribute "correctly"
    const flattendAttributedTimesheets = this.timesheetsService.generateFlattenedAttributedTimesheets(
      enrichedTimesheets,
      periodDates.adjustedDates.start.valueOf(),
      organization
    );

    // after attribution, we filter out timesheets that are not eligible for the payroll, but were used for attribution
    const eligibleAttributedTimesheets = filterEligibleTimesheets(
      flattendAttributedTimesheets,
      periodDates.periodStartTimestamp,
      periodDates.periodEndTimestamp
    );

    return convertDatesToTimestamps(eligibleAttributedTimesheets);
  }

  generateBulkEmployeeQueryString({
    timesheets,
    salariedEmployees,
    earnings = [],
    reimbursements = [],
    timeOffRequests = [],
  }: {
    timesheets: ExtendedTimesheet[];
    salariedEmployees: ExtendedUserPartial[];
    earnings?: PayloadEarning[];
    reimbursements?: PayloadReimbursement[];
    timeOffRequests?: TimeOffRequest[];
  }) {
    const checkEmployeeIds = this.getCheckEmployeeIds({
      timesheets,
      salariedEmployees,
      earnings,
      reimbursements,
      timeOffRequests,
    });

    const listOfEmployeeIds = prefixCollectionValues(checkEmployeeIds, "id=");

    return listOfEmployeeIds.join("&");
  }

  generateBulkContractorQueryString({
    timesheets,
    reimbursements,
    additionalContractorPayments,
  }: {
    timesheets: ExtendedTimesheet[];
    reimbursements: PayloadReimbursement[];
    additionalContractorPayments?: PayloadEarning[];
  }) {
    const checkContractorIds = this.getCheckContractorIds({
      timesheets,
      reimbursements,
      additionalContractorPayments,
    });

    const listOfContractorIds = prefixCollectionValues(checkContractorIds, "id=");

    return listOfContractorIds.join("&");
  }

  checkForUnapprovedTimesheets(
    timesheets: ExtendedTimesheet[],
    periodStartTimestamp: number,
    periodEndTimestamp: number
  ): boolean {
    return timesheets.some((timesheet) => {
      return (
        timesheet.status === "SUBMITTED" &&
        timesheet.clockIn.valueOf() >= periodStartTimestamp &&
        timesheet.clockIn.valueOf() <= periodEndTimestamp
      );
    });
  }

  /**
   * This function processes payroll data. It fetches timesheets, time off requests, and checks for unapproved timesheets. It then summarizes timesheet data and categorizes it. This is the main method for processing payroll and is used in every payroll controller method
   * @param organization - The organization object for which the payroll is being processed.
   * @param type - The type of payroll. (regular, off_cycle)
   * @param periodDates - The period dates for the payroll.
   * @param payroll - (optional) The existing payroll to include in processing if it exists.
   * @param salariedEmployeesToSkip - (optional) The list of salaried employees to skip.
   * @returns The processed payroll data.
   */
  async processPayrollData({
    organization,
    type,
    periodDates,
    payroll,
    salariedEmployeesToSkip,
  }: Partial<PayrollProcessingData>): Promise<PayrollProcessingData> {
    // if type is regular, we want to include salaried users in the timesheets fetch as offcycle don't have salaried users for the type of payroll
    const timesheets = await this.fetchTimesheetsForPayroll(organization.id, periodDates, type === "regular");

    const timeOffRequests = await this.fetchTimeOffRequestsForPayroll(organization.id, periodDates);
    const hasUnapprovedTimesheets = this.checkForUnapprovedTimesheets(
      timesheets,
      periodDates.periodStartTimestamp,
      periodDates.periodEndTimestamp
    );

    const enrichedTimesheets = await this.enrichTimesheets(timesheets, organization);
    const attributedTimesheets = this.processTimesheets(enrichedTimesheets, periodDates, organization);
    const eligibleTimesheets = attributedTimesheets.filter((timesheet) => timesheet.status === "APPROVED");
    const userEarningsSummaries = summarizeUserTimesheetData(eligibleTimesheets);
    const perDiemReimbursements = await this.fetchUsersPerDiemReimbursements({
      organizationId: organization.id,
      salariedEmployeesToSkip,
    });

    let salariedEmployeeSummaries: UserEarningsSummary[] = [];

    if (type && type === "regular") {
      salariedEmployeeSummaries = await this.fetchSalariedEmployeeSummaries(
        periodDates,
        organization,
        salariedEmployeesToSkip
      );
    }

    const mappedExistingPayrollDataAsPayload = payroll ? mapExistingPayrollToPayloadData(payroll) : null;

    const { hourlyEmployeeSummaries, contractorSummaries } = categorizeUserEarningsSummaries(userEarningsSummaries);

    const bulkEmployeeQueryString = this.generateBulkEmployeeQueryString({
      timesheets: eligibleTimesheets,
      salariedEmployees: salariedEmployeeSummaries,
      earnings: mappedExistingPayrollDataAsPayload?.earnings || [],
      reimbursements: mappedExistingPayrollDataAsPayload?.reimbursements || [],
    });

    const bulkContractorQueryString = this.generateBulkContractorQueryString({
      timesheets: eligibleTimesheets,
      reimbursements: mappedExistingPayrollDataAsPayload?.reimbursements || [],
    });

    const allEmployeesData = await this.checkService.getAllEmployees(organization, bulkEmployeeQueryString);

    const allContractorsData = await this.checkService.getAllContractors(organization, bulkContractorQueryString);

    // get users with missing check ids
    // get employees and contractors not finished onboarding
    const usersMissingCheckIds = this.usersMissingCheckIds(userEarningsSummaries);
    const employeesNotFinishedOnboarding = this.employeesNotFinishedOnboarding(
      [...hourlyEmployeeSummaries, ...salariedEmployeeSummaries],
      allEmployeesData
    );
    const contractorsNotFinishedOnboarding = this.contractorsNotFinishedOnboarding(
      contractorSummaries,
      allContractorsData
    );

    const listOfUsersToRemoveSummariesFrom = this.listOfUsersToRemoveSummariesFrom(
      usersMissingCheckIds,
      employeesNotFinishedOnboarding,
      contractorsNotFinishedOnboarding
    );

    // filter all the summaries that are in the listOfUsersToRemoveSummariesFrom
    const filteredHourlyEmployeeSummaries = hourlyEmployeeSummaries.filter((summary) => {
      return !listOfUsersToRemoveSummariesFrom.some((user) => user.id === summary.id);
    });

    const filteredSalariedEmployeeSummaries = salariedEmployeeSummaries.filter((summary) => {
      return !listOfUsersToRemoveSummariesFrom.some((user) => user.id === summary.id);
    });

    const filteredContractorSummaries = contractorSummaries.filter((summary) => {
      return !listOfUsersToRemoveSummariesFrom.some((user) => user.id === summary.id);
    });

    // when an employee is converted or has an active annual salary, they should not have timesheets that transform into summaries for hourly pay - these should be filtered out
    const finalFilteredHourlyEmployeeSummaries = filteredHourlyEmployeeSummaries.filter((summary) => {
      return !filteredSalariedEmployeeSummaries.some((salariedSummary) => salariedSummary.id === summary.id);
    });

    // timesheets also should be filtered out if they were part of the remove from summaries list
    const filterOutUsersToRemoveFromTimesheets = eligibleTimesheets.filter((timesheet) => {
      return !listOfUsersToRemoveSummariesFrom.some((user) => user.id === timesheet.workerId);
    });

    // we want to remove ineligible time off requests - salaried employees and contractors
    const eligibleTimeOffRequests = filterIneligibleTimeOffRequests(timeOffRequests, listOfUsersToRemoveSummariesFrom);

    return {
      payroll: payroll || null,
      organization,
      periodDates,
      timesheets: filterOutUsersToRemoveFromTimesheets,
      timeOffRequests: eligibleTimeOffRequests,
      userEarningsSummaries: [
        ...finalFilteredHourlyEmployeeSummaries,
        ...filteredContractorSummaries,
        ...filteredSalariedEmployeeSummaries,
      ],
      salariedEmployeeSummaries: filteredSalariedEmployeeSummaries,
      contractorSummaries: filteredContractorSummaries,
      hasUnapprovedTimesheets,
      hasMissingPayrollUsers: listOfUsersToRemoveSummariesFrom.length > 0,
      usersMissingCheckIds,
      employeesNotFinishedOnboarding,
      contractorsNotFinishedOnboarding,
      perDiemReimbursements,
    };
  }

  createSummaryObject(user: ExtendedUserPartial) {
    const hourlyEarningRate = user?.earningRates.find((rate) => rate.type === "REG");
    const otHourlyEarningRate = user?.earningRates.find((rate) => rate.type === "OT");
    const dotHourlyEarningRate = user?.earningRates.find((rate) => rate.type === "DOT");

    const userSummaryObject: ExtendedUserEarningsSummary = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      compensationType: hourlyEarningRate?.period || "HOURLY", // default to hourly
      checkRegEarningRateId: hourlyEarningRate?.checkEarningRateId,
      checkOtEarningRateId: otHourlyEarningRate?.checkEarningRateId,
      checkDotEarningRateId: dotHourlyEarningRate?.checkEarningRateId,
      checkEmployeeId: user?.checkEmployeeId,
      checkContractorId: user?.checkContractorId,
      regularMinutes: 0,
      totalMinutes: 0,
      overtimeMinutes: 0,
      doubleOvertimeMinutes: 0,
      breakMinutes: 0,
      totalWages: 0,
      checkDynamicFringeBenefit: null, // we're not pulling this so null
      timeWorkedByDay: [],
      nonPWTimesheetsSummary: [],
      pwTimesheetsSummary: [],
      checkBenefitOverrides: [],
      reimbursements: [],
      otherEarnings: [],
      paymentMethod: null,
      salary: hourlyEarningRate ? parseFloat(hourlyEarningRate.amount) : 0,
      hourlyWage: hourlyEarningRate ? parseFloat(hourlyEarningRate.amount) : 0,
      otHourlyWage: otHourlyEarningRate ? parseFloat(otHourlyEarningRate.amount) : 0,
      dotHourlyWage: dotHourlyEarningRate ? parseFloat(dotHourlyEarningRate.amount) : 0,
    };

    return userSummaryObject;
  }

  createMissingHourlyEmployeeSummaries(
    userEarningsSummaries: UserEarningsSummary[],
    hammrUserData: ExtendedUserPartial[]
  ) {
    // determine which employees don't have summaries
    const hourlyEmployeesWithoutSummaries = hammrUserData.filter((user) => {
      return (
        !userEarningsSummaries.find((summary) => summary.id === user.id) &&
        !user.earningRates.some((rate) => rate.period === "ANNUALLY")
      );
    });

    // take the employees with summaires and create summary objects for them
    const hourlyEmployeeSummaryObjects = hourlyEmployeesWithoutSummaries.map((user) => this.createSummaryObject(user));

    return hourlyEmployeeSummaryObjects;
  }

  createMissingContractorSummaries(contractorSummaries: UserEarningsSummary[], hammrUserData: ExtendedUserPartial[]) {
    // determine which contractors don't have summaries
    const contractorsWithoutSummaries = hammrUserData.filter((user) => {
      return user.checkContractorId !== null && !contractorSummaries.find((summary) => summary.id === user.id);
    });

    // take the contractors with summaires and create summary objects for them
    const contractorSummaryObjects = contractorsWithoutSummaries.map((user) => this.createSummaryObject(user));

    return contractorSummaryObjects;
  }

  createMissingSalariedEmployeeSummaries(
    salariedEmployeeSummaries: UserEarningsSummary[],
    hammrUserData: ExtendedUserPartial[]
  ) {
    // determine which salaried employees don't have summaries
    const salariedEmployeesWithoutSummaries = hammrUserData.filter((user) => {
      return (
        !salariedEmployeeSummaries.find((summary) => summary.id === user.id) &&
        !user.earningRates.some((rate) => rate.period === "HOURLY")
      );
    });

    // take the salaried employees with summaires and create summary objects for them
    const salariedEmployeeSummaryObjects = salariedEmployeesWithoutSummaries.map((user) =>
      this.createSummaryObject(user)
    );

    return salariedEmployeeSummaryObjects;
  }

  stitchPayrollData(payrollData: PayrollProcessingData, hammrUserData?: ExtendedUserPartial[]) {
    const salariedEmployeeSummaries = payrollData.salariedEmployeeSummaries;

    const { hourlyEmployeeSummaries, contractorSummaries } = categorizeUserEarningsSummaries(
      payrollData.userEarningsSummaries
    );

    // if hammrUserData is provided, we need to create summary objects for any employees that don't have them yet
    if (hammrUserData && hammrUserData.length > 0) {
      const missingHourlyEmployeeSummaries = this.createMissingHourlyEmployeeSummaries(
        payrollData.userEarningsSummaries,
        hammrUserData
      );

      const missingContractorSummaries = this.createMissingContractorSummaries(contractorSummaries, hammrUserData);

      const missingSalariedEmployeeSummaries = this.createMissingSalariedEmployeeSummaries(
        payrollData.salariedEmployeeSummaries,
        hammrUserData
      );

      hourlyEmployeeSummaries.push(...missingHourlyEmployeeSummaries);
      contractorSummaries.push(...missingContractorSummaries);
      salariedEmployeeSummaries.push(...missingSalariedEmployeeSummaries);
    }

    const extendedHourlyEmployeeSummaries = this.stitchReimbursementsToUserEarningsSummaries(
      hourlyEmployeeSummaries,
      payrollData.payroll,
      "employee"
    );

    const extendedContractorSummaries = this.stitchReimbursementsToUserEarningsSummaries(
      contractorSummaries,
      payrollData.payroll,
      "contractor"
    );

    const extendedSalariedEmployeeSummaries = this.stitchReimbursementsToUserEarningsSummaries(
      salariedEmployeeSummaries,
      payrollData.payroll,
      "employee"
    );

    // we want to stitch other earnings to user earning summaries now
    const extendedHourlyEmployeeSummariesWithOtherEarnings = this.stitchOtherEarningsToUserEarningsSummaries(
      extendedHourlyEmployeeSummaries,
      payrollData.payroll
    );

    // its possible salaried employees can have 2% shareholder earnings
    const extendedSalariedEmployeeSummariesWithOtherEarnings = this.stitchOtherEarningsToUserEarningsSummaries(
      extendedSalariedEmployeeSummaries,
      payrollData.payroll
    );

    // after stitching, we want to filter out any summaries where nonPWTimesheetsSummary is empty, pwTimesheetsSummary is empty, and otherEarnings is empty
    const filteredHourlyEmployeeSummaries = extendedHourlyEmployeeSummariesWithOtherEarnings.filter(
      (summary) =>
        summary.nonPWTimesheetsSummary.length > 0 ||
        summary.pwTimesheetsSummary.length > 0 ||
        summary.otherEarnings.length > 0 ||
        summary.reimbursements.length > 0
    );

    const filteredContractorSummaries = extendedContractorSummaries.filter(
      (summary) =>
        summary.nonPWTimesheetsSummary.length > 0 ||
        summary.pwTimesheetsSummary.length > 0 ||
        summary.otherEarnings.length > 0 ||
        summary.reimbursements.length > 0
    );

    const filteredSalariedEmployeeSummaries = extendedSalariedEmployeeSummariesWithOtherEarnings.filter(
      (summary) => summary.otherEarnings.length > 0 || summary.reimbursements.length > 0 || summary.totalWages > 0
    );

    return {
      payrollId: payrollData.payroll.id,
      periodStart: payrollData.payroll.period_start,
      periodEnd: payrollData.payroll.period_end,
      approvalDeadline: payrollData.payroll.approval_deadline,
      attributedTimesheets: payrollData.timesheets,
      hourlyEmployeeSummaries: filteredHourlyEmployeeSummaries,
      contractorSummaries: filteredContractorSummaries,
      salariedSummaries: filteredSalariedEmployeeSummaries, // salaried summaries should now filter out any summaries where totalWages is 0 or no other reimbursements
      payroll: payrollData.payroll,
    };
  }

  usersMissingCheckIds = (aggregateUserEarningsSummaries: UserEarningsSummary[]): UserToRemoveSummaryFrom[] => {
    // this should catch users that use timesheets but not in payroll - hourly employees and contractors
    const missingCheckKids = aggregateUserEarningsSummaries.filter((summary) => {
      if (
        (summary.checkEmployeeId === "" || summary.checkEmployeeId === null) &&
        (summary.checkContractorId === "" || summary.checkContractorId === null)
      ) {
        return true;
      }
    });

    return missingCheckKids.map((summary) => {
      return {
        id: summary.id,
        firstName: summary.firstName,
        lastName: summary.lastName,
      };
    });
  };

  employeesNotFinishedOnboarding = (
    employeeEarningSummaries: UserEarningsSummary[],
    checkEmployeeData: CheckEmployee[]
  ): UserToRemoveSummaryFrom[] => {
    const employeeIdsNotFinishedOnboarding = employeeEarningSummaries.filter((summary) => {
      const checkEmployeeObject = checkEmployeeData.find((employee) => {
        return employee.id === summary.checkEmployeeId;
      });

      if (checkEmployeeObject && checkEmployeeObject.onboard.status === "blocking") {
        return true;
      }
    });

    return employeeIdsNotFinishedOnboarding.map((summary) => {
      return {
        id: summary.id,
        checkEmployeeId: summary.checkEmployeeId,
        firstName: summary.firstName,
        lastName: summary.lastName,
      };
    });
  };

  contractorsNotFinishedOnboarding = (
    contractorEarningSummaries: UserEarningsSummary[],
    checkContractorData: CheckContractor[]
  ): UserToRemoveSummaryFrom[] => {
    const contractorIdsNotFinishedOnboarding = contractorEarningSummaries.filter((summary) => {
      const checkContractorObject = checkContractorData.find((contractor) => {
        return contractor.id === summary.checkContractorId;
      });

      if (checkContractorObject && checkContractorObject.onboard.status === "blocking") {
        return true;
      }
    });

    return contractorIdsNotFinishedOnboarding.map((summary) => {
      return {
        id: summary.id,
        checkContractorId: summary.checkContractorId,
        firstName: summary.firstName,
        lastName: summary.lastName,
      };
    });
  };

  listOfUsersToRemoveSummariesFrom = (
    usersMissingCheckIds: UserToRemoveSummaryFrom[],
    employeesNotFinishedOnboarding: UserToRemoveSummaryFrom[],
    contractorsNotFinishedOnboarding: UserToRemoveSummaryFrom[]
  ) => {
    return [...usersMissingCheckIds, ...employeesNotFinishedOnboarding, ...contractorsNotFinishedOnboarding];
  };

  generateHourlyEmployeeAllEarningObjects = (
    nonPWTimesheetsSummary: WageSummary[] = [],
    pwTimesheetsSummary: WageSummary[] = [],
    timeOffRequests: TimeOffRequest[] = [], // should be filtered by user.checkEmployeeId
    employee: CheckEmployee
  ) => {
    let earningResults: any[] = [];

    if (nonPWTimesheetsSummary.length > 0) {
      // basically create two objects for each nonPWTimesheetSummary one for Regular and one for Overtime
      const nonPWRows = nonPWTimesheetsSummary.flatMap((wageSummary) => {
        const tuple = [];

        if (wageSummary.regularWages > 0) {
          tuple.push({
            type: "hourly",
            workplace: employee.primary_workplace,
            earning_rate: wageSummary?.checkRegEarningRateId || null,
            amount: +wageSummary.regularWages.toFixed(2),
            hours: +(wageSummary.regularMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.overtimeWages > 0) {
          tuple.push({
            type: "overtime",
            workplace: employee.primary_workplace,
            earning_rate: wageSummary?.checkOtEarningRateId || null,
            amount: +wageSummary.overtimeWages.toFixed(2),
            hours: +(wageSummary.overtimeMinutes / 60).toFixed(2),
          });
        }

        if (
          wageSummary.doubleOvertimeWages > 0 &&
          wageSummary?.checkDotEarningRateId &&
          wageSummary?.checkDotEarningRateId !== "N/A"
        ) {
          tuple.push({
            type: "double_overtime",
            workplace: employee.primary_workplace,
            earning_rate: wageSummary?.checkDotEarningRateId || null,
            amount: +wageSummary?.doubleOvertimeWages.toFixed(2),
            hours: +(wageSummary?.doubleOvertimeMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.driveTimeWages > 0) {
          tuple.push({
            type: "hourly",
            workplace: employee.primary_workplace,
            amount: +wageSummary.driveTimeWages.toFixed(2),
            hours: +(wageSummary.driveTimeMinutes / 60).toFixed(2),
          });
        }

        return tuple;
      });

      earningResults = nonPWRows;
    }

    if (pwTimesheetsSummary.length > 0) {
      // basically create two objects for each pwTimesheetSummary one for Regular and one for Overtime
      const pwRows = pwTimesheetsSummary.flatMap((wageSummary) => {
        const tuple = [];

        if (wageSummary.regularWages > 0) {
          tuple.push({
            workplace: employee.primary_workplace,
            earning_code: wageSummary.checkRegEarningCodeId,
            amount: +wageSummary.regularWages.toFixed(2),
            hours: +(wageSummary.regularMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.overtimeWages > 0) {
          tuple.push({
            workplace: employee.primary_workplace,
            earning_code: wageSummary.checkOtEarningCodeId,
            amount: +wageSummary.overtimeWages.toFixed(2),
            hours: +(wageSummary.overtimeMinutes / 60).toFixed(2),
          });
        }

        if (
          wageSummary.doubleOvertimeWages > 0 &&
          wageSummary?.checkDotEarningCodeId &&
          wageSummary?.checkDotEarningCodeId !== "N/A"
        ) {
          tuple.push({
            workplace: employee.primary_workplace,
            earning_code: wageSummary?.checkDotEarningCodeId,
            amount: +wageSummary?.doubleOvertimeWages.toFixed(2),
            hours: +(wageSummary?.doubleOvertimeMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.driveTimeWages > 0) {
          tuple.push({
            type: "hourly",
            workplace: employee.primary_workplace,
            amount: +wageSummary.driveTimeWages.toFixed(2),
            hours: +(wageSummary.driveTimeMinutes / 60).toFixed(2),
          });
        }

        return tuple;
      });

      earningResults = earningResults.concat(pwRows);
    }

    if (timeOffRequests.length > 0) {
      const timeOffReqRows = timeOffRequests.map((timeOffRequest) => {
        const timeOffEarning: Partial<PayrollItemEarning> = {
          type: timeOffRequest.timeOffPolicy.type === "SICK" ? "sick" : "pto",
          workplace: employee.primary_workplace,
          amount: `${(
            parseFloat(timeOffRequest.user?.earningRates?.find((rate) => rate.period === "HOURLY")?.amount || "0") *
              timeOffRequest.totalHours || 0
          ).toFixed(2)}`,
          hours: timeOffRequest.totalHours,
          metadata: {
            timeOffPolicyId: `${timeOffRequest.timeOffPolicy.id}`,
            timeOffRequestId: `${timeOffRequest.id}`,
          },
        };

        if (timeOffRequest.timeOffPolicy.type !== "SICK" && timeOffRequest.timeOffPolicy.type !== "PTO") {
          timeOffEarning.description = `PTO - ${startCase(toLower(timeOffRequest?.timeOffPolicy?.type || ""))}`;
        }

        return timeOffEarning;
      });

      earningResults = earningResults.concat(timeOffReqRows);
    }

    return earningResults;
  };

  generateSalariedEmployeeAllEarningObjects = (
    checkEmployee: CheckEmployee,
    salariedEmployeeSummary: UserEarningsSummary
  ) => {
    const earnings: any[] = [];

    earnings.push({
      earning_rate: salariedEmployeeSummary?.checkRegEarningRateId,
      type: "salaried",
      hours: +(salariedEmployeeSummary?.totalMinutes / 60).toFixed(2),
      workplace: checkEmployee.primary_workplace,
    });

    return earnings;
  };

  generateEmployeeEarningObjects = ({
    checkEmployeeData,
    nonPWTimesheetsSummary,
    pwTimesheetsSummary,
    salariedEmployeeSummary,
    payloadEarnings,
    hourlyUserWithEarnings,
    timeOffRequests,
  }: GenerateEmployeeEarningObjectsParams) => {
    let earningsResult: any[] = [];

    if (nonPWTimesheetsSummary.length > 0) {
      const nonPWRows = nonPWTimesheetsSummary.flatMap((wageSummary) => {
        const tuple = [];

        if (wageSummary.regularWages > 0) {
          tuple.push({
            type: "hourly",
            workplace: checkEmployeeData.primary_workplace,
            earning_rate: wageSummary?.checkRegEarningRateId || null,
            amount: +wageSummary.regularWages.toFixed(2),
            hours: +(wageSummary.regularMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.overtimeWages > 0) {
          tuple.push({
            type: "overtime",
            workplace: checkEmployeeData.primary_workplace,
            earning_rate: wageSummary?.checkOtEarningRateId || null,
            amount: +wageSummary.overtimeWages.toFixed(2),
            hours: +(wageSummary.overtimeMinutes / 60).toFixed(2),
          });
        }

        if (
          wageSummary.doubleOvertimeWages > 0 &&
          wageSummary?.checkDotEarningRateId &&
          wageSummary?.checkDotEarningRateId !== "N/A"
        ) {
          tuple.push({
            type: "double_overtime",
            workplace: checkEmployeeData.primary_workplace,
            earning_rate: wageSummary?.checkDotEarningRateId || null,
            amount: +wageSummary?.doubleOvertimeWages.toFixed(2),
            hours: +(wageSummary?.doubleOvertimeMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.driveTimeWages > 0) {
          tuple.push({
            type: "hourly",
            workplace: checkEmployeeData.primary_workplace,
            amount: +wageSummary.driveTimeWages.toFixed(2),
            hours: +(wageSummary.driveTimeMinutes / 60).toFixed(2),
          });
        }

        return tuple;
      });

      earningsResult = nonPWRows;
    }

    if (pwTimesheetsSummary.length > 0) {
      // basically create two objects for each pwTimesheetSummary one for Regular and one for Overtime
      const pwRows = pwTimesheetsSummary.flatMap((wageSummary) => {
        const tuple = [];

        if (wageSummary.regularWages > 0) {
          tuple.push({
            workplace: checkEmployeeData.primary_workplace,
            earning_code: wageSummary.checkRegEarningCodeId,
            amount: +wageSummary.regularWages.toFixed(2),
            hours: +(wageSummary.regularMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.overtimeWages > 0) {
          tuple.push({
            workplace: checkEmployeeData.primary_workplace,
            earning_code: wageSummary.checkOtEarningCodeId,
            amount: +wageSummary.overtimeWages.toFixed(2),
            hours: +(wageSummary.overtimeMinutes / 60).toFixed(2),
          });
        }

        if (
          wageSummary.doubleOvertimeWages > 0 &&
          wageSummary?.checkDotEarningCodeId &&
          wageSummary?.checkDotEarningCodeId !== "N/A"
        ) {
          tuple.push({
            workplace: checkEmployeeData.primary_workplace,
            earning_code: wageSummary.checkDotEarningCodeId,
            amount: +wageSummary?.doubleOvertimeWages.toFixed(2),
            hours: +(wageSummary?.doubleOvertimeMinutes / 60).toFixed(2),
          });
        }

        if (wageSummary.driveTimeWages > 0) {
          tuple.push({
            type: "hourly",
            workplace: checkEmployeeData.primary_workplace,
            amount: +wageSummary.driveTimeWages.toFixed(2),
            hours: +(wageSummary.driveTimeMinutes / 60).toFixed(2),
          });
        }

        return tuple;
      });

      earningsResult = earningsResult.concat(pwRows);
    }

    if (salariedEmployeeSummary && !salariedEmployeeSummary.skip) {
      earningsResult.push({
        type: "salaried",
        workplace: checkEmployeeData.primary_workplace,
        earning_rate: salariedEmployeeSummary?.checkRegEarningRateId,
        hours: +(salariedEmployeeSummary?.totalMinutes / 60).toFixed(2),
      });
    }

    // handle any time off requests officially
    if (timeOffRequests.length > 0) {
      const timeOffReqRows = timeOffRequests.map((timeOffRequest) => {
        const amount =
          parseFloat(timeOffRequest.user?.earningRates?.find((rate) => rate.period === "HOURLY")?.amount || "0") *
            timeOffRequest.totalHours || 0;

        const timeOffEarning: Partial<PayrollItemEarning> = {
          type: timeOffRequest.timeOffPolicy.type === "SICK" ? "sick" : "pto",
          workplace: checkEmployeeData.primary_workplace,
          amount: amount.toFixed(2),
          hours: timeOffRequest.totalHours,
          metadata: {
            timeOffPolicyId: `${timeOffRequest.timeOffPolicy.id}`,
            timeoffRequestId: `${timeOffRequest.id}`,
          },
        };

        if (timeOffRequest.timeOffPolicy.type !== "SICK" && timeOffRequest.timeOffPolicy.type !== "PTO") {
          timeOffEarning.description = `PTO - ${startCase(toLower(timeOffRequest?.timeOffPolicy?.type || ""))}`;
        }

        return timeOffEarning;
      });

      earningsResult = earningsResult.concat(timeOffReqRows);
    }

    // first filter for only the payloadEarnings that match the checkEmployeeId
    // payloadEarnings are non-hourly/non-salaried earnings such as PTO, sick, etc - these are AD HOC - meaning they were added by admin in the edit payroll screen
    const filteredPayloadEarnings = payloadEarnings.filter((earning) => {
      // we do this extra check to allow custom "salaried" earnings
      const isExcludedPayrollItem =
        EXCLUDED_PAYLOAD_EARNING_TYPES.includes(earning.type) && !earning.metadata?.is_custom_salaried;

      return (
        earning.resourceId === checkEmployeeData.id &&
        !isExcludedPayrollItem &&
        !earning.earning_code &&
        !earning.earning_rate &&
        !earning?.metadata?.timeOffPolicyId
      );
    });

    const filteredPayloadEarningsPayrollItems = filteredPayloadEarnings.map((earning) => {
      const item: Partial<PayrollItemEarning> = {
        type: earning.type as PayrollEarningType,
        workplace: checkEmployeeData.primary_workplace,
        earning_code: earning.earning_code,
        metadata: earning.metadata,
      };

      if (earning.hours && earning.hours > 0) {
        // look for reg hourly earning rate and if not found, look for salaried from userWithEarnings
        // multiply hours by primary earning rate to get amount
        // usersWithEarningsInPayroll is only used here to calculate the "amount".
        // we passed down the userWithEarnings object to get the users' earningRates so that we can calculate the amount for the non-hourly/non-salaried earnings
        const applicableHourlyEarningRate = getHourlyEarningRate(hourlyUserWithEarnings.earningRates);

        const isCustomSalaried = earning.type === "salaried" && earning.metadata?.is_custom_salaried;

        /**
         * isCustomSalaried condition should have order priority as with a "salary" earning we have to send Check both amount and hours
         * The user enters the amount and then the client calculates the hours based on regular hourly wage
         **/

        if (isCustomSalaried) {
          item.hours = earning.hours;
          // the amount will be set again in the next condition block but keeping it here to make it
          // explicit that amount is also set
          item.amount = earning.amount;
        } else if (applicableHourlyEarningRate) {
          item.amount = (earning.hours * parseFloat(applicableHourlyEarningRate.amount)).toFixed(2);
          item.hours = earning.hours;
        }
      }

      if (earning.amount) {
        item.amount = earning.amount;
      }

      if (earning.description) {
        item.description = earning.description;
      }

      return item;
    });

    earningsResult = earningsResult.concat(filteredPayloadEarningsPayrollItems);

    return earningsResult;
  };

  /**
   * Update the Reimbursement payload for the latest Per Diem Reimbursements
   *
   * - It keeps existing reimbursement objects that aren't Per Diem.
   * - Then it gets all the per diem reimbursements for the employee.
   * - If there is a per diem reimbursement for an employee in the Payroll and it's above 0 it adds it to the reimbursements array.
   *
   * This only applies to Employees and not contractors as they are handled differently
   */
  generateEmployeeReimbursementObjects = ({
    checkEmployeeData,
    listOfReimbursements,
    perDiemReimbursements,
  }: GenerateEmployeeReimbursementObjectsParams) => {
    // First, get any existing reimbursements from the payload that aren't per diem
    const existingNonPerDiemReimbursements = listOfReimbursements.filter(
      (reimbursement) =>
        reimbursement.code !== PER_DIEM_REIMBURSEMENT_CODE && reimbursement.resourceId === checkEmployeeData.id
    );

    // Then get all the per diem reimbursements for the employee
    const perDiemReimbursement = perDiemReimbursements?.find(
      (reimbursement) => reimbursement.resourceId === checkEmployeeData.id
    );

    let reimbursements = [...existingNonPerDiemReimbursements];

    // Only add per diem reimbursement if it exists and has a non-zero amount
    if (perDiemReimbursement && parseFloat(perDiemReimbursement.reimbursementAmount) > 0) {
      reimbursements = [
        ...reimbursements,
        {
          resourceId: checkEmployeeData.id,
          amount: perDiemReimbursement.reimbursementAmount,
          description: "Per Diem Reimbursement",
          code: PER_DIEM_REIMBURSEMENT_CODE,
        },
      ];
    }

    return reimbursements;
  };

  generatePayrollItems = ({
    checkEmployeeData,
    hourlyEmployeeSummaries,
    salariedEmployeeSummaries,
    payloadEarnings,
    payloadReimbursements,
    payrollId,
    periodStartTimestamp,
    periodEndTimestamp,
    timezone,
    hourlyUsersWithEarnings,
    timeOffRequests,
    paymentMethodOverride,
    perDiemReimbursements,
  }: GeneratePayrollItemsParams) => {
    return checkEmployeeData.map((employee: CheckEmployee) => {
      const selectedHourlyEmpSummaryData = hourlyEmployeeSummaries.find((summary) => {
        return employee.id === summary.checkEmployeeId;
      });

      const selectedSalaryEmpSummaryData = salariedEmployeeSummaries.find((summary) => {
        return employee.id === summary.checkEmployeeId;
      });

      const foundHourlyUserWithEarnings = hourlyUsersWithEarnings.find((user) => user.checkEmployeeId === employee.id);
      const filteredTimeOffRequests = timeOffRequests.filter((timeOffRequest) => {
        return timeOffRequest.user.checkEmployeeId === employee.id;
      });

      return {
        payroll: payrollId,
        employee: employee.id,
        earnings: this.generateEmployeeEarningObjects({
          checkEmployeeData: employee,
          nonPWTimesheetsSummary: selectedHourlyEmpSummaryData?.nonPWTimesheetsSummary || [],
          pwTimesheetsSummary: selectedHourlyEmpSummaryData?.pwTimesheetsSummary || [],
          salariedEmployeeSummary: selectedSalaryEmpSummaryData as UserEarningsSummary,
          payloadEarnings,
          periodStartTimestamp,
          periodEndTimestamp,
          timezone,
          timeOffRequests: filteredTimeOffRequests,
          hourlyUserWithEarnings: foundHourlyUserWithEarnings,
        }),
        reimbursements: this.generateEmployeeReimbursementObjects({
          checkEmployeeData: employee,
          listOfReimbursements: payloadReimbursements,
          perDiemReimbursements,
        }),
        benefit_overrides: selectedHourlyEmpSummaryData?.checkBenefitOverrides || [],
        ...(paymentMethodOverride?.[employee.id] && { payment_method: paymentMethodOverride?.[employee.id] }),
      };
    });
  };

  generateContractorPaymentItems = (contractorSummaries: UserEarningsSummary[]) => {
    return contractorSummaries.map((summary) => {
      return {
        contractor: summary.checkContractorId,
        amount: summary.totalWages.toFixed(2),
        reimbursement_amount: summary?.reimbursements?.[0]?.totalWages || "0.00",
      };
    });
  };

  generateContractorPaymentItemsExtended = ({
    checkContractorData,
    contractorSummaries,
    payloadReimbursements,
    payrollId,
    paymentMethodOverride,
    perDiemReimbursements,
    additionalContractorPayments,
  }: GenerateContractorPaymentsParams) => {
    return checkContractorData
      .map((contractor: CheckContractor) => {
        const selectedContractorSummary = contractorSummaries.find((summary) => {
          return contractor.id === summary.checkContractorId;
        });

        const selectedContractorReimbursements = payloadReimbursements.filter((reimbursement) => {
          return reimbursement.resourceId === contractor.id;
        });

        const selectedContractorAdditionalPayment = additionalContractorPayments?.find((payment) => {
          return payment.resourceId === contractor.id;
        });

        /**
         * We don't allow to have timesheet data and contractor additional payments - so the priority should be:
         * 1. Timesheets data
         * 2. Contractor additional payments
         */
        const hasAdditionalPayment = selectedContractorAdditionalPayment?.amount
          ? parseFloat(selectedContractorAdditionalPayment.amount) > 0
          : false;

        const hasTimesheetsWages = selectedContractorSummary && selectedContractorSummary.totalWages > 0;

        const shouldUseAdditionalPayment = !hasTimesheetsWages && hasAdditionalPayment;

        const amount =
          selectedContractorSummary?.totalWages > 0
            ? selectedContractorSummary?.totalWages.toFixed(2)
            : shouldUseAdditionalPayment
            ? selectedContractorAdditionalPayment.amount
            : "0.00";

        /**
         * Per Diem Reimbursements are handled differently for contractors
         *
         * As in the Contractor Payroll Item, the reimbursement_amount property is a number
         * and not an object with like it is for employee reimbursements and custom earnings.
         *
         * In order to always calculate the correct amount we need to calculate on each payroll the
         * reimbursement amount, because we don't have metadata we can't just filter out for a specific per_diem
         * indicator, so we save the per_diem in the Contractor Payroll Item metadata and
         * then subtract it from the reimbursement_amount, this is done before in the payroll cycle
         * by the function mapExistingPayrollToPayloadData
         *
         * Here below it's added again with the latest values and updates the metadata
         *
         */
        const selectedContractorPerDiemReimbursement = perDiemReimbursements?.find((reimbursement) => {
          return reimbursement.resourceId === contractor.id;
        });

        const reimbursement_amount = selectedContractorReimbursements.reduce((acc, curr) => {
          return acc + parseFloat(curr.amount || "0");
        }, 0);

        const reimbursementAmountPlusPerDiem =
          reimbursement_amount +
          (selectedContractorPerDiemReimbursement?.reimbursementAmount
            ? parseFloat(selectedContractorPerDiemReimbursement.reimbursementAmount)
            : 0);

        const metadata: ContractorMetadata = {
          [PER_DIEM_REIMBURSEMENT_CODE]: selectedContractorPerDiemReimbursement?.reimbursementAmount,
        };

        if (shouldUseAdditionalPayment) {
          metadata[CONTRACTOR_PAYMENT_CODE] = selectedContractorAdditionalPayment.amount;
        }

        if (amount !== "0.00" || reimbursement_amount > 0 || reimbursementAmountPlusPerDiem > 0) {
          return {
            payroll: payrollId,
            contractor: contractor.id,
            amount,
            reimbursement_amount: reimbursementAmountPlusPerDiem,
            metadata,
            ...(paymentMethodOverride?.[contractor.id] && { payment_method: paymentMethodOverride?.[contractor.id] }),
          };
        }
      })
      .filter(Boolean);
  };

  stitchReimbursementsToUserEarningsSummaries = (
    userEarningsSummaries: UserEarningsSummary[],
    checkPayrollData: Payroll,
    type: "employee" | "contractor"
  ): ExtendedUserEarningsSummary[] => {
    const extendedUserEarningsSummaries: ExtendedUserEarningsSummary[] = userEarningsSummaries.map(
      (userEarningsSummary) => {
        const extendedUserEarningsSummary: ExtendedUserEarningsSummary = {
          ...userEarningsSummary,
          paymentMethod: null,
          reimbursements: [], // equivalent shape to WageSummary
        };

        const isEmployeeDataAvailable = checkPayrollData?.items?.length > 0;
        const isContractorDataAvailable = checkPayrollData?.contractor_payments?.length > 0;

        if (isEmployeeDataAvailable || isContractorDataAvailable) {
          const propertyToUse = type === "employee" ? "items" : "contractor_payments";
          const selectedEmpPayrollData = (
            checkPayrollData[propertyToUse] as Array<PayrollItem | ContractorPayment>
          ).find((item: PayrollItem | ContractorPayment) => {
            if (type === "employee") {
              return (item as PayrollItem)["employee"] === userEarningsSummary.checkEmployeeId;
            } else {
              return (item as ContractorPayment).contractor === userEarningsSummary.checkContractorId;
            }
          });

          if (type === "employee" && selectedEmpPayrollData && "reimbursements" in selectedEmpPayrollData) {
            const payrollItem = selectedEmpPayrollData as PayrollItem;
            if (payrollItem.reimbursements.length > 0) {
              extendedUserEarningsSummary.reimbursements = [
                {
                  isPrevailingWage: false,
                  classificationName: "N/A",
                  checkRegEarningCodeId: "N/A",
                  checkOtEarningCodeId: "N/A",
                  checkDotEarningCodeId: "N/A",
                  hourlyWage: null,
                  otHourlyWage: null,
                  dotHourlyWage: null,
                  regularMinutes: null,
                  overtimeMinutes: null,
                  doubleOvertimeMinutes: null,
                  breakMinutes: null,
                  driveTimeMinutes: null,
                  regularWages: null,
                  overtimeWages: null,
                  doubleOvertimeWages: null,
                  driveTimeWages: null,
                  totalWages: payrollItem.reimbursements.reduce(
                    (acc: number, curr: { amount: string }) => acc + parseFloat(curr.amount),
                    0
                  ),
                },
              ];
            }
          }

          if (type === "contractor" && selectedEmpPayrollData && "reimbursement_amount" in selectedEmpPayrollData) {
            const contractorPayment = selectedEmpPayrollData as ContractorPayment;
            extendedUserEarningsSummary.reimbursements = [
              {
                isPrevailingWage: false,
                classificationName: "N/A",
                checkRegEarningCodeId: "N/A",
                checkOtEarningCodeId: "N/A",
                checkDotEarningCodeId: "N/A",
                hourlyWage: null,
                otHourlyWage: null,
                dotHourlyWage: null,
                regularMinutes: null,
                overtimeMinutes: null,
                doubleOvertimeMinutes: null,
                breakMinutes: null,
                driveTimeMinutes: null,
                regularWages: null,
                overtimeWages: null,
                doubleOvertimeWages: null,
                driveTimeWages: null,
                totalWages: parseFloat(contractorPayment.reimbursement_amount),
                metadata: contractorPayment.metadata,
              },
            ];
          }
        }

        return extendedUserEarningsSummary;
      }
    );

    return extendedUserEarningsSummaries;
  };

  stitchOtherEarningsToUserEarningsSummaries = (
    userEarningsSummaries: UserEarningsSummary[],
    checkPayrollData: Payroll
  ) => {
    const extendedUserEarningsSummaries: ExtendedUserEarningsSummary[] = userEarningsSummaries.map(
      (userEarningsSummary) => {
        const extendedUserEarningsSummary: ExtendedUserEarningsSummary = {
          ...userEarningsSummary,
          paymentMethod: null,
          otherEarnings: [],
        };

        if (checkPayrollData && checkPayrollData.items && checkPayrollData.items.length > 0) {
          const selectedEmpPayrollData = checkPayrollData.items.find((item: PayrollItem) => {
            return item.employee === userEarningsSummary.checkEmployeeId;
          });

          if (selectedEmpPayrollData && selectedEmpPayrollData?.earnings.length > 0) {
            // loop through earnings and if earning type is not in EXCLUDED_PAYLOAD_EARNING_TYPES, add to otherEarnings
            selectedEmpPayrollData.earnings.forEach((earning: PayrollItemEarning) => {
              const isExcludedPayrollItem =
                EXCLUDED_PAYLOAD_EARNING_TYPES.includes(earning.type) && !earning.metadata?.is_custom_salaried;

              if (
                !isExcludedPayrollItem &&
                earning.amount !== "0.00" &&
                !earning.earning_code &&
                !earning.earning_rate
              ) {
                extendedUserEarningsSummary.otherEarnings.push({
                  isPrevailingWage: false,
                  classificationName: "N/A",
                  checkRegEarningCodeId: "N/A",
                  checkOtEarningCodeId: "N/A",
                  checkDotEarningCodeId: "N/A",
                  hourlyWage: null,
                  otHourlyWage: null,
                  dotHourlyWage: null,
                  regularMinutes: null,
                  overtimeMinutes: null,
                  doubleOvertimeMinutes: null,
                  breakMinutes: null,
                  driveTimeMinutes: null,
                  regularWages: null,
                  overtimeWages: null,
                  doubleOvertimeWages: null,
                  driveTimeWages: null,
                  totalWages: parseFloat(earning.amount),
                  metadata: {
                    type: earning.type,
                    ...earning.metadata,
                  },
                });
              }
            });
          }
        }

        return extendedUserEarningsSummary;
      }
    );

    return extendedUserEarningsSummaries;
  };

  async getPayroll(payrollId: string): Promise<Payroll> {
    const payroll = await this.checkService.get(`/payrolls/${payrollId}`);

    return payroll;
  }

  async handleCreatePayroll({
    payloadData,
    organization,
    checkEmployeeData = [],
    hourlyEmployeeSummaries = [],
    contractorSummaries = [],
    salariedEmployeeSummaries = [],
    timeOffRequests = [],
  }: HandleCreatePayrollParams): Promise<Payroll> {
    const payrollPayload = this.checkService.handleCreateCheckPayload(
      {
        ...payloadData,
        checkCompanyId: organization.checkCompanyId,
        payFrequency: organization?.paySchedule?.payFrequency,
      },
      "payrolls",
      "POST"
    );

    const generatedContractorPaymentItems = this.generateContractorPaymentItems(contractorSummaries);

    // like web, we can create the payroll with contractor payments first as we don't have to attach or generate payroll items then attach
    const checkCreatedPayroll = await this.checkService.post("/payrolls", {
      ...payrollPayload,
      contractor_payments: generatedContractorPaymentItems,
      // add metadata - updatedAt field
      metadata: {
        updatedAt: `${dayjs.utc().valueOf()}`,
      },
    });

    if (checkEmployeeData.length > 0) {
      const payrollItems = checkEmployeeData.reduce((items, employee: CheckEmployee) => {
        const selectedHourlyEmpSummaryData = hourlyEmployeeSummaries.find(
          (summary) => employee.id === summary.checkEmployeeId
        );

        const filteredTimeOffRequests = timeOffRequests.filter((timeOffRequest) => {
          return timeOffRequest.user.checkEmployeeId === employee.id;
        });

        if (selectedHourlyEmpSummaryData || filteredTimeOffRequests.length > 0) {
          items.push({
            payroll: checkCreatedPayroll.id,
            employee:
              selectedHourlyEmpSummaryData?.checkEmployeeId || filteredTimeOffRequests?.[0]?.user?.checkEmployeeId,
            earnings: this.generateHourlyEmployeeAllEarningObjects(
              selectedHourlyEmpSummaryData?.nonPWTimesheetsSummary,
              selectedHourlyEmpSummaryData?.pwTimesheetsSummary,
              filteredTimeOffRequests,
              employee
            ),
            benefit_overrides: selectedHourlyEmpSummaryData?.checkBenefitOverrides || [],
          });

          return items;
        }

        const selectedSalaryEmpSummaryData = salariedEmployeeSummaries.find(
          (summary) => employee.id === summary.checkEmployeeId
        );

        if (selectedSalaryEmpSummaryData) {
          items.push({
            payroll: checkCreatedPayroll.id,
            employee: selectedSalaryEmpSummaryData.checkEmployeeId,
            earnings: this.generateSalariedEmployeeAllEarningObjects(
              employee,
              selectedSalaryEmpSummaryData as UserEarningsSummary
            ),
            benefit_overrides: selectedSalaryEmpSummaryData.checkBenefitOverrides,
          });
        }

        return items;
      }, []);

      await this.checkService.post("/payroll_items", payrollItems);
    }

    const checkPayroll: Payroll = await this.checkService.get(`/payrolls/${checkCreatedPayroll.id}`);

    return checkPayroll;
  }

  async handleUpdatePayroll({
    payrollId,
    payloadData,
    organization,
    checkEmployeeData = [],
    checkContractorData = [],
    hourlyEmployeeSummaries = [],
    contractorSummaries = [],
    salariedEmployeeSummaries = [],
    timeOffRequests = [],
    periodStartTimestamp,
    periodEndTimestamp,
    salariedEmployeesToSkip = null,
    paymentMethodOverride = null,
    existingPayrollData,
    perDiemReimbursements = [],
  }: HandleUpdatePayrollParams) {
    // the idea is we're going to generate all the payroll items again (this time including the reimbursements)
    // then hopefully updating payroll will overwrite the old payroll items with the new ones
    // grab the reimbursements from the payloadData
    const payloadReimbursements = payloadData.reimbursements || [];
    const payloadEarnings = payloadData?.earnings || [];

    const additionalContractorPayments = payloadEarnings.filter(
      (earning: PayloadEarning) => earning.type === CONTRACTOR_PAYMENT_CODE
    );

    const uniqueCheckEmployeeIds = new Set([
      ...hourlyEmployeeSummaries.map((summary) => summary.checkEmployeeId),
      ...salariedEmployeeSummaries.map((summary) => summary.checkEmployeeId),
      ...(payloadData?.earnings
        ?.filter((earning: PayloadEarning) => getResourceType(earning.resourceId) === "employee")
        .map((earning: PayloadEarning) => earning.resourceId) || []),
      ...(payloadData?.reimbursements
        ?.filter((reimbursement: PayloadReimbursement) => getResourceType(reimbursement.resourceId) === "employee")
        .map((reimbursement: PayloadReimbursement) => reimbursement.resourceId) || []),
    ]);

    // Fetch all users with their active earning rates that were active at the time of the time off request.
    // Note: We already have checkEmployeeData which contains Check users with earnings in this request.
    // However, we need to account for users who didn't originally have earnings in the payroll,
    // but may have had earnings added later (e.g. if an admin added PTO). This query gets all users
    // who have any type of earning in this request period, so their rates can be used for calculations
    // downstream when processing things like PTO or sick time.
    const usersWithEarningsInPayroll: ExtendedUserPartial[] = await User.findAll({
      where: {
        organizationId: organization.id,
        isArchived: false,
        checkEmployeeId: {
          [Op.in]: Array.from(uniqueCheckEmployeeIds),
        },
      },
      include: [
        {
          model: EarningRate,
          as: "earningRates",
          where: {
            // remove active: true; technically, the active earning rate might not be the one that's active at the time of the time off request
            type: "REG",
            startDate: { [Op.lte]: periodStartTimestamp },
            endDate: { [Op.or]: [{ [Op.gte]: periodStartTimestamp }, { [Op.is]: null }] },
          },
          required: true,
        },
      ],
    });

    if (checkEmployeeData.length > 0) {
      // since payroll is a function of the various summaries. if a user wasn't originally part of the payroll (such as no timesheet), then we need to create a summary for them such as in the instance of sick time or PTO
      const missingHourlyEmployeeSummaries = this.createMissingHourlyEmployeeSummaries(
        hourlyEmployeeSummaries,
        usersWithEarningsInPayroll
      );

      hourlyEmployeeSummaries.push(...missingHourlyEmployeeSummaries);

      const existingPaymentMethodOverride = this.mergePaymentMethodOverrides(existingPayrollData);

      const updatedPaymentMethodOverride = paymentMethodOverride
        ? {
            ...existingPaymentMethodOverride,
            ...paymentMethodOverride,
          }
        : existingPaymentMethodOverride;

      // we want to generate the payroll items for the hourly employees and the salaried employees
      const payrollItems = this.generatePayrollItems({
        checkEmployeeData,
        hourlyEmployeeSummaries,
        salariedEmployeeSummaries,
        payloadEarnings,
        payloadReimbursements,
        payrollId,
        periodStartTimestamp,
        periodEndTimestamp,
        timezone: organization.timezone,
        hourlyUsersWithEarnings: usersWithEarningsInPayroll,
        timeOffRequests,
        paymentMethodOverride: updatedPaymentMethodOverride,
        perDiemReimbursements: existingPayrollData.type === "off_cycle" ? [] : perDiemReimbursements,
      });

      const filteredPayrollItems = payrollItems.filter(
        (item) => (item.earnings && item.earnings.length > 0) || (item.reimbursements && item.reimbursements.length > 0)
      );

      const payrollPayload = this.checkService.handleCreateCheckPayload(
        { ...payloadData, payrollItems: filteredPayrollItems, checkCompanyId: organization.checkCompanyId },
        "payrolls",
        "PATCH"
      );

      // handle contractor payments here - regenerate the contractor payment object
      const generatedContractorPaymentItems = this.generateContractorPaymentItemsExtended({
        checkContractorData,
        contractorSummaries,
        payloadReimbursements,
        payrollId,
        paymentMethodOverride: updatedPaymentMethodOverride,
        perDiemReimbursements: existingPayrollData.type === "off_cycle" ? [] : perDiemReimbursements,
        additionalContractorPayments,
      });

      const updatedSalariedEmployeesToSkip = salariedEmployeesToSkip
        ? JSON.stringify(salariedEmployeesToSkip)
        : existingPayrollData?.metadata?.salariedEmployeesToSkip;

      const manualDepositOverrides = updatedPaymentMethodOverride
        ? Object.keys(updatedPaymentMethodOverride).filter((key) => updatedPaymentMethodOverride[key] === "manual")
        : [];

      const manualDepositOverrides1 = manualDepositOverrides.slice(0, 16);
      const manualDepositOverrides2 = manualDepositOverrides.slice(16, 32);
      const manualDepositOverrides3 = manualDepositOverrides.slice(32);
      const manualDepositOverrides4 = manualDepositOverrides.slice(48);
      const manualDepositOverrides5 = manualDepositOverrides.slice(64);

      // metadata has a maximum value of 500 characters, so that's why we have to split up the overrides metadata
      // TODO -> split up salaried employees as it currently has a limit of 16 users
      const updatePayrollPayload = {
        ...payrollPayload,
        contractor_payments: generatedContractorPaymentItems,
        metadata: {
          updatedAt: `${dayjs.utc().valueOf()}`,
          salariedEmployeesToSkip: updatedSalariedEmployeesToSkip,
          manualDepositOverrides1: JSON.stringify(manualDepositOverrides1),
          manualDepositOverrides2: JSON.stringify(manualDepositOverrides2),
          manualDepositOverrides3: JSON.stringify(manualDepositOverrides3),
          manualDepositOverrides4: JSON.stringify(manualDepositOverrides4),
          manualDepositOverrides5: JSON.stringify(manualDepositOverrides5),
        },
      };

      const updatedPayroll = await this.checkService.patch(`/payrolls/${payrollId}`, updatePayrollPayload);

      return updatedPayroll;
    } else {
      throw {
        type: "ValidationError",
        message: "No check employee data found",
      };
    }
  }
}

// the metadata fields have a maximum of 500 characters length but can take up to 50 keys, so we need to split up
