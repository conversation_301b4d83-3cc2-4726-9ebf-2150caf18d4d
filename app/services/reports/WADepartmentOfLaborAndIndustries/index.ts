import { CertifiedPayrollData } from "@/services/reports";
import { Report } from "@/services/reports/Report";
import { Organization } from "@/models";
import * as XMLBuilder from "xmlbuilder2";
import dayjs from "dayjs";
import { calculateCoreBenefitHourlyContribution, calculatePensionHourlyContribution } from "@/util/payrollHelpers";
import { CheckBenefit } from "@/types/check";

/**
 * WA specific report
 */
export class WADepartmentOfLaborAndIndustries extends Report {
  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transformForReport(this.transform(organization, payrollData));
    const xmlContent = this.generateXML(data);

    const buffer = Buffer.from(xmlContent, "utf-8");

    return this.getResponse(buffer, "xml", "application/xml");
  }

  generateXML(data: ReturnType<WADepartmentOfLaborAndIndustries["transformForReport"]>): string {
    const root = XMLBuilder.create({ version: "1.0", encoding: "UTF-8" })
      .ele("WaPWCPR")
      .ele("projectIntent")
      .ele("intentId")
      .txt("1234") // TODO we need an IntentID
      .up()
      .up()
      .ele("payroll");

    const payrollWeek = root.ele("payrollWeek");
    payrollWeek
      .ele("endOfWeekDate")
      .txt(dayjs(data.endWeekDate).format("YYYY-MM-DD"))
      .up()
      .ele("noWorkPerformFlag")
      .txt(data.noWorkPerformed ? "true" : "false")
      .up();

    if (!data.noWorkPerformed) {
      const employees = payrollWeek.ele("employees");

      data.employees.forEach((empData) => {
        const employee = employees.ele("employee");
        employee
          .ele("firstName")
          .txt(empData.employee.name)
          .up()
          .ele("midName")
          .txt(empData.employee.middleName || "-")
          .up()
          .ele("lastName")
          .txt(empData.employee.lastName)
          .up()
          .ele("ssn")
          .txt(empData.employee.lastFourSSN)
          .up()
          .ele("ethnicity")
          .txt(empData.employee.ethnicity || "") // TODO check validation for options
          .up()
          .ele("gender")
          .txt(empData.employee.gender || "") //.txt(empData.gender || "")
          .up()
          .ele("veteranStatus")
          .txt(empData?.employee?.veteranStatus || "") //.txt(empData.veteranStatus || "")
          .up()
          .ele("address1")
          .txt(empData?.employee?.residence?.line1 || "") //.txt(empData.address1)
          .up()
          .ele("address2")
          .txt(empData?.employee?.residence?.line2 || "") //.txt(empData.address2 || "")
          .up()
          .ele("city")
          .txt(empData?.employee?.residence?.city) //.txt(empData.city)
          .up()
          .ele("state")
          .txt(empData?.employee?.residence?.state) //.txt(empData.state)
          .up()
          .ele("zip")
          .txt(empData?.employee?.residence?.postal_code) //.txt(empData.zip)
          .up()
          .ele("grossPay")
          .txt(empData.grossEarned)
          .up()
          .ele("fica")
          .txt(empData.deductionCategories.fica || "0")
          .up()
          .ele("taxWitholding")
          .txt(
            (
              parseFloat(empData.deductionCategories.stateTax || "0") +
              parseFloat(empData.deductionCategories.fica || "0") +
              parseFloat(empData.deductionCategories.medicare || "0")
            ).toFixed(2)
          )
          .up();

        if (empData.deductionCategories && Object.keys(empData.deductionCategories).length > 0) {
          const otherDeductions = employee.ele("otherDeductions");
          // Manually set other deductions
          const deductionsToInclude = [
            { name: "SDI", amount: empData.deductionCategories.sdi },
            { name: "Other", amount: empData.deductionCategories.other },
          ];

          deductionsToInclude.forEach((deduction) => {
            if (parseFloat(deduction.amount) > 0) {
              otherDeductions
                .ele("otherDeduction")
                .ele("deductionName")
                .txt(deduction.name)
                .up()
                .ele("deductionHourlyAmt")
                .txt(deduction.amount)
                .up()
                .up();
            }
          });
        }

        const tradeHoursWages = employee.ele("tradeHoursWages");

        // Loop through each userClassification for the employee
        empData.userClassifications.forEach((ucData) => {
          const userEmployeeBenefits = ucData.attributedTimesheets[0]?.user?.employeeBenefits || [];

          const tradeHoursWage = tradeHoursWages.ele("tradeHoursWage");
          tradeHoursWage
            .ele("trade")
            .txt(ucData.trade || "") // Add trade from userClassification if available
            .up()
            .ele("jobClass")
            .txt(ucData.jobClassification)
            .up()
            .ele("tradeNotes")
            .txt(ucData.tradeNotes || "N/A")
            .up()
            .ele("county")
            .txt(ucData.county || "N/A")
            .up()
            .ele("regularHourRateAmt")
            .txt(ucData.regRate)
            .up()
            .ele("overtimeHourRateAmt")
            .txt(ucData.overtimeRate)
            .up()
            .ele("doubletimeHourRateAmt")
            .txt(ucData.dotRate)
            .up()
            .ele("hourlyPensionRateAmt")
            .txt(calculatePensionHourlyContribution(ucData.benefits, userEmployeeBenefits, "company") || "0")
            .up()
            .ele("hourlyMedicalAmt")
            .txt(calculateCoreBenefitHourlyContribution(ucData.benefits, userEmployeeBenefits, "company") || "0")
            .up()
            .ele("hourlyVacationAmt")
            .txt(ucData.benefits?.vacation?.hourlyRate || "0")
            .up()
            .ele("hourlyHolidayAmt")
            .txt(ucData.benefits?.holiday?.hourlyRate || "0")
            .up()
            .ele("apprenticeBenefitAmt")
            .txt(ucData.benefits?.apprentice?.hourlyRate || "0")
            .up()
            .ele("apprenticeFlg")
            .txt(ucData.isApprentice ? "true" : "false")
            .up();

          // Add apprentice information if applicable
          if (ucData.isApprentice) {
            tradeHoursWage
              .ele("apprenticeId")
              .txt(ucData.apprenticeId || "")
              .up()
              .ele("apprenticeState")
              .txt(ucData.apprenticeState || "")
              .up()
              .ele("apprenticeOccpnName")
              .txt(ucData.apprenticeOccupation || "")
              .up()
              .ele("apprenticeStepName")
              .txt(ucData.apprenticeStep || "")
              .up()
              .ele("apprenticeStepBeginHours")
              .txt(ucData.apprenticeStepBeginHours?.toString() || "0")
              .up()
              .ele("apprenticeStepEndHours")
              .txt(ucData.apprenticeStepEndHours?.toString() || "0")
              .up();
          }

          // Add daily hours
          for (let i = 0; i < 7; i++) {
            tradeHoursWage
              .ele(`regularDay${i + 1}Hours`)
              .txt(ucData.pwRegularHours[i] || "0")
              .up()
              .ele(`overtimeDay${i + 1}Hours`)
              .txt(ucData.pwOvertimeHours[i] || "0")
              .up()
              .ele(`doubletimeDay${i + 1}Hours`)
              .txt(ucData.pwDotHours[i] || "0")
              .up();
          }

          // Add trade benefits
          const tradeBenefits = tradeHoursWage.ele("tradeBenefits");

          // Loop through all benefits in ucData.benefits
          if (ucData?.benefits as CheckBenefit[]) {
            Object.entries(ucData.benefits).forEach(([_benefitName, benefit]: [string, CheckBenefit]) => {
              if (benefit?.company_contribution_amount && parseFloat(benefit.company_contribution_amount) > 0) {
                tradeBenefits
                  .ele("tradeBenefit")
                  .ele("benefitHourlyName")
                  .txt(benefit?.description)
                  .up()
                  .ele("benefitHourlyAmt")
                  .txt((parseFloat(benefit?.company_contribution_amount) / parseFloat(ucData.pwHoursWorked)).toFixed(2))
                  .up()
                  .up();
              }
            });
          }
        });
      });
    }

    return root.end({ prettyPrint: true });
  }

  transformForReport(data: ReturnType<Report["transform"]>) {
    // Group both userClassifications and timesheets by employee ID
    const employeeMap = data.userClassifications.reduce(
      (acc, uc) => {
        const employeeId = uc.employee.id;

        if (!acc[employeeId]) {
          acc[employeeId] = {
            employee: uc.employee,
            userClassifications: [],
            timesheets: data.timesheets.filter((ts) => ts.employee.id === employeeId),
            grossEarned: uc.grossEarned,
            deductionCategories: uc.deductionCategories,
          };
        }

        acc[employeeId].userClassifications.push(uc);

        return acc;
      },
      {} as Record<
        string,
        {
          employee: any;
          userClassifications: any[];
          timesheets: any[];
          grossEarned: string;
          deductionCategories: Record<string, string>;
        }
      >
    );

    return {
      ...data,
      employees: Object.values(employeeMap),
      noWorkPerformed: data?.timesheets?.length === 0,
    };
  }
}
