import { Response } from "express";
import { CertifiedPayrollData, Deduction } from "@/services/reports";
import { Organization } from "@/models";
import { getLastSevenDays, getValuesForLastSevenDays } from "@/util/dateHelper";
import dayjs from "dayjs";
import { convertMinutesToHours } from "@/util/timeHelper";
import { sumTimesheetWagesBy } from "@/util/financial";
import { CheckBenefit } from "@/types/check";
import { hammrCategoryToCheckBenefitType } from "@/util/hammrToCheckMappings";
import { sumBy } from "lodash";

export abstract class Report<TData = any> {
  constructor(protected readonly response: Response) {}

  abstract report(organization: Organization, data: TData, type?: "csv" | "pdf" | null): Promise<Response>;

  /**
   * TODO refactor this method signature to remove the default file name.
   */
  getResponse(data: ArrayBuffer, fileExtension: string, contentType: string, fileName = "certified-payroll-report") {
    this.response.setHeader("Content-Type", contentType);
    this.response.setHeader("Content-Disposition", `attachment; filename=${fileName}.${fileExtension}`);
    this.response.setHeader("Content-Length", data.byteLength);

    return this.response.end(data);
  }

  /**
   * Handles CSV responses with proper headers
   */
  getCsvResponse(data: string, fileName = "certified-payroll-report") {
    this.response.setHeader("Content-Type", "text/csv");
    this.response.setHeader("Content-Disposition", `attachment; filename=${fileName}.csv`);

    return this.response.send(data);
  }

  transform(organization: Organization, data: CertifiedPayrollData) {
    const returnData = {
      name: data.companyName,
      company: data.company,
      companyAddress: data.company.fullAddress,
      isSubcontractor: data.isSubcontractor,
      payrollNumber: data.payrollNumber,
      payday: data.payday,
      timezone: organization.timezone,
      isLastCertifiedPayrollReport: data.isLastCertifiedPayrollReport,
      projectLocation: data?.project?.address
        ? `${data?.project?.name} - ${data?.project?.address}`
        : data?.project?.name,
      endWeekDate: data.weekEnding,
      weekDates: getLastSevenDays(data.weekEnding, organization.timezone),
      projectNumber: data?.project?.projectNumber,
      payFrequency: data?.payFrequency?.toUpperCase(),
      reportDate: dayjs().format("MM-DD-YYYY"),
      projectName: data?.project?.name,
      signerName: data?.signatoryName,
      signerTitle: data?.signatoryTitle,
      project: data?.project,
      areFringeBenefitsPaidToPlans: data?.areFringeBenefitsPaidToPlans,
      areFringeBenefitsPaidInCash: data?.areFringeBenefitsPaidInCash,
      remarks: data?.remarks,
      timesheets: data.employeesWeekData.map((employeeWork) => {
        const regHoursWorked = getValuesForLastSevenDays(
          employeeWork.attributedTimesheets,
          "regularMinutes",
          convertMinutesToHours,
          data.weekEnding,
          organization.timezone
        );

        const otHoursWorked = getValuesForLastSevenDays(
          employeeWork.attributedTimesheets,
          "overtimeMinutes",
          convertMinutesToHours,
          data.weekEnding,
          organization.timezone
        );

        const dotHoursWorked = getValuesForLastSevenDays(
          employeeWork.attributedTimesheets,
          "doubleOvertimeMinutes",
          convertMinutesToHours,
          data.weekEnding,
          organization.timezone
        );

        const deductions = this.consolidateDeductions(
          employeeWork.taxes,
          employeeWork.benefits,
          employeeWork.postTaxDeductions
        );

        const deductionCategories = this.consolidateDeductionsCategories(deductions);

        return {
          employee: employeeWork.employee,
          gender: employeeWork?.employee?.gender,
          ethnicity: employeeWork?.employee?.ethnicity,
          jobClassification: employeeWork.jobClassification,
          jobClassificationId: employeeWork.classificationId,
          regularHours: regHoursWorked,
          overtimeHours: otHoursWorked,
          dotHours: dotHoursWorked,
          numberOfExceptions: 0, // not sure where this comes from
          totalRegularHours: `${regHoursWorked
            .reduce((acc: number, curr: string) => acc + parseFloat(curr), 0)
            .toFixed(2)}`,
          totalOvertimeHours: `${otHoursWorked
            .reduce((acc: number, curr: string) => acc + parseFloat(curr), 0)
            .toFixed(2)}`,
          totalDotHours: `${dotHoursWorked
            .reduce((acc: number, curr: string) => acc + parseFloat(curr), 0)
            .toFixed(2)}`,
          baseRate: `${parseFloat(employeeWork.baseRate).toFixed(2)}`, // round as this is used as display
          fringePay: `${parseFloat(employeeWork.fringeRate).toFixed(2)}`, // round as this is used as display
          cashFringes: `${parseFloat(employeeWork.cashFringe).toFixed(2)}`, // round as this is used as display,
          regRate: `${parseFloat(employeeWork.regRateOfPay).toFixed(2)}`, // round as this is used as display, // this includes basePay + cashFringe
          overtimeRate: `${parseFloat(employeeWork.otRateOfPay).toFixed(2)}`, // round as this is used as display
          dotRate: `${parseFloat(employeeWork.dotRateOfPay).toFixed(2)}`, // round as this is used as display
          grossEarned: sumTimesheetWagesBy(
            employeeWork.attributedTimesheets,
            "userClassificationId",
            employeeWork.userClassificationId,
            false
          ).toFixed(2), // round as this is used as display
          grossEarnedAllProjects: `${parseFloat(employeeWork.grossEarned).toFixed(2)}`, // comes from Check
          deductions,
          deductionCategories,
          netWagesPaid: `${parseFloat(employeeWork.netWages).toFixed(2)}`, // round as this is used as display
          paymentMethod: employeeWork.paymentMethod,
          paperCheckNumber: employeeWork.paperCheckNumber,
          taxes: employeeWork?.taxes || [],
          postTaxDeductions: employeeWork?.postTaxDeductions || [],
          postTaxDeductionsOverrides: employeeWork?.postTaxDeductionsOverrides || [],
          benefits: employeeWork?.benefits || [],
          benefitsOverrides: employeeWork?.benefitsOverrides || [],
          attributedTimesheets: employeeWork?.attributedTimesheets || [],
          fringeBenefitClassifications: employeeWork?.fringeBenefitClassifications || [],
        };
      }),
      userClassifications: data.employeesWeekData.map((employeeWork) => {
        const regHoursWorked = getValuesForLastSevenDays(
          employeeWork.attributedTimesheets,
          "regularMinutes",
          convertMinutesToHours,
          data.weekEnding,
          organization.timezone
        );

        const otHoursWorked = getValuesForLastSevenDays(
          employeeWork.attributedTimesheets,
          "overtimeMinutes",
          convertMinutesToHours,
          data.weekEnding,
          organization.timezone
        );

        const dotHoursWorked = getValuesForLastSevenDays(
          employeeWork.attributedTimesheets,
          "doubleOvertimeMinutes",
          convertMinutesToHours,
          data.weekEnding,
          organization.timezone
        );

        const deductions = this.consolidateDeductions(
          employeeWork.taxes,
          employeeWork.benefits,
          employeeWork.postTaxDeductions
        );

        const deductionCategories = this.consolidateDeductionsCategories(deductions);

        return {
          ...employeeWork,
          employeeBenefits: employeeWork.attributedTimesheets[0].user?.employeeBenefits,
          paymentMethod: employeeWork.paymentMethod,
          paperCheckNumber: employeeWork.paperCheckNumber,
          pwRegularHours: regHoursWorked,
          pwOvertimeHours: otHoursWorked,
          pwDotHours: dotHoursWorked,
          numberOfExceptions: 0,
          grossEarned: sumTimesheetWagesBy(
            employeeWork.attributedTimesheets,
            "userClassificationId",
            employeeWork.userClassificationId,
            false
          ).toFixed(2), // round as this is used as display
          grossEarnedAllProjects: `${parseFloat(employeeWork.grossEarned).toFixed(2)}`, // comes from Check
          baseRate: `${parseFloat(employeeWork.baseRate).toFixed(2)}`, // round as this is used as display
          fringePay: `${parseFloat(employeeWork.fringeRate).toFixed(2)}`, // round as this is used as display
          cashFringes: `${parseFloat(employeeWork.cashFringe).toFixed(2)}`, // round as this is used as display
          regRate: `${parseFloat(employeeWork.regRateOfPay).toFixed(2)}`, // round as this is used as display, // this includes basePay + cashFringe
          overtimeRate: `${parseFloat(employeeWork.otRateOfPay).toFixed(2)}`, // round as this is used as display
          dotRate: `${parseFloat(employeeWork.dotRateOfPay).toFixed(2)}`, // round as this is used as display
          pwHoursWorked: regHoursWorked.reduce((acc: number, curr: string) => acc + parseFloat(curr), 0).toFixed(2),
          pwOvertimeHoursWorked: otHoursWorked
            .reduce((acc: number, curr: string) => acc + parseFloat(curr), 0)
            .toFixed(2),
          pwDotHoursWorked: dotHoursWorked.reduce((acc: number, curr: string) => acc + parseFloat(curr), 0).toFixed(2),
          deductions,
          deductionCategories,
          netWagesPaid: `${parseFloat(employeeWork.netWages).toFixed(2)}`, // round as this is used as display
        };
      }),
    };

    return returnData;
  }

  customDeductionsSort(a: string, b: string) {
    const priorityOrder = ["FICA", "Federal Income Tax"];

    // Handle "Total Deductions" first
    if (a.toLowerCase() === "total deductions") return 1;
    if (b.toLowerCase() === "total deductions") return -1;

    if (a === b) return 0;

    // Check for priority deductions
    for (const priority of priorityOrder) {
      if (a === priority) return -1;
      if (b === priority) return 1;
    }

    // Check for state tax
    const aIsStateTax = a.toLowerCase().includes("state tax");
    const bIsStateTax = b.toLowerCase().includes("state tax");

    if (aIsStateTax && !bIsStateTax) return -1;
    if (!aIsStateTax && bIsStateTax) return 1;

    // For all other cases, sort alphabetically
    return a.localeCompare(b);
  }

  // expects 3 collections - taxes[], benefits[], postTaxDeductions[]
  consolidateDeductions(taxes: any[] = [], benefits: CheckBenefit[] = [], postTaxDeductions: any[] = []): Deduction[] {
    const taxesDeductions = taxes.reduce((acc: any[], taxObj: any) => {
      if (taxObj.payer === "employee") {
        acc.push({
          name: taxObj.description,
          amount: taxObj.amount,
        });
      }

      return acc;
    }, []);

    const benefitsDeductions = benefits.reduce((acc: any[], benefitObj) => {
      if (benefitObj.employee_contribution_amount && parseFloat(benefitObj.employee_contribution_amount) > 0) {
        acc.push({
          name: benefitObj.description,
          type: benefitObj.benefit,
          amount: benefitObj.employee_contribution_amount,
        });
      }

      return acc;
    }, []);

    const postTaxDeductionsDeductions = postTaxDeductions.map((postTaxObj: any) => {
      return {
        name: postTaxObj.description,
        amount: postTaxObj.amount,
      };
    });

    const deductions = [...taxesDeductions, ...benefitsDeductions, ...postTaxDeductionsDeductions];

    // add a total object at the end of the array
    const totalDeductionAmount = deductions.reduce((acc, curr) => acc + parseFloat(curr.amount), 0);
    deductions.push({
      name: "Total Deductions",
      amount: `${totalDeductionAmount.toFixed(2)}`, // round this value
    });

    return deductions;
  }

  private consolidateDeductionsCategories(deductions: Deduction[]) {
    const pensionBenefitTypes = ["401K", "ROTH_401K"].map(hammrCategoryToCheckBenefitType) as string[];

    const healthAndWelfare = removeItems(deductions, (item) =>
      item.type ? item.type === hammrCategoryToCheckBenefitType("MEDICAL") : false
    );
    const pension = removeItems(healthAndWelfare.otherItems, (item) => pensionBenefitTypes.includes(item.type));
    const fica = removeItems(pension.otherItems, (item) => item.name.includes("FICA"));
    const federalIncomeTax = removeItems(fica.otherItems, (item) => item.name.includes("Federal Income Tax"));
    const medicare = removeItems(federalIncomeTax.otherItems, (item) => item.name.includes("Medicare"));
    const stateTax = removeItems(medicare.otherItems, (item) => item.name.includes("State Tax"));
    const sdi = removeItems(stateTax.otherItems, (item) => item.name.includes("SDI"));
    const total = removeItems(sdi.otherItems, (item) => item.name.includes("Total Deductions"));
    const other = total.otherItems;

    return {
      healthAndWelfare: sumBy(healthAndWelfare.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      pension: sumBy(pension.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      fica: sumBy(fica.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      medicare: sumBy(medicare.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      stateTax: sumBy(stateTax.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      federalIncomeTax: sumBy(federalIncomeTax.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      sdi: sumBy(sdi.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      total: sumBy(total.foundItems, (item) => parseFloat(item.amount)).toFixed(2),
      other: sumBy(other, (item) => parseFloat(item.amount)).toFixed(2),
    };
  }

  /**
   * Use this to calculate overtime hours where DOT is not reported seperately
   * @param overtimeHours - array of overtime hours
   * @param doubleOvertimeHours - array of dot hours
   * @returns total overtime hours
   */
  calculateTotalOvertimeAndDoubleOvertimeHours(overtimeHours?: string[], doubleOvertimeHours?: string[]): string {
    // Handle case where both arrays are undefined/null
    if (!overtimeHours && !doubleOvertimeHours) {
      return "0.00";
    }

    // Calculate regular overtime, defaulting to 0 if array is undefined/null
    const totalOvertimeHours =
      overtimeHours?.reduce((sum: number, hours: string) => {
        const parsedHours = parseFloat(hours);

        return sum + (isNaN(parsedHours) ? 0 : parsedHours);
      }, 0) || 0;

    // Calculate double overtime, defaulting to 0 if array is undefined/null
    const totalDoubleOvertimeHours =
      doubleOvertimeHours?.reduce((sum: number, hours: string) => {
        const parsedHours = parseFloat(hours);

        return sum + (isNaN(parsedHours) ? 0 : parsedHours);
      }, 0) || 0;

    return (totalOvertimeHours + totalDoubleOvertimeHours).toFixed(2);
  }

  /**
   * Calculates the sum of all deductions that aren't in the main categories (FICA, State Tax, Federal Tax, Total)
   * @param deductions Array of deduction objects with name and amount properties
   * @returns The formatted sum of other deductions with 2 decimal places
   */
  sumOtherDeductions(deductions: Deduction[]): string {
    if (!deductions || deductions.length === 0) {
      return "0.00";
    }

    const mainCategories = ["FICA", "State Tax", "Federal Income Tax", "Total Deductions"];

    const otherDeductionsTotal = deductions
      .filter(
        (deduction) =>
          // Check if the deduction name is not in the main categories (case-insensitive)
          !mainCategories.some((category) => deduction.name.toLowerCase().includes(category.toLowerCase()))
      )
      .reduce((sum, deduction) => sum + parseFloat(deduction?.value || deduction?.amount || "0"), 0);

    return otherDeductionsTotal.toFixed(2);
  }
}

function removeItems<T>(arr: T[], callback: (item: T) => boolean): { foundItems: T[]; otherItems: T[] } {
  const foundItems: T[] = [];
  const otherItems = arr.filter((item) => {
    const shouldRemove = callback(item);
    if (shouldRemove) {
      foundItems.push(item);
    }

    return !shouldRemove;
  });

  return { foundItems, otherItems };
}
