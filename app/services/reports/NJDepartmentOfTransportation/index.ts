import { CertifiedPayrollData } from "@/services/reports";
import { join } from "node:path";
import { Report } from "@/services/reports/Report";
import PDFMerger from "pdf-merger-js";
import fs from "fs";
import { Organization } from "@/models";
import dayjs from "dayjs";
import { GENDER, ETHNICITY } from "@/models/user";
import { JsReportService, getJsReportService, releaseJsReportService } from "@/services/reports/JsReportService";
import { Response } from "express";

export default class NJDepartmentOfTransportation extends Report {
  private jsReportService: JsReportService;
  private jsReportOptions = { httpPort: 5001 };

  constructor(response: Response) {
    super(response);
    this.jsReportService = getJsReportService(this.jsReportOptions);

    // Register custom handlebars helpers
    this.jsReportService.getJsReport().beforeRenderListeners.add("custom-helpers", (req, _res) => {
      req.template.helpers = `
        function paginateTimesheets(timesheets) {
          const pages = [];
          let currentPage = [];

          timesheets.forEach((timesheet, index) => {
            currentPage.push(timesheet);

            if (
              (index === 5) || // first page break after 6 items
              (index > 5 && (index - 5) % 10 === 0) || // subsequent breaks every 10 items
              (index === timesheets.length - 1) // last item
            ) {
              pages.push(currentPage);
              currentPage = [];
            }
          });

          return pages;
        }

        function add(a, b) {
          return a + b;
        }
      `;
    });
  }

  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transformForPdfTemplate(this.transform(organization, payrollData));

    // we can also attach this to the node app and load it on app start
    const merger = new PDFMerger();

    const cr347Page1 = fs.readFileSync(join(__dirname, "cr-347-1.handlebars"), "utf8");
    const cr347Page2 = fs.readFileSync(join(__dirname, "cr-347-2.handlebars"), "utf8");

    const [cr347PdfPage1, cr347PdfPage2] = await Promise.all([
      this.jsReportService.render<typeof data>(
        {
          content: cr347Page1,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
      this.jsReportService.render<typeof data>(
        {
          content: cr347Page2,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: false,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
    ]);
    await merger.add(cr347PdfPage1.content);
    await merger.add(cr347PdfPage2.content);

    // Set metadata
    await merger.setMetadata({
      producer: "Hammr",
      title: "Certified payroll report",
    });

    try {
      return this.getResponse(await merger.saveAsBuffer(), "pdf", "application/pdf");
    } finally {
      // Release the service when done
      releaseJsReportService(this.jsReportOptions);
    }
  }

  transformForPdfTemplate(data: ReturnType<Report["transform"]>) {
    const { endWeekDate } = data;

    const [weekEndDay, weekEndMonth, weekEndYear] = dayjs(endWeekDate).format("DD-MM-YYYY").split("-");
    const [weekStartDay, weekStartMonth, weekStartYear] = dayjs(endWeekDate)
      .subtract(6, "days")
      .format("DD-MM-YYYY")
      .split("-");

    const deductionsSet = new Set();

    data.timesheets.forEach((timesheet: any) => {
      timesheet.deductions.forEach((deduction: { name: string; value: string }) => {
        deductionsSet.add(deduction.name);
      });
    });

    const formattedTimesheets = data.timesheets.map((timesheet: any) => {
      let name;

      if (timesheet.name) {
        name = timesheet.name;
      } else if (timesheet.employee) {
        name = timesheet.employee.firstName + " " + timesheet.employee.lastName;
      }

      if (timesheet.employee.lastFourSSN) {
        name = name + " (" + timesheet.employee.lastFourSSN + ")";
      }

      return {
        ...timesheet,
        numberOfExceptions: timesheet.numberOfExceptions > 0 ? String(timesheet.numberOfExceptions) : "",
        name,
        gender: this.mapGenderToLetter(timesheet.employee.gender),
        ethnicity: this.mapEthnicityToLetter(timesheet.employee.ethnicity),
        calculatedHourlyRate: (parseFloat(timesheet.baseRate) + parseFloat(timesheet.cashFringes)).toFixed(2),
        overtimeHours: (timesheet?.overtimeHours || []).map((hours: string, index: number) =>
          (parseFloat(hours) + (timesheet?.dotHours?.[index] ? parseFloat(timesheet.dotHours[index]) : 0)).toFixed(2)
        ),
        totalOvertimeHours: this.calculateTotalOvertimeAndDoubleOvertimeHours(
          timesheet.overtimeHours,
          timesheet.dotHours
        ),
        deductions: Array.from(deductionsSet)
          .sort(this.customDeductionsSort)
          .map((deductionName) => {
            const deduction = timesheet.deductions.find(
              (deduction: { name: string; value: string }) => deduction.name === deductionName
            );

            return { name: deductionName, value: deduction ? deduction.amount : "0" };
          }),
      };
    });

    // calculate total pages based on the pagination logic:
    // first page: 6 rows
    // subsequent pages: 10 rows each
    const finalTimesheets = [...formattedTimesheets];
    const timesheetsLength = finalTimesheets.length;
    const totalPages = timesheetsLength <= 6 ? 1 : Math.ceil((timesheetsLength - 6) / 10) + 1;

    return {
      weekEndDay,
      weekEndMonth,
      weekEndYear,
      weekStartDay,
      weekStartMonth,
      weekStartYear,
      ...data,
      timesheets: finalTimesheets,
      weekDaysNames: data.weekDates.map((date) => date.format("dd").slice(0, 1)),
      weekDays: data.weekDates.map((date) => date.format("DD")),
      page: 1,
      totalPages,
    };
  }

  mapGenderToLetter(gender: typeof GENDER[number]) {
    if (gender === "MALE") return "M";
    if (gender === "FEMALE") return "F";
    if (gender === "NON_BINARY") return "";
    if (gender === "PREFER_NOT_TO_ANSWER") return "";

    return "";
  }

  mapEthnicityToLetter(ethnicity: typeof ETHNICITY[number]) {
    if (ethnicity === "AMERICAN_INDIAN") return "AI";
    if (ethnicity === "ASIAN") return "A";
    if (ethnicity === "BLACK") return "B";
    if (ethnicity === "HISPANIC") return "H";
    if (ethnicity === "OTHER_PACIFIC_ISLANDER") return "O";
    if (ethnicity === "WHITE") return "W";
    if (ethnicity === "TWO_OR_MORE") return "O";
    if (ethnicity === "PREFER_NOT_TO_ANSWER") return "";

    return "";
  }
}
