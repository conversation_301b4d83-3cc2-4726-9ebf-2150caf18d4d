<!--
Invoice dynamically rendered into html using handlebars and converted into pdf
using chrome-pdf recipe. The styles are extracted into separate asset for
better readability and later reuse.

Data to this sample are mocked at the design time and should be filled on the
incoming API request.
!-->
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet">

  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
    }
  </style>
</head>

<body>
  <div class="py-6 px-4 w-full">
    <section>
      <div class="flex items-center mb-4">
        <div class="w-52">
          <p class="text-gray-500 text-xs">Form CR-347</p>
        </div>
        <div class="flex-grow flex justify-center">
          <p class="font-bold text-3xl text-gray-500">
            PAYROLL
          </p>
        </div>
        <div class="w-52 text-right">
          <p class="text-gray-500 text-xs">Page: {{page}} of {{totalPages}}</p>
        </div>
      </div>
    </section>

    <section class="border-black" style="font-size: 8.5px; line-height: 9px">
      <div class="flex border-y h-20">
        <div class="border-r border-l w-1/2">
          <div class="flex gap-1 items-start justify-start border-b">
            <div class="flex gap-1 w-1/2 border-r px-1">
              <div class="pr-1 border-r flex-grow flex py-1">NAME OF CONTRACTOR</div>
              <div class="flex-grow flex">
                <input class="ml-1" type="checkbox" {{#unless isSubcontractor}} checked {{/unless}} />
              </div>
            </div>
            <div class="flex gap-1 w-1/2">
              <div class="mr-1 border-r flex-grow flex py-1">SUBCONTRACTOR</div>
              <div class="flex-grow flex">
                <input class="ml-1" type="checkbox" {{#if isSubcontractor}} checked {{/if}} />
              </div>
            </div>
          </div>
          <div class="flex gap-1 w-full h-3/4">
            <div class="w-1/2 flex border-r">
              <div class="mr-1 bg-[#f2f4ff] text-center flex-grow flex items-center m-1">
                {{#unless isSubcontractor}}{{name}}{{/unless}}
              </div>
            </div>
            <div class="w-1/2 flex">
              <div class="mr-1 bg-[#f2f4ff] text-center flex-grow flex items-center m-1">
                {{#if isSubcontractor}}{{name}}{{/if}}
              </div>
            </div>
          </div>

        </div>
        <!-- company address -->
        <div class="w-1/2 flex">
          <div class="border-r flex flex-col flex-grow">
            <div class="border-b p-1">ADDRESS</div>
            <div class="bg-[#f2f4ff] flex-grow flex items-center h-2/3 m-1">{{companyAddress}}</div>
          </div>
        </div>
      </div>

      <div class="w-full flex h-20 border-b border-x">
        <div class="w-1/2 flex gap-1">
          <div class="w-1/2 flex flex-col border-r">
            <div class="border-b m-1 py-1">PAYROLL NO.</div>
            <div class="flex-grow bg-[#f2f4ff] m-1 items-center">
              {{payrollNumber}}
            </div>
          </div>
          <div class="w-1/2 flex flex-col border-r">
            <div class="border-b m-1 py-1">FOR WEEK ENDING</div>
            <div class="flex-grow bg-[#f2f4ff] m-1 items-center">
              {{endWeekDate}}
            </div>
          </div>
        </div>
        <div class="w-1/2 flex">
          <div class="w-1/2 flex flex-col border-r">
            <div class="border-b m-1 py-1">PROJECT AND LOCATION</div>
            <div class="flex-grow bg-[#f2f4ff] m-1 items-center">
              {{projectLocation}}
            </div>
          </div>
          <div class="w-1/2 flex flex-col">
            <div class="border-b m-1 py-1">PROJECT OR CONTRACT NO.</div>
            <div class="flex-grow bg-[#f2f4ff] m-1 items-center">
              {{projectNumber}}
            </div>
          </div>
        </div>
      </div>

      <div class="flex h-26 border mt-2">
        <div class="w-1/2 border-r">
          <div class="grid h-full" style="grid-template-columns: 25% 5% 10% 5% 15% 40%;">
            <div class="flex flex-col justify-between items-center border-r">
              <div class="mt-1">(1)</div>
              <div class="flex-grow flex items-center justify-center m-1">
                NAME AND INDIVIDUAL IDENTIFYING NUMBER (e.g., LAST FOUR DIGITS
                OF SOCIAL SECURITY NUMBER) OF WORKER
              </div>
            </div>

            <div class="border-r">
              <div class="text-center mt-1">(2)</div>
              <div class="-rotate-90 relative -bottom-9 tracking-tight m-1">
                SEX
              </div>
            </div>

            <div class="border-r">
              <div class="text-center my-1">(3)</div>
              <div class="relative text-[6.25px] m-1">
                ETHNIC CODES<br>
                <br>
                B - Black<br>
                H - Hispanic<br>
                AI - American Indian<br>
                A - Asian<br>
                W - White<br>
                O - Other
              </div>
            </div>

            <div class="border-r">
              <div class="text-center mt-1">(4)</div>
              <div class="flex flex-col items-center">
                <div class="-rotate-90 relative -bottom-9 tracking-tight m-1 text-[7.25px] whitespace-nowrap">
                  <p>NO. OF WITHHOLDING</p>
                  <p class="text-center">EXCEPTIONS</p>
                </div>
              </div>
            </div>

            <div class="border-r flex flex-col justify-between text-center">
              <div class="mt-1 pb-1 border-b">(5)</div>
              <div class="flex-grow flex items-center justify-center m-1">WORK CLASSIFICATION</div>
            </div>

            <div class="flex">
              <div class="w-1/12 border-r">
                <div class="relative -bottom-2 text-center">
                  E<br>
                  A<br>
                  R<br>
                  N<br>
                  <br>
                  C<br>
                  O<br>
                  D<br>
                  E
                </div>
              </div>

              <div class="w-11/12 text-center">
                <div class="my-1">(6) DAY AND DATE</div>
                <div class="h-8 border-y mt-0.5 grid-cols-7 grid">
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[0]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[1]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[2]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[3]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[4]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[5]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center">
                    {{weekDaysNames.[6]}}
                  </div>
                </div>
                <div class="h-8 border-b grid-cols-7 grid">
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[0]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[1]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[2]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[3]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[4]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[5]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center">
                    {{weekDays.[6]}}
                  </div>
                </div>

                <div class="my-1">HOURS WORKED EACH DAY</div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-1/2 flex">
          <div class="max-w-9 border-r flex flex-col text-center">
            <div class="mt-1">(7)</div>
            <div class="px-0.5 flex-grow flex items-center justify-center">TOTAL HOURS</div>
          </div>
          <div class="min-w-12 max-w-10 border-r flex flex-col text-center">
            <div class="mt-1">(8)</div>
            <div class="px-0.5 flex-grow flex items-center justify-center">RATE OF PAY</div>
          </div>
          <div class="min-w-14 max-w-14 border-r flex flex-col text-center">
            <div class="mt-1">(9)</div>
            <div class="px-0.5 flex-grow flex items-center justify-center">GROSS AMOUNT EARNED</div>
          </div>


          <div class="border-r flex flex-col text-center w-80">
            <div class="flex flex-col items-center justify-center pb-2">
              <div class="my-1">(10)</div>
              <div>DEDUCTIONS</div>
            </div>
          </div>

          <div class="max-w-12 min-w-12 flex flex-col justify-between text-center">
            <div class="mt-1">(11)</div>
            <div class="px-1 flex-grow flex items-center justify-center">NET WAGES PAID FOR WEEK</div>
          </div>
        </div>
      </div>

      <div class="min-h-[400px]">
        {{#each (paginateTimesheets timesheets)}}
        {{#each this}}
        <div class="flex border min-h-14">
          <div class="w-1/2">
            <div class="grid h-full" style="grid-template-columns: 25% 5% 10% 5% 15% 40%;">
              <div class="flex flex-col justify-between items-center border-r mt-0.5">
                {{name}}
              </div>

              <div class="border-r mt-0.5 flex flex-col items-center">
                {{gender}}
              </div>

              <div class="border-r mt-0.5 flex flex-col items-center">
                {{ethnicity}}
              </div>

              <div class="border-r mt-0.5 flex flex-col items-center">
                {{numberOfExceptions}}
              </div>

              <div class="border-r flex flex-col justify-between text-center mt-0.5">
                {{jobClassification}}
              </div>

              <div class="flex">
                <div class="w-1/12 border-r">
                  <div class="h-1/2 border-b flex flex-col items-center justify-center">
                    OVT
                  </div>
                  <div class="h-1/2 flex flex-col items-center justify-center">
                    REG
                  </div>
                </div>

                <div class="w-11/12 text-center">
                  <div class="h-1/2 border-b grid-cols-7 grid">
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[0]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[1]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[2]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[3]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[4]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[5]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center">
                      {{overtimeHours.[6]}}
                    </div>
                  </div>
                  <div class="h-1/2 grid-cols-7 grid">
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[0]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[1]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[2]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[3]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[4]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[5]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center">
                      {{regularHours.[6]}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="w-1/2 flex">
            <div class="min-w-9 max-w-9 border-r flex flex-col justify-between text-center">
              <div class="h-1/2 border-b flex justify-center items-center">
                {{totalOvertimeHours}}
              </div>
              <div class="h-1/2 flex justify-center items-center">
                {{totalRegularHours}}
              </div>
            </div>
            <div class="min-w-12 max-w-12 border-r flex flex-col justify-between text-center">
              <div class="h-1/2 border-b flex items-center justify-center">
                {{overtimeRate}}
              </div>
              <div class="h-1/2 flex items-center">
                <div class="flex-grow" style="font-size:8px">{{baseRate}}</div>
                <div class="flex-grow" style="font-size:8px">{{cashFringes}}</div>
              </div>
            </div>
            <div
              style="background:linear-gradient(to top left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) calc(50% - 0.8px), rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0) calc(50% + 0.8px), rgba(0, 0, 0, 0) 100%);"
              class="min-w-14 max-w-14 border-r flex flex-col justify-between text-center">
              <div class="gross calculated text-start relative top-0.5 left-0.5">
                {{grossEarned}}
              </div>
              <div class="text-end relative bottom-0.5 right-0.5">{{grossEarnedAllProjects}}
              </div>
            </div>


            <div class="w-full h-full flex flex-wrap">
              {{#each deductions}}
              <div class="w-16 flex flex-col items-center justify-center border-r border-b h-8">
                <div class="w-full h-full">
                  <div class="px-0.5 text-center h-2/3 pt-0.5 overflow-hidden">
                    <div class="line-clamp-2 overflow-ellipsis">{{name}}</div>
                  </div>
                  <div class="w-full border-t text-center">
                    {{value}}
                  </div>
                </div>
              </div>
              {{/each}}
            </div>

            <div class="max-w-12 min-w-12 flex flex-col justify-center text-center">
              {{netWagesPaid}}
            </div>
          </div>
        </div>
        {{/each}}

        {{#unless @last}}
      </div>
    </section>
  </div>
</body>

</html>
<!DOCTYPE html>
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
    }

    .payroll-content {
      font-size: 8.5px;
      line-height: 9px;
    }
  </style>
</head>

<body class="payroll-content">
  <div class="py-6 px-4 w-full">
    <section>
      <div class="flex items-center mb-4">
        <div class="w-52">
          <p class="text-gray-500 text-xs">Form CR-347</p>
        </div>
        <div class="flex-grow flex justify-center">
          <p class="font-bold text-3xl text-gray-500">
            PAYROLL
          </p>
        </div>
        <div class="w-52 text-right">
          <p class="text-gray-500 text-xs">Page: {{add @index 2}} of {{../totalPages}}</p>
        </div>
      </div>
    </section>
    <section class="border-black payroll-content">
      {{/unless}}
      {{/each}}
  </div>

  </section>
</body>

</html>
