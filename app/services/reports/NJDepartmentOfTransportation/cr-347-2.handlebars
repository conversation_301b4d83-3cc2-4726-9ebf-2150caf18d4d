<html>

<head>
  <meta charset="utf-8" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet" />
  <style>
    /* set page to portrait letter with 0.2in margins */
    @page {
      size: 8.5in 11in portrait;
      margin: 0.2in;
    }

    /* ensure print color is preserved */
    @media print {
      body {
        -webkit-print-color-adjust: exact;
      }
    }

    html,
    body {
      margin: 0;
      padding: 0;
      font-family: "Open Sans", sans-serif;
      font-size: 10px;
      line-height: 1.2;
    }

    .bg-form {
      background-color: #f2f4ff;
    }
  </style>
</head>

<body>
  <div class="container mx-auto">
    <!-- header -->
    <div class="text-center">
      <!-- top header: department and form number -->
      <div class="text-base font-semibold text-left">
        Form CR-347-2 (01/2012)
      </div>
      <div class="relative">
        <div class="mt-1 text-center">NEW JERSEY DEPARTMENT OF TRANSPORTATION</div>
        <div class="absolute top-0 right-0 border-black border w-64 max-w-64 h-16"></div>
      </div>
      <div class="font-bold text-lg mt-1 text-center underline">
        STATEMENT OF COMPLIANCE
      </div>
    </div>

    <!-- date field -->
    <div class="mt-4 flex items-center">
      <div class="mr-2">Date:</div>
      <div class="px-2 border-b border-black bg-form" style="width: 2in;">
        {{reportDate}}
      </div>
    </div>

    <!-- signatory information -->
    <div class="mt-4">
      <div class="flex flex-wrap items-center">
        <span>I,</span>
        <div class="mx-2 text-center" style="width: 3in;">
          <div class="h-5 flex items-center justify-center bg-form border-b border-black">
            {{signerName}}
          </div>
          <div class="mt-0.5 text-xs">(Name of signatory party)</div>
        </div>
        <div class="text-center" style="width: 2in;">
          <div class="h-5 flex items-center justify-center bg-form border-b border-black">
            {{signerTitle}}
          </div>
          <div class="mt-0.5 text-xs">(Title)</div>
        </div>
        <span class="ml-2">do hereby state:</span>
      </div>
    </div>

    <!-- clause (1) -->
    <div class="mt-4">
      <div class="flex flex-wrap items-center">
        <span>That I pay or supervise the payment of the persons employed by</span>
        <div class="mx-2 text-center" style="width: 3in;">
          <div class="h-5 flex items-center justify-center bg-form border-b border-black">
            {{name}}
          </div>
          <div class="mt-0.5 text-xs">(Contractor or subcontractor)</div>
        </div>
        <span>on</span>
      </div>
      <div class="mt-1 flex flex-wrap items-center">
        the
        <span class="mx-2 inline-block text-center bg-form border-b border-black"
          style="width: 2in;">{{projectName}}</span>
        ; that during the payroll period commencing on the
      </div>
      <div class="mt-1 flex items-center">
        <span class="inline-block text-center bg-form border-b border-black mx-1"
          style="width: 1in;">{{weekStartDay}}</span>
        <span>day of</span>
        <span class="inline-block text-center bg-form border-b border-black mx-1"
          style="width: 1in;">{{weekStartMonth}}</span>
        <span>20</span>
        <span class="inline-block text-center bg-form border-b border-black mx-1"
          style="width: 1in;">{{weekStartYear}}</span>
        <span>and</span>
      </div>
      <div class="mt-1 flex items-center whitespace-nowrap">
        <span>ending the</span>
        <span class="inline-block text-center bg-form border-b border-black ml-1"
          style="width: 0.5in;">{{weekEndDay}}</span>
        <span>day of</span>
        <span class="inline-block text-center bg-form border-b border-black mx-1"
          style="width: 0.75in;">{{weekEndMonth}}</span>
        <span>20</span>
        <span class="inline-block text-center bg-form border-b border-black mx-1"
          style="width: 0.5in;">{{weekEndYear}}</span>
        <span>, all persons employed on said project have been paid the full weekly wages earned,</span>
      </div>
      <div class="mt-4 flex flex-wrap items-center">
        <span>that no rebates have been or will be made either directly or indirectly to
          or on behalf of said</span>
        <div class="mx-2 text-center" style="width: 3in;">
          <div class="h-5 flex items-center justify-center bg-form border-b border-black">
            {{name}}
          </div>
          <div class="mt-0.5 text-xs">(Contractor or subcontractor)</div>
        </div>
      </div>
      <p class="mt-2">
        from the full weekly wages earned by any person and that no deductions have
        been made either directly or indirectly from the full wages earned by any person,
        other than permissible deductions as defined in Regulations, Part 3 (29 C.F.R.
        Subtitle A), issued by the Secretary of Labor under the Copeland Act, as amended
        (48 Stat. 948, 63 Stat. 108, 72 Stat. 967; 76 Stat. 357; 40 U.S.C. §3145), and
        described below:
      </p>
    </div>

    <!-- permissible deductions lines -->
    <div class="mt-2">
      <div class="bg-form border-b border-black" style="height: 0.2in;"></div>
      <div class="bg-form border-b border-black mt-2" style="height: 0.2in;"></div>
      <div class="bg-form border-b border-black mt-2" style="height: 0.2in;"></div>
    </div>

    <!-- clause (2) -->
    <p class="mt-3 ml-6">
      (2) That any payrolls otherwise under this contract required to be submitted for
      the above period are correct and complete; that the wage rates for laborers or
      mechanics contained therein are not less than the applicable wage rates contained
      in any wage determination incorporated into the contract; that the classifications
      set forth therein for each laborer or mechanic conform with the work he performed.
    </p>

    <!-- clause (3) -->
    <p class="mt-3 ml-6">
      (3) That any apprentices employed in the above period are duly registered in a bona
      fide apprenticeship program registered with a State apprenticeship agency recognized
      by the Bureau of Apprenticeship and Training, United States Department of Labor,
      or if no such recognized agency exists in a State, are registered with the Bureau of
      Apprenticeship and Training, United States Department of Labor.
    </p>

    <p class="mt-3 ml-6">(4) That:</p>

    <!-- clause (4a) -->
    <div class="ml-10 mt-2">
      <p>(a) WHERE FRINGE BENEFITS ARE PAID TO APPROVED PLANS, FUNDS, OR PROGRAMS</p>
      <div class="flex items-start gap-2 mt-2 ml-3">
        <input type="checkbox" {{#if areFringeBenefitsPaidToPlans}}checked{{/if}} />
        <div>
          in addition to the basic hourly wage rates paid to each laborer or mechanic listed
          in the above referenced payroll, payments of fringe benefits as listed in the
          contract have been or will be made to appropriate programs for the benefit of such
          employees, except as noted in section 4(c) below.
        </div>
      </div>
    </div>

    <!-- clause (4b) -->
    <div class="ml-10 mt-3">
      <p>(b) WHERE FRINGE BENEFITS ARE PAID IN CASH</p>
      <div class="flex items-start gap-2 mt-2 ml-3">
        <input type="checkbox" {{#if areFringeBenefitsPaidInCash}}checked{{/if}} />
        <div>
          Each laborer or mechanic listed in the above referenced payroll has been paid,
          as noted on the payroll, an amount not less than the sum of the applicable basic
          hourly wage rate plus the amount of the required fringe benefits as listed in the
          contract, except as noted in section 4(c) below.
        </div>
      </div>
    </div>

    <!-- clause (4c) exceptions table -->
    <div class="mt-3">
      <p class="ml-10">(c) EXCEPTIONS</p>
      <table class="mt-1 w-full" style="border-collapse: collapse;">
        <thead>
          <tr>
            <th class="text-lg font-normal" style="width: 50%; border: 1px solid #000; padding: 2px;">
              EXCEPTION (CRAFT)
            </th>
            <th class="text-lg font-normal" style="width: 50%; border: 1px solid #000; padding: 2px;">
              EXPLANATION
            </th>
          </tr>
        </thead>
        <tbody>
          {{#each exceptions}}
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px;">
              {{craft}}
            </td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px;">
              {{explanation}}
            </td>
          </tr>
          {{else}}
          <!-- render empty rows to maintain consistent height -->
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
            <td style="border: 1px solid #000; background-color: #f2f4ff; padding: 2px; height: 0.2in;"></td>
          </tr>
          {{/each}}
        </tbody>
      </table>
    </div>

    <!-- remarks Section -->
    <div class="mt-3">
      <p class="ml-0.5">REMARKS</p>
      <div class="bg-form border border-black mt-1 p-1" style="height: .8in;">
        {{remarks}}
      </div>
    </div>

    <!-- signature Block -->
    <div class="mt-3 border border-black">
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="width: 50%; border-right: 1px solid #000; padding: 2px;">
            <p class="ml-1 mt-0.5 text-xs">NAME AND TITLE</p>
            <div class="bg-form p-1" style="height: 0.4in;">
              <div>{{signerName}}, {{signerTitle}}</div>
            </div>
          </td>
          <td style="width: 50%; padding: 2px;">
            <p class="ml-1 mt-0.5 text-xs">SIGNATURE</p>
            <div class="bg-form p-1 italic" style="height: 0.4in;">
              {{signerName}}
            </div>
          </td>
        </tr>
      </table>
      <div class="border-t border-black p-1 text-justify text-xs">
        The willful falsification of any of the above statements may subject the
        contractor or subcontractor to civil or criminal prosecution.
        See section 1001 of title 18 and section 231 of title 31 of the United States Code.
      </div>
    </div>

  </div>
</body>

</html>
