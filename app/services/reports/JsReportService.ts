import JsReport from "jsreport";

// Define the type for jsreport options
export interface JsReportOptions {
  httpPort?: number;
  extensions?: {
    [key: string]: any;
  };
}

// Define the type for render options
export interface RenderOptions {
  content: string;
  engine: string;
  recipe: string;
  chrome?: {
    landscape?: boolean;
    waitForNetworkIdle?: boolean;
    timeout?: number;
    printBackground?: boolean;
    marginTop?: string;
    marginBottom?: string;
    marginLeft?: string;
    marginRight?: string;
    format?: string;
    displayHeaderFooter?: boolean;
    pdfOptions?: {
      preferCSSPageSize?: boolean;
      printBackground?: boolean;
      metadata?: {
        title?: string;
        author?: string;
        producer?: string;
        creator?: string;
        creationDate?: string;
      };
    };
  };
}

export class JsReportService {
  private jsreport: ReturnType<typeof JsReport>;
  private initPromise: Promise<ReturnType<typeof JsReport.prototype.init>>;

  constructor(options: JsReportOptions = {}) {
    // default options
    const defaultOptions: JsReportOptions = {
      httpPort: 5001, // default port
      extensions: {
        "chrome-pdf": {
          launchOptions: {
            args: ["--no-sandbox", "--disable-setuid-sandbox"],
          },
        },
      },
    };

    // merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };

    // Initialize jsreport with merged options and store the init promise
    this.jsreport = JsReport({
      ...mergedOptions,
    });

    this.initPromise = this.jsreport.init();
  }

  getJsReport() {
    return this.jsreport;
  }

  /**
   * render a template with jsreport
   * @param template The template options
   * @param data The data to render the template with
   */
  async render<T>(template: RenderOptions, data: T): Promise<ReturnType<typeof this.jsreport.render>> {
    // Wait for init to complete
    await this.initPromise;

    return this.jsreport.render({
      template,
      data,
    });
  }

  /**
   * close the jsreport instance
   * this should be called when the service is no longer needed
   */
  async close(): Promise<void> {
    if (this.jsreport) {
      await this.jsreport.close();
    }
  }
}

// replace the map of instances with a single instance
let jsReportInstance: JsReportService | null = null;
const activeConfigurations: Set<string> = new Set();

/**
 * get the singleton JsReportService instance
 * this ensures we use a single instance with proper lifecycle management
 */
export function getJsReportService(options: JsReportOptions = {}): JsReportService {
  // Create the instance if it doesn't exist
  if (!jsReportInstance) {
    jsReportInstance = new JsReportService(options);
  }

  // Generate a unique key for this configuration
  const configKey = JSON.stringify(options);
  activeConfigurations.add(configKey);

  return jsReportInstance;
}

/**
 * release the JsReport service when a particular configuration is no longer needed
 * the instance will only be closed when all configurations are released
 */
export function releaseJsReportService(options: JsReportOptions = {}): void {
  const configKey = JSON.stringify(options);
  activeConfigurations.delete(configKey);

  // If no more active configurations, close the instance
  if (activeConfigurations.size === 0 && jsReportInstance) {
    jsReportInstance
      .close()
      .then(() => {
        jsReportInstance = null;
      })
      .catch(console.error);
  }
}
