import { CertifiedPayrollData } from "@/services/reports";
import { Report } from "@/services/reports/Report";
import { Organization } from "@/models";
import * as XMLBuilder from "xmlbuilder2";
import dayjs from "dayjs";

/**
 * California specific report
 */
export class CADepartmentOfIndustrialRelations extends Report {
  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transform(organization, payrollData);

    const xmlContent = this.generateXML(data);

    const buffer = Buffer.from(xmlContent, "utf-8");

    return this.getResponse(buffer, "xml", "application/xml");
  }

  generateXML(data: ReturnType<Report["transform"]>): string {
    const root = XMLBuilder.create({ version: "1.0", encoding: "UTF-8" })
      .ele("CPR:eCPR", { "xmlns:CPR": "http://www.dir.ca.gov/dlse/CPR-Prod-Test/CPR.xsd" })
      .ele("CPR:contractorInfo")
      .ele("CPR:contractorName")
      .txt(data.name)
      .up()
      .ele("CPR:contractorLicense")
      .ele("CPR:licenseType")
      .txt("") // blank for now until we know - don't capture this
      .up()
      // .ele("CPR:licenseNum").txt(data.contractorLicense || "").up()
      .ele("CPR:licenseNum")
      .txt("") // blank for now until we know - don't capture this
      .up()
      .up()
      .ele("CPR:contractorPWCR")
      .txt("NA") // unsure at the moment - don't capture this
      .up()
      // .ele("CPR:contractorFEIN").txt(data.contractorFEIN || "").up()
      .ele("CPR:contractorFEIN")
      .txt("") // can we pull this from Check?
      .up()
      .ele("CPR:contractorAddress")
      .ele("CPR:street")
      .txt(data.company.fullStreet || "N/A")
      .up()
      .ele("CPR:city")
      .txt(data.company.city || "N/A")
      .up()
      .ele("CPR:state")
      .txt(data.company.state || "N/A")
      .up()
      .ele("CPR:zip")
      .txt(data.company.zip || "N/A")
      .up()
      .up()
      // .ele("CPR:insuranceNum").txt(data.insuranceNum || "").up()
      .ele("CPR:insuranceNum")
      .txt("") // not sure we store this?
      .up()
      // .ele("CPR:contractorEmail").txt(data.email || "").up()
      .ele("CPR:contractorEmail")
      .txt("") // ideally should be an email address on org table
      .up()
      .up()
      .ele("CPR:projectInfo")
      // .ele("CPR:awardingBody").txt(data.awardingBody || "").up() // have this in project
      // .ele("CPR:contractAgencyID").txt(data.contractAgencyID || "").up()
      // .ele("CPR:contractAgency").txt(data.contractAgency || "").up()
      .ele("CPR:contractAgency")
      .txt("") // not sure we have this
      .up()
      .ele("CPR:projectName")
      .txt(data.projectName || "")
      .up()
      // .ele("CPR:projectID").txt(data.projectID || "").up()
      // .ele("CPR:awardingBodyID").txt(data.awardingBodyID || "").up()
      .ele("CPR:awardingBodyID")
      .txt("") // not sure we have this
      .up()
      .ele("CPR:projectNum")
      .txt(data.projectNumber || "N/A")
      .up()
      // .ele("CPR:contractID").txt(data.contractID || "").up()
      .ele("CPR:contractID")
      .txt("") // not sure we have this
      .up()
      .ele("CPR:projectLocation")
      .ele("CPR:description")
      .txt(data.projectLocation || "-")
      .up()
      .ele("CPR:street")
      .txt(data?.project?.address || "-") // this isn't broken out by street, city, county, state, zip in the project table
      .up() // TODO should this be empty? (we will update the app so that the project includes the full address)
      .ele("CPR:city")
      .txt("-")
      .up() // TODO should this be empty? (we will update the app so that the project includes the full address)
      .ele("CPR:county")
      .txt("-")
      .up() // TODO should this be empty? (we will update the app so that the project includes the full address)
      .ele("CPR:state")
      .txt("-")
      .up() // TODO should this be empty? (we will update the app so that the project includes the full address)
      .ele("CPR:zip")
      .txt("-")
      .up() // TODO should this be empty? (we will update the app so that the project includes the full address)
      .up()
      .up()
      .ele("CPR:payrollInfo")
      .ele("CPR:statementOfNP")
      .txt("false") // this should be like noWorkPerformed in DNI
      .up()
      .ele("CPR:payrollNum")
      .txt(parseInt(data.payrollNumber).toString()) // double check where this is coming from
      .up()
      .ele("CPR:amendmentNum")
      .txt("0000") // is this the same as the WH-347 payroll number?
      .up()
      .ele("CPR:forWeekEnding")
      .txt(dayjs(data.endWeekDate).format("YYYY-MM-DD"))
      .up();

    const employeesEle = root.ele("CPR:employees");
    data.userClassifications.forEach((ucData) => {
      // the assumption is employees can have multiple rows if they have multiple classifications for the project worked - this format is slightly different from LNI
      const employeeEle = employeesEle.ele("CPR:employee");
      employeeEle
        .ele("CPR:name", {
          id: `${ucData.employee.lastFourSSN}::${ucData.employee.name.toUpperCase()}`,
        })
        .txt(ucData.employee.name)
        .up()
        .ele("CPR:address")
        .ele("CPR:street")
        .txt(ucData.employee?.residence?.line1 ?? "-")
        .up()
        .ele("CPR:city")
        .txt(ucData.employee?.residence?.city ?? "-")
        .up()
        .ele("CPR:state")
        .txt(ucData.employee?.residence?.state ?? "-")
        .up()
        .ele("CPR:zip")
        .txt(ucData.employee?.residence?.postal_code ?? "-")
        .up()
        .up()
        .ele("CPR:ssn")
        .txt(ucData.employee.lastFourSSN)
        .up()
        .ele("CPR:numWithholdingExemp")
        .txt("0") // is this something we can get from Check?
        .up()
        .ele("CPR:workClass")
        .txt(ucData.jobClassification)
        .up();

      const payrollEle = employeeEle.ele("CPR:payroll");
      const hrsWorkedEachDayEle = payrollEle.ele("CPR:hrsWorkedEachDay");

      for (let i = 0; i < 7; i++) {
        hrsWorkedEachDayEle
          .ele("CPR:day", { id: (i + 1).toString() })
          .ele("CPR:date")
          .txt(data.weekDates[i].format("YYYY-MM-DD"))
          .up()
          .ele("CPR:straightTime")
          .txt(ucData.pwRegularHours[i])
          .up()
          .ele("CPR:overtime")
          .txt(ucData.pwOvertimeHours[i])
          .up()
          .ele("CPR:doubletime")
          .txt(ucData.pwDotHours[i])
          .up()
          .up();
      }

      const totHrsEle = payrollEle.ele("CPR:totHrs");
      totHrsEle
        .ele("CPR:totHrsStraightTime")
        .txt(ucData.pwHoursWorked)
        .up()
        .ele("CPR:totHrsOvertime")
        .txt(ucData.pwOvertimeHoursWorked)
        .up()
        .ele("CPR:totHrsDoubletime")
        .txt(ucData.pwDotHoursWorked)
        .up();

      const hrlyPayRateEle = payrollEle.ele("CPR:hrlyPayRate");
      hrlyPayRateEle
        .ele("CPR:hrlyPayRateStraightTime")
        .txt(ucData.regRate)
        .up()
        .ele("CPR:hrlyPayRateOvertime")
        .txt(ucData.overtimeRate)
        .up()
        .ele("CPR:hrlyPayRateDoubletime")
        .txt(ucData.dotRate)
        .up();

      const grossAmountEarnedEle = payrollEle.ele("CPR:grossAmountEarned");
      grossAmountEarnedEle
        .ele("CPR:thisProject")
        .txt(ucData.grossEarned)
        .up()
        .ele("CPR:allWork")
        .txt(ucData.grossEarnedAllProjects)
        .up();

      const deductionsContribPayEle = payrollEle.ele("CPR:deductionsContribPay");
      deductionsContribPayEle
        .ele("CPR:fedTax")
        .txt("0.00") // We don't have a specific field for federal income tax in our current data structure
        .up()
        .ele("CPR:FICA")
        .txt(ucData.deductionCategories.fica)
        .up()
        .ele("CPR:stateTax")
        .txt(ucData.deductionCategories.stateTax)
        .up()
        .ele("CPR:SDI")
        .txt(ucData.deductionCategories.sdi)
        .up()
        .ele("CPR:vacationHoliday")
        .txt("0.00")
        .up()
        .ele("CPR:healthWelfare")
        .txt(ucData.deductionCategories.healthAndWelfare)
        .up()
        .ele("CPR:pension")
        .txt(ucData.deductionCategories.pension)
        .up()
        .ele("CPR:training")
        .txt("0.00")
        .up()
        .ele("CPR:fundAdmin")
        .txt("0.00")
        .up()
        .ele("CPR:dues")
        .txt("0.00")
        .up()
        .ele("CPR:travelSubs")
        .txt("0.00")
        .up()
        .ele("CPR:savings")
        .txt("0.00")
        .up()
        .ele("CPR:other")
        .txt(ucData.deductionCategories.other) // TODO medicare is not in this report? Where should we add it? Does it need to be caught on "Other"?
        .up()
        .ele("CPR:total")
        .txt(ucData.deductionCategories.total)
        .up()
        .ele("CPR:notes")
        .txt("-")
        .up();

      payrollEle.ele("CPR:netWagePaidWeek").txt(ucData.netWagesPaid).up().ele("CPR:checkNum").txt("000").up();
    });

    return root.end({ prettyPrint: true });
  }
}
