import * as ExcelJS from "exceljs";
import { CertifiedPayrollData } from "@/services/reports";
import { join } from "node:path";
import { Report } from "@/services/reports/Report";
import { Organization } from "@/models";
import {
  calculateCoreBenefitHourlyContribution,
  calculatePensionHourlyContribution,
  calculateMiscHourlyContribution,
} from "@/util/payrollHelpers";
import { PayrollItemEarning } from "@/types/check";

export class LCPTrackerReport extends Report {
  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transform(organization, payrollData);

    const filePath = join(__dirname, "report.xlsx");

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);

    const worksheet = workbook.getWorksheet(1);

    // Start from row 3
    let rowNumber = 3;

    // Process each user classification
    for (const userClassification of data.userClassifications) {
      const newRowData = this.transformToXLSX(data, userClassification);
      const values = newRowData.map((item: any) => item.value);
      worksheet.spliceRows(rowNumber, 0, values);
      rowNumber++;
    }

    const buffer = await workbook.xlsx.writeBuffer();

    return this.getResponse(buffer, "xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
  }

  transformToXLSX(
    data: ReturnType<typeof Report.prototype.transform>,
    ucData: ReturnType<typeof Report.prototype.transform>["userClassifications"][number]
  ): Array<{ column: string; value: string | number | null }> {
    return [
      { column: "payroll_number", value: data.payrollNumber },
      { column: "project_code", value: data.projectNumber || null },
      { column: "contract_id", value: null },
      { column: "work_order", value: data.projectLocation },
      { column: "week_end_date", value: data.endWeekDate },
      {
        column: "check_num",
        value:
          ucData.paymentMethod === "manual"
            ? ucData.paperCheckNumber
            : ucData.paymentMethod === "direct_deposit"
            ? "direct deposit"
            : null,
      }, // F
      { column: "ssn", value: null }, // leave this empty for now - no full SSN
      { column: "employee_ID", value: ucData.employee.checkEmployeeId },
      { column: "class_code", value: null }, // I
      {
        column: "gross_employee_pay",
        value: ucData.earnings
          .filter((e: PayrollItemEarning) => e.earning_code)
          .reduce((a: number, c: PayrollItemEarning) => a + parseFloat(c.amount), 0),
      }, // this should be filter for earnings with earning code, then sum (reduce the amount)
      {
        column: "all_projects",
        value: ucData.earnings.reduce((a: number, c: PayrollItemEarning) => a + parseFloat(c.amount), 0),
      },
      {
        column: "wages_paid_in_lieu_of_fringes",
        value: (
          parseFloat(ucData.cashFringe) *
          ucData.earnings
            .filter((e: PayrollItemEarning) => e.earning_code)
            .reduce((a: number, c: PayrollItemEarning) => a + (c.hours || 0), 0)
        ).toFixed(2),
      },
      { column: "total_paid", value: ucData.netWages },
      { column: "st_hrs_date1", value: ucData.pwRegularHours[0] },
      { column: "st_hrs_date2", value: ucData.pwRegularHours[1] },
      { column: "st_hrs_date3", value: ucData.pwRegularHours[2] },
      { column: "st_hrs_date4", value: ucData.pwRegularHours[3] },
      { column: "st_hrs_date5", value: ucData.pwRegularHours[4] },
      { column: "st_hrs_date6", value: ucData.pwRegularHours[5] },
      { column: "st_hrs_date7", value: ucData.pwRegularHours[6] },
      { column: "ov_hrs_date1", value: ucData.pwOvertimeHours[0] },
      { column: "ov_hrs_date2", value: ucData.pwOvertimeHours[1] },
      { column: "ov_hrs_date3", value: ucData.pwOvertimeHours[2] },
      { column: "ov_hrs_date4", value: ucData.pwOvertimeHours[3] },
      { column: "ov_hrs_date5", value: ucData.pwOvertimeHours[4] },
      { column: "ov_hrs_date6", value: ucData.pwOvertimeHours[5] },
      { column: "ov_hrs_date7", value: ucData.pwOvertimeHours[6] },
      { column: "ov_hrsx2_date1", value: ucData.pwDotHours[0] },
      { column: "ov_hrsx2_date2", value: ucData.pwDotHours[1] },
      { column: "ov_hrsx2_date3", value: ucData.pwDotHours[2] },
      { column: "ov_hrsx2_date4", value: ucData.pwDotHours[3] },
      { column: "ov_hrsx2_date5", value: ucData.pwDotHours[4] },
      { column: "ov_hrsx2_date6", value: ucData.pwDotHours[5] },
      { column: "ov_hrsx2_date7", value: ucData.pwDotHours[6] },
      {
        column: "Total_Hours_All_Projects",
        value: ucData.allAttributedTimesheets
          ? (
              ucData.allAttributedTimesheets.reduce(
                (sum, timesheet) =>
                  sum +
                  (timesheet.regularMinutes || 0) +
                  (timesheet.overtimeMinutes || 0) +
                  (timesheet.doubleOvertimeMinutes || 0),
                0
              ) / 60
            ).toFixed(2)
          : null,
      },
      {
        column: "ep_haw",
        value: ucData.deductionCategories.healthAndWelfare || null,
      },
      {
        column: "ep_pension",
        value: ucData.deductionCategories.pension || null,
      },
      { column: "ep_vac_hol", value: null },
      { column: "ep_train", value: null },
      { column: "ep_all_other", value: null },
      { column: "vol_cont_pension", value: null },
      { column: "vol_emp_pay_med", value: null },
      { column: "dts_fed_tax", value: ucData.deductionCategories.federalIncomeTax || null }, // AQ
      { column: "dts_fica", value: ucData.deductionCategories.fica || null },
      {
        column: "dts_medicare",
        value: ucData.deductionCategories.medicare || null,
      },
      {
        column: "dts_state_tax",
        value: ucData.deductionCategories.stateTax || null,
      },
      { column: "dts_sdi", value: ucData.deductionCategories.sdi || null },
      { column: "dts_dues", value: null },
      { column: "dts_savings", value: null },
      {
        column: "dts_other",
        value: ucData.deductionCategories.other,
      },
      {
        column: "dts_total",
        value: ucData.deductionCategories.total || null,
      },
      { column: "trav_subs", value: null },
      { column: "pay_rate", value: ucData.regRate }, // BA equal to base rate + cash fringe
      { column: "OT_rate", value: ucData.overtimeRate }, // BB
      { column: "2OT_rate", value: ucData.dotRate }, // BC
      { column: "prnotes", value: null },
      { column: "Payment_date", value: data.reportDate },
      { column: "first_name", value: ucData.employee.firstName },
      { column: "last_name", value: ucData.employee.lastName },
      { column: "address1", value: ucData.employee.residence?.line1 },
      { column: "address2", value: ucData.employee.residence?.line2 },
      { column: "city", value: ucData.employee.residence?.city },
      { column: "state", value: ucData.employee.residence?.state },
      { column: "ZIP", value: ucData.employee.residence?.postal_code },
      { column: "phone", value: ucData.employee.phone }, // BM
      { column: "gender", value: null },
      { column: "ethnicity", value: null },
      { column: "apprentice_id", value: null },
      { column: "craft_id", value: null },
      { column: "vac_hol_dues_rate", value: ucData.fringePay }, // BR - currently unknown - subject to change
      {
        column: "emp_ep_haw",
        value: calculateCoreBenefitHourlyContribution(ucData.benefits, ucData.employeeBenefits, "company"),
      }, // BS
      {
        column: "emp_ep_pension",
        value: calculatePensionHourlyContribution(ucData.benefits, ucData.employeeBenefits, "company"),
      }, // BT
      {
        column: "emp_ep_other",
        value: calculateMiscHourlyContribution(ucData.benefits, ucData.employeeBenefits, "company"),
      }, // BU
      {
        column: "training_rate",
        value: ucData.fringeBenefitClassifications.find((fbc: any) => fbc.category === "TRAINING_FUND_CONTRIBUTION")
          ?.amount,
      }, // BV
      {
        column: "vol_cont_pension_rate",
        value: calculatePensionHourlyContribution(ucData.benefits, ucData.employeeBenefits, "employee"),
      }, // BW
      {
        column: "vol_emp_pay_med_rate",
        value: calculateCoreBenefitHourlyContribution(ucData.benefits, ucData.employeeBenefits, "employee"),
      }, // BX
      { column: "in_lieu_payment_rate", value: ucData.cashFringe },
      { column: "vac_chk_box", value: "Y" },
      { column: "fringe_paid_chk_box", value: data.areFringeBenefitsPaidInCash ? "Y" : "N" },
      { column: "date_hired", value: ucData.employee.hireDate },
      { column: "emp_status", value: ucData.employee.status },
      { column: "work_county", value: null },
      { column: "IsForeman", value: null },
      { column: "IsDisadvantaged", value: null },
      { column: "VeteranStatus", value: null },
      { column: "OtherDeductionNotes", value: null },
      { column: "num_exempt", value: ucData?.numberOfExceptions || null },
      { column: "DriversLicense", value: null },
      { column: "DriversLicenseState", value: null },
      { column: "Owner_Operator", value: null },
      { column: "I9Verified", value: null },
      { column: "Geographic_Ward", value: null },
      { column: "Geographic_Area", value: null },
      { column: "Congressional_District", value: null },
      { column: "State_Senate_District", value: null },
      { column: "OD_Category", value: null },
      { column: "OD_Type", value: null },
      { column: "OD_Amount", value: null },
      { column: "FringesProvidedByEmployer", value: null },
      { column: "LocalUnionNumber", value: null },
      { column: "YTD_SickPayTime", value: null },
      { column: "Email", value: null },
    ];
  }
}
