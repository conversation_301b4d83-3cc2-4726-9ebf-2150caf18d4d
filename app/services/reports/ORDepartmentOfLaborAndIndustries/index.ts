import fs from "fs";
import dayjs from "dayjs";
import PDFMerger from "pdf-merger-js";
import { join } from "node:path";
import { CertifiedPayrollData } from "@/services/reports";
import { Response } from "express";
import { Report } from "@/services/reports/Report";
import { JsReportService, getJsReportService, releaseJsReportService } from "@/services/reports/JsReportService";
import { EmployeeBenefit, Organization } from "@/models";
import { ExtendedTimesheet } from "@/models/timesheet";
import { SimplifiedFringeBenefitClassification } from "@/models/fringebenefitclassification";

export default class ORDepartmentOfLaborAndIndustries extends Report {
  private jsReportService: JsReportService;
  private jsReportOptions = { httpPort: 5001 };

  constructor(response: Response) {
    super(response);
    this.jsReportService = getJsReportService(this.jsReportOptions);

    // Register custom handlebars helpers
    this.jsReportService.getJsReport().beforeRenderListeners.add("custom-helpers", (req, _res) => {
      req.template.helpers = `
        function paginateTimesheets(timesheets) {
          if (!timesheets || !Array.isArray(timesheets)) {
            return [];
          }
          
          const pages = [];
          let currentPage = [];

          for (let i = 0; i < timesheets.length; i++) {
            currentPage.push(timesheets[i]);

            if (
              (i === 2) || // first page break after 3 items
              (i > 2 && (i - 2) % 6 === 0) || // subsequent breaks every 6 items
              (i === timesheets.length - 1) // last item
            ) {
              pages.push(currentPage);
              currentPage = [];
            }
          }

          if (currentPage.length > 0) {
            pages.push(currentPage);
          }

          return pages;
        }

        function limit(array, limit) {
          if (!Array.isArray(array)) {
            return [];
          }
          return array.slice(0, limit);
        }

        function slice(array, start) {
          if (!Array.isArray(array)) {
            return [];
          }
          return array.slice(start);
        }

        function chunk(array, size) {
          if (!Array.isArray(array)) {
            return [];
          }
          
          const chunks = [];
          for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
          }
          return chunks;
        }

        function length(arr) {
          return arr ? arr.length : 0;
        }

        function gt(a, b) {
          return a > b;
        }

        function lt(a, b) {
          return a < b;
        }

        function range(from, to) {
          const data = [];
          for (let i = from; i < to; i++) {
            data.push(i);
          }
          return data;
        }
        
        function subtract(a, b) {
          return a - b;
        }

        function formatNumber(value, options) {
          if (value === undefined || value === null) {
            return '';
          }

          const format = options.hash.format || '0.00';
          const number = parseFloat(value);

          if (isNaN(number)) {
            return '';
          }

          if (format === '0.00') {
            return number.toFixed(2);
          } else if (format === '0.0') {
            return number.toFixed(1);
          } else if (format === '0') {
            return Math.round(number).toString();
          }

          return number.toFixed(2);
        }

        // Register the helper with the name 'format-number'
        Handlebars.registerHelper('format-number', formatNumber);
      `;
    });
  }

  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transformForPdfTemplate(this.transform(organization, payrollData));

    // we can also attach this to the node app and load it on app start
    const merger = new PDFMerger();

    const wh38Page1 = fs.readFileSync(join(__dirname, "wh-38-1.handlebars"), "utf8");
    const wh38Page2 = fs.readFileSync(join(__dirname, "wh-38-2.handlebars"), "utf8");

    const [wh38PdfPage1, wh38PdfPage2] = await Promise.all([
      this.jsReportService.render<typeof data>(
        {
          content: wh38Page1,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
      this.jsReportService.render<typeof data>(
        {
          content: wh38Page2,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
    ]);
    await merger.add(wh38PdfPage1.content);
    await merger.add(wh38PdfPage2.content);

    // Set metadata
    await merger.setMetadata({
      producer: "Hammr",
      title: "Certified payroll report",
    });

    try {
      return this.getResponse(await merger.saveAsBuffer(), "pdf", "application/pdf");
    } finally {
      // Release the service when done
      releaseJsReportService(this.jsReportOptions);
    }
  }

  transformForPdfTemplate(data: ReturnType<Report["transform"]>) {
    const { endWeekDate, timezone } = data;

    const [weekEndDay, weekEndMonth, weekEndYear] = dayjs(endWeekDate).format("DD-MM-YYYY").split("-");
    const [weekStartDay, weekStartMonth, weekStartYear] = dayjs(endWeekDate)
      .subtract(6, "days")
      .format("DD-MM-YYYY")
      .split("-");

    const startWeekDate = `${weekStartMonth}-${weekStartDay}-${weekStartYear}`;

    // to proxy the date that work first began on the project - we sift through timesheets to find earliest clockIn
    let earliestClockIn: Date = null;
    // nested loop
    data.timesheets.forEach((uc: any) => {
      uc.attributedTimesheets.forEach((ts: any) => {
        if (!earliestClockIn || dayjs(ts.clockIn).isBefore(earliestClockIn)) {
          earliestClockIn = ts.clockIn;
        }
      });
    });

    const formattedEarliestClockIn = dayjs.tz(earliestClockIn, timezone).format("MM-DD-YYYY");

    const deductionsSet = new Set();

    data.timesheets.forEach((timesheet: any) => {
      timesheet.deductions.forEach((deduction: { name: string; value: string }) => {
        deductionsSet.add(deduction.name);
      });
    });

    const formattedTimesheets = data.timesheets.map((timesheet: any) => {
      let name;

      if (timesheet.name) {
        name = timesheet.name;
      } else if (timesheet.employee) {
        name = timesheet.employee.firstName + " " + timesheet.employee.lastName;
      }

      if (timesheet.employee.lastFourSSN) {
        name = name + " (" + timesheet.employee.lastFourSSN + ")";
      }

      // calculate aggregate deductions
      const aggregateDeductions = timesheet.deductions
        .reduce((total: number, deduction: { name: string; amount: string }) => {
          if (deduction.name === "Total Deductions") return total;

          return total + (parseFloat(deduction.amount) || 0);
        }, 0)
        .toFixed(2);

      // need to go through each timesheet's attributed timesheets and create the collection of fringe eligible benefits
      const eligibleBenefits: Record<string, object> = {};
      timesheet.attributedTimesheets.forEach((ts: ExtendedTimesheet) => {
        if (ts.user?.employeeBenefits) {
          ts.user.employeeBenefits.forEach((benefit: EmployeeBenefit) => {
            eligibleBenefits[benefit.name] = benefit;
          });
        }

        if (ts?.fringeBenefitClassifications) {
          (ts.fringeBenefitClassifications as unknown as SimplifiedFringeBenefitClassification[]).forEach(
            (benefit: SimplifiedFringeBenefitClassification) => {
              eligibleBenefits[benefit.name] = benefit;
            }
          );
        }
      });

      // array of unique benefits
      const uniqueBenefits = Object.values(eligibleBenefits);

      const mappedDeductions = Array.from(deductionsSet)
        .sort(this.customDeductionsSort)
        .map((deductionName) => {
          const deduction = timesheet.deductions.find(
            (deduction: { name: string; value: string }) => deduction.name === deductionName
          );

          return { name: deductionName, value: deduction ? deduction.amount : "0" };
        });

      const employeeAddress =
        timesheet.employee.residence !== undefined
          ? `${timesheet.employee.residence.line1}${
              timesheet.employee.residence.line2 ? ", " + timesheet.employee.residence.line2 : ""
            }, ${timesheet.employee.residence.city}, ${timesheet.employee.residence.state} ${
              timesheet.employee.residence.postal_code
            }`
          : "";

      return {
        ...timesheet,
        numberOfExceptions: timesheet.numberOfExceptions > 0 ? String(timesheet.numberOfExceptions) : "",
        name,
        employeeAddress,
        calculatedHourlyRate: (parseFloat(timesheet.baseRate) + parseFloat(timesheet.cashFringes)).toFixed(2),
        fringeCashDiff: Math.max(0, parseFloat(timesheet.fringePay) - parseFloat(timesheet.cashFringes)).toFixed(2),
        overtimeHours: (timesheet?.overtimeHours || []).map((hours: string, index: number) =>
          (parseFloat(hours) + (timesheet?.dotHours?.[index] ? parseFloat(timesheet.dotHours[index]) : 0)).toFixed(2)
        ),
        totalOvertimeHours: this.calculateTotalOvertimeAndDoubleOvertimeHours(
          timesheet.overtimeHours,
          timesheet.dotHours
        ),
        aggregateDeductions,
        eligibleFringeBenefits: uniqueBenefits,
        deductions: mappedDeductions,
      };
    });

    // calculate total pages based on the pagination logic:
    // first page: 3 rows
    // subsequent pages: 6 rows each
    const finalTimesheets = [...formattedTimesheets];
    const timesheetsLength = finalTimesheets.length;

    // Calculate total pages correctly
    const totalPages = timesheetsLength <= 3 ? 1 : Math.ceil((timesheetsLength - 3) / 6) + 1;

    return {
      weekEndDay,
      weekEndMonth,
      weekEndYear,
      weekStartDay,
      weekStartMonth,
      weekStartYear,
      startWeekDate,
      dateFirstStartedWorkingOnProject: formattedEarliestClockIn,
      ...data,
      timesheets: finalTimesheets,
      weekDaysNames: data.weekDates.map((date) => date.format("dd").slice(0, 1)),
      weekDays: data.weekDates.map((date) => date.format("DD")),
      page: 1,
      totalPages,
    };
  }
}
