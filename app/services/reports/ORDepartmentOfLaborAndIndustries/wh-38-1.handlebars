<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet">

  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
      font-size: 9px;
    }
    .checkbox {
      width: 10px;
      height: 10px;
    }
    .form-field {
      background-color: #f2f4ff;
    }
    .diagonal-line {
      position: relative;
      background: linear-gradient(to top left, transparent 49.5%, black 49.5%, black 50.5%, transparent 50.5%);
    }
    .diagonal-line-top-left {
      position: absolute;
      top: 5px;
      left: 5px;
    }
    .diagonal-line-bottom-right {
      position: absolute;
      bottom: 5px;
      right: 5px;
    }
    /* Override Tailwind's text-xs class */
    .text-xs {
      font-size: 8.5px !important;
      line-height: 9px !important;
    }
    /* New styles with more specific names to avoid Tailwind conflicts */
    .custom-text-xs {
      font-size: 7px !important;
    }
    .custom-text-sm {
      font-size: 8px !important;
    }
    .custom-text-tiny {
      font-size: 6px !important;
    }
  </style>
</head>

<body class="p-4">
  <div class="w-full">
    <!-- Header Section -->
    <div class="flex justify-between border-black pb-4">
      <div class="font-bold text-xs">
        BUREAU OF LABOR AND INDUSTRIES<br>
        WAGE AND HOUR DIVISION
      </div>
      <div class="text-right font-bold text-xs">
        PAYROLL/CERTIFIED STATEMENT FORM WH-38<br>
        FOR USE IN COMPLYING WITH ORS 279C.845*
      </div>
    </div>

    <!-- Contractor Info Section -->
    <div class="flex border-black">
      <div class="w-1/3 border-black">
        <div class="flex items-center h-4">
          <span class="text-xs">PRIME CONTRACTOR</span>
          <input type="checkbox" class="checkbox ml-1 mr-4" {{#unless isSubcontractor}} checked {{/unless}} />
          <span class="text-xs">SUBCONTRACTOR</span>
          <input type="checkbox" class="checkbox ml-1" {{#if isSubcontractor}} checked {{/if}} />
        </div>
        <div class="mt-1 border-t border-black flex items-center">
          <div class="text-xs whitespace-nowrap border-l border-black pl-1">Business Name (DBA):</div>
          <div class="form-field h-5 w-full ml-1">{{name}}</div>
        </div>
      </div>
      <div class="w-1/3 border-black">
        <div class="flex items-center h-4">
          <span class="text-xs whitespace-nowrap">PAYROLL NO.</span>
          <div class="form-field ml-1" style="width: 60%;">{{payrollNumber}}</div>
        </div>
        <div class="mt-1 border-t border-black flex items-center whitespace-nowrap">
          <span class="text-xs whitespace-nowrap">Phone: (</span>
          <div class="form-field h-5 w-12 inline-block"></div>
          <span class="text-xs whitespace-nowrap">)</span>
          <div class="form-field h-5 w-32 inline-block ml-1"></div>
        </div>
      </div>
      <div class="w-1/3">
        <div class="flex items-center h-4">
          <span class="text-xs">FINAL PAYROLL</span>
          <input type="checkbox" class="checkbox ml-1" {{#if isLastCertifiedPayrollReport}} checked {{/if}} />
        </div>
        <div class="mt-1 border-t border-black flex items-center">
          <div class="text-xs whitespace-nowrap">CCB Registration Number:</div>
          <div class="form-field h-5 w-full ml-1 border-r border-black pr-1">{{ccbRegistrationNumber}}</div>
        </div>
      </div>
    </div>

    <!-- Project Info Section - Full width for Project Name, Number, Type of Work -->
    <div class="flex border-b border-black">
      <div class="w-1/3">
        <div class="flex items-center pt-1">
          <div class="text-xs whitespace-nowrap border-l border-black pl-1">Project Name:</div>
          <div class="form-field h-5 w-full ml-1">{{projectName}}</div>
        </div>
      </div>
      <div class="w-1/3">
        <div class="flex items-center pt-1">
          <div class="text-xs whitespace-nowrap">Project Number:</div>
          <div class="form-field h-5 w-full ml-1">{{projectNumber}}</div>
        </div>
      </div>
      <div class="w-1/3">
        <div class="flex items-center pt-1">
          <div class="text-xs whitespace-nowrap">Type of Work:</div>
          <div class="form-field h-5 w-full ml-1 border-r border-black pr-1">{{projectTypeOfWork}}</div>
        </div>
      </div>
    </div>

    <!-- Address and Location Section - 50/50 split -->
    <div class="flex border-b border-x p-1 border-black">
      <div class="w-1/2 border-r border-black">
        <div class="mt-1 flex items-center">
          <div class="text-xs whitespace-nowrap">Street Address:</div>
          <div class="form-field h-5 w-full ml-1">{{companyAddress}}</div>
        </div>
        <div class="mt-1 flex items-center pt-1">
          <div class="text-xs whitespace-nowrap">Mailing Address:</div>
          <div class="form-field h-5 w-full ml-1">{{#if mailingAddress}}{{mailingAddress}}{{else}}{{companyAddress}}{{/if}}</div>
        </div>
      </div>
      <div class="w-1/2 px-1">
        <div class="mt-1 flex items-center">
          <div class="text-xs whitespace-nowrap">Project Location:</div>
          <div class="form-field h-5 w-full ml-1">{{projectLocation}}</div>
        </div>
        <div class="mt-1 flex items-center pt-1">
          <div class="text-xs whitespace-nowrap">Project County:</div>
          <div class="form-field h-5 w-full ml-1">{{projectCounty}}</div>
        </div>
      </div>
    </div>

    <!-- Date Pay Period Section -->
    <div class="flex border-b border-x p-1 border-black">
      <div class="w-1/2">
        <div class="flex items-center">
          <div class="text-xs whitespace-nowrap">Date Pay Period Began:</div>
          <div class="form-field h-5 w-full ml-1">{{startWeekDate}}</div>
        </div>
      </div>
      <div class="w-1/2 px-1">
        <div class="flex items-center">
          <div class="text-xs whitespace-nowrap">Date Pay Period Ended:</div>
          <div class="form-field h-5 w-full ml-1">{{endWeekDate}}</div>
        </div>
      </div>
    </div>

    <!-- Date Section -->
    <div class="flex border-b border-x p-1 border-black">
      <div class="w-1/2 border-r border-black">
        <div class="text-center font-bold text-xs mt-1 border-b border-black">THIS SECTION FOR PRIME CONTRACTORS ONLY</div>
        <div class="flex mt-1">
          <div class="w-full flex items-center">
            <div class="text-xs whitespace-nowrap">Public Contracting Agency Name:</div>
            <div class="form-field h-5 w-full ml-1">{{publicContractingAgencyName}}</div>
          </div>
        </div>
        <div class="flex mt-1 items-center whitespace-nowrap">
          <span class="text-xs whitespace-nowrap">Phone: (</span>
          <div class="form-field h-5 w-12 inline-block">{{publicContractingAgencyPhoneAreaCode}}</div>
          <span class="text-xs whitespace-nowrap">)</span>
          <div class="form-field h-5 w-32 inline-block ml-1">{{publicContractingAgencyPhone}}</div>
        </div>
        <div class="flex mt-1 items-center">
          <div class="text-xs whitespace-nowrap">Date Contract Specifications First Advertised for Bid:</div>
          <div class="form-field h-5 w-full ml-1">{{publicContractingAgencyDateContractSpecificationsFirstAdvertisedForBid}}</div>
        </div>
        <div class="flex mt-1 items-center">
          <div class="text-xs whitespace-nowrap">Contract Amount:</div>
          <div class="form-field h-5 w-full ml-1">{{publicContractingAgencyContractAmount}}</div>
        </div>
      </div>
      <div class="w-1/2 px-1">
        <div class="text-center font-bold text-xs border-b border-black">THIS SECTION FOR SUBCONTRACTORS ONLY</div>
        <div class="mt-1 flex items-center">
          <div class="text-xs whitespace-nowrap">Subcontract Amount:</div>
          <div class="form-field h-5 w-full ml-1">{{#if isSubcontractor}}{{subcontractAmount}}{{/if}}</div>
        </div>
        <div class="mt-1 flex items-center">
          <div class="text-xs whitespace-nowrap">Prime Contractor Business Name (DBA):</div>
          <div class="form-field h-5 w-full ml-1">{{#if isSubcontractor}}{{prevailingWagePrimeContractorName}}{{/if}}</div>
        </div>
        <div class="mt-1 flex items-center whitespace-nowrap">
          <span class="text-xs whitespace-nowrap">Prime Contractor Phone: (</span>
          <div class="form-field h-5 w-12 inline-block">{{#if isSubcontractor}}{{primeContractorPhoneAreaCode}}{{/if}}</div>
          <span class="text-xs whitespace-nowrap">)</span>
          <div class="form-field h-5 w-32 inline-block ml-1">{{#if isSubcontractor}}{{primeContractorPhone}}{{/if}}</div>
        </div>
        <div class="mt-1 flex items-center">
          <div class="text-xs whitespace-nowrap">Prime Contractor's CCB Registration Number:</div>
          <div class="form-field h-5 w-full ml-1">{{#if isSubcontractor}}{{primeContractorCcbRegistrationNumber}}{{/if}}</div>
        </div>
        <div class="mt-1 flex items-center">
          <div class="text-xs whitespace-nowrap">Date You Began Work on the Project:</div>
          <div class="form-field h-5 w-full ml-1">{{#if isSubcontractor}}{{dateFirstStartedWorkingOnProject}}{{/if}}</div>
        </div>
      </div>
    </div>

    <!-- Table Header -->
    <div class="flex border-x border-b border-black text-center custom-text-xs font-bold min-h-24">
      <div class="w-[10%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(1)</div>
        <div class="pt-1">NAME, ADDRESS AND EMPLOYEE'S IDENTIFICATION NUMBER</div>
      </div>
      <div class="w-[10%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(2)</div>
        <div class="pt-1">CLASSIFICATION (INCLUDE GROUP AND APPRENTICESHIP STEP IF APPLICABLE)</div>
      </div>
      <div class="w-[25%] border-r border-black">
        <div class="border-b border-black pb-2">(3) DAY AND DATE</div>
          <div class="flex h-[30%] w-full">
            <div class="w-[15%] border-r border-black text-center flex items-center justify-center"></div>
            <div class="w-[85%] grid grid-cols-7 border-b border-black">
              <div class="border-r border-black">{{weekDaysNames.[0]}}</div>
              <div class="border-r border-black">{{weekDaysNames.[1]}}</div>
              <div class="border-r border-black">{{weekDaysNames.[2]}}</div>
              <div class="border-r border-black">{{weekDaysNames.[3]}}</div>
              <div class="border-r border-black">{{weekDaysNames.[4]}}</div>
              <div class="border-r border-black">{{weekDaysNames.[5]}}</div>
              <div class="border-black">{{weekDaysNames.[6]}}</div>
            </div>
          </div>

          <!-- ST Row - takes up 3/8 of parent height -->
          <div class="flex h-[30%] border-b border-black w-full">
            <div class="w-[15%] border-r border-black text-center flex items-center justify-center"></div>
            <div class="w-[85%] grid grid-cols-7">
              <div class="border-r border-black">{{weekDays.[0]}}</div>
              <div class="border-r border-black">{{weekDays.[1]}}</div>
              <div class="border-r border-black">{{weekDays.[2]}}</div>
              <div class="border-r border-black">{{weekDays.[3]}}</div>
              <div class="border-r border-black">{{weekDays.[4]}}</div>
              <div class="border-r border-black">{{weekDays.[5]}}</div>
              <div class="border-black">{{weekDays.[6]}}</div>
            </div>
          </div>
        <div class="text-center mt-1 mb-2 border-black">HOURS WORKED EACH DAY</div>
      </div>
      <div class="w-[5%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(4)</div>
        <div class="pt-1">TOTAL HOURS</div>
      </div>
      <div class="w-[5%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(5)</div>
        <div class="pt-1">HOURLY BASE RATE</div>
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(6)</div>
        <div class="pt-1">HOURLY FRINGE BENEFIT AMOUNTS PAID AS WAGES TO EMPLOYEE</div>
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(7)</div>
        <div class="pt-1">GROSS AMOUNT EARNED (see directions)</div>
      </div>
      <div class="w-[11%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(8)</div>
        <div class="pt-1">ITEMIZED DEDUCTIONS FICA, FED, STATE, ETC.</div>
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(9)</div>
        <div class="pt-1">NET WAGES PAID</div>
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="border-b border-black pb-1">(10)</div>
        <div class="pt-1">HOURLY FRINGE BENEFITS PAID TO BENEFIT PARTY, PLAN, FUND, OR PROGRAM</div>
      </div>
      <div class="w-[6%] p-1">
        <div class="border-b border-black pb-1">(11)</div>
        <div class="pt-1">NAME OF BENEFIT PARTY, PLAN, FUND, OR PROGRAM</div>
      </div>
    </div>

    <!-- Employee Rows - First Page (max 3 rows) -->
    {{#each (limit timesheets 3)}}
    <div class="flex border-x border-b border-black relative">
      <div class="w-[10%] border-r border-black p-1">
        <div class="h-24">
          {{name}}
          <br>
          {{employeeAddress}}
        </div>
      </div>
      <div class="w-[10%] border-r border-black p-1">
        <div class="h-24">{{jobClassification}}</div>
      </div>

      <!-- Combined columns 3-5 for proper alignment -->
      <div class="w-[35%] flex min-h-24">
        <!-- Column 3: Day and Date -->
        <div class="w-[71.4%] border-black h-full flex flex-col">
          <!-- OT Row - takes up 3/8 of parent height -->
          <div class="flex h-[37.5%]">
            <div class="w-[15%] border-r border-b border-black text-center flex items-center justify-center">OT</div>
            <div class="w-[85%] grid grid-cols-7 border-b border-black">
              <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[0]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[1]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[2]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[3]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[4]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[5]}}</div>
              <div class="border-black flex items-center justify-center">{{overtimeHours.[6]}}</div>
            </div>
          </div>

          <!-- ST Row - takes up 3/8 of parent height -->
          <div class="flex h-[37.5%] border-b border-black">
            <div class="w-[15%] border-r border-black text-center flex items-center justify-center">ST</div>
            <div class="w-[85%] grid grid-cols-7">
              <div class="border-r border-black flex items-center justify-center">{{regularHours.[0]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{regularHours.[1]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{regularHours.[2]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{regularHours.[3]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{regularHours.[4]}}</div>
              <div class="border-r border-black flex items-center justify-center">{{regularHours.[5]}}</div>
              <div class="border-black flex items-center justify-center">{{regularHours.[6]}}</div>
            </div>
          </div>

          <!-- Bottom 1/4 reserved for schedule -->
          <div class="h-[25%]"></div>
        </div>

        <!-- Column 4: Total Hours -->
        <div class="w-[14.25%] border-black h-full flex flex-col">
          <!-- OT Hours - takes up 3/8 of parent height -->
          <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
            {{totalOvertimeHours}}
          </div>
          <!-- ST Hours - takes up 3/8 of parent height -->
          <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
            {{totalRegularHours}}
          </div>
          <!-- Bottom 1/4 reserved for schedule -->
          <div class="h-[25%]"></div>
        </div>

        <!-- Column 5: Hourly Base Rate -->
        <div class="w-[14.35%] border-r border-black h-full flex flex-col">
          <!-- OT Rate - takes up 3/8 of parent height -->
          <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
            {{overtimeRate}}
          </div>
          <!-- ST Rate - takes up 3/8 of parent height -->
          <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
            {{baseRate}}
          </div>
          <!-- Bottom 1/4 reserved for schedule -->
          <div class="h-[25%]"></div>
        </div>
      </div>

      <!-- Schedule row that spans columns 3-5 -->
      <div class="absolute bottom-0 left-[20%] w-[35%] pb-1">
        <div class="flex items-center text-xs justify-center w-full">
          <span>Schedule: 5/8</span>
          <input type="checkbox" class="checkbox mx-1" checked />
          <span>4/10</span>
          <input type="checkbox" class="checkbox mx-1" />
          <span>; Reg. Hrly. Schd: _ _ _ to _ _ _</span>
        </div>
      </div>

      <!-- Remaining columns -->
      <div class="w-[7%] border-r border-black p-1">
        {{cashFringes}}
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="h-24 diagonal-line relative">
          <div class="diagonal-line-top-left">{{grossEarned}}</div>
          <div class="diagonal-line-bottom-right">{{grossEarnedAllProjects}}</div>
        </div>
      </div>
      <div class="w-[11%] border-r border-black p-1">
        <div class="h-24">{{aggregateDeductions}}</div>
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="h-24">{{netWagesPaid}}</div>
      </div>
      <div class="w-[7%] border-r border-black p-1">
        <div class="min-h-24">
          {{#if eligibleFringeBenefits}}
            {{#each eligibleFringeBenefits}}
              <div class="text-xs py-1 {{#unless @last}}border-b border-black{{/unless}} text-right pr-1 whitespace-nowrap">
                {{#if fringeHourlyContribution}}{{format-number fringeHourlyContribution format="0.00"}}{{else}}{{format-number amount format="0.00"}}{{/if}}
              </div>
            {{/each}}
          {{/if}}
        </div>
      </div>
      <div class="w-[6%] p-1">
        <div class="min-h-24">
          {{#if eligibleFringeBenefits}}
            {{#each eligibleFringeBenefits}}
              <div class="text-xs py-1 {{#unless @last}}border-b border-black{{/unless}} truncate whitespace-nowrap overflow-hidden">
                {{#if name}}{{name}}{{else}}{{benefitName}}{{/if}}
              </div>
            {{/each}}
          {{/if}}
        </div>
      </div>
    </div>
    {{/each}}

    <!-- Fill remaining rows with empty rows if less than 3 employees -->
    {{#if (lt (length timesheets) 3)}}
      {{#each (range 0 (subtract 3 (length timesheets)))}}
      <div class="flex border-x border-b border-black relative">
        <div class="w-[10%] border-r border-black p-1">
          <div class="h-24"></div>
        </div>
        <div class="w-[10%] border-r border-black p-1">
          <div class="h-24"></div>
        </div>

        <!-- Combined columns 3-5 for proper alignment -->
        <div class="w-[35%] flex min-h-24">
          <!-- Column 3: Day and Date -->
          <div class="w-[71.4%] border-black h-full flex flex-col">
            <!-- OT Row - takes up 3/8 of parent height -->
            <div class="flex h-[37.5%]">
              <div class="w-[15%] border-r border-b border-black text-center flex items-center justify-center">OT</div>
              <div class="w-[85%] grid grid-cols-7 border-b border-black">
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div></div>
              </div>
            </div>

            <!-- ST Row - takes up 3/8 of parent height -->
            <div class="flex h-[37.5%] border-b border-black">
              <div class="w-[15%] border-r border-black text-center flex items-center justify-center">ST</div>
              <div class="w-[85%] grid grid-cols-7">
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div class="border-r border-black"></div>
                <div></div>
              </div>
            </div>

            <!-- Bottom 1/4 reserved for schedule -->
            <div class="h-[25%]"></div>
          </div>

          <!-- Column 4: Total Hours -->
          <div class="w-[14.25%] border-black h-full flex flex-col">
            <!-- OT Hours - takes up 3/8 of parent height -->
            <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
            </div>
            <!-- ST Hours - takes up 3/8 of parent height -->
            <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
            </div>
            <!-- Bottom 1/4 reserved for schedule -->
            <div class="h-[25%]"></div>
          </div>

            <!-- Column 5: Hourly Base Rate -->
            <div class="w-[14.35%] border-r border-black h-full flex flex-col">
              <!-- OT Rate - takes up 3/8 of parent height -->
              <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              </div>
              <!-- ST Rate - takes up 3/8 of parent height -->
              <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              </div>
              <!-- Bottom 1/4 reserved for schedule -->
              <div class="h-[25%]"></div>
            </div>
          </div>

          <!-- Schedule row that spans columns 3-5 -->
          <div class="absolute bottom-0 left-[20%] w-[35%] pb-1">
            <div class="flex items-center text-xs justify-center w-full">
              <span>Schedule: 5/8</span>
              <input type="checkbox" class="checkbox mx-1" />
              <span>4/10</span>
              <input type="checkbox" class="checkbox mx-1" />
              <span>; Reg. Hrly. Schd: _ _ _ to _ _ _</span>
            </div>
          </div>

          <!-- Remaining columns -->
          <div class="w-[7%] border-r border-black p-1">
          </div>
          <div class="w-[7%] border-r border-black p-1">
            <div class="h-24 diagonal-line relative">
              <div class="diagonal-line-top-left"></div>
              <div class="diagonal-line-bottom-right"></div>
            </div>
          </div>
          <div class="w-[11%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[7%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[7%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[6%] p-1">
            <div class="h-24"></div>
          </div>
        </div>
        {{/each}}
      {{/if}}

    <!-- Footer for first page -->
    <div class="text-xs mt-1">
      *Although this form has not been officially approved by the U.S. Department of Labor, it is designed to meet the requirements of both the state PWR law and the federal Davis-Bacon Act.
    </div>
    <div class="text-xs mt-1 text-center font-bold">
      THIS FORM CONTINUED ON REVERSE
    </div>
    <div class="text-xs mt-1">
      WH-38 (Rev. 05/16)
    </div>
  </div>
</body>

</html>

<!-- Continuation pages (if more than 3 employees) -->
{{#if (gt (length timesheets) 3)}}
  {{#each (chunk (slice timesheets 3) 6)}}
  <html>
  <head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
      rel="stylesheet">

    <style>
      body {
        font-family: "Open Sans", sans-serif;
        font-optical-sizing: auto;
        font-size: 9px;
      }
      .checkbox {
        width: 10px;
        height: 10px;
      }
      .form-field {
        background-color: #f2f4ff;
      }
      .diagonal-line {
        position: relative;
        background: linear-gradient(to top left, transparent 49.5%, black 49.5%, black 50.5%, transparent 50.5%);
      }
      .diagonal-line-top-left {
        position: absolute;
        top: 5px;
        left: 5px;
      }
      .diagonal-line-bottom-right {
        position: absolute;
        bottom: 5px;
        right: 5px;
      }
      .text-xs {
        font-size: 8.5px !important;
        line-height: 9px !important;
      }
      .custom-text-xs {
        font-size: 7px !important;
      }
      .custom-text-sm {
        font-size: 8px !important;
      }
      .custom-text-tiny {
        font-size: 6px !important;
      }
      .continuation-page-container {
        padding: 20px 16px; /* Increased padding for continuation pages */
      }
    </style>
  </head>

  <body class="py-4">
    <div class="continuation-page-container w-full">
      <!-- Continuation page header with more space -->
      <div class="flex justify-between border-black pb-4 pt-2 mb-2">
        <div class="font-bold text-xs">
          BUREAU OF LABOR AND INDUSTRIES<br>
          WAGE AND HOUR DIVISION
        </div>
        <div class="text-right font-bold text-xs">
          PAYROLL/CERTIFIED STATEMENT FORM WH-38 (CONTINUATION)<br>
          FOR USE IN COMPLYING WITH ORS 279C.845*
        </div>
      </div>

      <!-- Employee rows for continuation page (up to 6 per page) -->
      {{#each this}}
      <div class="flex border-x border-b border-t border-black relative">
        <div class="w-[10%] border-r border-black p-1">
          <div class="h-24">
            {{name}}
            <br>
            {{employeeAddress}}
          </div>
        </div>
        <div class="w-[10%] border-r border-black p-1">
          <div class="h-24">{{jobClassification}}</div>
        </div>

        <!-- Combined columns 3-5 for proper alignment -->
        <div class="w-[35%] flex min-h-24">
          <!-- Column 3: Day and Date -->
          <div class="w-[71.4%] border-black h-full flex flex-col">
            <!-- OT Row - takes up 3/8 of parent height -->
            <div class="flex h-[37.5%]">
              <div class="w-[15%] border-r border-b border-black text-center flex items-center justify-center">OT</div>
              <div class="w-[85%] grid grid-cols-7 border-b border-black">
                <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[0]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[1]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[2]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[3]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[4]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{overtimeHours.[5]}}</div>
                <div class="border-black flex items-center justify-center">{{overtimeHours.[6]}}</div>
              </div>
            </div>

            <!-- ST Row - takes up 3/8 of parent height -->
            <div class="flex h-[37.5%] border-b border-black">
              <div class="w-[15%] border-r border-black text-center flex items-center justify-center">ST</div>
              <div class="w-[85%] grid grid-cols-7">
                <div class="border-r border-black flex items-center justify-center">{{regularHours.[0]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{regularHours.[1]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{regularHours.[2]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{regularHours.[3]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{regularHours.[4]}}</div>
                <div class="border-r border-black flex items-center justify-center">{{regularHours.[5]}}</div>
                <div class="border-black flex items-center justify-center">{{regularHours.[6]}}</div>
              </div>
            </div>

            <!-- Bottom 1/4 reserved for schedule -->
            <div class="h-[25%]"></div>
          </div>

          <!-- Column 4: Total Hours -->
          <div class="w-[14.25%] border-black h-full flex flex-col">
            <!-- OT Hours - takes up 3/8 of parent height -->
            <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              {{totalOvertimeHours}}
            </div>
            <!-- ST Hours - takes up 3/8 of parent height -->
            <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              {{totalRegularHours}}
            </div>
            <!-- Bottom 1/4 reserved for schedule -->
            <div class="h-[25%]"></div>
          </div>

          <!-- Column 5: Hourly Base Rate -->
          <div class="w-[14.35%] border-r border-black h-full flex flex-col">
            <!-- OT Rate - takes up 3/8 of parent height -->
            <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              {{overtimeRate}}
            </div>
            <!-- ST Rate - takes up 3/8 of parent height -->
            <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              {{calculatedHourlyRate}}
            </div>
            <!-- Bottom 1/4 reserved for schedule -->
            <div class="h-[25%]"></div>
          </div>
        </div>

        <!-- Schedule row that spans columns 3-5 -->
        <div class="absolute bottom-0 left-[20%] w-[35%] pb-1">
          <div class="flex items-center text-xs justify-center w-full">
            <span>Schedule: 5/8</span>
            <input type="checkbox" class="checkbox mx-1" checked />
            <span>4/10</span>
            <input type="checkbox" class="checkbox mx-1" />
            <span>; Reg. Hrly. Schd: _ _ _ to _ _ _</span>
          </div>
        </div>

        <!-- Remaining columns -->
        <div class="w-[7%] border-r border-black p-1">
          {{cashFringes}}
        </div>
        <div class="w-[7%] border-r border-black p-1">
          <div class="h-24 diagonal-line relative">
            <div class="diagonal-line-top-left">{{grossEarned}}</div>
            <div class="diagonal-line-bottom-right">{{grossEarnedAllProjects}}</div>
          </div>
        </div>
        <div class="w-[11%] border-r border-black p-1">
          <div class="h-24">{{aggregateDeductions}}</div>
        </div>
        <div class="w-[7%] border-r border-black p-1">
          <div class="h-24">{{netWagesPaid}}</div>
        </div>
        <div class="w-[7%] border-r border-black p-1">
          <div class="min-h-24">
            {{#if eligibleFringeBenefits}}
              {{#each eligibleFringeBenefits}}
                <div class="text-xs py-1 {{#unless @last}}border-b border-black{{/unless}} text-right pr-1 whitespace-nowrap">
                  {{#if fringeHourlyContribution}}{{format-number fringeHourlyContribution format="0.00"}}{{else}}{{format-number amount format="0.00"}}{{/if}}
                </div>
              {{/each}}
            {{/if}}
          </div>
        </div>
        <div class="w-[6%] p-1">
          <div class="min-h-24">
            {{#if eligibleFringeBenefits}}
              {{#each eligibleFringeBenefits}}
                <div class="text-xs py-1 {{#unless @last}}border-b border-black{{/unless}} truncate whitespace-nowrap overflow-hidden">
                  {{#if name}}{{name}}{{else}}{{benefitName}}{{/if}}
                </div>
              {{/each}}
            {{/if}}
          </div>
        </div>
      </div>
      {{/each}}

      <!-- Fill remaining rows with empty rows if less than 6 employees on this page -->
      {{#if (lt (length this) 6)}}
        {{#each (range 0 (subtract 6 (length this)))}}
        <div class="flex border-x border-b border-black relative">
          <div class="w-[10%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[10%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>

          <!-- Combined columns 3-5 for proper alignment -->
          <div class="w-[35%] flex min-h-24">
            <!-- Column 3: Day and Date -->
            <div class="w-[71.4%] border-black h-full flex flex-col">
              <!-- OT Row - takes up 3/8 of parent height -->
              <div class="flex h-[37.5%]">
                <div class="w-[15%] border-r border-b border-black text-center flex items-center justify-center">OT</div>
                <div class="w-[85%] grid grid-cols-7 border-b border-black">
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div></div>
                </div>
              </div>

              <!-- ST Row - takes up 3/8 of parent height -->
              <div class="flex h-[37.5%] border-b border-black">
                <div class="w-[15%] border-r border-black text-center flex items-center justify-center">ST</div>
                <div class="w-[85%] grid grid-cols-7">
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div class="border-r border-black"></div>
                  <div></div>
                </div>
              </div>

              <!-- Bottom 1/4 reserved for schedule -->
              <div class="h-[25%]"></div>
            </div>

            <!-- Column 4: Total Hours -->
            <div class="w-[14.25%] border-black h-full flex flex-col">
              <!-- OT Hours - takes up 3/8 of parent height -->
              <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              </div>
              <!-- ST Hours - takes up 3/8 of parent height -->
              <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              </div>
              <!-- Bottom 1/4 reserved for schedule -->
              <div class="h-[25%]"></div>
            </div>

            <!-- Column 5: Hourly Base Rate -->
            <div class="w-[14.35%] border-r border-black h-full flex flex-col">
              <!-- OT Rate - takes up 3/8 of parent height -->
              <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              </div>
              <!-- ST Rate - takes up 3/8 of parent height -->
              <div class="text-center border-b border-l border-black h-[37.5%] flex items-center justify-center">
              </div>
              <!-- Bottom 1/4 reserved for schedule -->
              <div class="h-[25%]"></div>
            </div>
          </div>

          <!-- Schedule row that spans columns 3-5 -->
          <div class="absolute bottom-0 left-[20%] w-[35%] pb-1">
            <div class="flex items-center text-xs justify-center w-full">
              <span>Schedule: 5/8</span>
              <input type="checkbox" class="checkbox mx-1" />
              <span>4/10</span>
              <input type="checkbox" class="checkbox mx-1" />
              <span>; Reg. Hrly. Schd: _ _ _ to _ _ _</span>
            </div>
          </div>

          <!-- Remaining columns -->
          <div class="w-[7%] border-r border-black p-1">
          </div>
          <div class="w-[7%] border-r border-black p-1">
            <div class="h-24 diagonal-line relative">
              <div class="diagonal-line-top-left"></div>
              <div class="diagonal-line-bottom-right"></div>
            </div>
          </div>
          <div class="w-[11%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[7%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[7%] border-r border-black p-1">
            <div class="h-24"></div>
          </div>
          <div class="w-[6%] p-1">
            <div class="h-24"></div>
          </div>
        </div>
        {{/each}}
      {{/if}}

      <!-- Footer for continuation pages with more space -->
      <div class="text-xs mt-4">
        *Although this form has not been officially approved by the U.S. Department of Labor, it is designed to meet the requirements of both the state PWR law and the federal Davis-Bacon Act.
      </div>
      <div class="text-xs mt-2 mb-4">
        WH-38 (Rev. 05/16)
      </div>
    </div>
  </body>
  </html>
  {{/each}}
{{/if}}
