import fs from "fs";
import dayjs from "dayjs";
import PDFMerger from "pdf-merger-js";
import { join } from "node:path";
import { Response } from "express";
import { Report } from "@/services/reports/Report";
import { getJsReportService, JsReportService, releaseJsReportService } from "@/services/reports/JsReportService";
import Organization from "@/models/organization";
import User, { ETHNICITY } from "@/models/user";
import Project from "@/models/project";
import TimeSheet, { ExtendedTimesheet } from "@/models/timesheet";
import HTTPError from "@/errors/HTTPError";
import { TimesheetsService } from "@/services/timesheets";
import { CheckService } from "@/services/check";
import { calculateTotalWorkedMinutes } from "@/util/timesheetHelper";

// Helper function to calculate total hours from timesheet entries
function calculateTotalHours(timesheets: TimeSheet[]): number {
  return timesheets.reduce((total, timesheet) => total + calculateTotalWorkedMinutes(timesheet) / 60, 0);
}

interface ReportParams {
  projectId: number;
  startDate: string; // Expect 'YYYY-MM-DD'
  endDate: string; // Expect 'YYYY-MM-DD'
  percentOfWorkCompleted: number;
  tradeOrCraft?: string; // Optional as it might be hardcoded
  signatoryName: string;
  signatoryTitle: string;
}

export default class WorkforceDemographicsReport extends Report<ReportParams> {
  private jsReportService: JsReportService;
  private jsReportOptions = { httpPort: 5008 };
  private timesheetsService: TimesheetsService;
  private checkService: CheckService;

  constructor(response: Response) {
    super(response);
    this.jsReportService = getJsReportService(this.jsReportOptions);
    this.timesheetsService = new TimesheetsService();
    this.checkService = new CheckService();
  }

  async report(organization: Organization, params: ReportParams): Promise<Response> {
    const { projectId, startDate, endDate, percentOfWorkCompleted, signatoryName, signatoryTitle } = params;

    const project = await Project.findOne({
      where: {
        id: projectId,
        organizationId: organization.id,
      },
    });

    if (!project) {
      throw new HTTPError(404, "Project not found");
    }

    // Convert dates to timestamps for the service call
    // Assuming startDate and endDate are 'YYYY-MM-DD'
    const startTimestamp = dayjs.tz(startDate, organization.timezone).startOf("day").valueOf();
    const endTimestamp = dayjs.tz(endDate, organization.timezone).endOf("day").valueOf();

    // --- Fetch Timesheets using TimesheetsService.list ---
    const periodResponse = await this.timesheetsService.list(organization, {
      projectId: projectId.toString(),
      from: startTimestamp,
      to: endTimestamp,
    });
    // Filter here for valid clockIn/Out times needed for calculations
    const timesheetsPeriod = periodResponse.timesheets as ExtendedTimesheet[];

    const cumulativeResponse = await this.timesheetsService.list(organization, {
      projectId: projectId.toString(),
      // No date filter for cumulative
    });
    // Filter here for valid clockIn/Out times needed for calculations
    const timesheetsCumulative = cumulativeResponse.timesheets as ExtendedTimesheet[];

    const minorityEthnicities: typeof ETHNICITY[number][] = [
      "BLACK",
      "HISPANIC",
      "AMERICAN_INDIAN",
      "ASIAN",
      "OTHER_PACIFIC_ISLANDER",
      "TWO_OR_MORE",
    ];

    // Filter unique employees within the period
    const employeesInPeriod = Array.from(
      new Map(timesheetsPeriod.map((ts) => [ts.workerId, ts.user])).values()
    ) as User[];

    // Calculate Employee Counts (Column 11)
    const employeeCounts = {
      total: employeesInPeriod.length,
      black: employeesInPeriod.filter((emp) => emp?.ethnicity === "BLACK").length,
      hispanic: employeesInPeriod.filter((emp) => emp?.ethnicity === "HISPANIC").length,
      americanIndian: employeesInPeriod.filter((emp) => emp?.ethnicity === "AMERICAN_INDIAN").length,
      asian: employeesInPeriod.filter((emp) => emp?.ethnicity === "ASIAN").length,
      female: employeesInPeriod.filter((emp) => emp?.gender === "FEMALE").length,
    };

    // Calculate Minority Employee Count (Column 12)
    const minorityEmployeeCount =
      employeeCounts.black + employeeCounts.hispanic + employeeCounts.americanIndian + employeeCounts.asian; // Note: Based on spec, includes B-E

    // Calculate Work Hours (Column 13 & 15)
    const calculateHoursForGroup = (timesheets: ExtendedTimesheet[], filterFn: (user: User) => boolean): number => {
      const filteredTimesheets = timesheets.filter((ts) => filterFn(ts.user));

      return calculateTotalHours(filteredTimesheets);
    };

    const periodHoursRaw = {
      total: calculateTotalHours(timesheetsPeriod),
      minority: calculateHoursForGroup(timesheetsPeriod, (user) => minorityEthnicities.includes(user.ethnicity)),
      female: calculateHoursForGroup(timesheetsPeriod, (user) => user.gender === "FEMALE"),
    };

    const { historical_total_work_hours, historical_minority_hours, historical_female_hours } = project.metadata || {};

    const cumulativeHoursRaw = {
      total: (Number(historical_total_work_hours) || 0) + calculateTotalHours(timesheetsCumulative),
      minority:
        (Number(historical_minority_hours) || 0) +
        calculateHoursForGroup(timesheetsCumulative, (user) => minorityEthnicities.includes(user.ethnicity)),
      female:
        (Number(historical_female_hours) || 0) +
        calculateHoursForGroup(timesheetsCumulative, (user) => user.gender === "FEMALE"),
    };

    // --- Format Data for Template ---
    const formatNum = (num?: number): string => (num ? num.toFixed(2) : "");

    const calcPercent = (count: number, total: number): string => {
      const percentage = total && count ? (count / total) * 100 : 0;

      return percentage.toFixed(2).replace(/[.,]00$/, "");
    };

    const formattedPeriodHours = {
      total: formatNum(periodHoursRaw.total),
      minority: formatNum(periodHoursRaw.minority),
      female: formatNum(periodHoursRaw.female),
      minorityPercent: calcPercent(periodHoursRaw.minority, periodHoursRaw.total),
      femalePercent: calcPercent(periodHoursRaw.female, periodHoursRaw.total),
    };

    const formattedCumulativeHours = {
      total: formatNum(cumulativeHoursRaw.total),
      minority: formatNum(cumulativeHoursRaw.minority),
      female: formatNum(cumulativeHoursRaw.female),
      minorityPercent: calcPercent(cumulativeHoursRaw.minority, cumulativeHoursRaw.total),
      femalePercent: calcPercent(cumulativeHoursRaw.female, cumulativeHoursRaw.total),
    };

    const checkCompany = await this.checkService.get(`/companies/${organization.checkCompanyId}`);

    const companyAddress = `${checkCompany.address.line1}${
      checkCompany.address.line2 ? ", " + checkCompany.address.line2 : ""
    }, ${checkCompany.address.city}, ${checkCompany.address.state} ${checkCompany.address.postal_code}, ${
      checkCompany.address.country
    }`;

    // Prepare final data structure for the template
    const templateData = {
      // Column 1 & 3 related
      contractorName: project.prevailingWageIsSubcontractor
        ? project.prevailingWagePrimeContractorName
        : organization.name,
      contractorAddress: project.prevailingWageIsSubcontractor
        ? project.prevailingWagePrimeContractorAddress
        : companyAddress,
      contractorIdNumber: "", //organization.checkCompanyId || "",
      fIdOrSsNumber: project.prevailingWageIsSubcontractor ? "" : organization.metadata?.ssNumber || "",

      // Column 4
      reportingPeriodStartDate: dayjs(startDate).format("MM/DD/YYYY"),
      reportingPeriodEndDate: dayjs(endDate).format("MM/DD/YYYY"),

      // Column 5 - Blank for now
      publicAgencyAwardingContract: "",
      dateOfAward: "",

      // Column 6
      projectName: project.name,
      projectLocation: project.address || "", // Use project address field
      projectCounty: "", // Placeholder for Project County

      // Column 7
      projectIdNumber: project.projectNumber || "",

      // Column 8
      organizationName: organization.name,

      // Column 9
      percentWorkCompleted: formatNum(percentOfWorkCompleted),

      // Column 10
      // tradeCraft: organization.id === 76 ? "Painter" : tradeOrCraft || "", // Handle special case and default
      tradeCraft: params.tradeOrCraft || "",

      // Column 11 - Employee Counts (use raw counts)
      empCounts: employeeCounts,

      // Column 12 - Minority Employee Count (use raw count)
      minorityEmpCount: minorityEmployeeCount,

      // Column 13 & 14 - Period Work Hours & % (use formatted object)
      periodHours: formattedPeriodHours,

      // Column 15 & 16 - Cumulative Work Hours & % (use formatted object)
      cumulativeHours: formattedCumulativeHours,

      // Signatory Info
      signatoryName: signatoryName,
      signatoryTitle: signatoryTitle,
      reportDate: dayjs().tz(organization.timezone).format("MM/DD/YYYY"), // Current date formatted
    };

    // Create a new PDF merger instance
    const merger = new PDFMerger();

    // Read the handlebars template
    const aa202Template = fs.readFileSync(join(__dirname, "aa-202.handlebars"), "utf8");

    // Render the PDF
    const aa202Pdf = await this.jsReportService.render(
      {
        content: aa202Template,
        engine: "handlebars",
        recipe: "chrome-pdf",
        chrome: {
          landscape: true, // AA-202 is typically landscape
          waitForNetworkIdle: true,
          timeout: 60000,
          printBackground: true,
        },
      },
      templateData // Pass the final prepared data
    );

    await merger.add(aa202Pdf.content);

    // Construct title string separately
    const reportTitle = `Monthly Project Workforce Report (AA-202) - ${project.name} - ${startDate} to ${endDate}`;

    await merger.setMetadata({
      producer: "Hammr",
      title: reportTitle, // Use the variable
    });

    try {
      return this.getResponse(await merger.saveAsBuffer(), "pdf", "application/pdf", "AA-202");
    } finally {
      releaseJsReportService(this.jsReportOptions);
    }
  }
}
