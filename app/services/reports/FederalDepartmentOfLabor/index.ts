import { CertifiedPayrollData } from "@/services/reports";
import { join } from "node:path";
import { Report } from "@/services/reports/Report";
import PDFMerger from "pdf-merger-js";
import fs from "fs";
import { Organization } from "@/models";
import dayjs from "dayjs";
import { JsReportService, getJsReportService, releaseJsReportService } from "@/services/reports/JsReportService";
import { Response } from "express";

export default class FederalDepartmentOfLabor extends Report {
  private jsReportService: JsReportService;
  private jsReportOptions = { httpPort: 5001 };

  constructor(response: Response) {
    super(response);
    this.jsReportService = getJsReportService(this.jsReportOptions);

    // Register custom handlebars helpers
    this.jsReportService.getJsReport().beforeRenderListeners.add("custom-helpers", (req, _res) => {
      req.template.helpers = `
          function paginateTimesheets(timesheets) {
            // a simpler, more explicit pagination approach
            const pages = [];
            const itemsPerFirstPage = 8;
            const itemsPerSubsequentPage = 11;

            // If we have no data, return empty array
            if (timesheets.length === 0) {
              return pages;
            }

            // handle first page (up to 8 items)
            const firstPageCount = Math.min(itemsPerFirstPage, timesheets.length);
            pages.push(timesheets.slice(0, firstPageCount));

            // if we have more items, paginate the rest with 11 per page
            let currentIndex = firstPageCount;
            while (currentIndex < timesheets.length) {
              const remainingItems = timesheets.length - currentIndex;
              const itemsForThisPage = Math.min(itemsPerSubsequentPage, remainingItems);
              pages.push(timesheets.slice(currentIndex, currentIndex + itemsForThisPage));
              currentIndex += itemsForThisPage;
            }

            return pages;
          }
      `;
    });
  }

  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transformForPdfTemplate(this.transform(organization, payrollData));

    // we can also attach this to the node app and load it on app start
    const merger = new PDFMerger();

    const wh347Page1 = fs.readFileSync(join(__dirname, "wh347-1.handlebars"), "utf8");
    const wh347Page2 = fs.readFileSync(join(__dirname, "wh347-2.handlebars"), "utf8");

    const [wh347PdfPage1, wh347PdfPage2] = await Promise.all(
      [wh347Page1, wh347Page2].map(async (template) => {
        return this.jsReportService.render<typeof data>(
          {
            content: template,
            engine: "handlebars",
            recipe: "chrome-pdf",
            chrome: {
              landscape: true,
            },
          },
          data
        );
      })
    );
    await merger.add(wh347PdfPage1.content);
    await merger.add(wh347PdfPage2.content);

    // Set metadata
    await merger.setMetadata({
      producer: "Hammr",
      title: "Certified payroll report",
    });

    try {
      return this.getResponse(await merger.saveAsBuffer(), "pdf", "application/pdf");
    } finally {
      // Release the service when done
      releaseJsReportService(this.jsReportOptions);
    }
  }

  transformForPdfTemplate(data: ReturnType<Report["transform"]>) {
    const { endWeekDate } = data;

    const [weekEndDay, weekEndMonth, weekEndYear] = dayjs(endWeekDate).format("DD-MM-YYYY").split("-");
    const [weekStartDay, weekStartMonth, weekStartYear] = dayjs(endWeekDate)
      .subtract(6, "days")
      .format("DD-MM-YYYY")
      .split("-");

    const deductionsSet = new Set();

    data.timesheets.forEach((timesheet: any) => {
      timesheet.deductions.map((deduction: { name: string; value: string; type: string }) => {
        const deductionKey = deduction.name ?? deduction.type;
        deductionsSet.add(deductionKey);
      });
    });

    const formattedTimesheets = data.timesheets.map((timesheet: any) => {
      let name;

      if (timesheet.name) {
        name = timesheet.name;
      } else if (timesheet.employee) {
        name = timesheet.employee.firstName + " " + timesheet.employee.lastName;
      }

      if (timesheet.employee.lastFourSSN) {
        name = name + " (" + timesheet.employee.lastFourSSN + ")";
      }

      return {
        ...timesheet,
        numberOfExceptions: timesheet.numberOfExceptions > 0 ? String(timesheet.numberOfExceptions) : "",
        name,
        overtimeHours: (timesheet?.overtimeHours || []).map((hours: string, index: number) =>
          (parseFloat(hours) + (timesheet?.dotHours?.[index] ? parseFloat(timesheet.dotHours[index]) : 0)).toFixed(2)
        ),
        totalOvertimeHours: this.calculateTotalOvertimeAndDoubleOvertimeHours(
          timesheet.overtimeHours,
          timesheet.dotHours
        ),
        deductions: Array.from(deductionsSet)
          .sort(this.customDeductionsSort)
          .map((deductionName) => {
            const deduction = timesheet.deductions.find((deduction: { name: string; value: string; type: string }) => {
              // Use deduction.type as fallback if name is null/undefined
              const deductionKey = deduction.name ?? deduction.type;

              return deductionKey === deductionName;
            });

            return { name: deductionName, value: deduction ? deduction.amount : "0" };
          }),
      };
    });

    const finalTimesheets = [...formattedTimesheets];
    const timesheetsLength = finalTimesheets.length;

    // Calculate total pages based on our pagination logic:
    // First page: 8 rows max
    // Subsequent pages: 11 rows each
    const totalPages = timesheetsLength <= 8 ? 1 : Math.ceil((timesheetsLength - 8) / 11) + 1;

    return {
      weekEndDay,
      weekEndMonth,
      weekEndYear,
      weekStartDay,
      weekStartMonth,
      weekStartYear,
      ...data,
      timesheets: finalTimesheets,
      weekDaysNames: data.weekDates.map((date) => date.format("dd").slice(0, 1)),
      weekDays: data.weekDates.map((date) => date.format("DD")),
      totalPages,
      page: 1,
    };
  }
}
