<!--
Invoice dynamically rendered into html using handlebars and converted into pdf
using chrome-pdf recipe. The styles are extracted into separate asset for
better readability and later reuse.

Data to this sample are mocked at the design time and should be filled on the
incoming API request.
!-->
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet" />

  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
      font-weight: 500;
    }

    .text-xsm {
      font-size: 10px;
      line-height: 11px;
    }
  </style>
</head>

<body>
  <div class="pt-20 pb-6 w-full flex text-xsm">
    <div class="w-1/2 px-4">
      <div class="flex">
        <div class="flex items-end">
          Date
        </div>
        <div class="ml-1 bg-[#f2f4ff] border-b border-black text-center w-32 h-4 flex items-center justify-center">
          {{reportDate}}</div>
      </div>

      <div class="flex mt-2">
        <div class="leading-8">I,</div>

        <div class="h-10 ml-1 text-center w-40">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center">
            {{signerName}}
          </div>
          <div class="h-[1px] w-full bg-black"></div>
          <div class="mt-0.5">(Name of Signatory Party)</div>
        </div>

        <div class="h-10 ml-1 text-center w-40">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center">
            {{signerTitle}}
          </div>
          <div class="h-[1px] w-full bg-black"></div>
          <div class="mt-0.5">(Title)</div>
        </div>
      </div>

      <p>do hereby state:</p>
      <p class="ml-5 mt-3">(1) That I pay or supervise the payment of the persons employed by</p>

      <div class="flex">
        <div class="flex-grow mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center">
            {{name}}
          </div>
          <div class="h-[1px] w-full bg-black"></div>
          <div class="mt-0.5 text-center">(Contractor or Subcontractor)</div>

        </div>
        <div class="mx-2 leading-8">on the</div>
      </div>

      <div class="flex">
        <div class="w-1/2 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center">
            {{projectName}}
          </div>
          <div class="h-[1px] w-full bg-black"></div>
          <div class="mt-0.5 text-center">(Building or Work)</div>
        </div>
        <div class="mx-2 leading-8">; that during the payroll period commencing on the</div>
      </div>

      <div class="flex">
        <div class="w-10 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center border-black border-b">
            {{weekStartDay}}
          </div>
        </div>
        <div class="mx-2 leading-8">
          day of
        </div>

        <div class="w-16 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center border-black border-b">
            {{weekStartMonth}}
          </div>
        </div>

        <div class="ml-0.5 mr-1 leading-10">
          ,
        </div>

        <div class="w-10 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center border-black border-b">
            {{weekStartYear}}
          </div>
        </div>

        <div class="leading-8 px-0.5">
          and ending the
        </div>

        <div class="w-10 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center border-black border-b">
            {{weekEndDay}}
          </div>
        </div>
        <div class="mx-2 leading-8">
          day of
        </div>

        <div class="w-16 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center border-black border-b">
            {{weekEndMonth}}
          </div>
        </div>

        <div class="ml-0.5 mr-1 leading-10">
          ,
        </div>

        <div class="w-10 mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center border-black border-b">
            {{weekEndYear}}
          </div>
        </div>

        <div class="ml-0.5 mr-1 leading-10">
          ,
        </div>

      </div>

      <p class="mt-2">
        all persons employed on said project have been paid the full weekly wages earned, that no rebates
        have been or will be made either directly or indirectly to or on behalf of said
      </p>

      <div class="flex">
        <div class="flex-grow mt-1">
          <div class="bg-[#f2f4ff] h-5 flex items-center justify-center">
            {{name}}
          </div>
          <div class="h-[1px] w-full bg-black"></div>
          <div class="mt-0.5 text-center">(Contractor or Subcontractor)</div>

        </div>
        <div class="mx-2 leading-8">from the full</div>

      </div>

      <p>weekly wages earned by any person and that no deductions have been made either directly or
        indirectly from the full wages earned by any person, other than permissible deductions as defined in
        Regulations, Part 3 (29 C.F.R. Subtitle A), issued by the Secretary of Labor under the Copeland Act,
        as amended (48 Stat. 948, 63 Stat. 108, 72 Stat. 967; 76 Stat. 357; 40 U.S.C. § 3145), and described
        below:
      </p>

      <div class="bg-[#f2f4ff] h-6 flex items-center justify-center border-b border-black mt-2">
      </div>

      <div class="bg-[#f2f4ff] h-6 flex items-center justify-center border-b border-black mt-2">
      </div>
      <div class="bg-[#f2f4ff] h-6 flex items-center justify-center border-b border-black mt-2">
      </div>
      <div class="bg-[#f2f4ff] h-6 flex items-center justify-center border-b border-black mt-2">
      </div>

      <p class="mt-3">
        &ensp;&ensp;&ensp;&ensp;&ensp;(2) That any payrolls otherwise under this contract required to be
        submitted for the above period are correct and complete; that the wage rates for laborers or
        mechanics contained therein are not less than the applicable wage rates contained in any wage
        determination incorporated into the contract; that the classifications set forth therein for each
        laborer or mechanic conform with the work he performed.
      </p>

      <p class="mt-3">
        &ensp;&ensp;&ensp;&ensp;&ensp;(3) That any apprentices employed in the above period are duly
        registered in a bona fide apprenticeship program registered with a State apprenticeship agency
        recognized by the Bureau of Apprenticeship and Training, United States Department of Labor, or if no
        such recognized agency exists in a State, are registered with the Bureau of Apprenticeship and
        Training, United States Department of Labor.
      </p>

      <p class="mt-3">&ensp;&ensp;&ensp;&ensp;&ensp;(4) That:</p>
      <div class="ml-9">
        <p>(a) WHERE FRINGE BENEFITS ARE PAID TO APPROVED PLANS, FUNDS, OR PROGRAMS</p>

        <div class="flex items-start gap-2 mt-2 ml-3">
          <input type="checkbox" {{#if areFringeBenefitsPaidToPlans}}checked{{/if}} />
          <div style="line-height: 0;" class="text-sm">_</div>
          <div>
            in addition to the basic hourly wage rates paid to each laborer or mechanic listed in the above
            referenced payroll, payments of fringe benefits as listed in the contract have been or will be
            made to appropriate programs for the benefit of such employees, except as noted in section 4(c)
            below.</div>
        </div>
      </div>

    </div>
    <div class="w-1/2 px-4">
      <div class="ml-9">
        <p>(b) WHERE FRINGE BENEFITS ARE PAID IN CASH
        </p>

        <div class="flex items-start gap-2 mt-2 ml-3">
          <input type="checkbox" {{#if areFringeBenefitsPaidInCash}}checked{{/if}} />
          <div style="line-height: 0;" class="text-sm">_</div>
          <div>
            Each laborer or mechanic listed in the above referenced payroll has been paid, as indicated on
            the payroll, an amount not less than the sum of the applicable basic hourly wage rate plus the
            amount of the required fringe benefits as listed in the contract, except as noted in section
            4(c) below
          </div>
        </div>
      </div>

      <div class="ml-9 mt-3">
        <p>(c) EXCEPTIONS
        </p>
      </div>

      <div class="border-black border mt-1">
        <div class="flex border-b border-black">
          <div class="w-1/2 h-8 flex items-center justify-center border-black border-r">
            EXCEPTION (CRAFT)
          </div>
          <div class="w-1/2 h-8 flex items-center justify-center">EXPLANATION</div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black border-b">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

        <div class="flex border-black">
          <div class="w-1/2 h-8 bg-[#f2f4ff] border-r border-black pt-0.5 pl-0.5"></div>
          <div class="w-1/2 h-8 bg-[#f2f4ff] pt-0.5 pl-0.5"></div>
        </div>

      </div>

      <div class="border-black border mt-1">
        <p class="ml-0.5 mt-0.5">REMARKS</p>
        <div class="bg-[#f2f4ff] h-32">{{remarks}}</div>
      </div>

      <div class="border-black border mt-1">
        <div class="flex">

          <div class="w-1/2 border-black border-r">
            <p class="mt-0.5 ml-0.5">NAME AND TITLE</p>
            <div class="bg-[#f2f4ff] h-10 pl-0.5 pt-0.5">
              <div>

                {{signerName}}
              </div>
              <div>
                {{signerTitle}}
              </div>
            </div>
          </div>
          <div class="w-1/2">
            <p class="mt-0.5 ml-0.5">SIGNATURE</p>
            <div style="font-family:serif" class="bg-[#f2f4ff] h-10 pl-0.5 pt-0.5 italic text-sm">{{signerName}}</div>
          </div>
        </div>

        <div class="border-black border-t p-0.5">
          THE WILLFUL FALSIFICATION OF ANY OF THE ABOVE STATEMENTS MAY SUBJECT THE CONTRACTOR OR
          SUBCONTRACTOR TO CIVIL OR CRIMINAL PROSECUTION. SEE SECTION 1001 OF TITLE 18 AND SECTION 3729 OF
          TITLE 31 OF THE UNITED STATES CODE.
        </div>
      </div>

    </div>
  </div>
</body>

</html>
