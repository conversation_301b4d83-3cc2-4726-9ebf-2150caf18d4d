<!--
Invoice dynamically rendered into html using handlebars and converted into pdf
using chrome-pdf recipe. The styles are extracted into separate asset for
better readability and later reuse.

Data to this sample are mocked at the design time and should be filled on the
incoming API request.
!-->
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet">

  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
    }

    @mixin flex-wrap-fix($flex-basis, $max-viewport-width: 2000px) {
      flex-grow: 1;
      flex-basis: $flex-basis;
      max-width: 100%;

      $multiplier: 1;
      $current-width: 0px;

      @while $current-width < $max-viewport-width {
        $current-width: $current-width + $flex-basis;
        $multiplier: $multiplier + 1;

        @media(min-width: $flex-basis * $multiplier) {
          max-width: percentage(1/$multiplier);
        }
      }
    }


    .flex-wrap-fix {
      @include flex-wrap-fix(100px);
    }
  </style>
</head>

<body>
  <div class="py-6 px-4 w-full">
    <section>
      <div class="flex items-center">
        <div class="min-w-52">
          <p class="font-bold">U.S Department of Labor</p>
          <p class="text-sm">Wage and Hour Division</p>
        </div>
        <div class="flex-grow text-center self-end">
          <p class="font-bold mt-2">PAYROLL</p>
          <p class="font-bold text-xs">
            For contractor's optional use; see instructions at
            dol.gov/agencies/whd/forms/wh347
          </p>
          <p class="italic" style="font-size: 10px">
            Persons are not required to respond to the collection of
            information unless it displays a currently valid OMB control
            number.
          </p>
        </div>
        <div class="flex justify-end items-end">
          <img class="max-w-28"
            src="data:image/png;base64,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" />
        </div>
      </div>
    </section>

    <section class="border-black" style="font-size: 8.5px; line-height: 9px">
      <div class="flex border-y h-9">
        <div class="border-r w-1/2">
          <div class="flex gap-2 items-start justify-start">
            <div class="flex gap-1">
              <div>NAME OF CONTRACTOR</div>
              <input type="checkbox" {{#unless isSubcontractor}} checked {{/unless}} />
            </div>
            <div class="flex gap-1">
              <div>OR SUBCONTRACTOR</div>
              <input type="checkbox" {{#if isSubcontractor}} checked {{/if}} />
            </div>
          </div>
          <div class="mr-1 bg-[#f2f4ff] text-center h-1/2 top-0.5 relative">
            {{name}}
          </div>
        </div>
        <div class="w-1/2 flex">
          <div class="border-r flex flex-grow">
            <div>ADDRESS</div>
            <div class="pl-1 mr-1 flex-grow bg-[#f2f4ff]">{{companyAddress}}</div>
          </div>
          <div class="px-2">
            <div>OMB No. 1235-0008</div>
            <div>Expires 09/30/2026</div>
          </div>
        </div>
      </div>

      <div class="w-full flex h-9 border-y">
        <div class="w-1/2 flex">
          <div class="w-1/2">
            <div class="border-r flex-grow flex h-full">
              <div>PAYROLL NO.</div>
              <div class="pl-1 mr-1 flex-grow bg-[#f2f4ff]">
                {{payrollNumber}}
              </div>
            </div>
          </div>
          <div class="w-1/2">
            <div class="border-r flex-grow flex h-full">
              <div>FOR WEEK ENDING</div>
              <div class="pl-1 mr-1 flex-grow bg-[#f2f4ff]">
                {{endWeekDate}}
              </div>
            </div>
          </div>
        </div>
        <div class="w-1/2 flex">
          <div class="w-1/2 flex flex-col">
            <div>PROJECT AND LOCATION</div>
            <div class="flex-grow flex-grow bg-[#f2f4ff] mx-1">
              {{projectLocation}}
            </div>
          </div>
          <div class="w-1/2 flex flex-col">
            <div>PROJECT OR CONTRACT NO.</div>
            <div class="flex-grow flex-grow bg-[#f2f4ff] mx-1">
              {{projectNumber}}
            </div>
          </div>
        </div>
      </div>

      <div class="flex h-24 border-y">
        <div class="w-1/2 border-r">
          <div class="grid grid-cols-12 h-full">
            <div class="col-span-5 flex flex-col justify-between items-center border-r">
              <div class="mt-1">(1)</div>
              <div>
                NAME AND INDIVIDUAL IDENTIFYING NUMBER (e.g., LAST FOUR DIGITS
                OF SOCIAL SECURITY NUMBER) OF WORKER
              </div>
            </div>

            <div class="col-span-1 border-r">
              <div class="text-center mt-1">(2)</div>
              <div class="-rotate-90 relative -bottom-11 tracking-tight">
                NO. OF WITHHOLDING EXCEPTIONS
              </div>
            </div>

            <div class="col-span-2 border-r flex flex-col justify-between text-center">
              <div class="mt-1">(3)</div>
              <div>WORK CLASSIFICATION</div>
            </div>

            <div class="col-span-4 flex">
              <div class="w-3 border-r">
                <div class="-rotate-90 text-nowrap relative -bottom-16">
                  OT. ORST.
                </div>
              </div>

              <div class="w-full text-center">
                <div class="mt-1">(4) DAY AND DATE</div>
                <div class="h-8 border-y mt-0.5 grid-cols-7 grid">
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[0]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[1]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[2]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[3]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[4]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDaysNames.[5]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center">
                    {{weekDaysNames.[6]}}
                  </div>
                </div>
                <div class="h-8 border-b grid-cols-7 grid">
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[0]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[1]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[2]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[3]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[4]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center border-r">
                    {{weekDays.[5]}}
                  </div>
                  <div class="col-span-1 flex items-center justify-center">
                    {{weekDays.[6]}}
                  </div>
                </div>

                <div class="mt-0.5">HOURS WORKED EACH DAY</div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-1/2 flex">
          <div class="max-w-9 border-r flex flex-col justify-between text-center">
            <div class="mt-1">(5)</div>
            <div class="px-0.5">TOTAL HOURS</div>
          </div>
          <div class="min-w-12 max-w-10 border-r flex flex-col justify-between text-center">
            <div class="mt-1">(6)</div>
            <div class="px-0.5">RATE OF PAY</div>
          </div>
          <div class="min-w-14 max-w-14 border-r flex flex-col justify-between text-center">
            <div class="mt-1">(7)</div>
            <div class="px-0.5">GROSS AMOUNT EARNED</div>
          </div>


          <div class="border-r flex flex-col text-center w-80">
            <div class="flex flex-col items-center justify-center pb-2">
              <div>(8)</div>
              <div>DEDUCTIONS</div>
            </div>
          </div>

          <div class="max-w-12 min-w-12 flex flex-col justify-between text-center">
            <div class="mt-1">(9)</div>
            <div class="px-1">NET WAGES PAID FOR WEEK</div>
          </div>
        </div>
      </div>

      <div class="min-h-[400px]">

        {{#each (paginateTimesheets timesheets)}}
        {{#each this}}
        <div class="flex border-b min-h-[66px]">
          <div class="w-1/2 border-r">
            <div class="grid grid-cols-12 h-full">
              <div class="col-span-5 flex flex-col justify-between items-center border-r mt-0.5">
                {{name}}
              </div>

              <div class="col-span-1 border-r mt-0.5">
                {{numberOfExceptions}}
              </div>

              <div class="col-span-2 border-r flex flex-col justify-between text-center mt-0.5">
                {{jobClassification}}
              </div>

              <div class="col-span-4 flex">
                <div class="min-w-3 border-r">
                  <div class="h-1/2 border-b flex items-center justify-center">
                    O
                  </div>
                  <div class="h-1/2 flex items-center justify-center">
                    S
                  </div>
                </div>

                <div class="w-full text-center">
                  <div class="h-1/2 border-b grid-cols-7 grid">
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[0]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[1]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[2]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[3]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[4]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{overtimeHours.[5]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center">
                      {{overtimeHours.[6]}}
                    </div>
                  </div>
                  <div class="h-1/2 grid-cols-7 grid">
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[0]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[1]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[2]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[3]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[4]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center border-r">
                      {{regularHours.[5]}}
                    </div>
                    <div class="col-span-1 flex items-center justify-center">
                      {{regularHours.[6]}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="w-1/2 flex">
            <div class="min-w-9 max-w-9 border-r flex flex-col justify-between text-center">
              <div class="h-1/2 border-b flex justify-center items-center">
                {{totalOvertimeHours}}
              </div>
              <div class="h-1/2 flex justify-center items-center">
                {{totalRegularHours}}
              </div>
            </div>
            <div class="min-w-12 max-w-12 border-r flex flex-col justify-between text-center">
              <div class="h-1/2 border-b flex items-center justify-center">
                {{overtimeRate}}
              </div>
              <div class="h-1/2 flex items-center">
                <div class="flex-grow" style="font-size:8px">{{baseRate}}</div>
                <div class="flex-grow" style="font-size:8px">{{cashFringes}}</div>
              </div>
            </div>
            <div
              style="background:linear-gradient(to top left, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) calc(50% - 0.8px), rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0) calc(50% + 0.8px), rgba(0, 0, 0, 0) 100%);"
              class="min-w-14 max-w-14 border-r flex flex-col justify-between text-center">
              <div class="gross calculated text-start relative top-0.5 left-0.5">
                {{grossEarned}}
              </div>
              <div class="text-end relative bottom-0.5 right-0.5">{{grossEarnedAllProjects}}
              </div>
            </div>


            <div class="w-full h-full flex flex-wrap">
              {{#each deductions}}
              <div class="w-16 flex flex-col items-center justify-center border-r border-b h-8">
                <div class="w-full h-full">
                  <div class="px-0.5 text-center h-2/3 pt-0.5 overflow-hidden">
                    <div class="line-clamp-2 overflow-ellipsis">{{name}}</div>
                  </div>
                  <div class="w-full border-t text-center">
                    {{value}}
                  </div>
                </div>
              </div>
              {{/each}}
            </div>

            <div class="max-w-12 min-w-12 flex flex-col justify-center text-center">
              {{netWagesPaid}}
            </div>
          </div>
        </div>
        {{/each}}

        {{#unless @last}}
      </div>
    </section>
  </div>
</body>

</html>
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet">
  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
    }
  </style>
</head>

<body>
  <div class="py-6 px-4 w-full">
    <section class="border-black" style="font-size: 8.5px; line-height: 9px">
      {{/unless}}
      {{/each}}
  </div>

  </section>

  <section style="font-size:8px; line-height:9px" class="px-4">

    <div>
      While completion of Form WH-347 is optional, it is mandatory for covered contractors and subcontractors
      performing work on Federally financed or assisted construction contracts to respond to the information
      collection contained in 29 C.F.R. §§ 3.3, 5.5(a). The Copeland Act
      (40 U.S.C. § 3145) contractors and subcontractors performing work on Federally financed or assisted construction
      contracts to "furnish weekly a statement with respect to the wages paid each employee during the preceding
      week." U.S. Department of Labor (DOL) regulations at
      29 C.F.R. § 5.5(a)(3)(ii) require contractors to submit weekly a copy of all payrolls to the Federal agency
      contracting for or financing the construction project, accompanied by a signed "Statement of Compliance"
      indicating that the payrolls are correct and complete and that each laborer
      or mechanic has been paid not less than the proper Davis-Bacon prevailing wage rate for the work performed. DOL
      and federal contracting agencies receiving this information review the information to determine that employees
      have received legally required wages and fringe benefits.
    </div>

    <div class="font-bold text-center pt-2 pb-1">
      Public Burden Statement
    </div>

    <div>
      We estimate that is will take an average of 55 minutes to complete this collection, including time for reviewing
      instructions, searching existing data sources, gathering and maintaining the data needed, and completing and
      reviewing the collection of information. If you have
      any comments regarding these estimates or any other aspect of this collection, including suggestions for
      reducing this burden, send them to the Administrator, Wage and Hour Division, U.S. Department of Labor, Room
      S3502, 200 Constitution Avenue, N.W.
      Washington, D.C. 20210
    </div>

    <div class="h-[1px] w-full bg-black mt-2"></div>
    <div class="text-center">(over)</div>

  </section>
</body>

</html>
