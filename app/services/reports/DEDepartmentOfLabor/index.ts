import { CertifiedPayrollData } from "@/services/reports";
import { join } from "node:path";
import { Report } from "@/services/reports/Report";
import PDFMerger from "pdf-merger-js";
import fs from "fs";
import { Organization } from "@/models";
import dayjs from "dayjs";
import { JsReportService, getJsReportService, releaseJsReportService } from "@/services/reports/JsReportService";
import { Response } from "express";

export default class DEDepartmentOfLabor extends Report {
  private jsReportService: JsReportService;
  private jsReportOptions = { httpPort: 5003 };

  constructor(response: Response) {
    super(response);
    this.jsReportService = getJsReportService(this.jsReportOptions);

    // register custom handlebars helpers
    this.jsReportService.getJsReport().beforeRenderListeners.add("custom-helpers", (req, _res) => {
      req.template.helpers = `
        function paginateTimesheets(timesheets) {
          const pages = [];
          let currentPage = [];
          let pageIndex = 0;

          timesheets.forEach((timesheet, index) => {
            // Add isLastInPage flag
            timesheet.isLastInPage =
              (pageIndex === 0 && currentPage.length === 5) || // 6th row on first page
              (pageIndex > 0 && currentPage.length === 6);     // 7th row on subsequent pages

            currentPage.push(timesheet);

            // Check if we need to start a new page
            if (
              (pageIndex === 0 && currentPage.length === 6) || // first page - 6 items
              (pageIndex > 0 && currentPage.length === 7) ||   // subsequent pages - 7 items
              (index === timesheets.length - 1)                // last item
            ) {
              pages.push(currentPage);
              currentPage = [];
              pageIndex++;
            }
          });

          if (currentPage.length > 0) {
            pages.push(currentPage);
          }

          return pages;
        }

        function add(a, b) {
          return a + b;
        }

        function times(n, block) {
          // Convert n to a number if it's not already
          n = parseInt(n, 10);

          // Ensure n is a valid number
          if (isNaN(n) || n < 0) {
            return '';
          }

          let accum = '';
          for(let i = 0; i < n; ++i) {
            accum += block.fn(i);
          }
          return accum;
        }

        function range(start, end) {
          const array = [];
          for (let i = start; i <= end; i++) {
            array.push(i);
          }
          return array;
        }
      `;
    });
  }

  async report(organization: Organization, payrollData: CertifiedPayrollData) {
    const data = this.transformForPdfTemplate(this.transform(organization, payrollData));

    // we can also attach this to the node app and load it on app start
    const merger = new PDFMerger();

    const swornPayrollForm1 = fs.readFileSync(join(__dirname, "sworn-payroll-form-1.handlebars"), "utf8");
    const swornPayrollForm2 = fs.readFileSync(join(__dirname, "sworn-payroll-form-2.handlebars"), "utf8");

    const [swornPayrollForm1PdfPage1, swornPayrollForm1PdfPage2] = await Promise.all([
      this.jsReportService.render<typeof data>(
        {
          content: swornPayrollForm1,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
      this.jsReportService.render<typeof data>(
        {
          content: swornPayrollForm2,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
    ]);
    await merger.add(swornPayrollForm1PdfPage1.content);
    await merger.add(swornPayrollForm1PdfPage2.content);

    // Set metadata
    await merger.setMetadata({
      producer: "Hammr",
      title: "Sworn Payroll Form",
    });

    try {
      return this.getResponse(await merger.saveAsBuffer(), "pdf", "application/pdf");
    } finally {
      // Release the service when done
      releaseJsReportService(this.jsReportOptions);
    }
  }

  transformForPdfTemplate(data: ReturnType<Report["transform"]>) {
    const { endWeekDate } = data;

    const [weekEndDay, weekEndMonth, weekEndYear] = dayjs(endWeekDate).format("DD-MM-YYYY").split("-");
    const [weekStartDay, weekStartMonth, weekStartYear] = dayjs(endWeekDate)
      .subtract(6, "days")
      .format("DD-MM-YYYY")
      .split("-");

    const deductionsSet = new Set();

    data.timesheets.forEach((timesheet: any) => {
      timesheet.deductions.forEach((deduction: { name: string; value: string }) => {
        deductionsSet.add(deduction.name);
      });
    });

    const formattedTimesheets = data.timesheets.map((timesheet: any) => {
      let name;

      if (timesheet.name) {
        name = timesheet.name;
      } else if (timesheet.employee) {
        name = timesheet.employee.firstName + " " + timesheet.employee.lastName;
      }

      if (timesheet.employee.lastFourSSN) {
        name = name + " (" + timesheet.employee.lastFourSSN + ")";
      }

      const employeeAddress =
        timesheet.employee.residence !== undefined
          ? `${timesheet.employee.residence.line1}${
              timesheet.employee.residence.line2 ? ", " + timesheet.employee.residence.line2 : ""
            }, ${timesheet.employee.residence.city}, ${timesheet.employee.residence.state} ${
              timesheet.employee.residence.postal_code
            }`
          : "";

      return {
        ...timesheet,
        numberOfExceptions: timesheet.numberOfExceptions > 0 ? String(timesheet.numberOfExceptions) : "",
        name,
        employeeAddress,
        calculatedHourlyRate: (parseFloat(timesheet.baseRate) + parseFloat(timesheet.cashFringes)).toFixed(2),
        fringeCashDiff: Math.max(0, parseFloat(timesheet.fringePay) - parseFloat(timesheet.cashFringes)).toFixed(2),
        jobTitle: timesheet.employee.position,
        overtimeHours: (timesheet?.overtimeHours || []).map((hours: string, index: number) =>
          (parseFloat(hours) + (timesheet?.dotHours?.[index] ? parseFloat(timesheet.dotHours[index]) : 0)).toFixed(2)
        ),
        totalOvertimeHours: this.calculateTotalOvertimeAndDoubleOvertimeHours(
          timesheet.overtimeHours,
          timesheet.dotHours
        ),
        deductions: Array.from(deductionsSet)
          .sort(this.customDeductionsSort)
          .map((deductionName) => {
            const deduction = timesheet.deductions.find(
              (deduction: { name: string; value: string }) => deduction.name === deductionName
            );

            return { name: deductionName, value: deduction ? deduction.amount : "0" };
          }),
      };
    });

    // calculate total pages based on the pagination logic:
    // first page: 6 rows
    // subsequent pages: 7 rows each
    const finalTimesheets = [...formattedTimesheets];
    const timesheetsLength = finalTimesheets.length;
    const totalPages = timesheetsLength <= 6 ? 1 : Math.ceil((timesheetsLength - 6) / 8) + 1;

    return {
      weekEndDay,
      weekEndMonth,
      weekEndYear,
      weekStartDay,
      weekStartMonth,
      weekStartYear,
      ...data,
      timesheets: finalTimesheets,
      weekDaysNames: data.weekDates.map((date) => date.format("dd").toUpperCase()),
      weekDays: data.weekDates.map((date) => date.format("DD")),
      signedDate: dayjs().tz(data?.timezone).format("MM/DD/YYYY"),
      payday: dayjs(data.payday).format("MM-DD-YYYY"),
      page: 1,
      totalPages,
    };
  }
}
