<html>
<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
  <style>
    @page {
      size: 11in 8.5in landscape;
      margin: 0.2in;
    }
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
      font-size: 9px;
      line-height: 1.2;
      max-height: 8.1in;
      overflow: hidden;
    }
    .light-bg {
      background-color: #f5f8ff;
    }
    .benefits-table {
      border-collapse: collapse;
      width: 100%;
    }
    .benefits-table th, .benefits-table td {
      border: 1px solid black;
      padding: 4px;
    }
    .table-cell {
      border: 1px solid black;
      height: 18px;
      padding: 2px;
    }
    .input-field {
      border-bottom: 1px solid black;
      min-height: 16px;
      padding: 2px 4px;
    }
    .mt-8 {
      margin-top: 1.5rem !important;
    }
    .mt-4 {
      margin-top: 0.5rem !important;
    }
    .p-4 {
      padding: 0.5rem !important;
    }
    .numbered-section {
      display: flex;
      gap: 8px;
      margin-bottom: 0.5rem;
    }
    .number {
      flex: 0 0 auto;
      width: 20px;
    }
    .content {
      flex: 1;
    }
    .warning-text {
      font-style: italic;
      font-weight: bold;
      font-size: 11px;
      margin-top: 2rem;
      max-width: 95%;
    }
  </style>
</head>

<body class="p-4">
  <div class="flex">
    <!-- Left side -->
    <div class="w-1/2 pr-4">
      <div class="mb-4">
        <p class="mb-1">DATE <span class="light-bg input-field inline-block w-48">{{reportDate}}</span></p>
        
        <div class="mt-4">
          <div class="flex items-baseline gap-2">
            <p>I,</p>
            <div class="flex-1">
              <div class="light-bg input-field">{{signerName}}</div>
              <div class="text-center text-xs">(Name of signatory party)</div>
            </div>
            <div class="w-32">
              <div class="light-bg input-field">{{signerTitle}}</div>
              <div class="text-center text-xs">(Title)</div>
            </div>
          </div>
        </div>

        <p class="mt-2">do hereby state:</p>

        <!-- Numbered sections with proper spacing -->
        <div class="numbered-section mt-4">
          <div class="number">1.</div>
          <div class="content">
            That I pay or supervise the payment of persons employed by
            <div class="mt-2">
              <div class="light-bg input-field">{{name}}</div>
              <div class="text-center text-xs">(Contractor or Subcontractor)</div>
            </div>
            <div class="mt-2">on the</div>
            <div class="mt-2">
              <div class="light-bg input-field">{{projectName}}</div>
              <div class="text-center text-xs">(public project)</div>
            </div>
            <div class="mt-2">
              <p>that during the payroll period commencing on the
                <span class="light-bg input-field inline-block w-16">{{weekStartDay}}</span> day of
                <span class="light-bg input-field inline-block w-32">{{weekStartMonth}}</span>, 20
                <span class="light-bg input-field inline-block w-16">{{weekStartYear}}</span> and ending on the
                <span class="light-bg input-field inline-block w-16">{{weekEndDay}}</span> day of
                <span class="light-bg input-field inline-block w-32">{{weekEndMonth}}</span>, 20
                <span class="light-bg input-field inline-block w-16">{{weekEndYear}}</span>
                all persons employed on said project
              </p>
            </div>
            <div class="mt-2">
              <p>have been paid the full weekly wages earned, that no rebates have been or will be made either directly or indirectly to or on behalf of the contractor or subcontractor from the full weekly wages earned by any person and that no deductions have been made either directly or indirectly from the full wages earned by any person, other than permissible deductions as defined in the prevailing wage regulations of the State of Delaware.</p>
            </div>
          </div>
        </div>

        <div class="numbered-section">
          <div class="number">2.</div>
          <div class="content">
            That any payrolls otherwise under this contract required to be submitted for the above period are correct and complete; that the wage rates for laborers or mechanics contained therein are not less than applicable wage rates contained in any wage determination incorporated into the contract; that the classifications set forth therein for each laborer or mechanic conform with the work performed.
          </div>
        </div>

        <div class="numbered-section">
          <div class="number">3.</div>
          <div class="content">
            That any apprentices employed in the above period are duly registered in a bona fide apprenticeship program registered with a state apprenticeship agency recognized by the Bureau of Apprenticeship and Training, United States Department of Labor, and that the worksite ratio of apprentices to mechanics does not exceed the ratio permitted by the prevailing wage regulations of the State of Delaware.
          </div>
        </div>

        <!-- Warning text contained within left column -->
        <div class="warning-text">
          An employer who fails to submit sworn payroll information to the Department of Labor weekly shall be subject to fines of $1,000.00 and $5,000. for each violation.
        </div>
      </div>
    </div>

    <!-- Right side - Benefits Table -->
    <div class="w-1/2 pl-4">
      <p class="mb-1">List only those fringe benefits:</p>
      <p class="mb-1">For which the employer has paid; and</p>
      <p class="mb-2">Which have been used to offset the full prevailing wage rate.</p>
      
      <p class="mb-2">(See Delaware Prevailing Wage Regulations for explanation of how hourly value of benefits is the be computed.)</p>

      <!-- Benefits Table -->
      <div class="border border-black">
        <div class="text-center font-semibold p-1 border-b border-black bg-gray-200">
          HOURLY COST OF BENEFITS<br>
          (List in same order shown on front of record)
        </div>
        
        <table class="w-full">
          <tr class="border-b border-black">
            <td class="border-r border-black p-1 w-20">Employee</td>
            {{#times 7}}
            <td class="table-cell light-bg"></td>
            {{/times}}
          </tr>
          {{#each (range 1 8)}}
          <tr class="border-b border-black">
            <td class="border-r border-black p-1">{{this}}.</td>
            {{#times 7}}
            <td class="table-cell light-bg"></td>
            {{/times}}
          </tr>
          {{/each}}
        </table>
      </div>

      <!-- Certification -->
      <div class="mt-3">
        <p>I hereby certify that the foregoing information is true and correct to the best of m knowledge and belief. I realize that making a false statement under oath is a crime in State of Delaware</p>
        
        <div class="mt-3 flex justify-end">
          <div class="w-48">
            <div class="light-bg input-field italic">{{signerName}}</div>
            <div class="text-center text-xs">Signature</div>
          </div>
        </div>

        <div class="mt-3">
          <p>STATE OF <span class="light-bg input-field inline-block w-48">{{state}}</span></p>
          <p class="mt-1">COUNTY OF <span class="light-bg input-field inline-block w-48">{{county}}</span></p>
          
          <p class="mt-1">SWORN TO AND SUBSCRIBED BEFORE ME, A NOTARY PUBLIC,</p>
          
          <p class="mt-1">THIS <span class="light-bg input-field inline-block w-16">{{notaryDay}}</span> DAY OF
          <span class="light-bg input-field inline-block w-32">{{notaryMonth}}</span>, A.D. 20
          <span class="light-bg input-field inline-block w-16">{{notaryYear}}</span></p>
          
          <div class="mt-3">
            <div class="light-bg input-field w-48">{{notaryName}}</div>
            <div class="text-center text-xs">Notary Public</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
