<!--
Payroll Report form for Delaware Department of Labor dynamically rendered into html using handlebars 
and converted into pdf using chrome-pdf recipe. The styles use Tailwind CSS for better readability and reuse.

Data to this template should be provided in the incoming API request.
!-->
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet">
  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
      font-size: 8.5px;
      line-height: 1.2;
    }
    .main-grid {
      display: table;
      width: 100%;
      border-collapse: collapse;
    }
    .header-row {
      display: table-row;
    }
    .header-cell {
      display: table-cell;
      padding: 4px;
      vertical-align: top;
      height: inherit;
      border-right: 1px solid black;
    }
    .days-grid {
      display: grid;
      grid-template-columns: repeat(7, minmax(40px, 1fr));
    }
    .hours-cell {
      min-width: 40px;
      text-align: center;
    }
    .deductions-cell {
      width: 120px;
    }
    .light-bg {
      background-color: #f5f8ff;
    }
    input[type="checkbox"] {
      width: 8px;
      height: 8px;
    }
    .deductions-row {
      min-height: 3.5rem;
    }
    .row-height {
      height: 48px; /* Adjust this value to match deductions height exactly */
    }
    .data-grid {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
    }
    .header-row > .header-cell {
      border: none;
      border-right: 1px solid black;
    }
    .header-row > .header-cell:first-child {
      border-left: 1px solid black;
    }
    .data-row {
      border-bottom: 1px solid black;
    }
    .header-cell.border-l,
    .header-cell.border-r,
    .header-cell.border-b {
      border: none;
    }
    .data-grid {
      border: 1px solid black;
    }
    .header-row:first-child {
      border-top: 1px solid black;
    }
    .header-cell .border-r {
      border-right-color: black;
    }
    /* Remove right border from the 4th cell */
    .header-row > .header-cell:nth-child(3) {
      border-right: none;
      border-bottom: 1px solid black;
    }
    .header-row > .header-cell:nth-child(4) {
      border-left: 1px solid black;
    }
    .data-section {
      display: table;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
    }
    /* Additional styles to ensure borders are visible */
    .header-cell {
      position: relative;
      background-color: white;
    }
    /* Ensure subsequent pages maintain structure */
    .mt-8 {
      margin-top: 2rem !important;
    }
    /* Force borders to show */
    .header-row > .header-cell {
      border: 1px solid black;
    }
    /* Ensure page breaks and margins */
    @media print {
      .data-section {
        page-break-inside: avoid;
      }
    }
    .mb-32 {
      margin-bottom: 8rem !important;
    }
    /* Target only the 7th row of the first group */
    .data-grid > div:first-of-type .data-row:nth-child(7) {
        margin-bottom: 8rem !important;
        page-break-after: always !important;
    }
    /* Keep existing page break rule for 8th row */
    .data-row:nth-child(8) {
        margin-bottom: 8rem !important;
        page-break-after: always !important;
    }
  </style>
</head>

<body class="p-4">
  <!-- Header Section -->
  <div class="flex border border-black">
    <div class="w-1/3 p-2 border-r">
      <p class="font-bold">PAYROLL REPORT</p>
      <div class="flex items-center gap-2 mt-1">
        <div class="flex items-center gap-1">
          <span>NAME OF CONTRACTOR</span>
          <input type="checkbox" {{#unless isSubcontractor}} checked {{/unless}} />
        </div>
        <div class="flex items-center gap-1">
          <span>OR SUBCONTRACTOR</span>
          <input type="checkbox" {{#if isSubcontractor}} checked {{/if}} />
        </div>
      </div>
      <div class="light-bg mt-1 p-1">{{name}}</div>
    </div>
    <div class="w-1/3 p-2 border-r text-center">
      <p class="font-bold italic">Delaware Department of Labor</p>
      <p>DIA-Office of Construction Industry Enforcement</p>
      <p>252 Chapman Rd. STE 210</p>
      <p>Newark, DE 19702</p>
      <p>************</p>
    </div>
    <div class="w-1/3 p-2">
      <p>ADDRESS:</p>
      <div class="light-bg mt-1 p-1">{{companyAddress}}</div>
      <p class="mt-2">PHONE:</p>
      <div class="light-bg mt-1 p-1">{{phone}}</div>
    </div>
  </div>

  <!-- Project Info Section -->
  <div class="flex border-x border-b border-black">
    <div class="w-1/4 p-2 border-r">
      <p>PROJECT AND LOCATION</p>
      <div class="light-bg mt-1">{{projectLocation}}</div>
    </div>
    <div class="w-1/4 p-2 border-r">
      <p>WEEK ENDING DATE</p>
      <div class="light-bg mt-1">{{endWeekDate}}</div>
    </div>
    <div class="w-1/4 p-2 border-r">
      <p>CONTRACT NUMBER</p>
      <div class="light-bg mt-1">{{contractNumber}}</div>
    </div>
    <div class="w-1/4 p-2">
      <p>DATE OF PREVAILING WAGE DETERMINATION USED ON THIS PROJECT:</p>
      <div class="light-bg mt-1">{{prevailingWageDate}}</div>
    </div>
  </div>

  <!-- Main Data Grid -->
  <div class="data-grid border-x border-black mt-2">
    {{#each (paginateTimesheets timesheets)}}
      <div class="mt-2">
        <div class="header-row border-t border-x border-b border-black">
          <div class="header-cell w-40">
            <p class="text-center">NAME, ADDRESS AND</p>
            <p class="text-center">SOCIAL SECURITY NUMBER</p>
            <p class="text-center">OF EMPLOYEE</p>
          </div>
          <div class="header-cell w-16">
            <p class="text-center">WORK</p>
            <p class="text-center">CLASSIFICATION</p>
          </div>
          <div class="header-cell flex-grow">
            <p class="text-center">DAY & DATE & HOURS WORKED EACH DAY</p>
            <div class="flex">
              <div class="flex-grow">
                <!-- Dates Row -->
                <div class="grid" style="grid-template-columns: 16px repeat(7, minmax(26px, 1fr));">
                  <div class="border-r"></div>
                  {{#each ../weekDays}}
                  <div class="text-center border-r p-1">{{this}}</div>
                  {{/each}}
                </div>

                <!-- Days Row -->
                <div class="grid border-t" style="grid-template-columns: 16px repeat(7, minmax(26px, 1fr));">
                  <div class="border-r"></div>
                  {{#each ../weekDaysNames}}
                  <div class="text-center border-r p-1">{{this}}</div>
                  {{/each}}
                </div>
              </div>
              <div class="w-20 border-l">
                <p class="text-center border-b">TOTAL HOURS &</p>
                <p class="text-center border-b">RATE OF PAY</p>
                <div class="grid grid-cols-2">
                  <div class="text-center border-r">HOURS</div>
                  <div class="text-center">RATE</div>
                </div>
              </div>
            </div>
          </div>
          <div class="header-cell w-16 text-center">
            <p>GROSS</p>
            <p>AMOUNT</p>
            <p>EARNED</p>
          </div>
          <div class="header-cell w-96 text-center">
            <p>DEDUCTIONS</p>
            <div class="w-full flex flex-wrap">
              {{#each ../deductions}}
              <div class="w-16 border-r border-t">
                <div class="px-0.5 text-center h-2/3 pt-0.5 overflow-hidden">
                  <div class="line-clamp-2 overflow-ellipsis">{{name}}</div>
                </div>
                <div class="w-full border-t text-center">{{value}}</div>
              </div>
              {{/each}}
            </div>
          </div>
          <div class="header-cell w-16 text-center">
            <p>NET</p>
            <p>WAGES</p>
            <p>PAID</p>
          </div>
          <div class="header-cell w-16 text-center">
            <p>HOURLY</p>
            <p>VALUE OF</p>
            <p>FRINGES</p>
          </div>
        </div>

        <!-- Data Rows -->
        {{#each this}}
        <div class="header-row deductions-row border-x border-b border-black data-row">
          <div class="header-cell border-l border-r border-b border-black">{{name}}<br/><br />{{employeeAddress}}</div>
          <div class="header-cell border-r border-b border-black">{{jobClassification}}</div>
          <div class="header-cell flex items-center justify-center min-h-4 h-full border-r border-b border-black">
            <div class="flex w-full">
              <div class="flex-grow">
                <div class="grid row-height" style="grid-template-columns: 16px repeat(7, minmax(26px, 1fr));">
                  <!-- S/O Labels Column -->
                  <div class="border-r grid grid-rows-2">
                    <div class="border-b flex items-center justify-center">S</div>
                    <div class="flex items-center justify-center">O</div>
                  </div>

                  <!-- Days Columns -->
                  {{#each ../../weekDays}}
                  <div class="border-r grid grid-rows-2">
                    <div class="border-b flex items-center justify-center">
                      {{lookup ../regularHours @index}}
                    </div>
                    <div class="flex items-center justify-center">
                      {{lookup ../overtimeHours @index}}
                    </div>
                  </div>
                  {{/each}}
                </div>
              </div>
              <!-- Hours & Rate Data -->
              <div class="w-20 border-l row-height">
                <div class="grid grid-cols-2 h-full">
                  <div class="border-r grid grid-rows-2">
                    <div class="border-b flex items-center justify-center">{{totalRegularHours}}</div>
                    <div class="flex items-center justify-center">{{totalOvertimeHours}}</div>
                  </div>
                  <div class="grid grid-rows-2">
                    <div class="border-b flex items-center justify-center">{{calculatedHourlyRate}}</div>
                    <div class="flex items-center justify-center">{{overtimeRate}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="header-cell text-center border-r border-b border-black">{{grossEarned}}</div>
          <div class="header-cell border-r border-b border-black">
            <div class="w-full flex flex-wrap">
              {{#each deductions}}
              <div class="w-16 flex flex-col items-center justify-center border-r border-b min-h-8">
                <div class="w-full h-full">
                  <div class="px-0.5 text-center h-2/3 pt-0.5 overflow-hidden">
                    <div class="line-clamp-2 overflow-ellipsis">{{name}}</div>
                  </div>
                  <div class="w-full border-t text-center">
                    {{value}}
                  </div>
                </div>
              </div>
              {{/each}}
            </div>
          </div>
          <div class="header-cell text-center border-r border-b border-black">{{netWagesPaid}}</div>
          <div class="header-cell text-center border-r border-b border-black">{{fringeCashDiff}}</div>
        </div>
        {{/each}}
      </div>

      {{#unless @last}}
      </div>
    {{/unless}}
  {{/each}}
</div>
</body>
</html>
