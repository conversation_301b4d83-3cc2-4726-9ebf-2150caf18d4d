import { CertifiedPayrollData } from "@/services/reports";
import { join } from "node:path";
import { Report } from "@/services/reports/Report";
import PDFMerger from "pdf-merger-js";
import fs from "fs";
import { Organization } from "@/models";
import dayjs from "dayjs";
import { GENDER, ETHNICITY } from "@/models/user";
import { JsReportService, getJsReportService, releaseJsReportService } from "@/services/reports/JsReportService";
import { Response } from "express";
import { stringify } from "csv-stringify/sync";
import { mapDatesToWeekDays } from "@/util/dateHelper";

export default class NJDepartmentOfLaborAndWorkforce extends Report {
  private jsReportService: JsReportService;
  private jsReportOptions = { httpPort: 5003 };

  constructor(response: Response) {
    super(response);
    this.jsReportService = getJsReportService(this.jsReportOptions);

    // register custom handlebars helpers (paginateTimesheets)
    this.jsReportService.getJsReport().beforeRenderListeners.add("custom-helpers", (req, _res) => {
      req.template.helpers = `
        function paginateTimesheets(timesheets) {
          const pages = [];
          let currentPage = [];

          timesheets.forEach((timesheet, index) => {
            currentPage.push(timesheet);

            if (
              (index === 4) || // first page break after 5 items
              (index > 4 && (index - 4) % 6 === 0) || // subsequent breaks every 6 items
              (index === timesheets.length - 1) // last item
            ) {
              pages.push(currentPage);
              currentPage = [];
            }
          });

          return pages;
        }

        function add(a, b) {
          return a + b;
        }

        function subtract(a, b) {
          return a - b;
        }

        function gt(a, b) {
          return a > b;
        }

        function lt(a, b) {
          return a < b;
        }

        function and(a, b) {
          return a && b;
        }

        function length(arr) {
          return arr ? arr.length : 0;
        }

        function times(n, block) {
          // Convert n to a number if it's not already
          n = parseInt(n, 10);

          // Ensure n is a valid number
          if (isNaN(n) || n < 0) {
            return '';
          }

          let accum = '';
          for(let i = 0; i < n; ++i) {
            accum += block.fn(i);
          }
          return accum;
        }
      `;
    });
  }

  async report(organization: Organization, payrollData: CertifiedPayrollData, type: "csv" | "pdf" | null = null) {
    // only perform CSV generation if CSV is requested
    if (type === "csv") {
      const csvData = await this.generateCsvReport(organization, payrollData);

      return this.getCsvResponse(csvData);
    }

    // for PDF reports, do PDF transformation
    const data = this.transformForPdfTemplate(this.transform(organization, payrollData));

    // we can also attach this to the node app and load it on app start
    const merger = new PDFMerger();

    const mw562Page1 = fs.readFileSync(join(__dirname, "mw-562-1.handlebars"), "utf8");
    const mw562Page2 = fs.readFileSync(join(__dirname, "mw-562-2.handlebars"), "utf8");

    const [mw562PdfPage1, mw562PdfPage2] = await Promise.all([
      this.jsReportService.render<typeof data>(
        {
          content: mw562Page1,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
      this.jsReportService.render<typeof data>(
        {
          content: mw562Page2,
          engine: "handlebars",
          recipe: "chrome-pdf",
          chrome: {
            landscape: true,
            waitForNetworkIdle: true,
            timeout: 60000,
            printBackground: true,
          },
        },
        data
      ),
    ]);
    await merger.add(mw562PdfPage1.content);
    await merger.add(mw562PdfPage2.content);

    // Set metadata
    await merger.setMetadata({
      producer: "Hammr",
      title: "Certified payroll report",
    });

    try {
      const pdfBuffer = await merger.saveAsBuffer();

      return this.getResponse(pdfBuffer, "pdf", "application/pdf");
    } finally {
      // Release the service when done
      releaseJsReportService(this.jsReportOptions);
    }
  }

  transformForPdfTemplate(data: ReturnType<Report["transform"]>, reportType: "csv" | "pdf" | null = null) {
    const { endWeekDate } = data;

    const [weekEndDay, weekEndMonth, weekEndYear] = dayjs(endWeekDate).format("DD-MM-YYYY").split("-");
    const [weekStartDay, weekStartMonth, weekStartYear] = dayjs(endWeekDate)
      .subtract(6, "days")
      .format("DD-MM-YYYY")
      .split("-");

    const deductionsSet = new Set();

    data.timesheets.forEach((timesheet: any) => {
      timesheet.deductions.forEach((deduction: { name: string; value: string }) => {
        deductionsSet.add(deduction.name);
      });
    });

    const formattedTimesheets = data.timesheets.map((timesheet: any) => {
      let name;

      if (timesheet.name) {
        name = timesheet.name;
      } else if (timesheet.employee) {
        name = timesheet.employee.firstName + " " + timesheet.employee.lastName;
      }

      const employeeAddress =
        timesheet.employee.residence !== undefined
          ? `${timesheet.employee.residence.line1}${
              timesheet.employee.residence.line2 ? ", " + timesheet.employee.residence.line2 : ""
            }, ${timesheet.employee.residence.city}, ${timesheet.employee.residence.state} ${
              timesheet.employee.residence.postal_code
            }`
          : "";

      return {
        ...timesheet,
        numberOfExceptions: timesheet.numberOfExceptions > 0 ? String(timesheet.numberOfExceptions) : "",
        name,
        employeeAddress,
        calculatedHourlyRate: (parseFloat(timesheet.baseRate) + parseFloat(timesheet.cashFringes)).toFixed(2),
        fringeCashDiff: Math.max(0, parseFloat(timesheet.fringePay) - parseFloat(timesheet.cashFringes)).toFixed(2),
        jobTitle: timesheet.employee.position,
        gender: this.mapGenderToLetter(timesheet.employee.gender, reportType),
        race: this.mapRaceToLetter(timesheet.employee.ethnicity),
        ethnicity: this.mapEthnicityToLetter(timesheet.employee.ethnicity),
        overtimeHours: (timesheet?.overtimeHours || []).map((hours: string, index: number) =>
          (parseFloat(hours) + (timesheet?.dotHours?.[index] ? parseFloat(timesheet.dotHours[index]) : 0)).toFixed(2)
        ),
        totalOvertimeHours: this.calculateTotalOvertimeAndDoubleOvertimeHours(
          timesheet.overtimeHours,
          timesheet.dotHours
        ),
        deductions: Array.from(deductionsSet)
          .sort(this.customDeductionsSort)
          .map((deductionName) => {
            const deduction = timesheet.deductions.find(
              (deduction: { name: string; value: string }) => deduction.name === deductionName
            );

            return { name: deductionName, value: deduction ? deduction.amount : "0" };
          }),
      };
    });

    // calculate total pages based on the pagination logic:
    // first page: 5 rows
    // subsequent pages: 6 rows each
    const finalTimesheets = [...formattedTimesheets];
    const timesheetsLength = finalTimesheets.length;
    const totalPages = timesheetsLength <= 5 ? 1 : Math.ceil((timesheetsLength - 5) / 6) + 1;

    return {
      weekEndDay,
      weekEndMonth,
      weekEndYear,
      weekStartDay,
      weekStartMonth,
      weekStartYear,
      ...data,
      timesheets: finalTimesheets,
      weekDaysNames: data.weekDates.map((date) => date.format("dd").toUpperCase()),
      weekDays: data.weekDates.map((date) => date.format("DD")),
      signedDate: dayjs().tz(data?.timezone).format("MM/DD/YYYY"),
      payday: dayjs(data.payday).format("MM-DD-YYYY"),
      page: 1,
      totalPages,
    };
  }

  mapGenderToLetter(gender: typeof GENDER[number], reportType: "csv" | "pdf" | null = null) {
    if (gender === "MALE") return "M";
    if (gender === "FEMALE") return "F";
    if (gender === "NON_BINARY") return reportType === "csv" ? "X" : "N";
    if (gender === "PREFER_NOT_TO_ANSWER") return "";

    return "";
  }

  mapRaceToLetter(race: typeof ETHNICITY[number]) {
    if (race === "AMERICAN_INDIAN") return "N";
    if (race === "ASIAN") return "A";
    if (race === "BLACK") return "B";
    if (race === "HISPANIC") return "";
    if (race === "OTHER_PACIFIC_ISLANDER") return "I";
    if (race === "WHITE") return "W";
    if (race === "TWO_OR_MORE") return "M";
    if (race === "PREFER_NOT_TO_ANSWER") return "";

    return "";
  }

  mapEthnicityToLetter(ethnicity: typeof ETHNICITY[number]) {
    if (ethnicity === "HISPANIC") return "H";

    return "N";
  }

  async generateCsvReport(organization: Organization, payrollData: CertifiedPayrollData) {
    try {
      // format the data
      const formattedData = this.transformForPdfTemplate(this.transform(organization, payrollData));

      const weekDays = mapDatesToWeekDays(formattedData.weekDates);

      const headers = [
        "Employee_Name",
        "Address",
        "Job_Title",
        "Work_Classification",
        "Sex",
        "Race",
        "Ethnicity",
        "Date_Sun",
        "Date_Mon",
        "Date_Tue",
        "Date_Wed",
        "Date_Thu",
        "Date_Fri",
        "Date_Sat",
        "Hours_Sun_S",
        "Hours_Mon_S",
        "Hours_Tue_S",
        "Hours_Wed_S",
        "Hours_Thu_S",
        "Hours_Fri_S",
        "Hours_Sat_S",
        "Total_Hours_S",
        "Hourly_Rate_S",
        "Hours_Sun_O",
        "Hours_Mon_O",
        "Hours_Tue_O",
        "Hours_Wed_O",
        "Hours_Thu_O",
        "Hours_Fri_O",
        "Hours_Sat_O",
        "Total_Hours_O",
        "Hourly_Rate_O",
        "Gross_Week",
        "Gross_Project",
        "Deductions_FICA",
        "Deductions_State_Tax",
        "Deductions_Fed_Tax",
        "Deductions_Other",
        "Deductions_Total",
        "Net_Wages_Week",
        "Fringe_Benefit_Hourly_Cost",
      ];

      const csvData = formattedData.timesheets.map((timesheet) => {
        // calculate other deductions using the helper function
        const otherDeductions = this.sumOtherDeductions(timesheet.deductions);

        return {
          Employee_Name: timesheet?.name || "",
          Address: timesheet?.employeeAddress || "",
          Job_Title: timesheet?.jobTitle || "",
          Work_Classification: timesheet?.jobClassification || "",
          Sex: timesheet?.gender || "",
          Race: timesheet?.race || "",
          Ethnicity: timesheet?.ethnicity || "",
          Date_Sun: weekDays.SUNDAY?.[0] || "",
          Date_Mon: weekDays.MONDAY?.[0] || "",
          Date_Tue: weekDays.TUESDAY?.[0] || "",
          Date_Wed: weekDays.WEDNESDAY?.[0] || "",
          Date_Thu: weekDays.THURSDAY?.[0] || "",
          Date_Fri: weekDays.FRIDAY?.[0] || "",
          Date_Sat: weekDays.SATURDAY?.[0] || "",
          Hours_Sun_S: timesheet.regularHours?.[weekDays.SUNDAY?.[1]],
          Hours_Mon_S: timesheet.regularHours?.[weekDays.MONDAY?.[1]],
          Hours_Tue_S: timesheet.regularHours?.[weekDays.TUESDAY?.[1]],
          Hours_Wed_S: timesheet.regularHours?.[weekDays.WEDNESDAY?.[1]],
          Hours_Thu_S: timesheet.regularHours?.[weekDays.THURSDAY?.[1]],
          Hours_Fri_S: timesheet.regularHours?.[weekDays.FRIDAY?.[1]],
          Hours_Sat_S: timesheet.regularHours?.[weekDays.SATURDAY?.[1]],
          Total_Hours_S: timesheet.totalRegularHours,
          Hourly_Rate_S: timesheet.calculatedHourlyRate,
          // NOTE: since mw-562 does not have dot seperated, we aggregate these values into the OT calculations so OT hours includes DOT hours
          Hours_Sun_O: timesheet.overtimeHours?.[weekDays.SUNDAY?.[1]],
          Hours_Mon_O: timesheet.overtimeHours?.[weekDays.MONDAY?.[1]],
          Hours_Tue_O: timesheet.overtimeHours?.[weekDays.TUESDAY?.[1]],
          Hours_Wed_O: timesheet.overtimeHours?.[weekDays.WEDNESDAY?.[1]],
          Hours_Thu_O: timesheet.overtimeHours?.[weekDays.THURSDAY?.[1]],
          Hours_Fri_O: timesheet.overtimeHours?.[weekDays.FRIDAY?.[1]],
          Hours_Sat_O: timesheet.overtimeHours?.[weekDays.SATURDAY?.[1]],
          Total_Hours_O: timesheet.totalOvertimeHours,
          Hourly_Rate_O: timesheet.overtimeRate,
          Gross_Week: timesheet.grossEarnedAllProjects,
          Gross_Project: timesheet.grossEarned,
          Deductions_FICA: timesheet.deductionCategories.fica,
          Deductions_State_Tax: timesheet.deductionCategories.stateTax,
          Deductions_Fed_Tax: timesheet.deductionCategories.federalIncomeTax,
          Deductions_Other: otherDeductions,
          Deductions_Total: timesheet.deductionCategories.total,
          Net_Wages_Week: timesheet.netWagesPaid,
          Fringe_Benefit_Hourly_Cost: timesheet.fringeCashDiff,
        };
      });

      const data = stringify(csvData, { header: true, columns: headers });

      return data;
    } catch (error) {
      console.error("Error in generateCsvReport:", error);
      throw error; // Re-throw to handle at a higher level
    }
  }
}
