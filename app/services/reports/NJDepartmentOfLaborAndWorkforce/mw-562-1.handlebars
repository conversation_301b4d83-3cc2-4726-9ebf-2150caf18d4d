<!--
Payroll Certification for Public Works Projects form (MW-562) dynamically rendered into html using handlebars 
and converted into pdf using chrome-pdf recipe. The styles use Tailwind CSS for better readability and reuse.

Data to this template should be provided in the incoming API request.
!-->
<html>

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
    rel="stylesheet">
  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
      font-size: 10px;
      line-height: 1.2;
    }
    .data-grid {
      display: grid;
      grid-template-columns: 12% 6% 8% 10% 3% 17% 3% 3% 7% 23% 4% 4%;
    }
    .light-bg {
      background-color: #f5f8ff;
    }
    .form-text {
      font-size: 8px;
    }
    .form-text-sm {
      font-size: 7px;
    }
    .form-text-xs {
      font-size: 6px;
    }
    .header-text {
      font-size: 10px;
    }
    .days-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
    }
    .deductions-grid {
      display: flex;
      flex-wrap: wrap;
      height: 100%;
      min-height: 40px;
    }
    .deduction-item {
      width: 16.66%;
      border-right: 1px solid black;
      display: flex;
      flex-direction: column;
      height: 40px;
      margin-bottom: 1px;
    }
    .deduction-name {
      flex: 2;
      padding: 2px 3px;
      text-align: center;
      font-size: 7px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 1.1;
      word-break: break-word;
      text-overflow: ellipsis;
      white-space: normal;
    }
    .deduction-value {
      flex: 1;
      border-top: 1px solid black;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 7px;
    }
    .data-row {
      min-height: 80px;
      height: auto;
    }
    input[type="checkbox"] {
      width: 8px;
      height: 8px;
    }
    .submit-box {
      font-size: 10px;
    }
  </style>
</head>

<body class="p-0 mx-4">
  {{#each (paginateTimesheets timesheets)}}
  <div class="w-full {{#unless @first}}mt-4{{/unless}}">
    <!-- Header Section -->
    <div class="flex justify-between items-start mb-2 mt-1 px-2">
      <div class="w-1/3 header-text">
        NJ Department of Labor & Workforce Development
      </div>
      <div class="w-1/3 text-center">
        <p class="font-bold text-sm">Payroll Certification for Public Works Projects</p>
        <p class="header-text text-center">for Contractor and Subcontractor's Weekly and Final Certification</p>
      </div>
      <div class="w-1/3 text-right">
        <p class="header-text">Page {{add @index 1}} of {{../totalPages}}</p>
      </div>
    </div>

    <!-- Main Form Grid -->
    {{#if @first}}
    <div class="grid grid-cols-4 border-l border-black" style="line-height: 1.1">
      <!-- Left Column - Name, FEIN, Payroll Info -->
      <div class="col-span-1 border-r border-b border-black">
        <!-- Name of Contractor/Subcontractor -->
        <div class="border-t border-black p-1">
          <div class="flex items-center gap-1 form-text">
            <span>Name of</span>
            <input type="checkbox" {{#unless ../isSubcontractor}} checked {{/unless}} />
            <span>Contractor or</span>
            <input type="checkbox" {{#if ../isSubcontractor}} checked {{/if}} />
            <span>Subcontractor</span>
          </div>
          <div class="light-bg h-6 mt-1">{{../name}}</div>
        </div>
        
        <!-- FEIN -->
        <div class="border-b border-black p-1">
          <div class="flex items-center gap-2">
            <div class="form-text whitespace-nowrap">F.E.I.N.</div>
            <div class="light-bg h-5 flex-1"></div>
          </div>
        </div>
        
        <!-- Payroll No. -->
        <div class="flex flex-row">
          <div class="flex-1 p-1 border-r">
            <div class="form-text">Payroll No.</div>
            <div class="light-bg h-5 mt-1">{{../payrollNumber}}</div>
          </div>
          
          <!-- Date Wages Due & Paid -->
          <div class="flex-1 border-r p-1">
            <div class="form-text">Date Wages Due & Paid</div>
            <div class="light-bg h-5 mt-1">{{../payday}}</div>
          </div>

          <!-- Week Ending Date -->
          <div class="p-1">
            <div class="form-text">Week Ending Date</div>
            <div class="light-bg h-5 mt-1">{{../endWeekDate}}</div>
            <div class="flex items-center gap-1 form-text mt-1">
              <span>or</span>
              <input type="checkbox" {{#if ../isLastCertifiedPayrollReport}} checked {{/if}} />
              <span>Final Certification</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Middle Column - Business Address and Project Location -->
      <div class="col-span-1 border-r border-b border-black">
        <div class="h-full flex flex-col">
          <!-- Business Address -->
          <div class="border-b border-t border-black p-1 flex-1">
            <div class="form-text">Business Address</div>
            <div class="light-bg h-6 mt-1">{{../companyAddress}}</div>
          </div>
          
          <!-- Project Location -->
          <div class="p-1 flex-1">
            <div class="form-text">Project Location</div>
            <div class="light-bg h-6 mt-1">{{../projectLocation}}</div>
          </div>
        </div>
      </div>
      
      <!-- Right Column - Project Info -->
      <div class="col-span-1">
        <div class="h-full p-1 flex flex-col justify-between border-t border-b border-r border-black">
          <div>
            <!-- Project Name -->
            <div class="form-text">Project Name</div>
            <div class="light-bg h-6 mt-1">{{../projectName}}</div>
          </div>
          
          <div>
            <!-- Contract ID -->
            <div class="form-text mt-1">Contract I.D. or Project I.D.</div>
            <div class="light-bg h-6 mt-1">{{../projectId}}</div>
          </div>
          
          <div>
            <!-- Contractor Registration -->
            <div class="form-text mt-1">Contractor Registration #</div>
            <div class="light-bg h-6 mt-1 mb-1">{{../contractorRegistration}}</div>
          </div>
        </div>
      </div>
      
      <!-- Far Right Column - Submit Info -->
      <div class="col-span-1 p-2 border-l border-t border-r border-black submit-box ml-1" style="margin-top: -25px; height: calc(100% + 25px); border-bottom: 1px solid black;">
        <p class="text-center text-xs"><span class="font-semibold text-sm">SUBMIT</span> form via the NJ Wage Hub</p>
        <p class="text-center text-xs">(<span class="italic">njwages.nj.gov</span>) or use other submission methods in the portal.</p>
        <p class="mt-1 px-2 text-xs"><span class="font-semibold text-sm text-red-600">IMPORTANT:</span> For purposes of law, you must <span class="italic">also</span> submit this form to the appropriate public body or lessor, either via the NJ Wage Hub or other methods.</p>
      </div>
    </div>
    {{/if}}

    <!-- Table Header -->
    <div class="data-grid mt-2 border-t border-x border-b border-black text-center form-text-sm font-semibold">
      <!-- Column 1 -->
      <div class="border-r p-1 flex flex-col justify-center items-center">
        <div>1.</div>
        <div class="mt-1">Employee Name</div>
        <div>and Address</div>
      </div>
      
      <!-- Column 2-3 - Work (Job Title & Classification) -->
      <div class="border-r p-1 flex flex-col justify-center items-center" style="grid-column: span 2;">
        <div class="border-b w-full pb-1">2. Work</div>
        <div class="grid grid-cols-2 w-full mt-1">
          <div class="border-r">
            <div class="pb-1">Job Title</div>
            <div class="form-text-xs italic">e.g., apprentice,</div>
            <div class="form-text-xs italic">journeyman, foreman</div>
          </div>
          <div>
            <div class="pb-1">Occupational Category</div>
            <div class="form-text-xs italic">e.g., carpenter,</div>
            <div class="form-text-xs italic">mason, plumber</div>
          </div>
        </div>
      </div>
      
      <!-- Column 4 - Demographics -->
      <div class="border-r p-1 flex flex-col justify-center items-center">
        <div class="border-b w-full">3. Demographics</div>
        <div class="grid grid-cols-3 w-full mt-1">
          <div class="border-r">
            <div class="underline pb-1">Sex</div>
            <div class="form-text-xs">M=Male</div>
            <div class="form-text-xs">F=Female</div>
            <div class="form-text-xs">N=Non-binary</div>
          </div>
          <div class="border-r">
            <div class="underline pb-1">Race</div>
            <div class="form-text-xs">See</div>
            <div class="form-text-xs">Key</div>
          </div>
          <div>
            <div class="underline pb-1">Ethnicity</div>
            <div class="form-text-xs">H=Hispanic</div>
            <div class="form-text-xs">N=Non-</div>
            <div class="form-text-xs">Hispanic</div>
          </div>
        </div>
      </div>
      
      <!-- Column 5 - O/S -->
      <div class="border-r p-1 flex flex-col justify-center items-center">
        <div class="-rotate-90 whitespace-normal" style="width: 60px;">
          <span>Overtime or</span>
          <br>
          <span>Straight Time</span>
        </div>
      </div>
      
      <!-- Column 6-12 - Days of Week -->
      <div class="border-r p-1">
        <div class="text-center">4. Day and Date</div>
        <div class="days-grid border-t mt-1">
          <div class="border-r text-center">{{../weekDaysNames.[0]}}</div>
          <div class="border-r text-center">{{../weekDaysNames.[1]}}</div>
          <div class="border-r text-center">{{../weekDaysNames.[2]}}</div>
          <div class="border-r text-center">{{../weekDaysNames.[3]}}</div>
          <div class="border-r text-center">{{../weekDaysNames.[4]}}</div>
          <div class="border-r text-center">{{../weekDaysNames.[5]}}</div>
          <div class="text-center">{{../weekDaysNames.[6]}}</div>
        </div>
        <div class="days-grid border-t border-b">
          <div class="border-r text-center">{{../weekDays.[0]}}</div>
          <div class="border-r text-center">{{../weekDays.[1]}}</div>
          <div class="border-r text-center">{{../weekDays.[2]}}</div>
          <div class="border-r text-center">{{../weekDays.[3]}}</div>
          <div class="border-r text-center">{{../weekDays.[4]}}</div>
          <div class="border-r text-center">{{../weekDays.[5]}}</div>
          <div class="text-center">{{../weekDays.[6]}}</div>
        </div>
        <div class="text-center mt-1">Hours worked each day</div>
      </div>
      
      <!-- Column 13 - Total Hours -->
      <div class="border-r p-1 flex flex-col h-full">
        <div>5.</div>
        <div class="mt-auto">
          <div>Total</div>
          <div>Hours</div>
        </div>
      </div>
      
      <!-- Column 14 - Hourly Rate -->
      <div class="border-r p-1 flex flex-col h-full">
        <div>6.</div>
        <div class="mt-auto">
          <div>Hourly</div>
          <div>Rate</div>
          <div>of Pay</div>
        </div>
      </div>
      
      <!-- Column 15-16 - Gross Amt Earned -->
      <div class="border-r p-1">
        <div>7.</div>
        <div class="border-b w-full pb-1">Gross Amt Earned</div>
        <div class="grid grid-cols-2 w-full h-full">
          <div class="border-r h-full flex items-center justify-center">This Project</div>
          <div class="h-full flex items-center justify-center">This Week</div>
        </div>
      </div>
      
      <!-- Column 17-20 - Deductions -->
      <div class="border-r p-1">
        <div>8.</div>
        <div class="border-b w-full pb-1">Deductions</div>
      </div>
      
      <!-- Column 22 - Net Wages -->
      <div class="border-r p-1 flex flex-col h-full">
        <div>9.</div>
        <div class="mt-auto">
          <div>Net</div>
          <div>Wages</div>
          <div>Paid for</div>
          <div>Week</div>
        </div>
      </div>
      
      <!-- Column 23 - Fringe Benefits -->
      <div class="p-1 flex flex-col h-full">
        <div>10.</div>
        <div class="mt-auto">
          <div>Total</div>
          <div>Fringe</div>
          <div>Benefits</div>
          <div>Cost/Hour</div>
        </div>
      </div>
    </div>

    <!-- Table Data Rows -->
    <div class="min-h-[400px] border-x border-black w-full">
      {{#each this}}
      <div class="data-grid border-b border-black form-text data-row">
        <!-- Employee Name and Address -->
        <div class="border-r p-1 light-bg">
          {{name}}
          <br>
          {{employeeAddress}}
        </div>
        
        <!-- Job Title and Work Classification (combined cell) -->
        <div class="border-r p-1 light-bg" style="grid-column: span 2;">
          <div class="grid grid-cols-2 h-full">
            <div class="border-r pr-1">
              {{jobTitle}}
            </div>
            <div class="pl-1">
              {{jobClassification}}
            </div>
          </div>
        </div>
        
        <!-- Demographics -->
        <div class="border-r p-1 grid grid-cols-3">
          <div class="border-r text-center">{{gender}}</div>
          <div class="border-r text-center">{{race}}</div>
          <div class="text-center">{{ethnicity}}</div>
        </div>
        
        <!-- O/S Column -->
        <div class="border-r h-full">
          <div class="h-1/2 flex items-center justify-center">S</div>
          <div class="h-1/2 border-t flex items-center justify-center">O</div>
        </div>
        
        <!-- Days of Week - Hours -->
        <div class="border-r h-full">
          <div class="days-grid h-1/2">
            <div class="border-r flex items-center justify-center">
              {{regularHours.[0]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{regularHours.[1]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{regularHours.[2]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{regularHours.[3]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{regularHours.[4]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{regularHours.[5]}}
            </div>
            <div class="flex items-center justify-center">
              {{regularHours.[6]}}
            </div>
          </div>
          <div class="days-grid border-t h-1/2">
            <div class="border-r flex items-center justify-center">
              {{overtimeHours.[0]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{overtimeHours.[1]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{overtimeHours.[2]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{overtimeHours.[3]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{overtimeHours.[4]}}
            </div>
            <div class="border-r flex items-center justify-center">
              {{overtimeHours.[5]}}
            </div>
            <div class="flex items-center justify-center">
              {{overtimeHours.[6]}}
            </div>
          </div>
        </div>
        
        <!-- Total Hours -->
        <div class="border-r h-full">
          <div class="h-1/2 flex items-center justify-center">
            {{totalRegularHours}}
          </div>
          <div class="h-1/2 border-t flex items-center justify-center">
            {{totalOvertimeHours}}
          </div>
        </div>
        
        <!-- Hourly Rate -->
        <div class="border-r h-full">
          <div class="h-1/2 flex items-center justify-center">
            {{calculatedHourlyRate}}
          </div>
          <div class="h-1/2 border-t flex items-center justify-center">
            {{overtimeRate}}
          </div>
        </div>
        
        <!-- Gross Amount Earned -->
        <div class="border-r h-full">
          <div class="h-full flex flex-row">
            <div class="flex flex-grow text-center p-1 border-r justify-center items-center">
              {{grossEarned}}
            </div>
            <div class="flex flex-grow justify-center items-center text-center p-1">
              {{grossEarnedAllProjects}}
            </div>
          </div>
        </div>
        
        <!-- Deductions -->
        <div class="border-r h-full p-1">
          <div class="w-full h-full flex flex-wrap">
            {{#each deductions}}
            <div class="w-11 flex flex-col items-center justify-center border-r border-b min-h-8">
              <div class="w-full h-full">
                <div class="px-0.5 text-center h-2/3 pt-0.5 overflow-hidden">
                  <div class="line-clamp-2 overflow-ellipsis">{{name}}</div>
                </div>
                <div class="w-full border-t text-center">
                  {{value}}
                </div>
              </div>
            </div>
            {{/each}}
          </div>
        </div>
        
        <!-- Net Wages -->
        <div class="border-r h-full flex items-center justify-center">
          {{netWagesPaid}}
        </div>
        
        <!-- Fringe Benefits - calculated cash fringe diff (fringe pay - cash fringe) -->
        <div class="h-full flex items-center justify-center">
          {{fringeCashDiff}}
        </div>
      </div>
      {{/each}}

      <!-- Empty Rows for Manual Entry - Only on first page and only if we have fewer than 5 rows -->
      {{#if @first}}
      {{#if (lt (length this) 5)}}
      {{#times (subtract 5 (length this))}}
      <div class="data-grid border-x border-b border-black form-text h-[40px]">
        <div class="border-r"></div>
        <div class="border-r" style="grid-column: span 2;">
          <div class="grid grid-cols-2 h-full">
            <div class="border-r"></div>
            <div></div>
          </div>
        </div>
        <div class="border-r"></div>
        <div class="border-r">
          <div class="h-1/2 flex items-center justify-center">S</div>
          <div class="h-1/2 border-t flex items-center justify-center">O</div>
        </div>
        <div class="border-r">
          <div class="days-grid h-1/2">
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div></div>
          </div>
          <div class="days-grid border-t h-1/2">
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div class="border-r"></div>
            <div></div>
          </div>
        </div>
        <div class="border-r"></div>
        <div class="border-r"></div>
        <div class="border-r"></div>
        <div class="border-r"></div>
        <div class="border-r"></div>
        <div></div>
      </div>
      {{/times}}
      {{/if}}
      {{/if}}
    </div>

    <!-- Footer Section -->
    <div class="border-x border-b border-black p-1">
      <div class="flex">
        <div class="w-1/4 border border-black p-1 form-text">
          <p class="font-semibold">KEY</p>
          <p>W= White; B= Black or African American;</p>
          <p>A= Asian; N= American Indian or Native Alaskan;</p>
          <p>I = Native Hawaiian or Pacific Islander; M= 2 or More</p>
        </div>
        <div class="w-3/4 flex flex-col items-end justify-between">
          <p class="form-text">See following page for instructions</p>
          <div class="flex items-center gap-1 form-text">
            <input type="checkbox" {{#if (and (gt ../totalPages 1) (lt (add @index 1) ../totalPages))}} checked {{/if}} />
            <span>Check if additional sheets attached</span>
          </div>
          <p class="form-text">MW-562 (6/23)</p>
        </div>
      </div>
    </div>
  </div>

  {{#unless @last}}
  <div style="page-break-after: always;"></div>
  {{/unless}}
  {{/each}}
</body>

</html>
