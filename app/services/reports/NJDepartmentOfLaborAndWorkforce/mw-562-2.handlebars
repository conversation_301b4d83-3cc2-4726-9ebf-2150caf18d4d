<!--
Second page of the Payroll Certification for Public Works Projects form (MW-562)
Contains benefit program information and certification statements
-->
<html>
<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: "Open Sans", sans-serif;
      font-optical-sizing: auto;
      width: 1400px; /* Landscape A4 width */
      margin: 0 auto;
    }
    .form-text {
      font-size: 9.5px;
      line-height: 1.4; /* Increased line height */
    }
    .form-text-sm {
      font-size: 8.5px;
    }
    .underline-field {
      border-bottom: 1px solid #000;
      min-height: 12px;
      display: inline-block;
    }
    .checkbox {
      width: 9px;
      height: 9px;
      border: 1px solid black;
      display: inline-block;
      vertical-align: middle;
      margin-right: 3px;
      position: relative;
      top: -1px;
    }
    .grid-container {
      display: grid;
      grid-template-columns: 30% 70%;
      min-height: 900px; /* Landscape A4 height */
    }
    .rotated-header {
      transform: rotate(-45deg);
      white-space: nowrap;
      position: absolute;
      bottom: 0;
      left: 15px;
      transform-origin: bottom left;
      font-weight: normal;
      font-size: 9px;
    }
    .header-container {
      height: 70px;
      position: relative;
    }
    .light-bg {
      background-color: #f5f8ff;
    }
    .red-text {
      color: #ff0000;
    }
    .statement-spacing {
      margin-top: 1.2rem; /* Increased spacing between statements */
    }
    .table-row {
      height: 1.4rem; /* Reduced row height to fit more rows */
    }
    .compact-header {
      line-height: 1.1;
      padding: 2px;
      vertical-align: bottom;
    }
  </style>
</head>

<body class="p-2">
  <div class="grid-container">
    <!-- Left Column - Certification Statements -->
    <div class="border-black pr-0 flex flex-col justify-between">
      <!-- Certification Statement Section -->
      <div class="p-4">
        <p class="form-text font-semibold">I, the undersigned, do hereby state and certify:</p>
        
        <!-- Statement 1 -->
        <div class="form-text mt-3">
          <p>(1) That I pay or supervise the payment of the persons employed by</p>
          <div class="light-bg h-5 my-2 w-full border-b border-black"{{name}}></div>
          <div class="flex items-center mt-1">
            <p class="mr-2">on the</p>
            <div class="light-bg h-5 flex-1 border-b border-black">{{projectName}}, {{projectLocation}}</div> <!-- Now on same line -->
          </div>
          <p class="form-text-sm text-gray-600">(Project Name & Location)</p>
          <p class="mt-2">that during the payroll period beginning on (date) <span class="light-bg inline-block w-24 h-4 border-b border-black">{{weekStartMonth}} {{weekStartDay}}, {{weekStartYear}}</span>, and ending on (date) <span class="light-bg inline-block w-24 h-4 border-b border-black">{{weekEndMonth}} {{weekEndDay}}, {{weekEndYear}}</span>, all persons employed on said project have been paid the full weekly wages earned, that no rebates have been or will be made either directly or indirectly to or on behalf of the aforementioned Contractor or Subcontractor from the full weekly wages earned by any person and that no deductions have been made either directly or indirectly from the full wages earned by any person, other than permissible deductions as defined in the New Jersey Prevailing Wage Act, N.J.S.A. 34:11-56.25 et seq. and Regulation N.J.A.C. 12:60 et seq. and the Payment of Wages Law, N.J.S.A. 34:11-4.1 et seq.</p>
        </div>

        <!-- Statement 2 -->
        <div class="form-text statement-spacing">
          <p>(2) That any payrolls otherwise under this contract required to be submitted for the above period are correct and complete; that the wage rates for laborers or mechanics contained therein are not less than the applicable wage rates contained in any wage determination incorporated into the contract; that the classifications set forth therein for each laborer or mechanic conform with the work he performed.</p>
        </div>

        <!-- Statement 3 -->
        <div class="form-text statement-spacing">
          <p>(3) That any apprentices employed in the above period are duly registered with the United States Department of Labor, Bureau of Apprenticeship and Training and enrolled in a certified apprenticeship program.</p>
        </div>

        <!-- Statement 4 -->
        <div class="form-text statement-spacing">
          <p>(4) That:</p>
          <div class="ml-4 mt-2">
            <p>(a) WHERE FRINGE BENEFITS ARE PAID TO APPROVED PLANS, FUNDS OR PROGRAMS</p>
            <div class="flex items-start gap-2 mt-1">
              <input type="checkbox" class="mt-1 h-2 w-2" style="transform: scale(0.7);">
              <p>In addition to the basic hourly wage rates paid to each laborer or mechanic listed in the above-referenced payroll, payments of fringe benefits have been or will be made when due to appropriate programs for the benefit of such employ-ees, as noted in Section 4(c) at right.</p>
            </div>
          </div>
          <div class="ml-4 mt-3">
            <p>(b) WHERE FRINGE BENEFITS ARE PAID IN CASH</p>
            <div class="flex items-start gap-2 mt-1">
              <input type="checkbox" class="mt-1 h-2 w-2" style="transform: scale(0.7);" checked>
              <p>Each laborer or mechanic listed in the above-referenced payroll has been paid as indicated on the payroll, an amount not less than the sum of the applicable basic hourly wage rate plus the amount of the required fringe benefits as listed in the contract, except as noted in Section 4(c) at right.</p>
            </div>
          </div>
        </div>

        <!-- Statement 5 -->
        <div class="form-text statement-spacing">
          <p>(5) N.J.S.A. 12:60-2.1 and 5.1 – The Public Works employers shall submit to the public body or lessor a certified payroll record each pay period within 10 days of the payment of wages.</p>
        </div>
      </div>

      <!-- Signature Section -->
      <div class="p-4 mt-auto">
        <div class="flex items-start gap-2">
          <input type="checkbox" class="mt-1 h-2 w-2" style="transform: scale(0.7);" checked>
          <span class="form-text">By checking this box and typing my name below, I am electronically signing this application. I understand that an electronic signature has the same legal effect as a written signature.</span>
        </div>
        <div class="form-text mt-6">
          <p>Name <span class="light-bg inline-block w-64 h-5 border-b border-black">{{ signerName }}</span></p>
          <div class="flex justify-between mt-4">
            <p>Title <span class="light-bg inline-block w-32 h-5 border-b border-black">{{ signerTitle }}</span></p>
            <p>Date (mm/dd/yy) <span class="light-bg inline-block w-32 h-5 border-b border-black">{{ signedDate }}</span></p>
          </div>
        </div>

        <!-- Warning Text -->
        <div class="mt-6 py-2 px-4 border border-black">
          <p class="form-text italic">THE FALSIFICATION OF ANY OF THE ABOVE STATEMENTS MAY SUBJECT THE CONTRACTOR OR SUBCONTRACTOR TO CIVIL OR CRIMINAL PROSECUTION.</p>
          <p class="form-text italic">– N.J.S.A. 34:11- 56.25 ET SEQ. AND N.J.A.C. 12:60 ET SEQ. AND N.J.S.A. 34:11-4.1 ET SEQ.</p>
        </div>
      </div>
    </div>

    <!-- Right Column - Benefit Program Information -->
    <div class="">
      <!-- Benefit Program Information Section -->
      <div class="h-full flex flex-col">
        <div class="text-center py-3 border-black">
          <p class="font-bold text-sm">4(c) Benefit Program Information in AMOUNT CONTRIBUTED PER HOUR <span class="font-normal text-sm red-text">(Must be completed if 4(a) is checked)</span></p>
          <p class="form-text mt-1">To calculate the cost per hour, divide 2,000 hours into the benefit cost per year per employee.</p>
        </div>
        
        <!-- Benefits Table -->
        <table class="w-full form-text flex-1 border-collapse">
          <thead>
            <tr>
              <th class="border-black compact-header w-[15%] font-normal">Program Title, Classification Title, or Individual Workers</th>
              <th class="border-black p-0 w-[6%]">
                <div class="header-container">
                  <div class="rotated-header">Health/Welfare</div>
                </div>
              </th>
              <th class="border-black p-0 w-[6%]">
                <div class="header-container">
                  <div class="rotated-header">Holiday/Holiday</div>
                </div>
              </th>
              <th class="border-black p-0 w-[6%]">
                <div class="header-container">
                  <div class="rotated-header">Apprentice Training</div>
                </div>
              </th>
              <th class="border-black p-0 w-[6%]">
                <div class="header-container">
                  <div class="rotated-header">Pension</div>
                </div>
              </th>
              <th class="border-black compact-header w-[22%] font-normal">Other Benefit Type and Amount<br>(e.g., training, long-term disability or life ins.)</th>
              <th class="border-black compact-header w-[19%] font-normal">Name & Address of Fringe Benefit Fund, Plan, or Program Administrator</th>
              <th class="border-black compact-header w-[10%] font-normal">USDOL Benefit Plan Filing Number/EIN</th>
              <th class="border-black compact-header w-[10%] font-normal">Third-Party Trustee &/or Contract Person</th>
            </tr>
          </thead>
          <tbody>
            {{#each benefitPrograms}}
            <tr class="table-row">
              <td class="border border-black p-1">{{programTitle}}</td>
              <td class="border border-black p-1">{{healthWelfare}}</td>
              <td class="border border-black p-1">{{holiday}}</td>
              <td class="border border-black p-1">{{apprenticeTraining}}</td>
              <td class="border border-black p-1">{{pension}}</td>
              <td class="border border-black p-1">{{otherBenefits}}</td>
              <td class="border border-black p-1">{{administrator}}</td>
              <td class="border border-black p-1">{{filingNumber}}</td>
              <td class="border border-black p-1">{{trustee}}</td>
            </tr>
            {{/each}}
            <!-- Empty rows for manual filling -->
            {{#times 18}}
            <tr class="table-row">
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
              <td class="border border-black light-bg"></td>
            </tr>
            {{/times}}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
</html>
