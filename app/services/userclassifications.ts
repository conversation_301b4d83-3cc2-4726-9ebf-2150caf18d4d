import { FindOptions } from "sequelize";
import { UserClassification, User, Classification } from "@/models";

export class UserClassificationsService {
  private userClassification = UserClassification;

  async findAll(options: FindOptions) {
    const includeOptions =
      options.include && (options.include as any).length
        ? options.include
        : [
            {
              model: User,
              required: true,
            },
            {
              model: Classification,
              required: true,
            },
          ];

    const extendedOptions: FindOptions = {
      ...options,
      include: includeOptions,
    };

    const userClassifications = await this.userClassification.findAll(extendedOptions);

    return userClassifications;
  }
}
