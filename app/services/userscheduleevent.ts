import { UserScheduleEvent } from "@/models";

export class UserScheduleEventService {
  private userScheduleEvent = UserScheduleEvent;

  async create(userScheduleEventObj: Partial<UserScheduleEvent>): Promise<UserScheduleEvent> {
    const userScheduleEvent = await this.userScheduleEvent.create(userScheduleEventObj);

    return userScheduleEvent;
  }

  async findAll(options: object): Promise<UserScheduleEvent[]> {
    const extendedOptions = { ...options, raw: true };

    return await this.userScheduleEvent.findAll(extendedOptions);
  }

  async delete(userId: number, scheduleEventId: number): Promise<number> {
    return await this.userScheduleEvent.destroy({
      where: {
        userId: userId,
        scheduleEventId: scheduleEventId,
      },
    });
  }
}
