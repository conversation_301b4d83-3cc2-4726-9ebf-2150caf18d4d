import { DeviceToken } from "@/models";

export class DeviceTokensService {
  private deviceToken = DeviceToken;

  async create(deviceTokenObj: Partial<DeviceToken>): Promise<DeviceToken> {
    return await this.deviceToken.create(deviceTokenObj);
  }

  async findOne(options: any): Promise<DeviceToken | null> {
    return this.deviceToken.findOne({ where: options });
  }

  async delete(options: any): Promise<number> {
    return this.deviceToken.destroy({ where: options });
  }
}
