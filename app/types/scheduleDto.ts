export interface UpdateScheduleDto {
  id?: number;
  startTime?: Date;
  endTime?: Date;
  note?: string;
  costCodeId?: number;
  projectId?: number;
  userIds?: string; // legacy? comma delimited string of user ids
  notificationScheduledAt?: Date;
  notifyViaText?: boolean;
  notifyViaPush?: boolean;
  linkedEventId?: string; // uuid v4
  addUsers?: string; // maintaining same format - comma delimited string of user ids
  removeUsers?: string; // maintaining same format - comma delimited string of user ids
}

export interface MultiUpdateScheduleDto {
  scheduleEventsToUpdate: UpdateScheduleDto[];
  usersToAdd: number[];
  usersToRemove: number[];
}
