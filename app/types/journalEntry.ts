export interface JournalEntryLineItem {
  account_id: string;
  total_amount: number;
  description: string;
  foundationCostClass?: string;
  project_id?: string;
}

export interface JournalEntry {
  transaction_date: string;
  currency_code: string;
  memo: string;
  line_items: JournalEntryLineItem[];
}

export interface DeptFinancials {
  wages: number;
  reimbursements: number;
  employerTaxes: number;
  employerBenefits: number;
  employeeBenefits: number;
  postTaxDeductions: number;
  garnishments: number;
  netPay: number;
  employeeTaxes: number;
}

export interface DeptTotals {
  [department: string]: {
    wages: number;
    reimbursements: number;
    employerTaxes: number;
    employerBenefits: number;
    employeeBenefits: number;
    postTaxDeductions: number;
    garnishments: number;
    netPay: number;
    employeeTaxes: number;
    stateTaxes: { [stateCode: string]: number };
    federalTaxes: number;
    benefitTotals: { [benefitName: string]: number };
    items?: any[];
  };
}

export interface PayrollCategory {
  category: string;
  amount: number;
}

export interface JournalEntryResponse {
  journal_entry: JournalEntry;
}

export interface Tax {
  payer: string;
  amount: string;
  description: string;
}

export interface PostTaxDeduction {
  amount: string;
  description?: string;
  managed?: boolean;
}

export interface Garnishment {
  amount: string;
  description?: string;
}
