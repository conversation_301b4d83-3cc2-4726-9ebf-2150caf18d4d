export type PayFrequency = "weekly" | "biweekly" | "semimonthly" | "monthly" | "quarterly" | "annually";

export interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string; // 2 character
  postal_code: string;
  country?: string;
}

export interface CreateCompanyDto {
  legal_name?: string;
  trade_name?: string;
  other_business_name?: string;
  business_type?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: Address;
  pay_frequency?: PayFrequency;
  start_date?: Date;
}

export interface UpdateCompanyDto {
  legal_name?: string;
  trade_name?: string;
  other_business_name?: string;
  business_type?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: Address;
  pay_frequency?: PayFrequency;
  start_date?: Date;
}

export interface CreateEmployeeDto {
  first_name: string;
  last_name: string;
  email?: string;
  company: string;
  dob?: string;
  start_date?: Date;
  workplaces: string[];
  residence?: Address;
}

export interface UpdateEmployeeDto {
  first_name?: string;
  last_name?: string;
  email?: string;
  dob?: string;
  residence?: Address;
  active?: boolean;
}

export interface CreateContractorDto {
  first_name?: string;
  last_name: string;
  business_name?: string;
  company: string;
  address?: Address;
  start_date?: Date;
  termination_date?: Date;
}

export interface UpdateContractorDto {
  first_name?: string;
  last_name?: string;
  business_name?: string;
  address?: Address;
  type?: string;
  termination_date?: Date;
}
