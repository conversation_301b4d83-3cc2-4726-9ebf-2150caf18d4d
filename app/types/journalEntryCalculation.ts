import { PayrollItem, ContractorPayment, CheckBenefit } from "@/types/check";
import { Tax, Garnishment } from "@/types/journalEntry";

// Define interface for benefit liability entry
export interface BenefitLiability {
  name: string;
  type: "EE" | "ER";
  amount: number;
}

// Extended types from check.ts specific to journal entry calculation
export interface ExtendedPayrollItemForCalc extends PayrollItem {
  garnishments?: Garnishment[];
}

// Define interface for consolidated liabilities
export interface ConsolidatedLiabilities {
  manualPayments: number;
  cashOut: number;
  garnishments: number;
  managedPostTaxDeductions: number;
  benefitLiabilities: Record<string, BenefitLiability>;
}

// Common interface for processed payroll items
export interface ProcessedPayrollItem {
  grossWages: number;
  reimbursements: number;
  employerTaxes: number;
  employeeTaxes: number;
  employerBenefits: number;
  employeeBenefits: number;
  postTaxDeductions: number;
  managedPostTaxDeductions: number;
  garnishments: number;
  netPay: number;
  isManualPayment: boolean;
}

// Define ProjectTotal type explicitly
export interface ProjectTotal {
  wages: number;
  reimbursements: number;
  employerTaxes: number;
  employerBenefits: number;
  employeeBenefits: number;
  postTaxDeductions: number;
  managedPostTaxDeductions: number;
  garnishments: number;
  netPay: number;
  manualPayments: number;
  employeeTaxes: number;
  benefitTotals: Record<string, number>;
  employeePayrollItems: PayrollItemWithProjectInfo[];
  timesheetRatio: number;
  contractorPayments: number;
  contractorNetPay: number;
  detailedBenefitContributions?: Array<{ name: string; type: "EE" | "ER"; amount: number }>;
}

export interface PayrollItemWithProjectInfo extends PayrollItem {
  projectRatio: number;
  isHourlyEmployee: boolean;
}

export interface ExtendedPayrollItem extends PayrollItem {
  garnishments?: Array<Garnishment>;
  projectRatio?: number;
}

export interface ExtendedContractorPayment extends ContractorPayment {
  garnishments?: Array<Garnishment>;
  taxes?: Array<Tax>;
}

export interface ExtendedCheckBenefit extends CheckBenefit {
  company_contribution?: string;
  employee_deduction?: string;
}
