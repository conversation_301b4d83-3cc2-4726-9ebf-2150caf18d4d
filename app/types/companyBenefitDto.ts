import sequelize from "sequelize";

// want to limit the fields that could be updatable
export interface UpdateCompanyBenefit {
  name?: string;
  active?: boolean;
  category?: string;
  contributionType?: string;
  benefitStartDate?: Date;
  benefitEndDate?: Date | sequelize.Utils.Literal;
  benefitProviderName?: string;
  benefitProviderAddress?: string;
  benefitProviderPhone?: string;
  metadata?: JSON;
  isApprovedFringe?: boolean;
  checkCompanyBenefitId?: string;
}
