// want to limit the fields that could be updatable
export interface UpdateEmployeeBenefit {
  name?: string;
  active?: boolean;
  category?: string;
  contributionType?: string;
  companyContributionAmount?: string;
  companyContributionPercent?: number; // float
  employeeContributionAmount?: string;
  employeeContributionPercent?: number; // float
  benefitStartDate?: string; // format YYYY-MM-DD
  benefitEndDate?: string; // format YYYY-MM-DD
  metadata?: JSON;
  checkBenefitId?: string;
  effectiveDate?: Date;
  changeReason?: string;
}
