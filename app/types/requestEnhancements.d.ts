// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { Request } from "express";
import User from "@/models/user";
import { ExtendedOrganization } from "@/models/organization";
import { ExtendedCompanyBenefit } from "@/models/companybenefit";
import { ExtendedEmployeeBenefit } from "@/models/employeebenefit";
import { ExtendedClassification } from "@/models/classification";
import { ExtendedProject } from "@/models/project";
import { ExtendedTimesheet } from "@/models/timesheet";
declare module "express-serve-static-core" {
  interface Request {
    checkCompanyId?: string | null;
    organization?: ExtendedOrganization;
    targetedUser?: User;
    targetedCompanyBenefit?: ExtendedCompanyBenefit;
    targetedEmployeeBenefit?: ExtendedEmployeeBenefit;
    targetedClassification?: ExtendedClassification;
    targetedProject?: ExtendedProject;
    targetedPayroll?: any;
    targetedProjectStartDate?: Date;
    targetedTimesheet?: ExtendedTimesheet;
    apiKeyType?: "public" | "organization";
    apiKeyOrganizationId?: number;
  }
}
