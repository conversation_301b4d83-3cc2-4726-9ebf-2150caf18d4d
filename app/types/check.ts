export type OnboardStatus = "completed" | "needs_attention" | "blocking";

type PayFrequency = "weekly" | "biweekly" | "semimonthly" | "monthly" | "quarterly" | "annually";

export type PayrollPayFrequency = "weekly" | "biweekly" | "semimonthly" | "monthly" | "quarterly" | "annually";

export type PayrollEarningType =
  | "regular"
  | "overtime"
  | "double_overtime"
  | "pto"
  | "sick"
  | "cash_tips"
  | "paycheck_tips"
  | "bonus"
  | "commission"
  | "group_term_life"
  | "other_imputed"
  | "salaried"
  | "hourly"
  | "2_percent_shareholder_benefits"
  | "2_percent_shareholder_hsa";

export interface Onboard {
  status: OnboardStatus;
  blocking_steps: string[];
  remaining_steps: string[];
}

export type OnboardEvent =
  | "check-onboard-app-loaded"
  | "check-onboard-app-completed"
  | "check-onboard-ssn-updated"
  | "check-onboard-payment-method-updated"
  | "check-onboard-bank-account-updated"
  | "check-onboard-tax-form-updated"
  | "check-onboard-authorization-form-updated"
  | "check-onboard-tax-setup-form-updated"
  | "check-onboard-filing-authorization-form-updated";

export interface CheckEmployee {
  id: string;
  start_date: string;
  first_name?: string;
  middle_name?: string;
  last_name: string;
  email?: string;
  dob: string;
  residence: Address;
  company: string;
  workplaces: string[];
  ssn_last_four?: string;
  primary_workplace?: string;
  payment_method_preference?: "manual" | "direct_deposit";
  active?: boolean;
  onboard?: Onboard;
  bank_accounts?: string[];
  metadata?: any;
}

export type ContractorMetadata = {
  per_diem?: string;
  contractor_payment?: string;
};

export interface CheckContractor {
  id?: string;
  type?: string;
  company: string;
  workplaces?: string[];
  first_name?: string;
  middle_name?: string;
  last_name: string;
  email?: string;
  dob: string;
  address: Address;
  start_date: string;
  termination_date?: string;
  ssn_last_four?: string;
  bank_accounts?: string[];
  default_net_pay_split?: string;
  ein?: string;
  metadata?: ContractorMetadata;
  payment_method_preference?: string;
  "1099_nec_electronic_consent_provided"?: string;
  onboard: Onboard;
}

export interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country?: string;
}

export interface CheckCompany {
  legal_name: string;
  trade_name?: string;
  email?: string;
  phone: string;
  address: Address;
  pay_frequency?: PayFrequency;
  start_date?: string;
  processing_period?: string;
  bank_accounts?: string[];
  // other fields are also part of the object, but not needed currently
}

// all metadata fields have to be treated as strings and parsed as json when manipulated objects
type PayrollMetadata = {
  // manualDepositOverridesN: string[];
  manualDepositOverrides1?: string;
  manualDepositOverrides2?: string;
  manualDepositOverrides3?: string;
  manualDepositOverrides4?: string;
  manualDepositOverrides5?: string;
  // paymentMethodOverride: Record<string, string>;
  paymentMethodOverride?: string;
  // salariedEmployeesToSkip: string[];
  salariedEmployeesToSkip?: string;
  // updatedAt: string;
  updatedAt?: string;
};

export interface Payroll {
  id?: string;
  company: string;
  period_start: string;
  period_end: string;
  payday: string;
  type?: "regular" | "off_cycle";
  pay_frequency?: PayrollPayFrequency;
  pay_schedule?: string;
  off_cycle_options?: PayrollOffCycleOption;
  approval_deadline?: string;
  approved_at?: string;
  status?: string;
  managed?: boolean;
  totals?: any;
  items?: Array<PayrollItem>;
  contractor_payments?: Array<ContractorPayment>;
  metadata?: PayrollMetadata;
}

export interface PayrollOffCycleOption {
  force_supplemental_withholding?: boolean;
  apply_benefits?: boolean;
  apply_post_tax_deductions?: boolean;
}

export interface PayrollFilterOptions {
  type?: "regular" | "off_cycle";
}

export interface PayrollItem {
  id?: string;
  payroll?: string;
  employee: string;
  payment_method?: string;
  net_pay?: string;
  earnings?: Array<PayrollItemEarning>;
  reimbursements?: Array<PayrollItemReimbursement>;
  pto_balance_hours?: number;
  sick_balance_hours?: number;
  taxes?: Array<any>;
  benefits?: CheckBenefit[];
  benefit_overrides?: Array<any>;
  post_tax_deductions?: Array<any>;
  post_tax_deduction_overrides?: Array<any>;
  warnings?: Array<any>;
  paper_check_number?: string;
}

export interface PayrollItemEarning {
  amount: string;
  hours?: number;
  type?: PayrollEarningType;
  workplace: string;
  code?: string;
  description?: string;
  earning_code?: string;
  earning_rate?: string;
  metadata?: any;
}

export interface PayloadPayrollItemEarnings extends PayrollItemEarning {
  resourceId: string;
  piece_units?: string;
  tip_credit_amount?: string;
}

export interface PayrollItemReimbursement {
  amount: string;
  code?: string;
  description?: string;
}

export interface NewPayrollItem {
  name: string;
  type: string;
  organizationId?: number;
  id?: number;
}

type BenefitFields = {
  id?: string;
  benefit: string;
  description: string;
  employee_contribution_amount: string;
  employee_contribution_percent: string;
  company_contribution_amount: string;
  company_contribution_percent: string;
  company_period_amount: string;
};

// CheckBenefit requires all fields
export type CheckBenefit = BenefitFields;

// CheckBenefitOverride makes contribution fields optional
export type CheckBenefitOverride = Omit<
  BenefitFields,
  | "description"
  | "employee_contribution_amount"
  | "employee_contribution_percent"
  | "company_contribution_amount"
  | "company_contribution_percent"
  | "company_period_amount"
> & {
  employee_contribution_amount?: string;
  employee_contribution_percent?: string;
  company_contribution_amount?: string;
  company_contribution_percent?: string;
  company_period_amount?: string;
};

export interface BenefitOverrideCalculation {
  benefit: string;
  employee_contribution_amount: number;
  company_contribution_amount: number;
}

export interface ContractorPayment {
  id?: string;
  payroll?: string;
  contractor: string;
  status?: string;
  payment_method?: string;
  amount: string;
  reimbursement_amount?: string;
  net_pay?: string;
  paper_check_number?: string;
  workplace?: string;
  metadata?: any;
}

export interface Workplace {
  id: string;
  name: string;
  address: {
    line1: string;
    line2: string | null;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  company: string;
  active: boolean;
  metadata: Record<string, any>;
}

export interface CheckCompanyDefinedAttribute {
  name: string;
  value: string;
  effective_start?: string;
}
