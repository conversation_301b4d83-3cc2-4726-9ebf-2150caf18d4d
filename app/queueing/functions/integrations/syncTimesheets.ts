import { inngest } from "../../client";
import { RutterService } from "@/services/integrations/rutter";
import { IntegrationSettingsService } from "@/services/integrations/settings";
import { TimeSheet } from "@/models";
import { Op } from "sequelize";
import IntegrationMapping, { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";
import * as Sentry from "@sentry/node";

export const syncTimesheets = inngest.createFunction(
  { id: "sync-timesheets-to-integrations" },
  { event: "integration/timesheets-sync" },
  async ({ event, step }) => {
    const { timesheetIds = [], organizationId } = event.data;

    try {
      const integrationSettingsService = new IntegrationSettingsService();
      const integrationTimesheetsSync = await integrationSettingsService.getIntegrationObjectSetting(
        organizationId,
        "TIMESHEETS"
      );

      const integration = integrationTimesheetsSync?.integration;

      if (!integration?.isEnabled || !integrationTimesheetsSync?.isEnabled) {
        return {
          status: "skipped",
          reason: "Integrations are not enabled for timesheets",
        };
      }

      const timesheets = await TimeSheet.findAll({
        where: {
          id: {
            [Op.in]: timesheetIds,
          },
        },
      });

      const existingMappings = await IntegrationMapping.findAll({
        where: {
          integrationUserTokenId: integration.id,
          objectType: INTEGRATION_OBJECT_TYPE.TIMESHEET,
        },
      });

      const existingMappingsByHammrId = Object.fromEntries(
        existingMappings.map((mapping) => [mapping.hammrId, mapping])
      ) as Record<number, IntegrationMapping>;

      const rutterService = new RutterService();
      const timesheetsToSync = timesheets.filter((timesheet) => !existingMappingsByHammrId[timesheet.id]);

      const syncResults = await step.run(
        `sync-to-integration-${integrationTimesheetsSync.integration.platform}`,
        async () => {
          const timeSheetPromises = timesheetsToSync.map(async (timesheet) => {
            const result = await rutterService.createTimesheet(organizationId, timesheet);

            return {
              status: "success",
              result,
            };
          });

          const results = await Promise.allSettled(timeSheetPromises);
          const uniqueErrorsMap = new Map();
          results.forEach((result, index) => {
            if (result.status === "rejected") {
              // Use the original error object directly
              const error = result.reason;
              const timesheetId = timesheetsToSync[index].id;

              uniqueErrorsMap.set(error, {
                error,
                timesheetId,
              });
            }
          });

          // Log each unique error to Sentry with context
          uniqueErrorsMap.forEach(({ error, timesheetId }) => {
            Sentry.captureException(error, {
              tags: {
                function: "syncTimesheets",
                organizationId,
                timesheetId,
              },
              extra: {
                operation: "timesheet-sync",
              },
            });
          });

          // process the errors and add more info to them
          return results.map((result, index) => {
            const timesheetId = timesheetsToSync[index].id;
            if (result.status === "fulfilled") {
              return {
                timesheetId,
                status: "success",
                ...result.value,
              };
            } else {
              return {
                timesheetId,
                status: "error",
                error: result.reason?.message || String(result.reason),
              };
            }
          });
        }
      );

      return {
        status: "completed",
        totalTimesheets: timesheets.length,
        syncedTimesheets: timesheetsToSync.length,
        errorCount: syncResults.filter((result) => result.status === "error").length,
        syncResults,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        status: "failed",
        error: error.message,
      };
    }
  }
);
