import { inngest } from "../../client";
import { RutterService } from "@/services/integrations/rutter";
import { IntegrationSettingsService } from "@/services/integrations/settings";
import { User } from "@/models";
import * as Sentry from "@sentry/node";

export const updateEmployee = inngest.createFunction(
  { id: "update-employee-to-integrations" },
  { event: "employee/updated" },
  async ({ event }) => {
    const { userId } = event.data;

    const user = await User.findByPk(userId);

    if (!user) {
      return {
        status: "failed",
        reason: "User not found",
      };
    }

    const integrationSettingsService = new IntegrationSettingsService();
    const integrationEmployeesSync = await integrationSettingsService.getIntegrationObjectSetting(
      user.organizationId,
      "EMPLOYEES"
    );
    const integration = integrationEmployeesSync.integration;

    if (!integration.isEnabled || !integrationEmployeesSync?.isEnabled) {
      return {
        status: "skipped",
        reason: "QuickBooks integration not enabled for employees",
      };
    }

    try {
      const rutterService = new RutterService();

      const syncResult = await rutterService.updateEmployee(userId, user);

      return {
        status: "completed",
        result: syncResult,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        status: "failed",
        error: error.message,
      };
    }
  }
);
