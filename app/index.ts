import * as dotenv from "dotenv";
dotenv.config();

import modelsInit from "@/models";

import { startAPI } from "@/routes/api";
import { checkEnviromentVariables } from "@/util/env";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

// We are always using these in the code, so I just added them here in the main file
// Feel free to remove the .extend from other files and test if that code still works (it should)
dayjs.extend(utc);
dayjs.extend(timezone);

checkEnviromentVariables();
modelsInit();
startAPI();
