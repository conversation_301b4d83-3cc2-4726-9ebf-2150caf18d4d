import { EarningRate } from "@/models";
import { ExtendedUserPartial } from "@/models/user";
import { UserEarningsSummary } from "@/models/timesheet";
import { calculateSalariedWorkingHours } from "@/util/dateHelper";
import { PayFrequency } from "@/types/checkDto";

export const getActiveEarningRate = (earningRates: EarningRate[] = [], type = "REG"): EarningRate | undefined => {
  return earningRates.find((rate) => rate?.type === type && rate?.active);
};

export const getHourlyEarningRate = (earningRates: EarningRate[] = []): EarningRate | undefined => {
  return earningRates.find((rate) => rate?.period === "HOURLY");
};

export const summarizeSalariedEmployeeEarnings = (
  salariedEmployees: ExtendedUserPartial[],
  payFrequency: PayFrequency
): UserEarningsSummary[] => {
  return salariedEmployees.map((employee) => {
    const weeklyHours = employee?.earningRates[0]?.weeklyHours ?? 40;
    const workedHoursPerYear = 52 * weeklyHours; // 52 weeks in a year
    const hourlyRateFromSalary = parseFloat(employee?.earningRates[0]?.amount ?? "0") / workedHoursPerYear;
    const payFrequencyHours = calculateSalariedWorkingHours(weeklyHours, payFrequency);

    return {
      id: employee.id,
      firstName: employee.firstName,
      lastName: employee.lastName,
      compensationType: employee?.earningRates[0]?.period,
      hourlyWage: 0,
      otHourlyWage: 0,
      dotHourlyWage: 0,
      salary: parseFloat(employee?.earningRates[0]?.amount ?? "0"),
      checkRegEarningRateId: employee?.earningRates[0]?.checkEarningRateId,
      checkOtEarningRateId: null,
      checkDotEarningRateId: null,
      checkEmployeeId: employee.checkEmployeeId,
      checkContractorId: employee.checkContractorId,
      regularMinutes: payFrequencyHours * 60,
      overtimeMinutes: 0,
      doubleOvertimeMinutes: 0,
      totalMinutes: payFrequencyHours * 60,
      breakMinutes: 0,
      totalWages: payFrequencyHours * hourlyRateFromSalary,
      checkDynamicFringeBenefit: null,
      timeWorkedByDay: [],
      nonPWTimesheetsSummary: [],
      pwTimesheetsSummary: [],
      checkBenefitOverrides: [],
      reimbursements: [],
      otherEarnings: [],
      paymentMethod: null,
    } as UserEarningsSummary;
  });
};

export type ResourceType = "employee" | "contractor";

export const getResourceType = (resourceId: string): ResourceType | null => {
  if (resourceId.startsWith("emp_")) {
    return "employee";
  } else if (resourceId.startsWith("ctr_")) {
    return "contractor";
  } else {
    return null;
  }
};
