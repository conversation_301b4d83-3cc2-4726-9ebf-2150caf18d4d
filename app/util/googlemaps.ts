const fetch = require("node-fetch");
const apiKey = "AIzaSyCcBEskrub51Nire1J0hYtUq0KPf7w2BW8";

export async function getAddressFromCoords(lat: number, lng: number) {
  const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`;

  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch address`);
    }
    const data = await response.json();
    if (data.results.length > 0) {
      return data.results[0].formatted_address; // Return the first result's formatted address
    } else {
      throw new Error("No address found for the given coordinates.");
    }
  } catch (error) {
    console.error("Error fetching address:", error);
    throw new Error(`Failed to fetch address`);
  }
}
