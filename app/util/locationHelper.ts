import { point } from "@turf/helpers";
import distance from "@turf/distance";
import { Geometry, Point } from "geojson";

/**
 * Checks if an event location is within the geofence of a project location.
 *
 * @param projectLocation - The project location as a GeoJSON Geometry assumed to be a Point.
 * @param eventLocation - The event location as a GeoJSON Point object.
 * @param radius - The radius of the geofence in feet.
 * @returns boolean - True if the event is within the geofence, false otherwise.
 */
export function isWithinGeofence(
  projectLocation: Geometry,
  eventLocation: Geometry,
  radius: number
): boolean {
  const projectLocationPoint = projectLocation as Point;
  const eventLocationPoint = eventLocation as Point;
  const turfProjectLocation = point(projectLocationPoint.coordinates as [number, number]);
  const turfEventLocation = point(eventLocationPoint.coordinates as [number, number]);

  // Calculate the distance between the project and the event in kilometers
  const distKm = distance(turfProjectLocation, turfEventLocation, { units: "kilometers" });

  // Convert the radius from feet to kilometers (1 foot = 0.0003048 kilometers)
  const radiusKm = radius * 0.0003048;

  // Check if the distance is within the specified radius
  return distKm <= radiusKm;
}

export const createPoint = (long: any, lat: any): Point => {
  let point: Point = null;
  if (long && lat && parseFloat(long) && parseFloat(lat)) {
    point = { type: "Point", coordinates: [parseFloat(long), parseFloat(lat)] };
  }

  return point;
};
