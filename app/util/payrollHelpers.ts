import { ExtendedTimesheet, UserEarningsSummary } from "@/models/timesheet";
import TimeOffRequest from "@/models/timeoffrequest";
import { getLocalizedTimestampForDateString, getNearestFullWeekIntervals } from "@/util/dateHelper";
import { CheckBenefit, PayloadPayrollItemEarnings, Payroll } from "@/types/check";
import {
  CONTRACTOR_PAYMENT_CODE,
  PayloadEarning,
  PayloadReimbursement,
  PER_DIEM_REIMBURSEMENT_CODE,
} from "@/services/payrolls";
import PaySchedule from "@/models/payschedule";
import EmployeeBenefit, { ExtendedEmployeeBenefit } from "@/models/employeebenefit";
import { WeekDay } from "@/models/overtimesettings";
import { UserToRemoveSummaryFrom } from "@/models/payroll";

export interface PayrollProcessingData {
  earnings: PayloadEarning[];
  reimbursements: PayloadReimbursement[];
}

export const getPayrollPeriodDates = async (
  periodStartDate: string,
  periodEndDate: string,
  paySchedule: PaySchedule,
  weekStartDay: WeekDay,
  timezone: string
) => {
  const periodStart = periodStartDate;
  const periodEnd = periodEndDate;
  const startDayOfWeek = paySchedule.payFrequency === "weekly" ? weekStartDay : "MONDAY";

  const periodStartTimestamp = getLocalizedTimestampForDateString(periodStart, timezone, "start");
  const periodEndTimestamp = getLocalizedTimestampForDateString(periodEnd, timezone, "end");

  return {
    periodStartTimestamp,
    periodEndTimestamp,
    adjustedDates: getNearestFullWeekIntervals(periodStartTimestamp, periodEndTimestamp, startDayOfWeek),
    startDayOfWeek,
  };
};

export const filterEligibleTimesheets = (
  timesheets: Partial<ExtendedTimesheet>[],
  periodStartTimestamp: number,
  periodEndTimestamp: number
): Partial<ExtendedTimesheet>[] => {
  return timesheets.filter((timesheet) => {
    return timesheet.clockInTimestamp >= periodStartTimestamp && timesheet.clockInTimestamp <= periodEndTimestamp;
  });
};

export const categorizeUserEarningsSummaries = (
  userEarningsSummaries: UserEarningsSummary[]
): { hourlyEmployeeSummaries: UserEarningsSummary[]; contractorSummaries: UserEarningsSummary[] } => {
  const hourlyEmployeeSummaries = userEarningsSummaries.filter(
    (summary) => summary.compensationType === "HOURLY" && summary.checkEmployeeId
  );

  const contractorSummaries = userEarningsSummaries.filter(
    (summary) => summary.compensationType === "HOURLY" && summary.checkContractorId
  );

  return { hourlyEmployeeSummaries, contractorSummaries };
};

export const mapExistingPayrollToPayloadData = (existingPayroll: Payroll): PayrollProcessingData => {
  const result: PayrollProcessingData = {
    earnings: [],
    reimbursements: [],
  };

  existingPayroll.items.forEach((item) => {
    // process earnings
    if (item.earnings && item.earnings.length > 0) {
      const itemEarnings = item.earnings.map((earning) => ({
        ...earning,
        resourceId: item.employee,
      }));
      result.earnings.push(...itemEarnings);
    }

    // process reimbursements
    if (item.reimbursements && item.reimbursements.length > 0) {
      const itemReimbursements = item.reimbursements.map((reimbursement) => ({
        ...reimbursement,
        resourceId: item.employee,
      }));
      result.reimbursements.push(...itemReimbursements);
    }
  });

  /**
   * Contractor payments has a special case because of Reimbursements, we need to remove the
   * per diem reimbursement as that's going to be calculated later in the payroll cycle in function generateContractorPaymentItemsExtended
   */
  const contractorPayments = existingPayroll.contractor_payments;

  // contractors do not have earnings, only reimbursements
  if (contractorPayments && contractorPayments.length > 0) {
    // > Note only one contractor payment is allowed per contractor per payroll, so technically we don't need to loop through reimbursements
    contractorPayments.forEach((payment) => {
      if (payment.metadata && parseFloat(payment.metadata[CONTRACTOR_PAYMENT_CODE]) > 0) {
        result.earnings.push({
          amount: payment.metadata[CONTRACTOR_PAYMENT_CODE],
          resourceId: payment.contractor,
          type: CONTRACTOR_PAYMENT_CODE,
        });
      }

      if (payment.reimbursement_amount) {
        let amount = payment.reimbursement_amount;

        // if it has per diem reimbursement metadata then we subtract it as we are including it again later
        if (payment.metadata && parseFloat(payment.metadata[PER_DIEM_REIMBURSEMENT_CODE])) {
          amount = (parseFloat(amount) - parseFloat(payment.metadata[PER_DIEM_REIMBURSEMENT_CODE])).toFixed(2);
        }

        result.reimbursements.push({
          resourceId: payment.contractor,
          amount,
        });
      }
    });
  }

  return result;
};

// need a method that will calculate hourly rate paid for medical, etc benefits
// should consider benefitsOverride first, then benefits. The logic should match the benefit type in order to see if it qualifies to be calculated
export const calculateCoreBenefitHourlyContribution = (
  benefits: CheckBenefit[],
  employeeBenefits: ExtendedEmployeeBenefit[],
  contributionType: "company" | "employee" = "company"
): string => {
  // Define core benefits to include
  const coreBenefits = ["125_medical", "125_vision", "125_dental", "125_medical_other"];

  let totalHourlyContribution = 0;

  // Iterate through benefits
  // fundamentally this changes the logic because we originally were just pulling the rate Check indicates that is paid out (usually based on the payroll frequency) so our calculations will feel a little off
  for (const benefit of benefits) {
    if (coreBenefits.includes(benefit.benefit)) {
      const foundBenefit = getMatchingBenefit(benefit.id, employeeBenefits);
      if (foundBenefit && contributionType === "company" && benefit?.company_contribution_amount) {
        totalHourlyContribution += foundBenefit?.fringeHourlyContribution;
      } else if (foundBenefit && contributionType === "employee" && benefit?.employee_contribution_amount) {
        totalHourlyContribution += foundBenefit?.employeeHourlyContribution;
      }
    }
  }

  return totalHourlyContribution.toFixed(2);
};

export const calculatePensionHourlyContribution = (
  benefits: CheckBenefit[],
  employeeBenefits: EmployeeBenefit[],
  contributionType: "company" | "employee" = "company"
): string => {
  // Define pension-related benefits to include
  const pensionBenefits = ["401k", "403b", "457", "simple_ira", "roth_401k", "roth_403b", "roth_457"];

  let totalHourlyContribution = 0;

  // Iterate through benefits
  for (const benefit of benefits) {
    if (pensionBenefits.includes(benefit.benefit)) {
      const foundBenefit = getMatchingBenefit(benefit.id, employeeBenefits);
      if (foundBenefit && contributionType === "company" && benefit?.company_contribution_amount) {
        totalHourlyContribution += foundBenefit?.fringeHourlyContribution;
      } else if (foundBenefit && contributionType === "employee" && benefit?.employee_contribution_amount) {
        totalHourlyContribution += foundBenefit?.employeeHourlyContribution;
      }
    }
  }

  return totalHourlyContribution.toFixed(2);
};

export const calculateMiscHourlyContribution = (
  benefits: CheckBenefit[],
  employeeBenefits: EmployeeBenefit[],
  contributionType: "company" | "employee" = "company"
): string => {
  // Define known categories to exclude (medical, pension, voluntary benefits)
  const excludedCategories = [
    "125_medical",
    "125_vision",
    "125_dental",
    "125_medical_other",
    "401k",
    "403b",
    "457",
    "simple_ira",
    "roth_401k",
    "roth_403b",
    "roth_457",
    "125_disability",
    "125_accident",
    "125_cancer",
    "125_critical_illness",
    "125_hospital",
    "125_life",
  ];

  let totalHourlyContribution = 0;

  // Iterate through benefits
  for (const benefit of benefits) {
    // Include only benefits that are not in the excluded categories
    if (!excludedCategories.includes(benefit.benefit)) {
      const foundBenefit = getMatchingBenefit(benefit.id, employeeBenefits);
      if (foundBenefit && contributionType === "company" && benefit?.company_contribution_amount) {
        totalHourlyContribution += foundBenefit?.fringeHourlyContribution;
      } else if (foundBenefit && contributionType === "employee" && benefit?.employee_contribution_amount) {
        totalHourlyContribution += foundBenefit?.employeeHourlyContribution;
      }
    }
  }

  return totalHourlyContribution.toFixed(2);
};

export const getMatchingBenefit = (
  checkBenefitId: string,
  employeeBenefits: ExtendedEmployeeBenefit[] = []
): ExtendedEmployeeBenefit | undefined => {
  return employeeBenefits.find((benefit) => benefit.checkBenefitId === checkBenefitId);
};

export const filterForNonRegularEarnings = (payrollEarnings: PayloadPayrollItemEarnings[] = []) => {
  return payrollEarnings.filter((earning) => {
    return !earning.metadata?.timeOffPolicyId;
  });
};

/**
 * Filters out time off requests for contractors and salaried employees
 * @param timeOffRequests Array of time off requests with user data
 * @returns Filtered array of eligible time off requests
 */
export const filterIneligibleTimeOffRequests = (
  timeOffRequests: TimeOffRequest[],
  ineligibleUsers: UserToRemoveSummaryFrom[]
) => {
  return timeOffRequests.filter((request) => {
    const user = request.user;

    // filter out contractors (has contractorId but no employeeId)
    if (user.checkContractorId && !user.checkEmployeeId) {
      return false;
    }

    // filter out salaried employees (has active ANNUALLY earning rate)
    const hasSalariedRate = user.earningRates.some((rate) => rate.active && rate.period === "ANNUALLY");
    if (hasSalariedRate) {
      return false;
    }

    // filter out users that are in the ineligibleUsers array
    if (ineligibleUsers.some((userToRemove) => userToRemove.id === user.id)) {
      return false;
    }

    return true;
  });
};
