import fetch from "node-fetch";

import { getAdditionalPathFromPathName, getIdFromPathName, getRootFromPathName } from "@/util/path";
import { UsersService } from "@/services/users";
import { User } from "@/models";

const CHECK_API_URL = process.env.CHECK_API_URL;
const CHECK_API_KEY = process.env.CHECK_API_KEY;

// TO DO: better type safety for user references - the shape is slightly different from the model
export const isUserCompanyAdmin = (user: any): boolean => {
  if (user && user.role) {
    return user.role === "ADMIN";
  }

  return false;
};

export const validateCompanyQuery = (user: any, queryParams: any): boolean => {
  if (queryParams?.company === user.organizationCheckCompanyId) {
    return true;
  }

  return false;
};

export const doesResourceIdBelongToCompany = async (endpoint: string, companyId: string): Promise<boolean> => {
  const options = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${CHECK_API_KEY}`,
    },
  };
  const res = await fetch(`${CHECK_API_URL}/${endpoint}`, options);

  // TO DO: additional type safety would be to map out expected props from Check API calls
  const resource: any = await res.json();

  if (resource?.company === companyId) {
    return true;
  }

  // in-case no company exists, we determine what company the employee belongs to
  if (resource?.employee) {
    return await doesResourceIdBelongToCompany(`employees/${resource?.employee}`, companyId);
  }

  // in-case no company exists, we determine what company the contractor belongs to
  if (resource?.contractor) {
    return await doesResourceIdBelongToCompany(`contractors/${resource?.contractor}`, companyId);
  }

  return false;
};

export const doesResourceIdBelongToEmployee = async (endpoint: string, employeeId: string): Promise<boolean> => {
  const options = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${CHECK_API_KEY}`,
    },
  };
  const res = await fetch(`${CHECK_API_URL}/${endpoint}`, options);

  // TO DO: additional type safety would be to map out expected props from Check API calls
  const resource: any = await res.json();
  if (resource?.employee === employeeId) {
    return true;
  }

  return false;
};

export const doesResourceIdBelongToContractor = async (endpoint: string, contractorId: string): Promise<boolean> => {
  const options = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${CHECK_API_KEY}`,
    },
  };

  const res = await fetch(`${CHECK_API_URL}/${endpoint}`, options);

  // TO DO: additional type safety would be to map out expected props from Check API calls
  const resource: any = await res.json();
  if (resource?.contractor === contractorId) {
    return true;
  }

  return false;
};

// TO DO - should test this because it had to be changed from Firebase call to Users service call
export const getUserByCheckEmployeeId = async (id: string, orgId: number): Promise<any> => {
  const usersService = new UsersService();
  const user = await usersService.findOne({
    where: {
      checkEmployeeId: id,
      organizationId: orgId,
    },
    simple: true,
    forceRawResponse: true,
  });

  if (user) {
    return user; // return first result
  } else {
    return null;
  }
};

export const getUserByCheckContractorId = async (id: string, orgId: number): Promise<any> => {
  const usersService = new UsersService();
  const user = await usersService.findOne({
    where: {
      checkContractorId: id,
      organizationId: orgId,
    },
    simple: true,
    forceRawResponse: true,
  });

  if (user) {
    return user; // return first result
  } else {
    return null;
  }
};

//
// MORE ON VALIDATION STARTING HERE
//

// TO DO - GO THROUGH EACH ROOT SWITCH SCENARIO AND CHECK THE VALIDATION!! - NEED TO BE THOROUGH
export const validateRequest = async (
  authUserRole: User["role"],
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
): Promise<boolean> => {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise<boolean>(async (resolve) => {
    if (!pathName) resolve(false);
    const root = getRootFromPathName(pathName);

    switch (root) {
      case "companies":
        resolve(validateCompanyEndpoints(user, pathName, method));
        break;
      case "employees":
        resolve(await validateEmployeeEndpoints(authUserRole, user, pathName, method, queryParams, body));
        break;
      case "contractors":
        resolve(await validateContractorEndpoints(user, pathName, method, queryParams, body));
        break;
      case "earning_rates":
        resolve(await validateEarningRateEndpoints(user, pathName, method, queryParams, body));
        break;
      case "pay_schedules":
        resolve(await validatePayscheduleEndpoints(user, pathName, method, queryParams, body));
        break;
      case "payrolls":
        resolve(await validatePayrollEndpoints(user, pathName, method, queryParams, body));
        break;
      case "payroll_items":
        resolve(await validatePayrollItemsEndpoints(user, pathName, method, queryParams, body));
        break;
      case "documents":
        resolve(await validateDocumentsEndpoint(user, pathName, method, queryParams));
        break;
      case "workplaces":
        resolve(await validateWorkplaceEndpoints(user, pathName, method, queryParams, body));
        break;
      case "bank_accounts":
        resolve(await validateBankAccountEndpoints(user, pathName, method, queryParams));
        break;
      case "post_tax_deductions":
        resolve(await validatePostTaxDeductionEndpoint(user, pathName, method, queryParams, body));
        break;
      case "contractor_payments":
        resolve(await validateContractorPaymentsEndpoint(user, pathName, method));
        break;
    }
    resolve(false);
  });
};

const validateDocumentsEndpoint = async (user?: any, pathName?: string, method?: string, queryParams?: any) => {
  const id = pathName.split("/")[3]; // /documents/employee_tax_documents/id --> id
  const additionalPath = pathName.split("/")[2]; // /documents/employee_tax_documents --> employee_tax_documents

  // GET --> /documents/employee_tax_documents?employee=id
  if (additionalPath === "employee_tax_documents" && queryParams?.employee && method === "GET") {
    if (user?.isCompanyAdmin) {
      return await doesResourceIdBelongToCompany(`employees/${queryParams.employee}`, user.organizationCheckCompanyId);
    } else {
      return user.checkEmployeeId === queryParams.employee;
    }
  }

  // GET --> /documents/employee_tax_documents/id/download
  if (id && additionalPath === "employee_tax_documents" && method === "GET") {
    if (user?.isCompanyAdmin) {
      return await doesResourceIdBelongToCompany(
        `documents/employee_tax_documents/${id}`,
        user.organizationCheckCompanyId
      );
    } else {
      return await doesResourceIdBelongToEmployee(`documents/employee_tax_documents/${id}`, user.checkEmployeeId);
    }
  }

  // GET --> /documents/contractor_tax_documents?contractor=id
  if (additionalPath === "contractor_tax_documents" && queryParams?.contractor && method === "GET") {
    if (user?.isCompanyAdmin) {
      return await doesResourceIdBelongToCompany(
        `contractors/${queryParams.contractor}`,
        user.organizationCheckCompanyId
      );
    } else {
      return user.checkContractorId === queryParams.contractor;
    }
  }

  // GET --> /documents/contractor_tax_documents/id/download
  if (id && additionalPath === "contractor_tax_documents" && method === "GET") {
    if (user?.isCompanyAdmin) {
      return await doesResourceIdBelongToCompany(
        `documents/contractor_tax_documents/${id}`,
        user.organizationCheckCompanyId
      );
    } else {
      return await doesResourceIdBelongToContractor(`documents/contractor_tax_documents/${id}`, user.checkContractorId);
    }
  }

  // GET --> /documents/company_tax_documents?company=id
  if (user?.isCompanyAdmin && additionalPath === "company_tax_documents" && queryParams?.company && method === "GET") {
    return queryParams.company === user.organizationCheckCompanyId;
  }

  // GET --> /documents/company_tax_documents/id/download
  if (user?.isCompanyAdmin && id && additionalPath === "company_tax_documents" && method === "GET") {
    return await doesResourceIdBelongToCompany(
      `documents/company_tax_documents/${id}`,
      user.organizationCheckCompanyId
    );
  }

  // GET --> /documents/company_tax_documents?company=id
  if (
    user?.isCompanyAdmin &&
    additionalPath === "company_authorization_documents" &&
    queryParams?.company &&
    method === "GET"
  ) {
    return queryParams.company === user.organizationCheckCompanyId;
  }

  // GET --> /documents/company_tax_documents/id/download
  if (user?.isCompanyAdmin && id && additionalPath === "company_authorization_documents" && method === "GET") {
    return await doesResourceIdBelongToCompany(
      `documents/company_authorization_documents/${id}`,
      user.organizationCheckCompanyId
    );
  }

  return false;
};

const validatePostTaxDeductionEndpoint = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  // company admin POST -> /post_tax_deductions
  if (user?.isCompanyAdmin && !additionalPath && body?.employee && method === "POST") {
    // does employee belong to company
    return await doesResourceIdBelongToCompany(`employees/${body?.employee}`, user.organizationCheckCompanyId);
  }

  // company admin GET -> /post_tax_deductions
  if (user?.isCompanyAdmin && method === "GET" && queryParams?.employee) {
    return await doesResourceIdBelongToCompany(`employees/${queryParams?.employee}`, user.organizationCheckCompanyId);
  }

  // GET | PATCH --> /post_tax_deductions/id
  if (id && user?.isCompanyAdmin && !additionalPath && (method === "GET" || method === "PATCH"))
    return await doesResourceIdBelongToCompany(`post_tax_deductions/${id}`, user.organizationCheckCompanyId);

  return false;
};

const validatePayrollEndpoints = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  // Allow GET requests to paper_checks endpoint for company admins
  if (user?.isCompanyAdmin && id && additionalPath === "paper_checks" && method === "GET") {
    return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);
  }

  // Allow GET requests to paper_checks.pdf endpoint for company admins
  if (user?.isCompanyAdmin && id && additionalPath?.startsWith("paper_checks.pdf") && method === "GET") {
    return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);
  }

  // company admin creating a payroll
  if (user?.isCompanyAdmin && !additionalPath && body?.company && method === "POST") {
    return user.organizationCheckCompanyId === body?.company;
  }

  // admin getting payrolls for their company
  if (user?.isCompanyAdmin && method === "GET" && queryParams?.company) {
    return validateCompanyQuery(user, queryParams);
  }

  // non-admin or admin getting a bank account
  if (id) {
    // GET | PATCH | DELETE --> /payrolls/id
    if (user?.isCompanyAdmin && !additionalPath && (method === "GET" || method === "PATCH" || method === "DELETE"))
      return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);

    // POST --> /payrolls/id/approve
    if (user?.isCompanyAdmin && additionalPath === "approve" && method === "POST")
      return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);

    // GET --> /payrolls/id/preview
    if (user?.isCompanyAdmin && additionalPath === "preview" && method === "GET")
      return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);

    // POST --> /payrolls/id/reopen
    if (user?.isCompanyAdmin && additionalPath === "reopen" && method === "POST")
      return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);

    // GET --> /payrolls/id/reports
    if (user?.isCompanyAdmin && additionalPath === "reports" && method === "GET") {
      return await doesResourceIdBelongToCompany(`payrolls/${id}`, user.organizationCheckCompanyId);
    }
  }

  return false;
};

const validatePayrollItemsEndpoints = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  if (id) {
    if (user?.isCompanyAdmin && (method === "GET" || method === "PATCH" || method === "DELETE")) {
      return await doesResourceIdBelongToCompany(`payroll_items/${id}`, user.checkCompanyId);
    }
  }

  // admin getting payroll items for their payroll or employee
  if (user?.isCompanyAdmin && method === "GET" && !additionalPath) {
    if (queryParams?.payroll) {
      return await doesResourceIdBelongToCompany(`payroll_items?payroll=${queryParams?.payroll}`, user.checkCompanyId);
    } else {
      return await doesResourceIdBelongToCompany(`payroll_items?payroll=${queryParams?.employee}`, user.checkCompanyId);
    }
  }

  // company admin creating a contractor payment
  if (user?.isCompanyAdmin && !additionalPath && method === "POST") {
    // so we should check if all the payroll ids are the same for the collection
    const payrollId = body[0].payroll;
    const samePayrollId = body.every((payrollItem: any) => {
      return payrollItem.payroll === payrollId;
    });
    // then make sure the payroll id belongs to company
    if (samePayrollId) {
      return await doesResourceIdBelongToCompany(`payrolls/${payrollId}`, user.organizationCheckCompanyId);
    }
  }

  return false;
};

const validateBankAccountEndpoints = async (user?: any, pathName?: string, method?: string, queryParams?: any) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  // non-admin or admin getting a bank account
  if (id && !additionalPath) {
    if (
      (user?.isCompanyAdmin && (method === "GET" || method === "PATCH")) ||
      (!user?.isCompanyAdmin && method === "GET")
    )
      return await doesResourceIdBelongToCompany(`bank_accounts/${id}`, user.organizationCheckCompanyId);
  }

  if (!id && !additionalPath && queryParams?.employee && method === "GET") {
    if (user?.isCompanyAdmin) {
      // admin can get a list of bank accounts for one of their employees
      return await doesResourceIdBelongToCompany(`employees/${queryParams?.employee}`, user.organizationCheckCompanyId);
    } else {
      // non-admin can get a list of bank accounts for themselves
      return queryParams?.employee === user.checkEmployeeId;
    }
  }

  if (!id && !additionalPath && queryParams?.contractor && method === "GET") {
    if (user?.isCompanyAdmin) {
      // admin can get a list of bank accounts for one of their employees
      return await doesResourceIdBelongToCompany(
        `contractors/${queryParams?.contractor}`,
        user.organizationCheckCompanyId
      );
    } else {
      // non-admin can get a list of bank accounts for themselves
      return queryParams?.contractor === user.checkContractorId;
    }
  }

  return false;
};

const validateWorkplaceEndpoints = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  // non-admin or admin getting a workplace for their company
  if (id) {
    if (
      (user?.isCompanyAdmin && (method === "GET" || method === "PATCH")) ||
      (!user?.isCompanyAdmin && method === "GET")
    )
      return await doesResourceIdBelongToCompany(`workplaces/${id}`, user.organizationCheckCompanyId);
  }

  // admin getting workplaces for their company
  if (user?.isCompanyAdmin && method === "GET" && queryParams?.company) {
    return validateCompanyQuery(user, queryParams);
  }

  // company admin creating a workplace
  if (user?.isCompanyAdmin && !additionalPath && body?.company && method === "POST") {
    return user.organizationCheckCompanyId === body?.company;
  }

  return false;
};

const validatePayscheduleEndpoints = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  // non-admin or admin getting pay schedules for their company
  if (!additionalPath && method === "GET" && queryParams?.company) {
    return validateCompanyQuery(user, queryParams);
  }

  // non-admin or admin gettting paydays from pay schedule
  if (id && additionalPath === "paydays" && method === "GET") {
    return await doesResourceIdBelongToCompany(`pay_schedules/${id}`, user.organizationCheckCompanyId);
  }

  // company admin getting or patching a pay schedule
  if (user?.isCompanyAdmin && id && !additionalPath && (method === "GET" || method === "PATCH")) {
    return await doesResourceIdBelongToCompany(`pay_schedules/${id}`, user.organizationCheckCompanyId);
  }

  // company admin creating pay schedule
  if (user?.isCompanyAdmin && !additionalPath && body?.company && method === "POST") {
    return user.organizationCheckCompanyId === body?.company;
  }

  return false;
};

const validateCompanyEndpoints = (user: any, pathName: string, method: string) => {
  if (user?.isCompanyAdmin) {
    const id = getIdFromPathName(pathName);
    const additionalPath = getAdditionalPathFromPathName(pathName);

    // company admin creating a new company
    if (method === "POST" && !additionalPath) {
      return true;
    }

    // company onboard POST -> /companies/id/onboard
    if (id === user.organizationCheckCompanyId && additionalPath === "onboard" && method === "POST") {
      return true;
    }

    // company components POST -> /companies/id/components
    if (id === user.organizationCheckCompanyId && additionalPath === "components" && method === "POST") {
      return true;
    }

    // non-admin GET -> /companies/(own id)/reports
    if (id === user.organizationCheckCompanyId && additionalPath === "reports" && method === "GET") {
      return true;
    }

    // company admin getting or updating their own company
    if (id && id === user.organizationCheckCompanyId && !additionalPath && (method === "GET" || method === "PATCH")) {
      return true;
    }
  }

  return false;
};

const validateEmployeeEndpoints = async (
  authUserRole: User["role"],
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);
  const organizationId = user?.organizationId;
  let employeeId = user?.checkEmployeeId;

  if (user?.isCompanyAdmin) {
    // company admin POST -> /employees
    if (!additionalPath && method === "POST") {
      if (body?.company === user.organizationCheckCompanyId) {
        return true;
      } else {
        return false;
      }
    }

    // company admin GET all -> /employees?company=id
    if (!additionalPath && method === "GET" && queryParams?.company) {
      return validateCompanyQuery(user, queryParams);
    }

    // company admin GET all -> /employees?{id=checkEmployeeId(repeated)}
    if (!additionalPath && method === "GET" && queryParams?.id) {
      // TO DO: this may need to change in the future to be more robust/secure.
      // just check the first id and use that as a proxy
      let employeeId;
      if (Array.isArray(queryParams?.id)) {
        employeeId = queryParams?.id[0];
      } else {
        employeeId = queryParams?.id;
      }

      return doesResourceIdBelongToCompany(`employees/${employeeId}`, user.organizationCheckCompanyId);
    }

    if (id) {
      const fetchedUser = await getUserByCheckEmployeeId(id, organizationId);
      // Checks if employee id belongs to the same company as admin
      // and then sets it so it can be used by the below conditionals
      if (user.organizationCheckCompanyId === fetchedUser?.organizationCheckCompanyId) {
        employeeId = fetchedUser?.checkEmployeeId;
      }
    }
  }

  // admin or non-admin GET -> /employees/id/paystubs
  if (id === employeeId && additionalPath === "paystubs" && method === "GET") {
    return true;
  }

  // admin or non-admin POST -> /employees/id/onboard
  if (id === employeeId && additionalPath === "onboard" && method === "POST") {
    return true;
  }

  // admin or non-admin POST -> /employees/id/components/component_name
  if (id === employeeId && additionalPath === "components" && method === "POST") {
    return true;
  }

  // admin or non-admin POST -> /employees/:id/company_defined_attributes
  if (
    user?.isCompanyAdmin &&
    additionalPath === "company_defined_attributes" &&
    (method === "GET" || method === "PATCH")
  ) {
    return true;
  }

  // admin or non-admin GET | PATCH -> /employees/id
  if (id && id === employeeId && !additionalPath && (method === "PATCH" || method === "GET")) {
    return true;
  }

  return false;
};

const validateContractorEndpoints = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);
  const organizationId = user?.organizationId;
  let contractorId = user?.checkContractorId;

  if (user?.isCompanyAdmin) {
    // company admin POST -> /contractors
    if (!additionalPath && method === "POST") {
      if (body?.company === user.organizationCheckCompanyId) {
        return true;
      } else {
        return false;
      }
    }

    // company admin GET all -> /contractors?company=id
    if (!additionalPath && method === "GET" && queryParams?.company) {
      return validateCompanyQuery(user, queryParams);
    }

    // company admin GET all -> /contractors?{id=checkContractorId(repeated)}
    if (!additionalPath && method === "GET" && queryParams?.id) {
      // TO DO: this may need to change in the future to be more robust/secure.
      // just check the first id and use that as a proxy
      let contractorId;
      if (Array.isArray(queryParams?.id)) {
        contractorId = queryParams?.id[0];
      } else {
        contractorId = queryParams?.id;
      }

      return doesResourceIdBelongToCompany(`contractors/${contractorId}`, user.organizationCheckCompanyId);
    }

    if (id) {
      const fetchedUser = await getUserByCheckContractorId(id, organizationId);
      // Checks if contractor id belongs to the same company as admin
      // and then sets it so it can be used by the below conditionals
      if (user.organizationCheckCompanyId === fetchedUser?.organizationCheckCompanyId) {
        contractorId = fetchedUser?.checkContractorId;
      }
    }
  }

  // admin or non-admin GET -> /contractors/id/payments
  if (id === contractorId && additionalPath === "payments" && method === "GET") {
    return true;
  }

  // admin or non-admin POST -> /contractors/id/onboard
  if (id === contractorId && additionalPath === "onboard" && method === "POST") {
    return true;
  }

  // potentially not necessary
  // admin or non-admin POST -> /contractors/id/components/component_name
  if (id === contractorId && additionalPath === "components" && method === "POST") {
    return true;
  }

  // admin or non-admin GET | PATCH -> /contractors/id
  if (id && id === contractorId && !additionalPath && (method === "PATCH" || method === "GET")) {
    return true;
  }

  return false;
};

const validateEarningRateEndpoints = async (
  user?: any,
  pathName?: string,
  method?: string,
  queryParams?: any,
  body?: any
) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);
  if (user?.isCompanyAdmin) {
    // company admin can create earning rate for their employees
    if (!additionalPath && method === "POST") {
      return await doesResourceIdBelongToCompany(`employees/${body?.employee}`, user.organizationCheckCompanyId);
    }

    // company admin can get list of earning rate for their employees
    if (!additionalPath && method === "GET" && queryParams?.employee) {
      return await doesResourceIdBelongToCompany(`employees/${queryParams?.employee}`, user.organizationCheckCompanyId);
    }

    // company admin can get or update earning rate for their employee
    if (id && !additionalPath && (method === "PATCH" || method === "GET")) {
      return await doesResourceIdBelongToCompany(`earning_rates/${id}`, user.organizationCheckCompanyId);
    }
  } else {
    // employee can get own earning rate information
    if (id && !additionalPath && method === "GET") {
      return await doesResourceIdBelongToEmployee(`earning_rates/${id}`, user.checkEmployeeId);
    }
  }

  return false;
};

const validateContractorPaymentsEndpoint = async (user?: any, pathName?: string, method?: string) => {
  const id = getIdFromPathName(pathName);
  const additionalPath = getAdditionalPathFromPathName(pathName);

  if (id) {
    if (user?.isCompanyAdmin && (method === "GET" || method === "PATCH" || method === "DELETE")) {
      return await doesResourceIdBelongToCompany(`contractor_payments/${id}`, user.organizationCheckCompanyId);
    }

    if (user?.isCompanyAdmin && method === "GET" && !additionalPath) {
      return await doesResourceIdBelongToCompany(`contractor_payments/${id}`, user.checkCompanyId);
    }
  }

  return false;
};
