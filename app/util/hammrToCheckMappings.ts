export const propertyIsPartOfAddress = (property: string) => {
  return (
    property === "address" ||
    property === "address1" ||
    property === "address2" ||
    property === "city" ||
    property === "state" ||
    property === "postalCode"
  );
};

export const hammrCategoryToCheckBenefitType = (category: string) => {
  switch (category) {
    case "MEDICAL":
      return "125_medical";
    case "DENTAL":
      return "125_dental";
    case "VISION":
      return "125_vision";
    case "LIFE":
      return "125_life";
    case "DISABILITY":
      return "125_disability";
    case "401K":
      return "401k";
    case "ROTH_401K":
      return "roth_401k";
    case "FSA_MEDICAL":
      return "fsa_medical";
    case "FSA_DEPENDENT_CARE":
      return "fsa_dependent_care";
    case "HSA":
      return "hsa";
    case "SIMPLE_IRA":
      return "simple_ira";
    // it's preferable we have a default and default currently set on the model is `MEDICAL` - we will do the same here, but this shouldn't ever happen as we will catch it in the validation
    // see middlewares/validation/benefits.ts
    default:
      return "MEDICAL";
  }
};

export const payloadToCheckCompanyMappings = {
  name: "legal_name",
  tradeName: "trade_name",
  otherBusinessName: "other_business_name",
  businessType: "business_type",
  website: "website",
  email: "email",
  phone: "phone",
  address1: "line1",
  address2: "line2",
  city: "city",
  state: "state",
  postalCode: "postal_code",
  payPeriod: "pay_frequency",
  startDate: "start_date",
};

export const payloadToCheckEmployeeMappings = {
  firstName: "first_name",
  lastName: "last_name",
  email: "email",
  active: "active",
  address1: "line1",
  address2: "line2",
  city: "city",
  state: "state",
  postalCode: "postal_code",
  dob: "dob",
  company: "company",
  workplaces: "workplaces",
  startDate: "start_date",
  metadata: "metadata",
};

export const payloadToCheckContractorMappings = {
  firstName: "first_name",
  lastName: "last_name",
  email: "email",
  address1: "line1",
  address2: "line2",
  city: "city",
  state: "state",
  postalCode: "postal_code",
  startDate: "start_date",
  company: "company",
};

export const payloadToCheckEarningRatesMappings = {
  amount: "amount",
  period: "period",
  checkEmployeeId: "employee",
  name: "name",
  active: "active",
  weeklyHours: "workweek_hours",
};

export const payloadToCheckCompanyBenefitsMappings = {
  name: "description",
  category: "benefit",
  checkCompanyId: "company",
  benefitStartDate: "effective_start", // string
  benefitEndDate: "effective_end", // string
  metadata: "metadata",
};

export const payloadToCheckEmployeeBenefitsMappings = {
  name: "description",
  period: "period",
  companyContributionPercent: "company_contribution_percent",
  employeeContributionPercent: "employee_contribution_percent",
  companyPeriodAmount: "company_period_amount",
  employeePeriodAmount: "employee_period_amount",
  companyContributionAmount: "company_contribution_amount",
  employeeContributionAmount: "employee_contribution_amount",
  benefitStartDate: "effective_start", // string
  benefitEndDate: "effective_end", // string
  category: "benefit",
  checkEmployeeId: "employee",
  checkCompanyBenefitId: "company_benefit",
  metadata: "metadata",
};

export const payloadToCheckEarningCodeMappings = {
  name: "name",
  checkCompanyId: "company",
  type: "type",
  metadata: "metadata",
};

export const payloadToCheckPayrollMappings = {
  checkCompanyId: "company",
  periodStart: "period_start",
  periodEnd: "period_end",
  payday: "payday",
  processingPeriod: "processing_period",
  type: "type",
  payFrequency: "pay_frequency",
  fundingMethod: "funding_payment_method",
  paySchedule: "pay_schedule",
  payrollItems: "items",
  contractorPayments: "contractor_payments",
  offCycleOptions: "off_cycle_options",
  metadata: "metadata",
};

export const mappingsSelector = {
  companies: payloadToCheckCompanyMappings,
  employees: payloadToCheckEmployeeMappings,
  contractors: payloadToCheckContractorMappings,
  earning_rates: payloadToCheckEarningRatesMappings,
  company_benefits: payloadToCheckCompanyBenefitsMappings,
  benefits: payloadToCheckEmployeeBenefitsMappings,
  earning_codes: payloadToCheckEarningCodeMappings,
  payrolls: payloadToCheckPayrollMappings,
};
