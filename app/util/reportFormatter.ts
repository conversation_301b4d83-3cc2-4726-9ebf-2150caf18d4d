import { stringify } from "csv-stringify/sync";
import { UserWage } from "./financial";

const format = (value: any) => (value < 10 ? `0${value}` : value.toString());
const decimalFormat = (value: any) => `${format(Math.floor(value / 60))}.${format(Math.round((value % 60) / 0.6))}`;
const minutesFormat = (value: any) => `${format(Math.floor(value / 60))}:${format(value % 60)}`;
export const formattedMinutes = (value: any, useDecimal: boolean) =>
  useDecimal ? decimalFormat(value) : minutesFormat(value);

export const formatProjectHoursCost = (
  employees: UserWage[],
  totalWages: number,
  useDecimalFormat: boolean,
  expand: string
) => {
  let body = "";
  let projectTotalMinutes: any = 0;
  let projectTotalBreakMinutes: any = 0;

  // if expand === "day" - do loop instead of stringifying entire report - needs to add individual date rows
  if (expand === "day") {
    employees.forEach((userWageObj) => {
      const { firstName, lastName, hourlyWage, totalMinutes, breakMinutes, totalWages, timeWorkedByDay } = userWageObj;
      projectTotalMinutes += totalMinutes;
      projectTotalBreakMinutes += breakMinutes;

      const aggUserWageObj = {
        name: `${firstName} ${lastName}`,
        hourlyWage,
        totalMinutes: formattedMinutes(totalMinutes, useDecimalFormat),
        breakMinutes: formattedMinutes(breakMinutes, useDecimalFormat),
        totalWages: totalWages.toLocaleString("en-us", {
          style: "currency",
          currency: "USD",
        }),
      };

      // stringify aggUserWageObj --> then loop through timesheets and add rows
      body += stringify([aggUserWageObj]);

      // enrich timesheetObj properties
      const formattedDayAndHours = timeWorkedByDay.map((timesheetObj) => {
        return {
          date: `${timesheetObj.formattedDate}`,
          hourlyWage: "",
          totalMinutes: formattedMinutes(timesheetObj.totalMinutes as number, useDecimalFormat),
          breakMinutes: formattedMinutes(timesheetObj.breakMinutes as number, useDecimalFormat),
          totalWages: "",
          description: timesheetObj.timesheetDescription,
        };
      });

      body += stringify(formattedDayAndHours);
    });
  } else {
    // default / original report formatting
    const report = employees.map(({ firstName, lastName, hourlyWage, totalMinutes, breakMinutes, totalWages }) => {
      projectTotalMinutes += totalMinutes;
      projectTotalBreakMinutes += breakMinutes;

      return {
        name: `${firstName} ${lastName}`,
        hourlyWage,
        totalMinutes: formattedMinutes(totalMinutes, useDecimalFormat),
        breakMinutes: formattedMinutes(breakMinutes, useDecimalFormat),
        totalWages: totalWages.toLocaleString("en-us", {
          style: "currency",
          currency: "USD",
        }),
      };
    });

    body += stringify(report);
  }

  // same total row for all report types
  const totalAmount = totalWages.toLocaleString("en-us", {
    style: "currency",
    currency: "USD",
  });

  // if expand, no total - else total
  body += stringify([
    {
      0: "Total",
      1: "",
      2: formattedMinutes(projectTotalMinutes, useDecimalFormat),
      3: formattedMinutes(projectTotalBreakMinutes, useDecimalFormat),
      4: totalAmount,
    },
  ]);
  body += ",,,,,\n";

  return body;
};
