export const getRootFromPathName = (pathName: string): string => {
  return pathName.split("/")[1]; // i.e. /employees --> employees
};

export const getIdFromPathName = (pathName: string): string => {
  return pathName.split("/")[2]; // i.e. /employees/id/ --> id
};

export const getAdditionalPathFromPathName = (pathName: string): string => {
  return pathName.split("/")[3]; // i.e. /employees/id/onboard --> onboard
};

export const getNestedProperty = (obj: Record<string, any>, path: string) => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};
