import { PAYROLL_CATEGORY_GROUPS } from "@/models/glaccountmapping";
import GlAccountMapping from "@/models/glaccountmapping";
import { CompanyBenefit } from "../models";

/** Formats a numeric currency value to a number with exactly two decimal places. */
export function formatCurrency(amount: number): number {
  return parseFloat(amount.toFixed(2));
}

/** Converts a display name (e.g., "Gross Wages") to an internal category key format (e.g., "GROSS_WAGES"). */
export function formatCategoryKey(displayName: string): string {
  return displayName.trim().toUpperCase().replace(/\s+/g, "_");
}

/** Converts an internal category key (e.g., "EMPLOYER_TAXES") to a user-friendly display name (e.g., "Employer Taxes"). */
export function formatDisplayName(categoryKey: string): string {
  return categoryKey
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

/** Generates a standardized internal key for benefit-related expense or liability categories. */
export function formatBenefitKey(benefitName: string, type: "ER_EXPENSE" | "ER_LIABILITY" | "EE_LIABILITY"): string {
  const normalizedName = formatCategoryKey(benefitName);

  return `${normalizedName}_${type}`;
}

/** Formats a line item description, optionally appending a project name. */
export function formatDescription(baseDescription: string, projectName?: string): string {
  return projectName ? `${baseDescription} - ${projectName}` : baseDescription;
}

/** Attempts to find the corresponding GL account ID for a given payroll category using various matching strategies. */
export function getAccountId(category: string, glAccountMap: Record<string, string>): string {
  // Direct match
  if (glAccountMap[category]) {
    return glAccountMap[category];
  }

  // Standardized key match
  const standardizedKey = formatCategoryKey(category);
  if (glAccountMap[standardizedKey]) {
    return glAccountMap[standardizedKey];
  }

  // Benefit category matching logic
  if (category.includes("ER") || category.includes("EE")) {
    if (category.includes("(ER contribution)")) {
      const benefitName = category.split("(ER contribution)")[0].trim();
      const benefitKey = formatBenefitKey(benefitName, "ER_EXPENSE");
      if (glAccountMap[benefitKey]) return glAccountMap[benefitKey];
    }
    if (category.includes("Liability (ER contribution)")) {
      const benefitName = category.split("Liability (ER contribution)")[0].trim();
      const benefitKey = formatBenefitKey(benefitName, "ER_LIABILITY");
      if (glAccountMap[benefitKey]) return glAccountMap[benefitKey];
    }
    if (category.includes("(EE contribution)")) {
      const benefitName = category.split("(EE contribution)")[0].trim();
      const benefitKey = formatBenefitKey(benefitName, "EE_LIABILITY");
      if (glAccountMap[benefitKey]) return glAccountMap[benefitKey];
    }
    // Internal format like SOME_BENEFIT_ER_LIABILITY
    if (category.includes("_ER") || category.includes("_EE")) {
      const benefitMatches = category.match(/^(.+?)_(ER|EE)(?:_LIABILITY)?$/);
      if (benefitMatches) {
        const [_, benefitName, contributionType] = benefitMatches;
        // Determine if it's liability or expense based on the original category string
        const type = category.includes("LIABILITY") ? `${contributionType}_LIABILITY` : `${contributionType}_EXPENSE`;
        // Construct the key and check the map
        const benefitKey = formatBenefitKey(benefitName, type as "ER_EXPENSE" | "ER_LIABILITY" | "EE_LIABILITY");
        if (glAccountMap[benefitKey]) return glAccountMap[benefitKey];
      }
    }
  }

  // Tax category fallback to general employer tax expense
  if (category.includes("Tax") || category === "EMPLOYER_TAXES") {
    if (glAccountMap["EMPLOYER_TAX_EXPENSE"]) return glAccountMap["EMPLOYER_TAX_EXPENSE"];
  }

  // Return empty string if no match found
  return "";
}

/** Checks if all required payroll categories, including dynamic benefit categories, have corresponding GL account mappings. */
export async function validateRequiredGlAccountMappings(
  integrationUserTokenId: number,
  organizationId: number,
  companyBenefits: CompanyBenefit[]
): Promise<{ isValid: boolean; missingCategories: string[] }> {
  // Start with base required categories from predefined groups using a Set for efficiency
  const expectedCategoriesSet = new Set<string>([
    ...PAYROLL_CATEGORY_GROUPS.ASSETS,
    ...PAYROLL_CATEGORY_GROUPS.EXPENSES.filter((cat) => cat !== "EMPLOYER_BENEFITS_EXPENSE"), // Base expense handled separately
    ...PAYROLL_CATEGORY_GROUPS.LIABILITIES.filter(
      (cat) => cat !== "EMPLOYEE_BENEFITS_LIABILITY" && cat !== "EMPLOYER_BENEFITS_LIABILITY" // Base liabilities handled separately
    ),
  ]);

  // Add specific required categories for each company benefit
  companyBenefits.forEach((benefit) => {
    const erExpenseKey = formatBenefitKey(benefit.name, "ER_EXPENSE");
    const erLiabilityKey = formatBenefitKey(benefit.name, "ER_LIABILITY");
    const eeLiabilityKey = formatBenefitKey(benefit.name, "EE_LIABILITY");

    expectedCategoriesSet.add(erExpenseKey);
    expectedCategoriesSet.add(erLiabilityKey);
    expectedCategoriesSet.add(eeLiabilityKey);
  });

  // Fetch existing mappings for this integration
  const glAccountMappings = await GlAccountMapping.findAll({
    where: {
      organizationId,
      integrationUserTokenId,
    },
  });

  // Determine which expected categories are missing a mapping
  const mappedCategories = glAccountMappings.map((mapping: GlAccountMapping) => mapping.payrollCategory);
  const expectedCategoriesArray = Array.from(expectedCategoriesSet); // Convert Set to Array for filtering
  const missingCategories = expectedCategoriesArray.filter((category) => !mappedCategories.includes(category));

  return {
    isValid: missingCategories.length === 0,
    missingCategories,
  };
}
