import fetch from "node-fetch";
import * as Sentry from "@sentry/node";
import { JournalEntryResponse } from "@/types/journalEntry";

const { RUTTER_API_URL, RUTTER_CLIENT_ID, RUTTER_CLIENT_SECRET, RUTTER_VERSION } = process.env;

// Interface for Rutter account data
export interface RutterAccount {
  id: string;
  account_type: string;
  name: string;
  platform_id: string;
  platform_data: {
    SystemAccount?: boolean;
    [key: string]: any;
  };
  accountCategory?: string;
  accountType?: string;
}

export async function exchangePublicTokenForAccessToken(publicToken: string) {
  try {
    const response = await fetch(`${RUTTER_API_URL}/item/public_token/exchange`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Rutter-Version": RUTTER_VERSION,
      },
      body: JSON.stringify({
        client_id: RUTTER_CLIENT_ID,
        public_token: publicToken,
        secret: RUTTER_CLIENT_SECRET,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "An error occurred while exchanging the token.");
    }

    const data = await response.json();

    return data;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}

export async function getAccountingAccounts(accessToken: string) {
  try {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");

    let allAccounts: RutterAccount[] = [];
    let nextCursor: string | null = null;

    do {
      // Build URL with cursor parameter if it exists
      let url = `${RUTTER_API_URL}/versioned/accounting/accounts?access_token=${accessToken}&expand=platform_data`;
      if (nextCursor) {
        url += `&cursor=${nextCursor}`;
      }

      const response = await fetch(url, {
        headers: {
          Authorization: `Basic ${encodedCredentials}`,
          "X-Rutter-Version": RUTTER_VERSION,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();

        Sentry.captureException(errorData);

        if (errorData.error_type === "CONNECTION_ERROR") {
          // something is wrong with the connection
          if (errorData.error_code === "PRODUCT_NOT_READY") {
            // accounts are not ready yet
            return null;
          }

          // anything else is an expired connection
          throw new Error("expired");
        }

        return null;
      }

      const data = await response.json();
      // Add current page of accounts to our collection
      if (data.accounts && Array.isArray(data.accounts)) {
        allAccounts = [...allAccounts, ...data.accounts];
      }

      // Update cursor for next iteration
      nextCursor = data.next_cursor || null;
    } while (nextCursor);

    // filter out account that is non_posting or has platform_data.SystemAccount
    // account type cannot be accounts_payable, or accounts_receivable
    const filteredAccounts = allAccounts.filter(
      (account: RutterAccount) =>
        !["non_posting", "accounts_payable", "accounts_receivable"].includes(account.account_type) &&
        !account.platform_data.SystemAccount
    );

    return filteredAccounts;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}

export async function createJournalEntry(accessToken: string, body: JournalEntryResponse) {
  try {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");
    const response = await fetch(`${RUTTER_API_URL}/versioned/accounting/journal_entries?access_token=${accessToken}`, {
      method: "POST",
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        "X-Rutter-Version": RUTTER_VERSION,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (response.status === 451 || response.status === 401) {
      // connection unauthenticated, maybe expired
      throw new Error("expired");
    }

    if (!response.ok) {
      const errorData = await response.json();
      Sentry.captureException(errorData);

      throw new Error(errorData.error_message || "Failed to create journal entry");
    }

    const data = await response.json();

    return data;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}

export async function getAccessTokenConnection(accessToken: string) {
  try {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");
    const response = await fetch(`${RUTTER_API_URL}/versioned/connections/access_token?access_token=${accessToken}`, {
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        "X-Rutter-Version": RUTTER_VERSION,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to get access token connection");
    }

    const data = await response.json();

    return data.connection;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}

export async function deleteConnection(connectionId: string) {
  try {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");
    const response = await fetch(`${RUTTER_API_URL}/versioned/connections/${connectionId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Basic ${encodedCredentials}`, // Correct Basic Auth usage
        "X-Rutter-Version": RUTTER_VERSION,
      },
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();

    return data;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}

export async function getConnectionStatus(accessToken: string) {
  try {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");
    const response = await fetch(`${RUTTER_API_URL}/versioned/connections/status?access_token=${accessToken}`, {
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        "X-Rutter-Version": RUTTER_VERSION,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();

      Sentry.captureException(errorData);

      return null;
    }

    const data = await response.json();

    return data.status;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}

export async function triggerIncrementalSync(accessToken: string) {
  try {
    const credentials = `${RUTTER_CLIENT_ID}:${RUTTER_CLIENT_SECRET}`;
    const encodedCredentials = Buffer.from(credentials).toString("base64");
    const response = await fetch(
      `${RUTTER_API_URL}/versioned/connections/incremental_sync?access_token=${accessToken}&force_fetch=true`,
      {
        method: "POST",
        headers: {
          Authorization: `Basic ${encodedCredentials}`,
          "X-Rutter-Version": RUTTER_VERSION,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.status === 451 || response.status === 401) {
      // connection unauthenticated, maybe expired
      throw new Error("expired");
    }

    if (!response.ok) {
      const errorData = await response.json();
      Sentry.captureException(errorData);

      throw new Error(errorData.error_message || errorData.error || "Failed to trigger incremental sync");
    }

    const data = await response.json();

    // Rutter returns { success: true } on success

    return data;
  } catch (e) {
    Sentry.captureException(e);

    throw new Error(e.message);
  }
}
