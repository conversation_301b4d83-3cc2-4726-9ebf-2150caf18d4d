import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import isBetween from "dayjs/plugin/isBetween";
import { PayFrequency } from "@/types/checkDto";
import { WEEK_DAYS, type WeekDay } from "@/models/overtimesettings";

// extends dayjs with isBetween plugin
dayjs.extend(isBetween);

// extends dayjs with utc plugin
dayjs.extend(utc);
dayjs.extend(timezone);

const ONE_DAY = 86400000; // in milliseconds

export interface AdjustedDates {
  start: Date;
  end: Date;
}

export const toEpoch = (date: Date) => (date ? new Date(date)?.getTime() : null);

// the algorithm - calculate shifts based on user's local timezone
export const calculateDateShift = (timezone: string, previousDateTime: Date, newDateTime: Date) => {
  const dateTimeShifts: Record<string, number> = {
    days: 0,
    hours: null,
    minutes: null,
  };

  if (newDateTime) {
    const days =
      (dayjs(newDateTime).tz(timezone).startOf("day").valueOf() -
        dayjs(previousDateTime).tz(timezone).startOf("day").valueOf()) /
      ONE_DAY;
    dateTimeShifts.days = days;
    dateTimeShifts.hours = dayjs(newDateTime).tz(timezone).hour();
    dateTimeShifts.minutes = dayjs(newDateTime).tz(timezone).minute();
  }

  return dateTimeShifts;
};

// should return new Date after applying shifts/changes
export const applyDateTimeChanges = (
  timezone: string,
  existingDate: Date,
  existingEventIsSingleDay: boolean,
  changedEventOriginallySingleDay: boolean,
  dateTimeShifts: Record<string, number>
) => {
  let newDate = dayjs(existingDate).tz(timezone);

  // algorithmically - date shifts only apply when the original targeted event and the changed event are both either single day events or multiday events
  if (existingEventIsSingleDay === changedEventOriginallySingleDay) {
    if (dateTimeShifts?.days && dateTimeShifts?.days > 0) {
      newDate = newDate.add(dateTimeShifts.days, "day");
    } else if (dateTimeShifts?.days && dateTimeShifts?.days < 0) {
      newDate = newDate.subtract(Math.abs(dateTimeShifts.days), "day");
    }
  }

  // set the hour and minutes if dateTimeShift properties
  if (dateTimeShifts?.hours) {
    newDate = newDate.set("hour", dateTimeShifts.hours);
  }

  if (dateTimeShifts?.minutes) {
    newDate = newDate.set("minute", dateTimeShifts.minutes);
  }

  return newDate.toDate();
};

export const isEventSingleDay = (startDateTime: Date, endDateTime: Date, timezone: string) => {
  // Parse the dates using dayjs
  const start = dayjs(startDateTime).tz(timezone);
  const end = dayjs(endDateTime).tz(timezone);

  // Check if both dates are valid
  if (!start.isValid() || !end.isValid()) {
    throw new Error("Invalid startDateTime or endDateTime.");
  }

  // Compare if the start and end date are the same
  return start.isSame(end, "day");
};

/**
 * Calculates the week number for a given date relative to the start of the first week.
 *
 * @param {dayjs.Dayjs} startOfFirstWeek - The start date of the first week.
 * @param {dayjs.Dayjs} currentDate - The date for which to calculate the week number.
 * @returns {number} - The week number of the currentDate relative to the startOfFirstWeek.
 */
export function calculateWeekNumber(startOfFirstWeek: dayjs.Dayjs, currentDate: dayjs.Dayjs) {
  const start = startOfFirstWeek.startOf("day");
  const current = currentDate.startOf("day");
  const differenceInDays = current.diff(start, "day");
  const weekNumber = Math.floor(differenceInDays / 7) + 1;

  return weekNumber;
}

export function convertDatesToTimestamps(obj: any, seen = new WeakSet()): any {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  // Check for circular references
  if (seen.has(obj)) {
    return obj;
  }
  seen.add(obj);

  if (obj instanceof Date) {
    return obj.getTime();
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => convertDatesToTimestamps(item, seen));
  }

  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value instanceof Date) {
      result[key] = value.getTime();
    } else if (typeof value === "object" && value !== null) {
      result[key] = convertDatesToTimestamps(value, seen);
    } else {
      result[key] = value;
    }
  }

  return result;
}

export const calculateDayDiffsForFullWeeks = (
  startTimestamp: number,
  endTimestamp: number,
  startDayTarget: number,
  endDayTarget: number
): { adjustStart: number; adjustEnd: number } => {
  const startDate = dayjs(startTimestamp);
  const endDate = dayjs(endTimestamp);

  // Calculate adjustments for start date
  let adjustStart = startDayTarget - startDate.day();
  if (adjustStart > 0) {
    // If the target day is later in the week, subtract a full week to get the previous occurrence
    adjustStart -= 7;
  }

  // Calculate adjustments for end date
  let adjustEnd = endDayTarget - endDate.day();
  if (adjustEnd < 0) {
    // If the target day has already passed, add a full week to get the next occurrence
    adjustEnd += 7;
  }

  return { adjustStart, adjustEnd };
};

// day of week for dayjs - 0 is Sunday, 1 is Monday, etc.
export const mapWeekStartDayToInt = (weekStartDay: string) => {
  switch (weekStartDay) {
    case "SUNDAY":
      return 0;
    case "MONDAY":
      return 1;
    case "TUESDAY":
      return 2;
    case "WEDNESDAY":
      return 3;
    case "THURSDAY":
      return 4;
    case "FRIDAY":
      return 5;
    case "SATURDAY":
      return 6;
    default: // return Monday or 1
      return 1;
  }
};

export const getNearestFullWeekIntervals = (
  startTimestamp = 1609488000000, // 2021-01-01 00:00:00 - Hammr's assumed birthday
  endTimestamp: number = Date.now().valueOf(),
  startDayOfWeek: string
): { start: dayjs.Dayjs; end: dayjs.Dayjs } => {
  const startDayOfWeekInt = mapWeekStartDayToInt(startDayOfWeek);
  const endDayOfWeekInt = (startDayOfWeekInt + 6) % 7;

  const { adjustStart, adjustEnd } = calculateDayDiffsForFullWeeks(
    startTimestamp,
    endTimestamp,
    startDayOfWeekInt,
    endDayOfWeekInt
  );

  const beginningFullWeekDate = dayjs(startTimestamp).add(adjustStart, "day").startOf("day");
  const endFullWeekDate = dayjs(endTimestamp).add(adjustEnd, "day").endOf("day");

  const adjustedDates = {
    start: beginningFullWeekDate,
    end: endFullWeekDate,
  };

  return adjustedDates;
};

// weekEnding is a date string (MM-DD-YYYY) - no timezone - it should then be parsed with the organization's timezone
export const getLastSevenDays = (weekEnding: string, tz = "America/Los_Angeles") => {
  const endDay = dayjs.tz(weekEnding, tz);

  const days = [];

  for (let i = 6; i >= 0; i--) {
    days.push(endDay.subtract(i, "day"));
  }

  return days;
};

export const getValuesForLastSevenDays = (
  attributedTimesheets: any[],
  attribute: string,
  conversionFn = (val: any) => val,
  weekEnding: string,
  tz = "America/Los_Angeles"
): string[] => {
  const mappedDatesIndex = getLastSevenDays(weekEnding).map((date) => date.format("MM-DD-YYYY"));

  const valuesForLastSevenDays = mappedDatesIndex.map((date) => {
    let valueToReturn = 0;
    const day = dayjs.tz(date, tz);

    attributedTimesheets.forEach((timesheet) => {
      const timesheetDate = dayjs(timesheet.clockIn).tz(tz);

      if (timesheetDate.isSame(day, "day")) {
        const convertedValue = conversionFn(timesheet[attribute]);
        // aggregate values since there could be multiple timesheets for the same day
        valueToReturn += Number(convertedValue);
      }
    });

    // Return in string format with 2 decimal places
    return `${valueToReturn.toFixed(2)}`;
  });

  return valuesForLastSevenDays;
};

// helper to take a date in YYYY-MM-DD format and return a timezone adjusted ISO string of either start or end of day
export const getISOStringForDateString = (
  dateString: string,
  whichTime: "start" | "end",
  tz = "America/Los_Angeles"
): string => {
  const date = dayjs.tz(dateString, tz);

  if (whichTime === "start") {
    return date.startOf("day").toISOString();
  } else if (whichTime === "end") {
    return date.endOf("day").toISOString();
  }

  return null;
};

export const getLocalizedTimestampForDateString = (
  dateString: string,
  tz = "America/Los_Angeles",
  startOrEnd: "start" | "end" = "start"
) => {
  let date = dayjs.tz(dateString, tz);

  if (startOrEnd === "start") {
    date = date.startOf("day");
  } else if (startOrEnd === "end") {
    date = date.endOf("day");
  }

  return date.valueOf();
};

export const calculateSalariedWorkingHours = (weeklyHours: number, payFrequency: PayFrequency) => {
  switch (payFrequency) {
    case "weekly":
      return weeklyHours;
    case "biweekly":
      return weeklyHours * 2;
    case "semimonthly":
      // more precise calculation: (weeklyHours * 52) / 24
      return Math.round(((weeklyHours * 52) / 24) * 100) / 100;
    case "monthly":
      return (weeklyHours * 52) / 12; // 52 weeks in a year, 12 months in a year
    case "quarterly":
      return (weeklyHours * 52) / 4; // 52 weeks in a year, 4 quarters in a year
    case "annually":
      return weeklyHours * 52;
    default:
      return weeklyHours;
  }
};

export const formatDateIso8601 = (date: Date, timezone = "America/Los_Angeles"): string => {
  const convertedDate = dayjs.tz(date, timezone);

  return convertedDate.format("YYYY-MM-DD");
};

export const getDayOfWeekString = (dayNumber: number): WeekDay => {
  return WEEK_DAYS[dayNumber];
};

export const mapDatesToWeekDays = (
  dates: Array<dayjs.Dayjs | string | Date>
): Record<WeekDay, [string, number] | null> => {
  const result: Record<WeekDay, [string, number] | null> = {
    SUNDAY: null,
    MONDAY: null,
    TUESDAY: null,
    WEDNESDAY: null,
    THURSDAY: null,
    FRIDAY: null,
    SATURDAY: null,
  };

  dates.forEach((date, index) => {
    // convert to dayjs if not already (handles string/Date input gracefully)
    const dayOfWeek = dayjs(date).day();
    const weekDay = WEEK_DAYS[dayOfWeek];
    const formattedDate = dayjs(date).format("MM/DD/YYYY");
    result[weekDay] = [formattedDate, index];
  });

  return result;
};
