import dayjs from "dayjs";
import {
  calculateBreakMinutes,
  calculateTotalWorkedMinutes,
  calculateJobsiteWorkedMinutes,
  calculateOvertime,
  calculateDailyOvertime,
  updateMinutesPerDay,
  clockInString,
  clockOutString,
  dateInOrganizationTimezone,
} from "@/util/timesheetHelper";

import { Organization, User, UserClassification } from "../models";

import TimeSheet, { ExtendedTimesheet } from "../models/timesheet";
import { ExtendedEmployeeBenefit } from "@/models/employeebenefit";
import { ExtendedFringeBenefitClassification } from "@/models/fringebenefitclassification";
import { ExtendedOrganization } from "@/models/organization";

import {
  employeeContributionAmountToHourly,
  companyContributionAmountToHourly,
  convertPercentFringeContributionToHourly,
  getEffectiveOvertimePolicy,
} from "@/util/utils";
import { formatDateIso8601, getDayOfWeekString } from "./dateHelper";

export interface UserWage {
  id: number;
  firstName: string;
  lastName: string;
  hourlyWage: number;
  totalMinutes: number;
  breakMinutes: number;
  totalWages: number;
  timeWorkedByDay: Record<string, string | number | Date>[];
}
interface AggregateUserWage {
  totalMinutes: number;
  totalBreakMinutes: number;
  totalWages: number;
  employees: Array<UserWage>;
}

export interface TimesheetRow {
  id: number;
  firstName: string;
  lastName: string;
  position: string;
  employeeId: string;
  hourlyWage: number;
  projectName: string;
  projectNumber: string;
  costCodeName: string;
  formattedDate: string;
  clockIn: string;
  clockOut: string;
  workedMinutes?: number;
  totalMinutes?: number;
  regularMinutes?: number;
  overtimeMinutes: number;
  breakMinutes: number;
  wages: number;
  description: string;
  isTotalRow: boolean;
}

/*

Notes:

Several of these helper functions such as calculateUserEarnings, employeeTimesheetsPerDay and calculateWagesByProject are legacy and used in `/reports and /dashboard` routes. They are used to calculate wages and timesheets for users and projects, but before the introduction of earning rates and "attribution".

The /reports and /dashboard routes should be eventually deprecated or refactored to use the new /timesheets/report route which uses the new "attribution" system.

Attempting to update these functions to use the new "attribution" system would be duplicating the work done in the /timesheets/report route.

*/

export const calculateUsersEarnings = (
  users: any,
  organization: ExtendedOrganization,
  options: Record<string, any> = {}
): AggregateUserWage => {
  const { expand = null } = options;
  const employees: any = [];

  let totalMinutes = 0,
    totalWages = 0,
    totalBreakMinutes = 0;

  users.forEach((user: any) => {
    let employeeMinutes = 0,
      employeeBreakMinutes = 0,
      employeeWages = 0;

    const formattedTimesheets: Record<string, string | number | Date>[] = [];
    const minutesPerDay: Record<string, number> = {};

    user.timesheets.forEach((timesheet: any) => {
      const breakMinutes = calculateBreakMinutes(timesheet);
      employeeBreakMinutes += breakMinutes;
      const minutesWorked = calculateTotalWorkedMinutes(timesheet);
      employeeMinutes += minutesWorked;

      updateMinutesPerDay(minutesPerDay, timesheet, organization);

      const convertedDate = new Date(timesheet.clockIn);
      const formattedDate = dateInOrganizationTimezone(timesheet.clockIn, organization);
      const timesheetDescription = timesheet.description ? timesheet.description : "";

      // if expand by day
      if (expand === "day") {
        formattedTimesheets.push({
          date: convertedDate,
          formattedDate: formattedDate,
          minutesWorked,
          breakMinutes,
          timesheetDescription,
        });
      }
    });

    totalMinutes += employeeMinutes;
    totalBreakMinutes += employeeBreakMinutes;

    let employeePaidMinutes = employeeMinutes;
    if (organization.timeTrackingSettings.areBreaksPaid) {
      employeePaidMinutes += employeeBreakMinutes;
    }

    const employeeOvertimeMinutes = calculateOvertime(minutesPerDay, organization, employeeMinutes);

    const employeeRegularMinutes = employeePaidMinutes - employeeOvertimeMinutes;
    // the assumption is you return only the active REG earning rate so it should be the first one
    const userHourlyRate = user?.earningRates ? parseFloat(user.earningRates[0]?.amount || "0") : 0;

    employeeWages =
      (employeeRegularMinutes * userHourlyRate +
        employeeOvertimeMinutes * userHourlyRate * organization.overtimeSettings.overtimeMultiplier) /
      60;

    totalWages += employeeWages;

    // sort by date (ascending) if expand === "day" - otherwise no objects to sort
    if (expand === "day") {
      formattedTimesheets.sort((a, b) => {
        return (a.date as Date).getTime() - (b.date as Date).getTime();
      });
    }

    employees.push({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      hourlyWage: userHourlyRate,
      checkEmployeeId: user.checkEmployeeId,
      checkContractorId: user.checkContractorId,
      regularMinutes: employeeRegularMinutes,
      totalMinutes: employeeMinutes,
      overtimeMinutes: employeeOvertimeMinutes,
      breakMinutes: employeeBreakMinutes,
      totalWages: Math.ceil(employeeWages),
      timeWorkedByDay: formattedTimesheets,
    });
  });

  return {
    totalMinutes,
    totalBreakMinutes,
    totalWages,
    employees,
  };
};

export const employeeTimesheetsPerDay = (users: User[], organization: Organization): Array<TimesheetRow> => {
  const timesheetRows: Array<TimesheetRow> = [];

  users.forEach((user: any) => {
    let employeeMinutes = 0,
      employeeBreakMinutes = 0,
      employeeWages = 0;

    const userHourlyRate = user?.earningRates ? parseFloat(user.earningRates[0]?.amount || "0") : 0;

    const minutesPerDay: Record<string, number> = {};

    const userTimesheetRows: Array<TimesheetRow> = [];

    user.timesheets.forEach((timesheet: any) => {
      const breakMinutes = calculateBreakMinutes(timesheet);
      employeeBreakMinutes += breakMinutes;
      const minutesWorked = calculateTotalWorkedMinutes(timesheet);

      let overtimeMinutes = 0;
      if (organization.overtimeSettings.dailyOvertimeEnabled) {
        overtimeMinutes = calculateDailyOvertime(
          minutesPerDay,
          organization.overtimeSettings.dailyOvertimeThreshold,
          minutesWorked,
          timesheet,
          organization
        );
      }

      employeeMinutes += minutesWorked;
      const formattedDate = dateInOrganizationTimezone(timesheet.clockIn, organization);
      const clockIn = clockInString(timesheet, organization);
      const clockOut = clockOutString(timesheet, organization);

      // do this after calling `calculateDailyOvertime` because it updates the minutesPerDay object
      updateMinutesPerDay(minutesPerDay, timesheet, organization);

      userTimesheetRows.push({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        position: user.position,
        employeeId: user.employeeId,
        hourlyWage: userHourlyRate,
        projectName: timesheet.project.name,
        projectNumber: timesheet.project.projectNumber,
        costCodeName: timesheet.costCode?.name ?? "",
        formattedDate: formattedDate,
        clockIn: clockIn,
        clockOut: clockOut,
        workedMinutes: minutesWorked,
        overtimeMinutes,
        breakMinutes: breakMinutes,
        wages: 0,
        description: timesheet.description,
        isTotalRow: false,
      });
    });

    // data rows
    timesheetRows.push(...userTimesheetRows);

    let employeePaidMinutes = employeeMinutes;
    if (organization.timeTrackingSettings.areBreaksPaid) {
      employeePaidMinutes += employeeBreakMinutes;
    }

    const employeeOvertimeMinutes = calculateOvertime(minutesPerDay, organization, employeeMinutes);

    const employeeRegularMinutes = employeePaidMinutes - employeeOvertimeMinutes;
    employeeWages =
      (employeeRegularMinutes * userHourlyRate +
        employeeOvertimeMinutes * userHourlyRate * organization.overtimeSettings.overtimeMultiplier) /
      60;

    // total row
    timesheetRows.push({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      position: user.position,
      employeeId: user.employeeId,
      hourlyWage: null,
      projectName: null,
      projectNumber: null,
      costCodeName: null,
      formattedDate: null,
      clockIn: null,
      clockOut: null,
      workedMinutes: employeeMinutes,
      overtimeMinutes: employeeOvertimeMinutes,
      breakMinutes: employeeBreakMinutes,
      wages: employeeWages,
      description: null,
      isTotalRow: true,
    });

    // insert empty row after each user
    timesheetRows.push({
      id: null,
      firstName: "",
      lastName: "",
      position: "",
      employeeId: "",
      hourlyWage: null,
      projectName: null,
      projectNumber: null,
      costCodeName: null,
      formattedDate: null,
      clockIn: null,
      clockOut: null,
      workedMinutes: null,
      overtimeMinutes: null,
      breakMinutes: null,
      wages: null,
      description: null,
      isTotalRow: false,
    });
  });

  return timesheetRows;
};

export const calculateWagesByProject = (timesheets: any, organization: ExtendedOrganization, user: any) => {
  let projectWages = 0;
  let projectMinutes = 0;
  let projectBreakMinutes = 0;

  const minutesPerDay: Record<string, number> = {};

  timesheets.forEach((timesheet: any) => {
    const breakMinutes = calculateBreakMinutes(timesheet);
    projectBreakMinutes += breakMinutes;
    const minutesWorked = calculateTotalWorkedMinutes(timesheet);
    projectMinutes += minutesWorked;

    updateMinutesPerDay(minutesPerDay, timesheet, organization);
    const userHourlyRate = user?.earningRates ? parseFloat(user.earningRates[0]?.amount || "0") : 0;

    // calculate wages
    if (userHourlyRate) {
      let paidMinutes = minutesWorked;
      if (organization.timeTrackingSettings.areBreaksPaid) {
        paidMinutes += breakMinutes;
      }

      const overtimeMinutes = calculateOvertime(minutesPerDay, organization, projectMinutes);

      const regularMinutes = paidMinutes - overtimeMinutes;
      projectWages +=
        (regularMinutes * userHourlyRate +
          overtimeMinutes * userHourlyRate * organization.overtimeSettings.overtimeMultiplier) /
        60;
    }
  });

  return {
    projectWages: Math.ceil(projectWages),
    projectMinutes,
    projectBreakMinutes,
  };
};

type OTDistribution = "sequential" | "weighted";

// we want this function to be able to coherently calculate wages and overtime for each "batch" of timesheets by week

/**
 *
 * @param timesheets ExtendedTimesheet[] - a collection of SORTED (clockIn ASC) timesheets to be attributed - collection should ONLY contain timesheets for a single user within a single week
 * @param orgData Organization - organization data that contains the settings for the organization
 * @returns ExtendedTimesheet[] - a collection of timesheets with the appropriate attributes for wages and overtime
 */
export const attributeWeeklyTimesheetCalculations = (timesheets: ExtendedTimesheet[], orgData: Organization) => {
  const {
    timeTrackingSettings: { areBreaksPaid },
    overtimeSettings: {
      dailyOvertimeEnabled,
      dailyOvertimeThreshold, // this is in minutes set to 480 minutes
      overtimeMultiplier,
      weeklyOvertimeEnabled,
      weeklyOvertimeThreshold, // this is in minutes set to 2400 minutes
    },
  } = orgData;

  const attributedTimesheets: ExtendedTimesheet[] = [];
  const otDistribution: OTDistribution = "sequential"; // maybe this eventually comes from orgData, but defaults to cummulative

  // this is used to track the date of the previous timesheet to aggregate worked minutes for a day
  let previousTimesheetDate = null;
  // this is used to keep track of the total worked minutes for a day across multiple timesheets
  let dailyWorkedMinutes = 0;
  let weeklyRegMinutes = 0;
  let weeklyOTMinutes = 0;

  // timesheets should already be sorted by clockIn
  for (let i = 0; i < timesheets.length; i++) {
    let currentTimesheet = timesheets[i];

    if (currentTimesheet instanceof TimeSheet) {
      currentTimesheet = currentTimesheet.get({ plain: true });
    }

    const isAnnually = currentTimesheet?.regEarningRate?.period === "ANNUALLY" || false;

    // if salary - convert wages to hourly
    let hourlyRate = isAnnually
      ? parseFloat(currentTimesheet?.regEarningRate?.amount) / 2080
      : currentTimesheet?.regEarningRate?.amount
      ? parseFloat(currentTimesheet?.regEarningRate?.amount)
      : 0;

    let otHourlyRate = hourlyRate * overtimeMultiplier;
    let cashFringeRate = 0; // contribution per hour
    let companyDynamicFringeContributionRate = 0; // co. contribution per hour
    let employeeDynamicFringeContributionPerecent = 0;

    // if the project is a PW project identified from the timesheet - user_classification_id is not null, then useHourlyWage becomes classification.basePay + cashFringe (needs to be calculated)
    if (currentTimesheet.project.isPrevailingWage && currentTimesheet.userClassificationId) {
      cashFringeRate = calculateCashFringeDiff(
        currentTimesheet.userClassification.fringePay,
        currentTimesheet.fringeBenefitClassifications,
        currentTimesheet.user.employeeBenefits
      );

      // extend attribution to do dynamic fringe distribution and 0 out cashFringe if currentTimesheet.employeeBenefits contains a benefit where contributionType is DYNAMIC
      const dynamicFringeBenefit = currentTimesheet.user.employeeBenefits.find(
        (eb) => eb.contributionType === "DYNAMIC"
      );
      if (dynamicFringeBenefit) {
        // set dynamic fringe contribution rate to cash fringe
        companyDynamicFringeContributionRate = cashFringeRate;
        // set other data and set cashFringeRate to 0
        employeeDynamicFringeContributionPerecent = dynamicFringeBenefit.employeeContributionPercent;
        cashFringeRate = 0;
      }

      // Determine which rate to use based on org setting overridePwIfBelowRegularRate:
      // - Use regular rate if it's higher than PW rate AND override setting is enabled
      // - Otherwise use PW rate (higher rate or when override disabled)
      // Same logic applies to OT rates - uses corresponding regular or PW base rate
      const prevailingWageHourlyRate = parseFloat(currentTimesheet.userClassification.basePay) + cashFringeRate;
      const shouldUseHourlyRates =
        orgData?.prevailingWageSettings?.overridePwIfBelowRegularRate && hourlyRate > prevailingWageHourlyRate;

      if (!shouldUseHourlyRates) {
        hourlyRate = prevailingWageHourlyRate;
        otHourlyRate = parseFloat(currentTimesheet.userClassification.basePay) * overtimeMultiplier + cashFringeRate;
      }
    }

    let attributedTimesheet: ExtendedTimesheet = currentTimesheet;
    // workedMinutes is regWorkedMinutes + otWorkedMinutes
    const workedMinutes = calculateTotalWorkedMinutes(currentTimesheet);
    let regWorkedMinutes = workedMinutes;
    let otWorkedMinutes = 0;
    const breakMinutes = calculateBreakMinutes(currentTimesheet);

    if (dailyOvertimeEnabled) {
      const currentTimesheetDate = dateInOrganizationTimezone(currentTimesheet.clockIn, orgData);
      if (previousTimesheetDate && currentTimesheetDate === previousTimesheetDate) {
        dailyWorkedMinutes += workedMinutes;
        if (dailyWorkedMinutes > dailyOvertimeThreshold) {
          regWorkedMinutes = Math.max(0, dailyOvertimeThreshold - (dailyWorkedMinutes - workedMinutes));
          otWorkedMinutes = workedMinutes - regWorkedMinutes;
        } else {
          regWorkedMinutes = workedMinutes;
        }
      } else {
        dailyWorkedMinutes = workedMinutes;
        regWorkedMinutes = Math.min(workedMinutes, dailyOvertimeThreshold);
        otWorkedMinutes = Math.max(0, workedMinutes - dailyOvertimeThreshold);
      }

      attributedTimesheet = updateAttributedTimesheet(
        currentTimesheet,
        regWorkedMinutes,
        otWorkedMinutes,
        breakMinutes,
        hourlyRate,
        otHourlyRate,
        areBreaksPaid,
        cashFringeRate,
        companyDynamicFringeContributionRate,
        employeeDynamicFringeContributionPerecent
      );

      previousTimesheetDate = currentTimesheetDate;
    }

    if (weeklyOvertimeEnabled) {
      // if otDistribution is sequential
      if (otDistribution === "sequential") {
        const cachedWeeklyMinutesBeforeAddingDay = weeklyRegMinutes;
        weeklyRegMinutes += regWorkedMinutes;

        if (weeklyRegMinutes > weeklyOvertimeThreshold) {
          if (weeklyOTMinutes === 0) {
            const overtime = weeklyRegMinutes - weeklyOvertimeThreshold + otWorkedMinutes;
            weeklyOTMinutes += overtime;
            weeklyRegMinutes = weeklyOvertimeThreshold; // Reset weekly minutes to threshold
            const regWorkedMinutes = weeklyOvertimeThreshold - cachedWeeklyMinutesBeforeAddingDay;
            attributedTimesheet = updateAttributedTimesheet(
              attributedTimesheet,
              regWorkedMinutes,
              overtime,
              breakMinutes,
              hourlyRate,
              otHourlyRate,
              areBreaksPaid,
              cashFringeRate,
              companyDynamicFringeContributionRate,
              employeeDynamicFringeContributionPerecent
            );
          } else {
            weeklyOTMinutes += workedMinutes;
            attributedTimesheet = updateAttributedTimesheet(
              attributedTimesheet,
              0,
              workedMinutes,
              breakMinutes,
              hourlyRate,
              otHourlyRate,
              areBreaksPaid,
              cashFringeRate,
              companyDynamicFringeContributionRate,
              employeeDynamicFringeContributionPerecent
            );
          }
        } else if (!dailyOvertimeEnabled) {
          attributedTimesheet = updateAttributedTimesheet(
            attributedTimesheet,
            workedMinutes,
            0,
            breakMinutes,
            hourlyRate,
            otHourlyRate,
            areBreaksPaid,
            cashFringeRate,
            companyDynamicFringeContributionRate,
            employeeDynamicFringeContributionPerecent
          );
        }
      } else if (otDistribution === "weighted") {
        // TBD
      }
    }

    if (!dailyOvertimeEnabled && !weeklyOvertimeEnabled) {
      // no overtime calculations
      attributedTimesheet = updateAttributedTimesheet(
        attributedTimesheet,
        workedMinutes,
        0,
        breakMinutes,
        hourlyRate,
        otHourlyRate,
        areBreaksPaid,
        cashFringeRate,
        companyDynamicFringeContributionRate,
        employeeDynamicFringeContributionPerecent
      );
    }

    // push attributed timesheet to the array
    attributedTimesheets.push(attributedTimesheet);
  }

  return attributedTimesheets;
};

function updateAttributedTimesheet(
  attributedTimesheet: ExtendedTimesheet,
  regWorkedMinutes: number,
  overtimeMinutes: number,
  breakMinutes: number,
  hourlyRate: number,
  otHourlyRate: number,
  areBreaksPaid: boolean,
  cashFringeRate: number,
  companyDynamicFringeContributionRate: number,
  employeeDynamicFringeContributionPerecent: number // float
) {
  const regularWages = calculateRegularWages(hourlyRate, regWorkedMinutes, breakMinutes, areBreaksPaid);
  const overtimeWages = calculateOvertimeWages(otHourlyRate, overtimeMinutes, breakMinutes, areBreaksPaid);
  const companyDynamicFringeAllocation = calculateDynamicFringeAllocation(
    companyDynamicFringeContributionRate,
    regWorkedMinutes + overtimeMinutes
  );
  const employeeDynamicFringeAllocation = calculateDynamicFringeAllocation(
    hourlyRate * (employeeDynamicFringeContributionPerecent / 100),
    regWorkedMinutes + overtimeMinutes
  );

  return Object.assign(attributedTimesheet, {
    hourlyWage: hourlyRate,
    otHourlyWage: otHourlyRate,
    cashFringeRate,
    companyDynamicFringeContributionRate,
    employeeDynamicFringeContributionPerecent,
    regularMinutes: regWorkedMinutes,
    overtimeMinutes,
    breakMinutes,
    regularWages,
    overtimeWages,
    totalWages: regularWages + overtimeWages,
    companyDynamicFringeAllocation,
    employeeDynamicFringeAllocation,
  });
}

function calculateOvertimeWages(
  otHourlyRate: number,
  otMinutesWorked: number,
  breakMinutes: number,
  areBreaksPaid: boolean
) {
  return areBreaksPaid ? ((otMinutesWorked + breakMinutes) / 60) * otHourlyRate : (otMinutesWorked / 60) * otHourlyRate;
}

function calculateRegularWages(
  userHourlyWage: number,
  minutesWorked: number,
  breakMinutes: number,
  areBreaksPaid: boolean
) {
  return areBreaksPaid ? ((minutesWorked + breakMinutes) / 60) * userHourlyWage : (minutesWorked / 60) * userHourlyWage;
}

// for co calculation - pass in the company dynamic fringe contribution rate; for employee calculation pass in the (hourlyRate * employee contribution percent (convert to decimal))
function calculateDynamicFringeAllocation(dynamicFringeRate: number, totalMinutesWorked: number) {
  return (totalMinutesWorked / 60) * dynamicFringeRate;
}

export const calculateCashFringeDiff = (
  fringePay: string,
  fringeBenefitClassifications: Partial<ExtendedFringeBenefitClassification>[] = [], // its weird but fringeBenefitClassification is where the amount is stored
  employeeBenefits: Partial<ExtendedEmployeeBenefit>[] = []
) => {
  const sumOfFringeBenefits = fringeBenefitClassifications?.reduce((acc, curr) => acc + parseFloat(curr.amount), 0);

  const sumOfEmployeeBenefits = employeeBenefits?.reduce((acc, curr) => acc + curr.fringeHourlyContribution, 0);

  const cashFringeDiff = Math.max(0, parseFloat(fringePay) - sumOfFringeBenefits - sumOfEmployeeBenefits);

  return cashFringeDiff;
};

/**
 * A utility function that algorithmically calculates the fringeHourlyContribution for each employee benefit
 * @param userClassification
 * @param employeeBenefits
 * @param projectFringes
 * @returns employeeBenefits with fringeHourlyContribution sorted
 */
export const attributeHourlyFringeContribution = (
  userClassification: UserClassification,
  employeeBenefits: ExtendedEmployeeBenefit[],
  projectFringes: ExtendedFringeBenefitClassification[],
  payFrequency: string
) => {
  const getPriority = (type: string) => {
    if (type === "AMOUNT") return 0;
    if (type === "PERCENT") return 1;

    return 2;
  };
  // sort employee benefits by contribution type - we want any benefits with a contributionType of AMOUNT to be sorted first
  const sortedEmployeeBenefits = employeeBenefits.sort((a, b) => {
    // helper function to get priority (AMOUNT = 0, PERCENT = 1, others = 2)
    return getPriority(a.contributionType) - getPriority(b.contributionType);
  });

  const amountEmployeeBenefits = sortedEmployeeBenefits.filter((eb) => eb.contributionType === "AMOUNT");
  const percentageEmployeeBenefits = sortedEmployeeBenefits.filter((eb) => eb.contributionType === "PERCENT");

  // extend the amountOnlyEmployeeBenefits with fringeHourlyContribution
  const amountOnlyEmployeeBenefitsWithFringeHourlyContribution = amountEmployeeBenefits.map((eb) => {
    return {
      ...eb,
      fringeHourlyContribution: companyContributionAmountToHourly(eb, payFrequency),
    };
  });

  const totalAmountEmployeeBenefits = amountOnlyEmployeeBenefitsWithFringeHourlyContribution.reduce(
    (acc, curr) => acc + curr.fringeHourlyContribution,
    0
  );

  // after adding sorted amount benefits, we want to add the fringe benefits from the project
  const totalFringeBenefits = projectFringes?.reduce((acc, curr) => acc + parseFloat(curr.amount), 0);

  // this gives us totalAmountBenefits
  const totalAmountBenefits = totalAmountEmployeeBenefits + totalFringeBenefits;
  // totalPercentContributionFromBenefits is a float value - ex: 7.9%
  const totalPercentContributionFromBenefits = percentageEmployeeBenefits.reduce(
    (acc, curr) => acc + curr.companyContributionPercent,
    0
  );

  // loop through extendedEmployeeBenefits and add new properties
  const extendedEmployeeBenefits = sortedEmployeeBenefits.map((eb) => {
    const baseObj = eb.dataValues ? { ...eb.dataValues } : { ...eb };

    if (eb.contributionType === "AMOUNT") {
      return {
        ...baseObj,
        fringeHourlyContribution: companyContributionAmountToHourly(eb, payFrequency),
        employeeHourlyContribution: employeeContributionAmountToHourly(eb, payFrequency),
        benefitProviderName: eb.companyBenefit.benefitProviderName,
        benefitProviderAddress: eb.companyBenefit.benefitProviderAddress,
        benefitProviderPhone: eb.companyBenefit.benefitProviderPhone,
        category: eb.companyBenefit.category,
        benefitName: eb.companyBenefit.name,
        companyBenefitName: eb.companyBenefit.name,
      };
    }

    if (eb.contributionType === "PERCENT") {
      const totalHourlyPayMinusAountBenefits =
        parseFloat(userClassification.basePay) + parseFloat(userClassification.fringePay) - totalAmountBenefits;

      const grossWageHourly = totalHourlyPayMinusAountBenefits / (1 + totalPercentContributionFromBenefits / 100);

      return {
        ...baseObj,
        fringeHourlyContribution: convertPercentFringeContributionToHourly(
          grossWageHourly,
          eb.companyContributionPercent
        ),
        employeeHourlyContribution: convertPercentFringeContributionToHourly(
          grossWageHourly,
          eb.employeeContributionPercent
        ),
        benefitProviderName: eb.companyBenefit.benefitProviderName,
        benefitProviderAddress: eb.companyBenefit.benefitProviderAddress,
        benefitProviderPhone: eb.companyBenefit.benefitProviderPhone,
        category: eb.companyBenefit.category,
        benefitName: eb.companyBenefit.name,
        companyBenefitName: eb.companyBenefit.name,
      };
    }

    return {
      ...baseObj,
      fringeHourlyContribution: 0,
      employeeHourlyContribution: 0,
      benefitProviderName: eb.companyBenefit.benefitProviderName,
      benefitProviderAddress: eb.companyBenefit.benefitProviderAddress,
      benefitProviderPhone: eb.companyBenefit.benefitProviderPhone,
      category: eb.companyBenefit.category,
      benefitName: eb.companyBenefit.name,
      companyBenefitName: eb.companyBenefit.name,
    };
  });

  // delete companyBenefit object from extendedEmployeeBenefits
  extendedEmployeeBenefits.forEach((eb) => {
    delete eb.companyBenefit;
  });

  return extendedEmployeeBenefits;
};

export function sumEarnings(earnings: any[], checkRegEarningCode = "", checkOtEarningCode = "") {
  let totalAmount = 0;

  earnings.forEach((earning: any) => {
    if (earning.earning_code === checkRegEarningCode || earning.earning_code === checkOtEarningCode) {
      totalAmount += parseFloat(earning.amount);
    }
  });

  return totalAmount.toFixed(2); // the result as a string with two decimal places
}

export const sumTimesheetWagesBy = (
  extendedTimesheets: ExtendedTimesheet[],
  propertyName: keyof ExtendedTimesheet,
  propertyValue: any,
  includeDriveTimeWages = true
) => {
  let totalWages = 0;

  extendedTimesheets.forEach((timesheet) => {
    if (timesheet[propertyName] === propertyValue) {
      if (includeDriveTimeWages) {
        totalWages += timesheet.totalWages;
      } else {
        // Subtract driveTimeWages if they exist, otherwise use totalWages
        const driveTimeWages = timesheet.driveTimeWages || 0;
        totalWages += timesheet.totalWages - driveTimeWages;
      }
    }
  });

  return totalWages;
};

/**
 * Calculates overtime and regular minutes for timesheets based on organization policies.
 * Handles complex overtime rules including daily/weekly thresholds and special day rules.
 *
 * @param {ExtendedTimesheet[]} timesheets - A collection of timesheets for a single week to process
 * @param {Organization} orgData - Organization data containing overtime settings and policies
 *
 * @returns {ExtendedTimesheet[]} Enhanced timesheets with the following added properties:
 *   - policyId: ID of the overtime policy applied to the timesheet
 *   - regularMinutes: Minutes calculated as regular time based on policy rules
 *   - overtimeMinutes: Minutes calculated as overtime based on policy rules
 *   - doubleOvertimeMinutes: Minutes calculated as double overtime (if applicable)
 *   - breakMinutes: Break duration in minutes
 *   - hourlyWage: Regular hourly wage rate
 *   - otHourlyWage: Overtime hourly wage rate
 *   - regularWages: Total wages earned from regular time
 *   - overtimeWages: Total wages earned from overtime
 *   - doubleOvertimeWages: Total wages earned from double overtime (if applicable)
 *   - totalWages: Sum of all wage categories
 *   - Additional fringe benefit calculations if applicable
 */
export const refinedAttributedWeeklyTimesheets = (timesheets: ExtendedTimesheet[], orgData: Organization) => {
  const { overtimeSettings: defaultOvertimeSettings } = orgData;

  // Assume timesheets are already for one week.
  let weekTimesheets = timesheets;

  // Group timesheets by day - used for debugging only
  const _timesheetsByDay = groupTimesheetsByDay(weekTimesheets);

  // Track special day timesheets with a Set instead of modifying the object
  const specialDayTimesheets = new Set<number>();

  // First pass: Check for special days and mark them accordingly
  for (const ts of weekTimesheets) {
    const otPolicy = getEffectiveOvertimePolicy(ts, defaultOvertimeSettings);

    const dayOfWeekInt = dayjs.tz(ts.clockIn, orgData.timezone).day();
    const dayOfWeek = getDayOfWeekString(dayOfWeekInt);

    // Ensure totalMinutes is calculated
    if (typeof ts.totalMinutes === "undefined") {
      ts.totalMinutes = calculateTotalWorkedMinutes(ts);
    }

    const jobsiteWorkedMinutes = calculateJobsiteWorkedMinutes(ts);

    // Store policy ID for later grouping
    ts.otPolicyId = otPolicy.id;

    // Handle special days first
    if (otPolicy.dailyDoubleOvertimeEnabled && otPolicy.doubleOvertimeDays.includes(dayOfWeek)) {
      ts.regularMinutes = 0;
      ts.overtimeMinutes = 0;
      ts.doubleOvertimeMinutes = jobsiteWorkedMinutes;
      specialDayTimesheets.add(ts.id);
    } else if (otPolicy.dailyOvertimeEnabled && otPolicy.overtimeDays.includes(dayOfWeek)) {
      ts.regularMinutes = 0;
      ts.overtimeMinutes = jobsiteWorkedMinutes;
      ts.doubleOvertimeMinutes = 0;
      specialDayTimesheets.add(ts.id);
    }
  }

  // Group timesheets by policy ID
  const timesheetsByPolicy: Record<string, ExtendedTimesheet[]> = {};

  for (const ts of weekTimesheets) {
    const otPolicyId = ts.otPolicyId || "default";
    if (!timesheetsByPolicy[otPolicyId]) {
      timesheetsByPolicy[otPolicyId] = [];
    }
    timesheetsByPolicy[otPolicyId].push(ts);
  }

  // Process each policy's timesheets independently
  for (const otPolicyId of Object.keys(timesheetsByPolicy)) {
    const policyTimesheets = timesheetsByPolicy[otPolicyId];

    if (policyTimesheets.length === 0) continue;

    // Use the first timesheet's policy for this group
    const sampleTimesheet = policyTimesheets[0];
    const policy = getEffectiveOvertimePolicy(sampleTimesheet, defaultOvertimeSettings);

    // Determine global weekly thresholds for this policy
    const weeklyOTThreshold = policy.weeklyOvertimeEnabled ? policy.weeklyOvertimeThreshold : Infinity;
    const _weeklyDOTThreshold = policy.dailyDoubleOvertimeEnabled ? policy.dailyDoubleOvertimeThreshold * 5 : Infinity;

    // Process each day's timesheets for this policy
    let totalWeeklyRegularMinutes = 0;

    // Group timesheets by day for this policy
    const policyTimesheetsByDay: Record<string, ExtendedTimesheet[]> = {};

    for (const ts of policyTimesheets) {
      const dayKey = formatDateIso8601(ts.clockIn, orgData.timezone);
      if (!policyTimesheetsByDay[dayKey]) {
        policyTimesheetsByDay[dayKey] = [];
      }
      policyTimesheetsByDay[dayKey].push(ts);
    }

    // Process days in order
    const sortedDays = Object.keys(policyTimesheetsByDay).sort();

    for (const dayKey of sortedDays) {
      const dayTimesheets = policyTimesheetsByDay[dayKey];
      const dailyEligible = dayTimesheets.filter((ts) => !specialDayTimesheets.has(ts.id));

      if (dailyEligible.length === 0) continue;

      // Find the effective daily thresholds for this day in this policy
      const dailyOTThreshold = policy.dailyOvertimeEnabled ? policy.dailyOvertimeThreshold : Infinity;
      const dailyDOTThreshold = policy.dailyDoubleOvertimeEnabled ? policy.dailyDoubleOvertimeThreshold : Infinity;

      let dailyRegularMinutes = 0;
      // Process each timesheet
      for (const ts of dailyEligible) {
        const jobsiteWorkedMinutes = calculateJobsiteWorkedMinutes(ts);
        // Skip timesheets with no OT policies
        if (!policy.dailyOvertimeEnabled && !policy.weeklyOvertimeEnabled && !policy.dailyDoubleOvertimeEnabled) {
          ts.regularMinutes = jobsiteWorkedMinutes;
          ts.overtimeMinutes = 0;
          ts.doubleOvertimeMinutes = 0;
          totalWeeklyRegularMinutes += jobsiteWorkedMinutes;
          dailyRegularMinutes += jobsiteWorkedMinutes;
          continue;
        }

        // Apply daily thresholds first
        let regularMinutes = jobsiteWorkedMinutes;
        let overtimeMinutes = 0;
        let doubleOvertimeMinutes = 0;

        if (policy.dailyOvertimeEnabled) {
          // if we have already crossed daily threshold, set regular minutes to 0 and overtime to total minutes
          if (dailyRegularMinutes > dailyOTThreshold) {
            regularMinutes = 0;
            overtimeMinutes = jobsiteWorkedMinutes;
          } else if (dailyRegularMinutes + jobsiteWorkedMinutes > dailyOTThreshold) {
            // else if this timesheet will cross the daily threshold,
            // set regular minutes to the number of minutes left in the daily threshold
            regularMinutes = dailyOTThreshold - dailyRegularMinutes;
            overtimeMinutes = jobsiteWorkedMinutes - regularMinutes;

            // If there's also a double OT threshold and we exceed it
            if (policy.dailyDoubleOvertimeEnabled && jobsiteWorkedMinutes > dailyDOTThreshold) {
              overtimeMinutes = dailyDOTThreshold - dailyOTThreshold;
              doubleOvertimeMinutes = jobsiteWorkedMinutes - dailyDOTThreshold;
            }
          }
        }

        // Track the allocation
        const _allocation = {
          id: ts.id,
          totalMinutes: ts.totalMinutes,
          jobsiteWorkedMinutes: jobsiteWorkedMinutes,
          dailyRegular: regularMinutes,
          dailyOvertime: overtimeMinutes,
          dailyDoubleOvertime: doubleOvertimeMinutes,
          totalWeeklyRegularBefore: totalWeeklyRegularMinutes,
        };

        // Update ts with our calculated allocations - these may get adjusted in the next pass
        ts.regularMinutes = regularMinutes;
        ts.overtimeMinutes = overtimeMinutes;
        ts.doubleOvertimeMinutes = doubleOvertimeMinutes;

        // Update total daily and weekly regular minutes
        totalWeeklyRegularMinutes += regularMinutes;
        dailyRegularMinutes += regularMinutes;
      }
    }

    // Calculate the total regular minutes allocated for this policy
    const totalRegularMinutes = policyTimesheets.reduce((sum, ts) => sum + (ts.regularMinutes || 0), 0);

    // If we exceeded the weekly OT threshold for this policy, redistribute minutes
    if (totalRegularMinutes > weeklyOTThreshold && weeklyOTThreshold < Infinity) {
      // Sort eligible timesheets by day ascending
      const eligibleTimesheets = policyTimesheets
        .filter((ts) => !specialDayTimesheets.has(ts.id))
        .sort((a, b) => a.clockIn.getTime() - b.clockIn.getTime());

      let remainingRegularBudget = weeklyOTThreshold;

      for (const ts of eligibleTimesheets) {
        // Remember the original allocation from daily rules
        const originalRegular = ts.regularMinutes;
        const originalOvertime = ts.overtimeMinutes;
        const _originalDoubleOvertime = ts.doubleOvertimeMinutes;

        // Adjust regular minutes based on weekly budget
        const adjustedRegular = Math.min(originalRegular, remainingRegularBudget);
        remainingRegularBudget -= adjustedRegular;

        // Any reduction in regular minutes becomes overtime
        const additionalOvertime = originalRegular - adjustedRegular;

        // Update the allocation
        ts.regularMinutes = adjustedRegular;
        ts.overtimeMinutes = originalOvertime + additionalOvertime;

        // Stop processing if we've used up all regular minutes
        if (remainingRegularBudget <= 0) break;
      }
    }

    // Third pass - handle any timesheets that weren't processed in the second pass
    if (totalRegularMinutes >= weeklyOTThreshold) {
      const sortedTimesheets = [...policyTimesheets].sort((a, b) => a.clockIn.getTime() - b.clockIn.getTime());

      // Find the point where we've accumulated the weekly threshold
      let accumulatedRegular = 0;
      let thresholdReachedIndex = -1;

      for (let i = 0; i < sortedTimesheets.length; i++) {
        accumulatedRegular += sortedTimesheets[i].regularMinutes || 0;
        if (accumulatedRegular >= weeklyOTThreshold) {
          thresholdReachedIndex = i;
          break;
        }
      }

      // All timesheets after the threshold is reached should have 0 regular minutes
      if (thresholdReachedIndex >= 0) {
        for (let i = thresholdReachedIndex + 1; i < sortedTimesheets.length; i++) {
          const ts = sortedTimesheets[i];
          if (ts.regularMinutes > 0) {
            ts.overtimeMinutes += ts.regularMinutes;
            ts.regularMinutes = 0;
          }
        }
      }
    }
  }

  // Calculate timesheet earnings (after OT attribution)
  weekTimesheets = calculateTimesheetEarnings(weekTimesheets, orgData);

  return weekTimesheets;
};

// Groups timesheets by day (YYYY-MM-DD).
export const groupTimesheetsByDay = (timesheets: ExtendedTimesheet[]): { [day: string]: ExtendedTimesheet[] } => {
  const grouped: { [day: string]: ExtendedTimesheet[] } = {};
  for (const ts of timesheets) {
    const dayKey = formatDateIso8601(ts.clockIn);
    if (!grouped[dayKey]) {
      grouped[dayKey] = [];
    }
    grouped[dayKey].push(ts);
  }

  return grouped;
};

/**
 * Calculates financial earnings for each timesheet based on the allocated minutes.
 * This function takes timesheets that already have overtime minutes allocated and
 * calculates the appropriate wages based on rates and organization settings.
 *
 * @param {ExtendedTimesheet[]} weekTimesheets - Timesheets with regularMinutes, overtimeMinutes,
 *   and doubleOvertimeMinutes already allocated
 * @param {Organization} orgData - Organization data containing pay settings, prevailing wage settings,
 *   and overtime multipliers
 *
 * @returns {ExtendedTimesheet[]} Enhanced timesheets with the following financial properties:
 *   - hourlyWage: Regular hourly wage rate
 *   - otHourlyWage: Overtime hourly wage rate
 *   - cashFringeRate: Cash fringe rate (for prevailing wage projects)
 *   - regularWages: Calculated wages for regular time
 *   - overtimeWages: Calculated wages for overtime
 *   - doubleOvertimeWages: Calculated wages for double overtime
 *   - totalWages: Sum of all wage categories
 *   - companyDynamicFringeContributionRate: Company's dynamic fringe contribution per hour
 *   - employeeDynamicFringeContributionPerecent: Employee's dynamic fringe contribution percentage
 *   - companyDynamicFringeAllocation: Total company dynamic fringe allocation
 *   - employeeDynamicFringeAllocation: Total employee dynamic fringe allocation
 */
export const calculateTimesheetEarnings = (weekTimesheets: ExtendedTimesheet[], orgData: Organization) => {
  const areBreaksPaid = orgData.timeTrackingSettings.areBreaksPaid;
  const isDriveTimeEnabled = orgData.timeTrackingSettings.isDriveTimeEnabled;

  // Calculate earnings for every timesheet
  for (const ts of weekTimesheets) {
    // Set up the wage rates and fringe benefit calculations
    let hourlyRate = 0;
    let otHourlyRate = 0;
    let dotHourlyRate = 0;
    let cashFringeRate = 0;
    let companyDynamicFringeContributionRate = 0;
    let employeeDynamicFringeContributionPerecent = 0;

    // Get the multipliers from org settings
    const overtimeMultiplier = orgData.overtimeSettings.overtimeMultiplier || 1.5;
    const doubleOvertimeMultiplier = 2.0;

    // Calculate the base hourly rate from the earning rate
    const isAnnually = ts?.regEarningRate?.period === "ANNUALLY" || false;
    hourlyRate = isAnnually
      ? parseFloat(ts?.regEarningRate?.amount) / 2080
      : ts?.regEarningRate?.amount
      ? parseFloat(ts?.regEarningRate?.amount)
      : 0;

    otHourlyRate = hourlyRate * overtimeMultiplier;
    dotHourlyRate = hourlyRate * doubleOvertimeMultiplier;

    // Handle prevailing wage calculations
    if (ts.project.isPrevailingWage && ts.userClassificationId) {
      cashFringeRate = calculateCashFringeDiff(
        ts.userClassification.fringePay,
        ts.fringeBenefitClassifications || [],
        ts.user.employeeBenefits || []
      );

      const dynamicFringeBenefit = ts.user.employeeBenefits?.find((eb) => eb.contributionType === "DYNAMIC");

      if (dynamicFringeBenefit) {
        companyDynamicFringeContributionRate = cashFringeRate;
        employeeDynamicFringeContributionPerecent = dynamicFringeBenefit.employeeContributionPercent;
        cashFringeRate = 0;
      }

      const prevailingWageHourlyRate = parseFloat(ts.userClassification.basePay) + cashFringeRate;
      const shouldUseHourlyRates =
        orgData?.prevailingWageSettings?.overridePwIfBelowRegularRate && hourlyRate > prevailingWageHourlyRate;

      if (!shouldUseHourlyRates) {
        hourlyRate = prevailingWageHourlyRate;
        otHourlyRate = parseFloat(ts.userClassification.basePay) * overtimeMultiplier + cashFringeRate;
        dotHourlyRate = parseFloat(ts.userClassification.basePay) * doubleOvertimeMultiplier + cashFringeRate;
      }
    }

    // Calculate the wages based on allocated minutes
    const breakMinutes = calculateBreakMinutes(ts);
    ts.breakMinutes = breakMinutes;

    // Assign all computed values to the timesheet
    ts.hourlyWage = hourlyRate;
    ts.otHourlyWage = otHourlyRate;
    ts.dotHourlyWage = dotHourlyRate;
    ts.cashFringeRate = cashFringeRate;
    ts.companyDynamicFringeContributionRate = companyDynamicFringeContributionRate;
    ts.employeeDynamicFringeContributionPerecent = employeeDynamicFringeContributionPerecent;

    // Calculate regular wages including paid breaks if enabled
    ts.regularWages = areBreaksPaid
      ? ((ts.regularMinutes + breakMinutes) / 60) * hourlyRate
      : (ts.regularMinutes / 60) * hourlyRate;

    // Calculate overtime wages
    ts.overtimeWages = areBreaksPaid
      ? ((ts.overtimeMinutes + breakMinutes) / 60) * otHourlyRate
      : (ts.overtimeMinutes / 60) * otHourlyRate;

    // Calculate double overtime wages
    ts.doubleOvertimeWages = areBreaksPaid
      ? ((ts.doubleOvertimeMinutes + breakMinutes) / 60) * dotHourlyRate
      : (ts.doubleOvertimeMinutes / 60) * dotHourlyRate;

    // Calculate drive time wages
    ts.driveTimeMinutes = ts.driveTimeDuration ? ts.driveTimeDuration / 60 : 0;
    ts.driveTimeWages = isDriveTimeEnabled
      ? (ts.driveTimeMinutes / 60) * orgData.timeTrackingSettings.driveTimeRate
      : 0;

    // Calculate total wages
    ts.totalWages = ts.regularWages + ts.overtimeWages + ts.doubleOvertimeWages + ts.driveTimeWages;

    // Calculate dynamic fringe allocations
    const totalMinutesWorked = calculateJobsiteWorkedMinutes(ts);

    ts.companyDynamicFringeAllocation = calculateDynamicFringeAllocation(
      companyDynamicFringeContributionRate,
      totalMinutesWorked
    );

    ts.employeeDynamicFringeAllocation = calculateDynamicFringeAllocation(
      hourlyRate * (employeeDynamicFringeContributionPerecent / 100),
      totalMinutesWorked
    );
  }

  return weekTimesheets;
};

/**
 * A simplified version of refinedAttributedWeeklyTimesheets that uses the manually set
 * otDuration property for overtime minutes rather than calculating them
 *
 * @param {ExtendedTimesheet[]} timesheets - A collection of timesheets to have overtime attributed.
 *   These timesheets will be enhanced with overtime, regular minutes and wage calculations.
 * @param {Organization} orgData - Organization data containing settings for break pay, overtime rates, etc.
 *
 * @returns {ExtendedTimesheet[]} Enhanced timesheets with the following added properties:
 *   - regularMinutes: Minutes worked as regular time (minutesWorked - overtime)
 *   - overtimeMinutes: Minutes worked as overtime (from otDuration converted to minutes)
 *   - doubleOvertimeMinutes: Always 0 in manual mode (not yet supported)
 *   - breakMinutes: Break duration in minutes
 *   - hourlyWage: Regular hourly wage rate
 *   - otHourlyWage: Overtime hourly wage rate
 *   - regularWages: Total wages earned from regular time
 *   - overtimeWages: Total wages earned from overtime
 *   - doubleOvertimeWages: Always 0 in manual mode (not yet supported)
 *   - totalWages: Sum of regular and overtime wages
 *   - Additional fringe benefit calculations if applicable
 */
export const manualAttributedWeeklyTimesheets = (timesheets: ExtendedTimesheet[], orgData: Organization) => {
  let weekTimesheets = timesheets;

  // Process each timesheet to set minutes based on manual overtime
  for (const ts of weekTimesheets) {
    // Calculate total minutes worked
    if (typeof ts.totalMinutes === "undefined") {
      ts.totalMinutes = calculateTotalWorkedMinutes(ts);
    }

    const jobsiteWorkedMinutes = calculateJobsiteWorkedMinutes(ts);

    ts.overtimeMinutes = ts.otDuration ? Math.floor(ts.otDuration / 60) : 0;

    ts.doubleOvertimeMinutes = ts.dotDuration ? Math.floor(ts.dotDuration / 60) : 0;

    // Regular minutes are the total minutes worked minus the manual overtime minutes
    ts.regularMinutes = Math.max(0, jobsiteWorkedMinutes - ts.overtimeMinutes - ts.doubleOvertimeMinutes);

    // Calculate drive time minutes
    ts.driveTimeMinutes = ts.driveTimeDuration ? ts.driveTimeDuration / 60 : 0;
  }

  // Calculate timesheet earnings using the same pattern as refined attribution
  weekTimesheets = calculateTimesheetEarnings(weekTimesheets, orgData);

  return weekTimesheets;
};
