const requiredVariables = [
  "ENVIRONMENT",
  "API_PUBLIC_TOKEN",
  "MIGRATION_API_SECRET_TOKEN",
  "SMS_COOLDOWN_SECONDS",
  "POSTGRESS_DB_URI",
  "SESSION_SECRET",
  "API_PORT",
  "TWILIO_ACCOUNT_SID",
  "TWILIO_AUTH_TOKEN",
  "TWILIO_NUMBER",
  "MINIMUM_APP_VERSION_IOS",
  "MINIMUM_APP_VERSION_ANDROID",
  "DOMAIN",
];

export const basePath = "/api/v1";

export const checkEnviromentVariables = () => {
  for (const env of requiredVariables) {
    if (!process.env[env]) {
      throw new Error(`Expected env ${env} was not provided`);
    }
  }

  const { ENVIRONMENT, APP_URL, POSTGRESS_DB_URI, DOMAIN, MINIMUM_APP_VERSION_IOS, MINIMUM_APP_VERSION_ANDROID } =
    process.env;

  if (isNaN(parseFloat(MINIMUM_APP_VERSION_IOS))) {
    throw new Error(`Invalid MINIMUM_APP_VERSION_IOS:${MINIMUM_APP_VERSION_IOS}`);
  }

  if (!APP_URL) {
    throw new Error(`Please provide the APP URL`);
  }

  if (isNaN(parseFloat(MINIMUM_APP_VERSION_ANDROID))) {
    throw new Error(`Invalid MINIMUM_APP_VERSION_ANDROID:${MINIMUM_APP_VERSION_ANDROID}`);
  }

  if (ENVIRONMENT === "PRODUCTION" && DOMAIN !== ".hammr.com") {
    throw new Error(`Production DOMAIN env is wrong:${DOMAIN}`);
  } else if (ENVIRONMENT === "STAGING" && DOMAIN !== ".hammr.com") {
    throw new Error(`Staging DOMAIN env is wrong:${DOMAIN}`);
  } else if (ENVIRONMENT === "DEVELOPMENT" && DOMAIN !== "localhost") {
    throw new Error(`Development DOMAIN env is wrong:${DOMAIN}`);
  }

  if (ENVIRONMENT !== "DEVELOPMENT" && POSTGRESS_DB_URI.indexOf("rds.amazonaws") === -1) {
    throw new Error(`Production POSTGRESS_DB_URI env is wrong: ${POSTGRESS_DB_URI}`);
  }
};
