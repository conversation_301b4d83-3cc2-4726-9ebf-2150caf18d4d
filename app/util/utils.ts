import { ExtendedTimesheet } from "@/models/timesheet";
import EmployeeBenefit from "@/models/employeebenefit";
import OvertimeSettings from "@/models/overtimesettings";

export const employeeContributionAmountToHourly = (benefit: EmployeeBenefit, payFrequency: string): number => {
  if (benefit.period === "MONTHLY") {
    return (parseFloat(benefit.employeePeriodAmount) * 12) / 2080;
  } else {
    return convertAmountFromPayFrequencyToHourly(parseFloat(benefit.employeeContributionAmount), payFrequency);
  }
};

export const companyContributionAmountToHourly = (benefit: EmployeeBenefit, payFrequency: string): number => {
  if (benefit.period === "MONTHLY") {
    return (parseFloat(benefit.companyPeriodAmount) * 12) / 2080;
  } else {
    return convertAmountFromPayFrequencyToHourly(parseFloat(benefit.companyContributionAmount), payFrequency);
  }
};

const convertAmountFromPayFrequencyToHourly = (amount: number, payFrequency: string): number => {
  if (isNaN(amount)) {
    throw new Error("Amount must be a number");
  }

  if (amount < 0) return 0;

  if (payFrequency === "weekly") {
    return (amount * 52) / 2080;
  } else if (payFrequency === "biweekly") {
    return (amount * 26) / 2080;
  } else if (payFrequency === "semimonthly") {
    return (amount * 24) / 2080;
  } else if (payFrequency === "monthly") {
    return (amount * 12) / 2080;
  }

  return amount;
};

export const convertPercentFringeContributionToHourly = (
  grossWageHourly: number,
  contributionPercent: number
): number => {
  return grossWageHourly * (contributionPercent / 100);
};

// This can be used to replace field names (eg: from validation errors) into user-friendly readable names
export function beautifyError(message: string, mappings: Record<string, string>) {
  return Object.entries(mappings).reduce((message, [field, name]) => message.replace(field, `${name}`), message);
}

export const getEffectiveOvertimePolicy = (timesheet: ExtendedTimesheet, defaultPolicy: OvertimeSettings) => {
  const policyToUse = timesheet?.project?.overtimeSettings || defaultPolicy;

  return policyToUse;
};
