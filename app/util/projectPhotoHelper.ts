import { ProjectPhotosCollection } from "../models";
import { toEpoch } from "./dateHelper";

export function serializeProjectPhotosCollection(projectPhotosCollection: ProjectPhotosCollection): any {
  const projectPhotosCollectionJSON = projectPhotosCollection.toJSON();
  projectPhotosCollectionJSON.createdAt = toEpoch(projectPhotosCollectionJSON.createdAt);
  projectPhotosCollectionJSON.updatedAt = toEpoch(projectPhotosCollectionJSON.updatedAt);

  return projectPhotosCollectionJSON;
}

export function serializeProjectPhoto(projectPhoto: any): any {
  const projectPhotoJSON = projectPhoto.toJSON();
  projectPhotoJSON.createdAt = toEpoch(projectPhotoJSON.createdAt);
  projectPhotoJSON.updatedAt = toEpoch(projectPhotoJSON.updatedAt);

  return projectPhotoJSON;
}
