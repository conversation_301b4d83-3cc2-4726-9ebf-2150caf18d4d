import type { Twilio } from "twilio";

const { TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_NUMBER } = process.env;

let client: Twilio;
if (process.env.NODE_ENV !== "test") {
  client = require("twilio")(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
}

export const sendSMS = (toNumber: string, message: string) => {
  if (process.env.NODE_ENV !== "test") {
    return client?.messages.create({
      body: message,
      from: TWILIO_NUMBER,
      to: toNumber,
    });
  }
};
