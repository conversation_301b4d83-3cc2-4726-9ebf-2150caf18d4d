import dayjs from "dayjs";
import {
  Break,
  Classification,
  CompanyBenefit,
  CostCode,
  EarningRate,
  Organization,
  OvertimeSettings,
  Project,
  User,
  UserClassification,
} from "../models";

import { toEpoch } from "./dateHelper";
import { TimesheetRow } from "@/util/financial";

import { formattedMinutes } from "@/util/reportFormatter";
import { ExtendedTimesheet, UserEarningsSummary } from "@/models/timesheet";
import { ExtendedEarningRate } from "@/models/earningrate";
import EmployeeBenefit, { ExtendedEmployeeBenefit } from "@/models/employeebenefit";
import { RoundingSettings } from "@/models/timetrackingsettings";
import { BenefitOverrideCalculation, CheckBenefitOverride } from "@/types/check";
import { ExtendedOrganization } from "@/models/organization";
import { Op } from "sequelize";

interface ColumnMapping {
  name: string;
  key: string;
  // eslint-disable-next-line no-unused-vars
  reportKey: (data: TimesheetRow, org: Organization) => any;
  // eslint-disable-next-line no-unused-vars
  totalKey: (data: TimesheetRow, org: Organization, groupBy: string) => any;
}

export const calculateBreakMinutes = (timesheet: any): number => {
  if (timesheet.breaks && timesheet.breaks.length > 0) {
    return timesheet.breaks.reduce((total: number, current: any) => {
      if (!current.start || !current.end) return total;
      const start = roundedDown(current.start);
      const end = roundedDown(current.end);
      const breakDuration = end.diff(start, "minute");

      if (isNaN(breakDuration)) return total;

      return total + breakDuration;
    }, 0);
  }

  return timesheet.breakDuration ? Math.round(timesheet.breakDuration / 60) : 0;
};

// Returns clock in as a string in organization's timezone
export const clockInString = (timesheet: any, organization: Organization): string => {
  return timeInOrganizationTimezone(roundedDown(timesheet.clockIn), organization);
};

// Returns clock out as a string in organization's timezone
export const clockOutString = (timesheet: any, organization: Organization): string => {
  if (dayjs(timesheet.clockOut).isValid()) {
    return timeInOrganizationTimezone(roundedDown(timesheet.clockOut), organization);
  } else {
    return "";
  }
};

export const timeInOrganizationTimezone = (date: dayjs.Dayjs, organization: Organization): string => {
  return dayjs(date).tz(organization.timezone).format("h:mm A");
};

export const dateInOrganizationTimezone = (date: Date | dayjs.Dayjs, organization: Organization): string => {
  return dayjs(date).tz(organization.timezone).format("M/D/YYYY");
};

export const calculateTimesheetMinutes = (timesheet: any): number => {
  const clockInRounded = roundedDown(timesheet.clockIn);
  const clockOut = timesheet.clockOut ?? new Date().getTime();
  const clockOutRounded = roundedDown(clockOut);

  return clockOutRounded.diff(clockInRounded, "minute");
};

const roundedDown = (date: Date): dayjs.Dayjs => {
  return dayjs(date).startOf("minute");
};

/**
 * Calculate the total minutes worked on jobsites (clock out - clock in - breaks - drive time)
 * @param timesheet Timesheet
 * @returns minutes worked
 */
export const calculateJobsiteWorkedMinutes = (timesheet: any): number => {
  const workedMinutes = calculateTotalWorkedMinutes(timesheet);
  const driveTimeDuration = timesheet.driveTimeDuration ? timesheet.driveTimeDuration / 60 : 0;

  return Math.max(workedMinutes - driveTimeDuration, 0);
};

/**
 * Calculate the total minutes worked for a timesheet (clock out - clock in - breaks)
 * @param timesheet Timesheet
 * @returns minutes worked
 */
export const calculateTotalWorkedMinutes = (timesheet: any): number => {
  const timesheetDuration = calculateTimesheetMinutes(timesheet);
  const breakMinutes = calculateBreakMinutes(timesheet);

  return Math.max(timesheetDuration - breakMinutes, 0);
};

export function calculateOvertime(
  minutesPerDay: Record<string, number>,
  organization: ExtendedOrganization,
  totalMinutes: number
): number {
  let overtimeMinutes = 0;
  const dailyOvertimeThreshold = organization.overtimeSettings.dailyOvertimeThreshold;
  const weeklyOvertimeThreshold = organization.overtimeSettings.weeklyOvertimeThreshold;

  if (organization.overtimeSettings.dailyOvertimeEnabled) {
    Object.keys(minutesPerDay).forEach((date) => {
      const minutes = minutesPerDay[date];
      if (minutes > dailyOvertimeThreshold) {
        overtimeMinutes += minutes - dailyOvertimeThreshold;
      }
    });
  }

  if (organization.overtimeSettings.weeklyOvertimeEnabled && totalMinutes > weeklyOvertimeThreshold) {
    const weeklyOvertimeMinutes = calculateWeeklyOvertime(minutesPerDay, weeklyOvertimeThreshold);
    overtimeMinutes = Math.max(overtimeMinutes, weeklyOvertimeMinutes);
  }

  return overtimeMinutes;
}

export function calculateDailyOvertime(
  minutesPerDay: Record<string, number>,
  dailyOvertimeThreshold: number,
  minutesWorked: number,
  timesheet: any,
  organization: any
): number {
  const formattedDate = dateInOrganizationTimezone(timesheet.clockIn, organization);
  const existingMinutesForDay = minutesPerDay[formattedDate];

  if (existingMinutesForDay) {
    if (existingMinutesForDay > dailyOvertimeThreshold) {
      return minutesWorked;
    }

    const overtimeMinutes = existingMinutesForDay + minutesWorked - dailyOvertimeThreshold;

    return Math.max(0, overtimeMinutes);
  } else {
    const overtimeMinutes = minutesWorked - dailyOvertimeThreshold;

    return Math.max(0, overtimeMinutes);
  }
}

function calculateWeeklyOvertime(minutesPerDay: Record<string, number>, weeklyOvertimeThreshold: number): number {
  const sortedDays = Object.keys(minutesPerDay).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

  let totalOvertime = 0;
  let totalMinutesForCurrentWeek = 0;
  let weekStart = new Date(sortedDays[0]);
  let weekEnd = new Date(weekStart.getTime());
  weekEnd.setDate(weekStart.getDate() + 6);

  for (let i = 0; i < sortedDays.length; i++) {
    const currentDay = new Date(sortedDays[i]);

    // If current day is in next week, calculate overtime for the current week
    if (currentDay.getTime() > weekEnd.getTime()) {
      if (totalMinutesForCurrentWeek > weeklyOvertimeThreshold) {
        totalOvertime += totalMinutesForCurrentWeek - weeklyOvertimeThreshold;
      }

      // Prepare for the next week
      weekStart = new Date(currentDay);
      weekEnd = new Date(weekStart.getTime());
      weekEnd.setDate(weekStart.getDate() + 6);
      totalMinutesForCurrentWeek = minutesPerDay[sortedDays[i]];
    } else if (i === sortedDays.length - 1) {
      // if current day is the last day in the list, calculate overtime for the current week
      totalMinutesForCurrentWeek += minutesPerDay[sortedDays[i]];
      if (totalMinutesForCurrentWeek > weeklyOvertimeThreshold) {
        totalOvertime += totalMinutesForCurrentWeek - weeklyOvertimeThreshold;
      }
    } else {
      totalMinutesForCurrentWeek += minutesPerDay[sortedDays[i]];
    }
  }

  return totalOvertime;
}

export function updateMinutesPerDay(minutesPerDay: Record<string, number>, timesheet: any, organization: any) {
  const formattedDate = dateInOrganizationTimezone(timesheet.clockIn, organization);
  const minutesWorked = calculateTotalWorkedMinutes(timesheet);

  if (minutesPerDay[formattedDate]) {
    minutesPerDay[formattedDate] += minutesWorked;
  } else {
    minutesPerDay[formattedDate] = minutesWorked;
  }
}

export function serializeTimesheet(timesheet: any): any {
  const newTimesheet = { ...timesheet };

  if (newTimesheet.clockIn) {
    newTimesheet.clockIn = toEpoch(newTimesheet.clockIn);
  }

  if (newTimesheet.clockOut) {
    newTimesheet.clockOut = toEpoch(newTimesheet.clockOut);
  }

  if (newTimesheet.geofenceEnterAt) {
    newTimesheet.geofenceEnterAt = toEpoch(newTimesheet.geofenceEnterAt);
  }

  if (newTimesheet.createdByUser?.firstName && newTimesheet.createdByUser?.lastName) {
    newTimesheet.createdBy = `${newTimesheet.createdByUser.firstName} ${newTimesheet.createdByUser.lastName}`;
  }

  if (newTimesheet.editedByUser?.firstName && newTimesheet.editedByUser?.lastName) {
    newTimesheet.editedBy = `${newTimesheet.editedByUser.firstName} ${newTimesheet.editedByUser.lastName}`;
  }

  newTimesheet.breakDuration = calculateBreakMinutes(newTimesheet);

  newTimesheet.breaks = serializeBreaks(newTimesheet);

  return newTimesheet;
}

export function serializeBreaks(timesheet: any): any {
  if (timesheet.breaks) {
    return timesheet.breaks.map((breakItem: any) => {
      const newBreakItem = { ...breakItem };
      newBreakItem.start = newBreakItem.start ? toEpoch(newBreakItem.start) : null;
      newBreakItem.end = newBreakItem.end ? toEpoch(newBreakItem.end) : null;

      return newBreakItem;
    });
  }

  return null;
}

const defaultColumnOrder = [
  "name",
  "position",
  "employeeId",
  "projectName",
  "projectNumber",
  "costCodeName",
  "formattedDate",
  "clockIn",
  "clockOut",
  "totalHours",
  "regularHours",
  "overtime",
  "break",
  "paidBreak",
  "hourlyWage",
  "earnings",
  "description",
];

export function reportColumnsMapper(columnOrder: string[] = defaultColumnOrder): ColumnMapping[] {
  const columnsMappings: ColumnMapping[] = [
    {
      name: "Employee Name",
      key: "name",
      reportKey: (data: TimesheetRow) => `${data.firstName} ${data.lastName}`,
      totalKey: (data: TimesheetRow, _org: Organization, groupBy: string) =>
        groupBy === "employee" ? `${data.firstName} ${data.lastName} - Total` : "",
    },
    {
      name: "Position",
      key: "position",
      reportKey: (data: TimesheetRow) => data.position,
      totalKey: () => "",
    },
    {
      name: "Employee ID",
      key: "employeeId",
      reportKey: (data: TimesheetRow) => data.employeeId,
      totalKey: () => "",
    },
    {
      name: "Project Name",
      key: "projectName",
      reportKey: (data: TimesheetRow) => data.projectName,
      totalKey: (data: TimesheetRow, _org: Organization, groupBy: string) =>
        groupBy === "project" ? `${data.projectName} - Total` : "",
    },
    {
      name: "Project Number",
      key: "projectNumber",
      reportKey: (data: TimesheetRow) => data.projectNumber,
      totalKey: () => "",
    },
    {
      name: "Cost Code",
      key: "costCodeName",
      reportKey: (data: TimesheetRow) => data.costCodeName,
      totalKey: (data: TimesheetRow, _org: Organization, groupBy: string) =>
        groupBy === "costCode" ? `${data.costCodeName} - Total` : "",
    },
    {
      name: "Date",
      key: "formattedDate",
      reportKey: (data: TimesheetRow) => data.formattedDate,
      totalKey: (data: TimesheetRow, _org: Organization, groupBy: string) => {
        return groupBy === "date" ? `${data.formattedDate} - Total` : "";
      },
    },
    {
      name: "Clock In",
      key: "clockIn",
      reportKey: (data: TimesheetRow) => data.clockIn,
      totalKey: () => "",
    },
    {
      name: "Clock Out",
      key: "clockOut",
      reportKey: (data: TimesheetRow) => data.clockOut,
      totalKey: () => "",
    },
    {
      name: "Total Hours",
      key: "totalHours",
      reportKey: (data: TimesheetRow, org: Organization) =>
        data.totalMinutes ? formattedMinutes(data.totalMinutes, org.timeTrackingSettings.useDecimalHours) : "",
      totalKey: (data: TimesheetRow, org: Organization) =>
        data.totalMinutes ? formattedMinutes(data.totalMinutes, org.timeTrackingSettings.useDecimalHours) : "",
    },
    {
      name: "Regular Hours",
      key: "regularHours",
      reportKey: (data: TimesheetRow, org: Organization) =>
        data.regularMinutes ? formattedMinutes(data.regularMinutes, org.timeTrackingSettings.useDecimalHours) : "",
      totalKey: (data: TimesheetRow, org: Organization) =>
        data.regularMinutes ? formattedMinutes(data.regularMinutes, org.timeTrackingSettings.useDecimalHours) : "",
    },
    {
      name: "Overtime Hours",
      key: "overtime",
      reportKey: (data: TimesheetRow, org: Organization) =>
        data.overtimeMinutes ? formattedMinutes(data.overtimeMinutes, org.timeTrackingSettings.useDecimalHours) : "",
      totalKey: (data: TimesheetRow, org: Organization) =>
        data.overtimeMinutes ? formattedMinutes(data.overtimeMinutes, org.timeTrackingSettings.useDecimalHours) : "",
    },
    {
      name: "Break",
      key: "break",
      reportKey: (data: TimesheetRow, org: Organization) =>
        data.breakMinutes ? formattedMinutes(data.breakMinutes, org.timeTrackingSettings.useDecimalHours) : "",
      totalKey: (data: TimesheetRow, org: Organization) =>
        data.breakMinutes ? formattedMinutes(data.breakMinutes, org.timeTrackingSettings.useDecimalHours) : "",
    },
    {
      name: "Paid Break",
      key: "paidBreak",
      reportKey: (data: TimesheetRow, org: Organization) =>
        data.breakMinutes ? formattedMinutes(data.breakMinutes, org.timeTrackingSettings.useDecimalHours) : "",
      totalKey: (data: TimesheetRow, org: Organization) =>
        data.breakMinutes ? formattedMinutes(data.breakMinutes, org.timeTrackingSettings.useDecimalHours) : "",
    },
    {
      name: "Hourly Wage",
      key: "hourlyWage",
      reportKey: (data: TimesheetRow) => data.hourlyWage,
      totalKey: () => "",
    },
    {
      name: "Earnings",
      key: "earnings",
      reportKey: () => "",
      totalKey: (data: TimesheetRow) =>
        data.wages.toLocaleString("en-us", {
          style: "currency",
          currency: "USD",
        }),
    },
    {
      name: "Description",
      key: "description",
      reportKey: (data: TimesheetRow) => data.description,
      totalKey: () => "",
    },
  ];

  return columnOrder.map((columnKey) => {
    const column = columnsMappings.find((column) => column.key === columnKey);

    return column;
  });
}

/**
 * Function that generates subarrays of timesheets for a single user ordered by clockIn
 * @param arr ExtendedTimesheet[] - expects sorted array of timesheets by clockIn for a SINGLE USER
 * @param fromTimestamp number - timestamp of the first day of the week
 * @returns Timesheet[][]
 */
export const generateWeeklyTimesheetSubArrays = (timesheets: ExtendedTimesheet[], fromTimestamp?: number) => {
  if (timesheets.length === 0) return []; // Handle empty array gracefully

  const SEVEN_DAYS_IN_MILLISECONDS = 604800000;
  const subArrayOfTimesheets = [];

  let startPointer = 0;
  let endPointer = 0;

  let currentWeekStartTimestamp = fromTimestamp;
  let currentWeekEndTimestamp = fromTimestamp + SEVEN_DAYS_IN_MILLISECONDS;

  for (let i = 0; i < timesheets.length; i++) {
    const currentTimesheet = timesheets[i];
    const currentTimesheetClockInTimestamp = currentTimesheet.clockIn.getTime();

    if (
      currentTimesheetClockInTimestamp >= currentWeekStartTimestamp &&
      currentTimesheetClockInTimestamp < currentWeekEndTimestamp
    ) {
      endPointer += 1;
    } else {
      if (endPointer > startPointer) {
        subArrayOfTimesheets.push(timesheets.slice(startPointer, endPointer));
      }
      startPointer = i;
      endPointer = i + 1;
      currentWeekStartTimestamp = currentWeekEndTimestamp;
      currentWeekEndTimestamp += SEVEN_DAYS_IN_MILLISECONDS;
    }
  }

  // Add the last week's timesheets, if any
  if (endPointer > startPointer) {
    subArrayOfTimesheets.push(timesheets.slice(startPointer, endPointer));
  }

  return subArrayOfTimesheets;
};

/**
 * Generates an array of arrays, where each element is an array of timesheets for one particular user
 * @param arr ExtendedTimesheet[] - expects sorted array of timesheets by user and then clockIn
 * @returns Timesheet[][]
 */
export const generateUserTimesheetSubArrays = (timesheets: ExtendedTimesheet[]) => {
  if (timesheets.length === 0) return []; // Handle empty array gracefully

  const allUserTimesheets = [];
  let startPointer = 0;
  let currentUserId = timesheets[0].workerId;

  for (let i = 0; i < timesheets.length; i++) {
    const currentTimesheet = timesheets[i];
    if (currentTimesheet.workerId !== currentUserId) {
      const userTimesheets = timesheets.slice(startPointer, i);
      allUserTimesheets.push(userTimesheets);
      startPointer = i;
      currentUserId = currentTimesheet.workerId;
    }
  }

  // Add the last user's timesheets
  const userTimesheets = timesheets.slice(startPointer, timesheets.length);
  allUserTimesheets.push(userTimesheets);

  return allUserTimesheets;
};

export const summarizeUserTimesheetData = (
  timeEntries: Partial<ExtendedTimesheet>[],
  pruneEarnings = false
): UserEarningsSummary[] => {
  const summaries = timeEntries.reduce((acc, entry) => {
    let user = acc.find((u) => u.id === entry.workerId);
    const dynamicFringeBenefitId =
      entry.user.employeeBenefits.find((eb) => eb.contributionType === "DYNAMIC")?.checkBenefitId || null;

    if (!user) {
      user = {
        id: entry.workerId,
        firstName: entry.user.firstName,
        lastName: entry.user.lastName,
        compensationType: entry?.regEarningRate?.period || "HOURLY", // default to hourly
        hourlyWage: parseFloat(entry?.regEarningRate?.amount || "0") || 0,
        otHourlyWage: parseFloat(entry?.otEarningRate?.amount || "0") || 0,
        dotHourlyWage: parseFloat(entry?.dotEarningRate?.amount || "0") || 0,
        salary: 0,
        checkRegEarningRateId: entry?.regEarningRate?.checkEarningRateId,
        checkOtEarningRateId: entry?.otEarningRate?.checkEarningRateId,
        checkDotEarningRateId: entry?.dotEarningRate?.checkEarningRateId,
        checkEmployeeId: entry.user.checkEmployeeId,
        checkContractorId: entry.user.checkContractorId,
        regularMinutes: 0,
        totalMinutes: 0,
        overtimeMinutes: 0,
        doubleOvertimeMinutes: 0,
        breakMinutes: 0,
        driveTimeMinutes: 0,
        totalWages: 0,
        checkDynamicFringeBenefit: dynamicFringeBenefitId,
        timeWorkedByDay: [],
        nonPWTimesheetsSummary: [],
        pwTimesheetsSummary: [],
        checkBenefitOverrides: [],
        reimbursements: [],
        otherEarnings: [],
        paymentMethod: null,
      };
      acc.push(user);
    }

    user.regularMinutes += entry.regularMinutes;
    user.overtimeMinutes += entry.overtimeMinutes;
    user.doubleOvertimeMinutes += entry.doubleOvertimeMinutes;
    user.breakMinutes += entry.breakMinutes || 0;
    user.driveTimeMinutes += entry.driveTimeMinutes || 0;
    user.totalMinutes +=
      entry.regularMinutes + entry.overtimeMinutes + entry.doubleOvertimeMinutes + entry.driveTimeMinutes;
    user.totalWages += entry.totalWages;

    // aggregate non-prevailing wage summaries
    if (entry.project.isPrevailingWage === false) {
      let nonPWSummary = user.nonPWTimesheetsSummary.find((s: any) => s.isPrevailingWage === false);

      if (!nonPWSummary) {
        nonPWSummary = {
          isPrevailingWage: false,
          description: "Non-Prevailing Wage",
          classificationName: entry?.userClassification?.classification?.name || "N/A",
          checkRegEarningCodeId: entry?.userClassification?.classification?.checkRegEarningCodeId || "N/A",
          checkOtEarningCodeId: entry?.userClassification?.classification?.checkOtEarningCodeId || "N/A",
          checkDotEarningCodeId: entry?.userClassification?.classification?.checkDotEarningCodeId || "N/A",
          checkRegEarningRateId: entry?.regEarningRate?.checkEarningRateId,
          checkOtEarningRateId: entry?.otEarningRate?.checkEarningRateId,
          checkDotEarningRateId: entry?.dotEarningRate?.checkEarningRateId,
          hourlyWage: parseFloat(entry.regEarningRate?.amount || "0") || 0,
          otHourlyWage: parseFloat(entry?.otEarningRate?.amount || "0") || 0,
          dotHourlyWage: parseFloat(entry?.dotEarningRate?.amount || "0") || 0,
          regularMinutes: 0,
          overtimeMinutes: 0,
          doubleOvertimeMinutes: 0,
          breakMinutes: 0,
          driveTimeMinutes: 0,
          regularWages: 0,
          driveTimeWages: 0,
          overtimeWages: 0,
          doubleOvertimeWages: 0,
          totalWages: 0,
        };
        user.nonPWTimesheetsSummary.push(nonPWSummary);
      }
      nonPWSummary.regularMinutes += entry.regularMinutes;
      nonPWSummary.overtimeMinutes += entry.overtimeMinutes;
      nonPWSummary.doubleOvertimeMinutes += entry.doubleOvertimeMinutes;
      nonPWSummary.breakMinutes += entry.breakMinutes || 0;
      nonPWSummary.driveTimeMinutes += entry.driveTimeMinutes || 0;
      nonPWSummary.regularWages += entry.regularWages;
      nonPWSummary.overtimeWages += entry.overtimeWages;
      nonPWSummary.doubleOvertimeWages += entry.doubleOvertimeWages || 0;
      nonPWSummary.driveTimeWages += entry.driveTimeWages;
      nonPWSummary.totalMinutes +=
        entry.regularMinutes + entry.overtimeMinutes + entry.doubleOvertimeMinutes + entry.driveTimeMinutes;
      nonPWSummary.totalWages += entry.totalWages;
    }

    // aggregate prevailing wage summaries
    if (entry.project.isPrevailingWage === true && entry?.userClassification?.classification) {
      // for prevailing wage jobs, we need to keep track of dynamic fringes and allocations here
      let pwSummary = user.pwTimesheetsSummary.find(
        (s: any) => s.classificationName === entry?.userClassification?.classification?.name
      );

      if (!pwSummary) {
        pwSummary = {
          isPrevailingWage: true,
          description: `${entry.project.name} - Prevailing Wage`,
          classificationName: entry?.userClassification?.classification?.name,
          checkRegEarningCodeId: entry?.userClassification?.classification?.checkRegEarningCodeId || "N/A",
          checkOtEarningCodeId: entry?.userClassification?.classification?.checkOtEarningCodeId || "N/A",
          checkDotEarningCodeId: entry?.userClassification?.classification?.checkDotEarningCodeId || "N/A",
          checkRegEarningRateId: entry?.regEarningRate?.checkEarningRateId || "N/A",
          checkOtEarningRateId: entry?.otEarningRate?.checkEarningRateId || "N/A",
          checkDotEarningRateId: entry?.dotEarningRate?.checkEarningRateId || "N/A",
          hourlyWage: entry?.hourlyWage || 0,
          otHourlyWage: entry?.otHourlyWage || 0,
          dotHourlyWage: entry?.dotHourlyWage || 0,
          regularMinutes: 0,
          overtimeMinutes: 0,
          doubleOvertimeMinutes: 0,
          breakMinutes: 0,
          driveTimeMinutes: 0,
          regularWages: 0,
          overtimeWages: 0,
          doubleOvertimeWages: 0,
          driveTimeWages: 0,
          totalWages: 0,
        };
        user.pwTimesheetsSummary.push(pwSummary);
      }

      pwSummary.regularMinutes += entry.regularMinutes;
      pwSummary.overtimeMinutes += entry.overtimeMinutes;
      pwSummary.doubleOvertimeMinutes += entry.doubleOvertimeMinutes;
      pwSummary.breakMinutes += entry.breakMinutes || 0;
      pwSummary.driveTimeMinutes += entry.driveTimeMinutes || 0;
      pwSummary.regularWages += entry.regularWages;
      pwSummary.overtimeWages += entry.overtimeWages;
      pwSummary.doubleOvertimeWages += entry.doubleOvertimeWages || 0;
      pwSummary.driveTimeWages += entry.driveTimeWages;
      pwSummary.totalMinutes +=
        entry.regularMinutes + entry.overtimeMinutes + entry.doubleOvertimeMinutes + entry.driveTimeMinutes;
      pwSummary.totalWages += entry.totalWages;
    }

    // handle any benefit override creation - dynamic fringe
    if (dynamicFringeBenefitId) {
      let checkDynamicBenefitOverride = user.checkBenefitOverrides.find(
        (cb: BenefitOverrideCalculation) => cb.benefit === dynamicFringeBenefitId
      );

      if (!checkDynamicBenefitOverride) {
        // create a new benefit override
        checkDynamicBenefitOverride = {
          benefit: dynamicFringeBenefitId,
          employee_contribution_amount: 0,
          company_contribution_amount: 0,
        };

        user.checkBenefitOverrides.push(checkDynamicBenefitOverride);
      }

      // do the accumulations
      checkDynamicBenefitOverride.employee_contribution_amount += entry.employeeDynamicFringeAllocation;
      checkDynamicBenefitOverride.company_contribution_amount += entry.companyDynamicFringeAllocation;
    }

    return acc;
  }, []);

  summaries.forEach((user: any) => {
    user.totalWages = +user.totalWages.toFixed(2);

    // Round total wages for non-prevailing wage summaries at the end
    user.nonPWTimesheetsSummary.forEach((summary: any) => {
      summary.regularWages = +summary.regularWages.toFixed(2);
      summary.overtimeWages = +summary.overtimeWages.toFixed(2);
      summary.doubleOvertimeWages = +summary.doubleOvertimeWages.toFixed(2);
      summary.totalWages = +summary.totalWages.toFixed(2);
      // Calculate total minutes
      summary.totalMinutes =
        summary.regularMinutes + summary.overtimeMinutes + summary.doubleOvertimeMinutes + summary.driveTimeMinutes;
    });

    // Round total wages for prevailing wage summaries at the end
    user.pwTimesheetsSummary.forEach((summary: any) => {
      summary.regularWages = +summary.regularWages.toFixed(2);
      summary.overtimeWages = +summary.overtimeWages.toFixed(2);
      summary.doubleOvertimeWages = +summary.doubleOvertimeWages.toFixed(2);
      summary.totalWages = +summary.totalWages.toFixed(2);
      // Calculate total minutes
      summary.totalMinutes =
        summary.regularMinutes + summary.overtimeMinutes + summary.doubleOvertimeMinutes + summary.driveTimeMinutes;
    });

    // Convert checkDynamicBenefitOverride amounts to strings
    user.checkBenefitOverrides = user.checkBenefitOverrides.map(
      (override: BenefitOverrideCalculation): CheckBenefitOverride => ({
        benefit: override.benefit,
        employee_contribution_amount: override.employee_contribution_amount.toFixed(2),
        company_contribution_amount: override.company_contribution_amount.toFixed(2),
      })
    );

    if (pruneEarnings) {
      delete user.regularWages;
      delete user.overtimeWages;
      delete user.hourlyWage;
      delete user.otHourlyWage;
      delete user.totalWages;
    }
  });

  return summaries;
};

// algorithm to assign the "correct" earning rate ids to timesheet
/**
 *
 * @param earningRates {EarningRate[]} - collection of earning rates
 * @param clockIn {integer} - clock in timestamp
 * @returns {EarningRate[]} - returns a collection of 3 earning rates (reg + ot) that are valid for the given clock in timestamp
 */
export const determineValidEarningRatesByClockIn = (
  earningRates: EarningRate[],
  clockIn: number
): [ExtendedEarningRate | null, ExtendedEarningRate | null, ExtendedEarningRate | null] => {
  let selectedRegRate: ExtendedEarningRate | null = null;
  let selectedOtRate: ExtendedEarningRate | null = null;
  let selectedDotRate: ExtendedEarningRate | null = null;
  let closestFutureRegRate: ExtendedEarningRate | null = null;
  let closestFutureOtRate: ExtendedEarningRate | null = null;
  let closestFutureDotRate: ExtendedEarningRate | null = null;

  // enhance earning rates with start and end timestamps
  const formattedEarningRates = earningRates.map((er: EarningRate) => {
    return {
      ...er,
      startDateTimestamp: new Date(er.startDate).getTime(),
      endDateTimestamp: new Date(er.endDate).getTime(),
    } as ExtendedEarningRate;
  });

  for (const er of formattedEarningRates) {
    const startDateTimestamp = new Date(er.startDate).getTime();
    const endDateTimestamp = er.endDate ? new Date(er.endDate).getTime() : Infinity; // Handle null end dates

    // check if the earning rate is within the active period
    if (startDateTimestamp <= clockIn && clockIn <= endDateTimestamp) {
      if (er.type === "REG" && (!selectedRegRate || selectedRegRate.startDateTimestamp < startDateTimestamp)) {
        selectedRegRate = er; // select the most recent 'REG' rate within the period
      } else if (er.type === "OT" && (!selectedOtRate || selectedOtRate.startDateTimestamp < startDateTimestamp)) {
        selectedOtRate = er; // select the most recent 'OT' rate within the period
      } else if (er.type === "DOT" && (!selectedDotRate || selectedDotRate.startDateTimestamp < startDateTimestamp)) {
        selectedDotRate = er; // select the most recent 'DOT' rate within the period
      }
    }

    // update the closest future rates if no current rates are applicable
    if (startDateTimestamp > clockIn) {
      if (
        er.type === "REG" &&
        (!closestFutureRegRate || startDateTimestamp < closestFutureRegRate.startDateTimestamp)
      ) {
        closestFutureRegRate = er; // select the earliest future 'REG' rate
      } else if (
        er.type === "OT" &&
        (!closestFutureOtRate || startDateTimestamp < closestFutureOtRate.startDateTimestamp)
      ) {
        closestFutureOtRate = er; // select the earliest future 'OT' rate
      } else if (
        er.type === "DOT" &&
        (!closestFutureDotRate || startDateTimestamp < closestFutureDotRate.startDateTimestamp)
      ) {
        closestFutureDotRate = er; // select the earliest future 'DOT' rate
      }
    }
  }

  // If no rates were found within the active period, use the closest future rates if available
  return [
    selectedRegRate || closestFutureRegRate,
    selectedOtRate || closestFutureOtRate,
    selectedDotRate || closestFutureDotRate,
  ];
};

// we need to scan through the list of employee benefits. if any of them are contributionType: "DYNAMIC"
// return true
export const shouldCashFringeBeSetToZero = (employeeBenefits: Partial<ExtendedEmployeeBenefit>[]) => {
  return employeeBenefits.some((eb) => eb.contributionType === "DYNAMIC");
};

const INTERVAL_MINUTES = {
  FIVE_MINUTES: 5,
  TEN_MINUTES: 10,
  FIFTEEN_MINUTES: 15,
} as const;

/**
 * Rounds timesheet clock in/out times according to specified rounding settings
 * @param timesheet - The timesheet to round
 * @param settings - Configuration object containing rounding rules
 * @returns TimeSheet with rounded clock in/out times
 */
export function roundTimesheet(timesheet: ExtendedTimesheet, settings: RoundingSettings): ExtendedTimesheet {
  if (!settings.timesheetRoundingEnabled || !settings.timesheetRoundingType || !settings.timesheetRoundingInterval) {
    return timesheet;
  }

  // add raw values before any rounding
  if (timesheet) {
    timesheet.dataValues.rawClockIn = timesheet.clockIn.valueOf();
    timesheet.dataValues.rawClockOut = timesheet.clockOut ? timesheet.clockOut.valueOf() : null;
  }

  const intervalMs = INTERVAL_MINUTES[settings.timesheetRoundingInterval as keyof typeof INTERVAL_MINUTES];

  if (!intervalMs) {
    return timesheet;
  }

  const roundTime = (time: Date, isClockIn: boolean): Date => {
    // start with clean minutes (no seconds/ms)
    const timestamp = dayjs(time).startOf("minute");

    // calculate how many minutes we are past the last interval
    // e.g. for 9:23 with 15 min intervals: 23 % 15 = 8 minutes
    const remainder = timestamp.minute() % intervalMs;

    switch (settings.timesheetRoundingType) {
      case "ROUND_UP":
        // add minutes needed to reach next interval
        // e.g. 15 - 8 = 7 minutes added
        return timestamp.add(intervalMs - remainder, "minute").toDate();

      case "ROUND_DOWN":
        // subtract remainder to reach previous interval
        // e.g. subtract 8 minutes
        return timestamp.subtract(remainder, "minute").toDate();

      case "ROUND_NEAREST":
        // if remainder is less than half interval, round down, otherwise up
        // e.g. for 15 min intervals: if remainder < 7.5 round down, else up
        return remainder < intervalMs / 2
          ? timestamp.subtract(remainder, "minute").toDate()
          : timestamp.add(intervalMs - remainder, "minute").toDate();

      case "EMPLOYEE_FRIENDLY":
        // most favorable for employee:
        // clock in: rounds down (starts later)
        // clock out: rounds up (ends later)
        return isClockIn
          ? timestamp.subtract(remainder, "minute").toDate()
          : timestamp.add(intervalMs - remainder, "minute").toDate();

      default:
        return time;
    }
  };

  // only round clock in/out times if timesheet.clockOut is not null
  if (timesheet.clockOut) {
    timesheet.clockIn = roundTime(timesheet.clockIn, true);
    timesheet.clockOut = roundTime(timesheet.clockOut, false);
  }

  // If timesheet has breaks, set the start and end time of each break to the rounded clock in/out times
  if (timesheet?.breaks) {
    timesheet.breaks.forEach((breakItem) => {
      breakItem.dataValues.rawStart = breakItem.start.valueOf();
      breakItem.dataValues.rawEnd = breakItem.end?.valueOf();

      if (!timesheet.clockOut) {
        return;
      }

      // only bound break times if all required values exist
      if (breakItem.start && breakItem.end && timesheet.clockIn && timesheet.clockOut) {
        // ensure break times are bounded by timesheet clock in/out times
        breakItem.start = new Date(
          Math.max(timesheet.clockIn.getTime(), Math.min(breakItem.start.getTime(), timesheet.clockOut.getTime()))
        );

        breakItem.end = new Date(
          Math.max(timesheet.clockIn.getTime(), Math.min(breakItem.end.getTime(), timesheet.clockOut.getTime()))
        );

        // ensure break start is never after break end
        if (breakItem.start > breakItem.end) {
          breakItem.start = new Date(breakItem.end.getTime());
        }
      }
    });
  }

  return timesheet;
}
export const getTimesheetIncludes = (
  fromDate: Date | undefined,
  toDate: Date | undefined,
  additionalUserQuery: any = {}
) => {
  const userIncludes = {
    model: User,
    attributes: [
      "id",
      "firstName",
      "lastName",
      "position",
      "employeeId",
      "checkEmployeeId",
      "checkContractorId",
      "phone",
      "workerClassification",
    ],
    include: [
      {
        model: EmployeeBenefit,
        required: false,
        where: {
          benefitStartDate: {
            [Op.lte]: fromDate,
          },
          [Op.or]: [{ benefitEndDate: null }, { benefitEndDate: { [Op.gte]: toDate } }],
        },
        include: [
          {
            model: CompanyBenefit,
            as: "companyBenefit",
            where: {
              is_approved_fringe: true,
            },
          },
        ],
      },
    ],
  };

  if (additionalUserQuery && Object.keys(additionalUserQuery).length) {
    userIncludes.include.push(additionalUserQuery);
  }

  return [
    userIncludes,
    {
      model: EarningRate,
      as: "regEarningRate",
      required: false,
      where: {
        period: {
          [Op.ne]: "ANNUALLY",
        },
      },
    },
    {
      model: EarningRate,
      as: "otEarningRate",
      required: false,
      where: {
        period: {
          [Op.ne]: "ANNUALLY",
        },
      },
    },
    {
      model: EarningRate,
      as: "dotEarningRate",
      required: false,
      where: {
        period: {
          [Op.ne]: "ANNUALLY",
        },
      },
    },
    {
      model: Project,
      attributes: ["id", "name", "projectNumber", "isPrevailingWage", "address"],
      include: [
        {
          model: OvertimeSettings,
          as: "overtimeSettings",
          required: false,
        },
      ],
    },
    {
      model: CostCode,
      attributes: ["id", "name", "number"],
    },
    {
      model: UserClassification,
      as: "userClassification",
      required: false,
      include: [
        {
          model: Classification,
          required: false,
        },
      ],
    },
    {
      model: Break,
      where: {
        isDeleted: false,
      },
      attributes: ["id", "start", "end", "timesheetId", "isManual"],
      required: false,
    },
  ];
};
