import fetch from "node-fetch";

const growthSlackChannel = "C02MPAV672B";

export const sendSlackMessage = async (message: string, channel?: string): Promise<void> => {
  const slackChannel = channel || growthSlackChannel;
  const response = await fetch("https://slack.com/api/chat.postMessage", {
    method: "POST",
    headers: {
      "Content-Type": "application/json; charset=utf-8",
      Authorization: `Bearer ********************************************************`,
      Accept: "application/json",
    },
    body: JSON.stringify({
      channel: slackChannel,
      text: message,
    }),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(`Server error ${response.status}: ${data.error}`);
  }
};
