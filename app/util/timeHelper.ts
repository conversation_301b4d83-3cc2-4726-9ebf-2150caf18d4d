export function convertMinutesToFormattedTime(numberOfMinutes: number): string {
  // Calculate the number of whole hours
  const hours = Math.floor(numberOfMinutes / 60);
  // Calculate the remaining minutes after extracting the hours
  const minutes = numberOfMinutes % 60;

  // Format the hours and minutes into a string "H:MM"
  const formattedTime = `${hours}:${minutes.toString().padStart(2, "0")}`;

  return formattedTime;
}

export function convertMinutesToHours(minutes: number): number {
  return minutes / 60;
}

export const convertMinutesToHoursWorked = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0 && remainingMinutes === 0) {
    return "-";
  }

  if (hours === 0) {
    return `${remainingMinutes}m`;
  }

  return `${hours}h ${remainingMinutes}m`;
};
