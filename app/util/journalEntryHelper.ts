import { Payroll, PayrollItem } from "@/types/check";
import { JournalEntry, Tax, Garnishment, JournalEntryLineItem } from "@/types/journalEntry";
import { CompanyBenefit, EmployeeBenefit, GlAccountMapping, User } from "@/models";
import { ExtendedTimesheet } from "../models/timesheet";
import {
  BenefitLiability,
  ConsolidatedLiabilities,
  ProcessedPayrollItem,
  ProjectTotal,
  ExtendedCheckBenefit,
  ExtendedPayrollItemForCalc,
  PayrollItemWithProjectInfo,
  ExtendedContractorPayment,
} from "@/types/journalEntryCalculation";
import { formatCurrency, getAccountId } from "@/util/accountingUtil";
import {
  createEmptyProjectTotal,
  distributeBenefitsAcrossProjects,
  processBenefitsForPayroll,
  processPayrollItem,
} from "@/util/journalEntryCalculation";

/** Creates a basic journal entry structure with transaction date and memo */
export function createBaseJournalEntry(payroll: Payroll): JournalEntry {
  return {
    transaction_date: payroll.payday || new Date().toISOString(),
    currency_code: "USD",
    memo: `Payroll for period ${payroll.period_start} to ${payroll.period_end}`,
    line_items: [],
  };
}

/** Validates that a journal entry's total debits and credits match, and adds a balancing account if necessary */
export function validateAndBalanceJournalEntry(journalEntry: JournalEntry): boolean {
  let totalDebits = 0;
  let totalCredits = 0;

  journalEntry.line_items = journalEntry.line_items.filter((item) => Math.abs(item.total_amount) >= 0.01);

  journalEntry.line_items.forEach((item) => {
    if (item.total_amount > 0) {
      totalDebits += item.total_amount;
    } else {
      totalCredits += Math.abs(item.total_amount);
    }
  });

  totalDebits = formatCurrency(totalDebits);
  totalCredits = formatCurrency(totalCredits);

  const difference = formatCurrency(Math.abs(totalDebits - totalCredits));

  const isBalanced = difference < 0.01;

  return isBalanced;
}

/**
 * Creates journal entry line items for each project
 * and adds them to the journal entry
 */
function createJournalEntryLines(
  projectTotals: Record<string, ProjectTotal>,
  projectMap: Map<string, string>,
  payroll: Payroll,
  glAccountMap: Record<string, string>,
  journalEntry: JournalEntry,
  projectIdMapping?: Map<string, string>
) {
  // Get project names from timesheet projects if available
  const getProjectName = (projectId: string) => {
    return projectMap.get(projectId) || projectId;
  };

  const employerBenefitsTotal = parseFloat(payroll.totals.company_benefits);
  const employeeBenefitsTotal = parseFloat(payroll.totals.employee_benefits);

  const projectTotalsWithDistributedBenefits = distributeBenefitsAcrossProjects(
    projectTotals,
    employerBenefitsTotal,
    employeeBenefitsTotal
  );

  // Create consolidated liability containers
  const consolidatedLiabilities: ConsolidatedLiabilities = {
    manualPayments: 0,
    cashOut: 0,
    garnishments: 0,
    managedPostTaxDeductions: 0,
    benefitLiabilities: {},
  };

  payroll.items.forEach((item: PayrollItem) => {
    item.earnings.forEach((earning: any) => {
      if (earning.type === "2_percent_shareholder_benefits" || earning.type === "2_percent_shareholder_hsa") {
        consolidatedLiabilities.manualPayments += parseFloat(earning.amount || "0");
      }
    });
  });

  // First pass: Add all expense entries and collect liability totals
  Object.entries(projectTotalsWithDistributedBenefits).forEach(([projectId, totals]: [string, ProjectTotal]) => {
    const projectName = projectId === "No Project" ? "" : getProjectName(projectId);

    // Create expense line items only (no liabilities)
    const expenseLineItems = createProjectExpenseLineItems(
      projectId,
      totals,
      projectName,
      glAccountMap,
      projectIdMapping
    );
    journalEntry.line_items.push(...expenseLineItems);

    // Collect liability totals
    consolidatedLiabilities.manualPayments += totals.manualPayments;

    // Calculate direct deposit net pay (only for non-manual payments)
    const directDepositEmployeeNetPay = formatCurrency(totals.netPay - totals.manualPayments - totals.contractorNetPay);
    const cashOutAmount = formatCurrency(
      directDepositEmployeeNetPay + totals.employeeTaxes + totals.employerTaxes + totals.managedPostTaxDeductions
    );
    consolidatedLiabilities.cashOut += cashOutAmount;

    // Collect garnishment amounts
    const nonManagedGarnishments = formatCurrency(totals.garnishments - totals.managedPostTaxDeductions);
    consolidatedLiabilities.garnishments += nonManagedGarnishments;
    consolidatedLiabilities.managedPostTaxDeductions += totals.managedPostTaxDeductions;

    // Collect benefit liability amounts
    Object.entries(totals.benefitTotals).forEach(([category, amount]: [string, number]) => {
      if (amount <= 0) return;

      // Handle employee benefit liabilities
      if (category.endsWith("_EE")) {
        const benefitName = category.replace("_EE", "");
        const liabilityKey = `${benefitName}_EE_LIABILITY`;

        if (!consolidatedLiabilities.benefitLiabilities[liabilityKey]) {
          consolidatedLiabilities.benefitLiabilities[liabilityKey] = {
            name: benefitName,
            type: "EE" as const,
            amount: 0,
          };
        }

        consolidatedLiabilities.benefitLiabilities[liabilityKey].amount += amount;
      }

      // Handle employer benefit liabilities
      if (category.endsWith("_ER")) {
        const benefitName = category.replace("_ER", "");
        const liabilityKey = `${benefitName}_ER_LIABILITY`;

        if (!consolidatedLiabilities.benefitLiabilities[liabilityKey]) {
          consolidatedLiabilities.benefitLiabilities[liabilityKey] = {
            name: benefitName,
            type: "ER" as const,
            amount: 0,
          };
        }

        consolidatedLiabilities.benefitLiabilities[liabilityKey].amount += amount;
      }
    });
  });

  // Add contractor net pay to manual payments if applicable
  let totalDirectDepositContractorNetPay = 0;
  payroll.contractor_payments?.forEach((payment) => {
    if (payment.payment_method === "manual") {
      consolidatedLiabilities.manualPayments += parseFloat(payment.net_pay || "0");
    } else if (payment.payment_method === "direct_deposit") {
      totalDirectDepositContractorNetPay += parseFloat(payment.net_pay || "0");
    }
  });

  // Add the total direct deposit contractor net pay to the cash out liability
  consolidatedLiabilities.cashOut += formatCurrency(totalDirectDepositContractorNetPay);

  // Second pass: Add consolidated liability entries

  // Manual payments liability
  if (consolidatedLiabilities.manualPayments > 0) {
    journalEntry.line_items.push({
      account_id: getAccountId("MANUAL_PAYMENTS_LIABILITY", glAccountMap),
      total_amount: -formatCurrency(consolidatedLiabilities.manualPayments),
      description: "Manual Payments Liability",
    });
  }

  // Cash Out (Direct Deposit)
  if (consolidatedLiabilities.cashOut > 0) {
    journalEntry.line_items.push({
      account_id: getAccountId("BANK_ACCOUNT", glAccountMap),
      total_amount: -formatCurrency(consolidatedLiabilities.cashOut),
      description: "Cash Out (Direct Deposit)",
    });
  }

  // Garnishments & Post-Tax Deductions Payable
  if (consolidatedLiabilities.garnishments > 0) {
    journalEntry.line_items.push({
      account_id: getAccountId("GARNISHMENTS_PAYABLE", glAccountMap),
      total_amount: -formatCurrency(consolidatedLiabilities.garnishments),
      description: "Garnishments & Post-Tax Deductions Payable",
    });
  }

  // Employee and Employer Benefits Liabilities
  Object.values(consolidatedLiabilities.benefitLiabilities).forEach((benefit: BenefitLiability) => {
    if (benefit.amount <= 0) return;

    const accountName = benefit.type === "EE" ? `${benefit.name}_EE_LIABILITY` : `${benefit.name}_ER_LIABILITY`;

    const description =
      benefit.type === "EE" ? `${benefit.name} (EE contribution)` : `${benefit.name} Liability (ER contribution)`;

    journalEntry.line_items.push({
      account_id: getAccountId(accountName, glAccountMap),
      total_amount: -formatCurrency(benefit.amount),
      description,
    });
  });
}

/**
 * Creates ONLY expense line items for a project (no liabilities)
 */
export function createProjectExpenseLineItems(
  projectId: string,
  totals: ProjectTotal,
  projectName: string,
  glAccountMap: Record<string, string>,
  projectIdMapping?: Map<string, string>
) {
  const lines: JournalEntryLineItem[] = [];

  // Skip projects with no activity
  if (totals.wages === 0 && totals.netPay === 0) {
    return lines;
  }

  // Determine the project ID to use
  // Only use mapped external project IDs, otherwise treat as "No Project"
  let projectIdParam: string | undefined = undefined;

  if (projectId !== "No Project") {
    if (projectIdMapping && projectIdMapping.has(projectId)) {
      // Use external QBO ID if mapping exists
      projectIdParam = projectIdMapping.get(projectId);
    }
    // If no mapping exists, leave as undefined (treats as "No Project")
  }

  // DEBIT ENTRIES (EXPENSES) - Include project_id for expense entries

  // Wages expense - use "Gross Wages Expense" as in default consolidation
  if (totals.wages > 0) {
    lines.push({
      account_id: getAccountId("WAGES_AND_SALARIES", glAccountMap),
      total_amount: formatCurrency(totals.wages),
      description: projectName ? `Wages and Salaries - ${projectName}` : "Wages and Salaries",
      project_id: projectIdParam,
    });
  }

  // Reimbursement expense
  if (totals.reimbursements > 0) {
    lines.push({
      account_id: getAccountId("EXPENSE_REIMBURSEMENTS", glAccountMap),
      total_amount: formatCurrency(totals.reimbursements),
      description: projectName ? `Expense Reimbursements - ${projectName}` : "Expense Reimbursements",
      project_id: projectIdParam,
    });
  }

  // Employer tax expense - use "Employer Taxes" as in default consolidation
  if (totals.employerTaxes > 0) {
    lines.push({
      account_id: getAccountId("EMPLOYER_TAX_EXPENSE", glAccountMap),
      total_amount: formatCurrency(totals.employerTaxes),
      description: projectName ? `Employer Taxes - ${projectName}` : "Employer Taxes",
      project_id: projectIdParam, // Include project_id for employer tax expense
    });
  }

  // Employer benefit expense
  if (totals.employerBenefits > 0) {
    // Add individual benefit lines for employer expense contributions
    Object.entries(totals.benefitTotals).forEach(([category, amount]: [string, number]) => {
      // Only process ER (employer) categories
      if (category.endsWith("_ER") && amount > 0) {
        const benefitName = category.replace("_ER", "");
        const benefitExpenseAccount = getAccountId(`${benefitName}_ER_EXPENSE`, glAccountMap);

        lines.push({
          account_id: benefitExpenseAccount,
          total_amount: formatCurrency(amount),
          description: projectName
            ? `${benefitName} (ER contribution) - ${projectName}`
            : `Employer Contribution Expense: ${benefitName}`,
          project_id: projectIdParam, // Include project_id for employer benefit expense
        });
      }
    });
  }

  // Contractor Payments expense
  if (totals.contractorPayments > 0) {
    lines.push({
      account_id: getAccountId("CONTRACTOR_PAYMENTS", glAccountMap),
      total_amount: formatCurrency(totals.contractorPayments),
      description: projectName ? `Contractor Payments - ${projectName}` : "Contractor Payments",
      project_id: projectIdParam,
    });
  }

  return lines.filter((line) => Math.abs(line.total_amount) >= 0.01);
}

/**
 * Processes payroll data and generates journal entries based on project attribution
 *
 * @param payroll - The payroll data to process
 * @param glAccountMappings - Mappings between payroll categories and GL accounts
 * @param users - List of users (employees and contractors)
 * @param companyBenefits - List of company benefits
 * @param employeeBenefits - List of employee benefit enrollments
 * @param timesheets - Processed timesheets with attribution data
 * @param projectMappings - Mappings between internal project IDs and external accounting system IDs
 * @returns A properly formatted journal entry
 */
export async function processPayrollByProject(
  payroll: Payroll,
  glAccountMappings: GlAccountMapping[],
  users: User[],
  companyBenefits: CompanyBenefit[],
  employeeBenefits: EmployeeBenefit[],
  timesheets: Partial<ExtendedTimesheet>[],
  projectMappings: Array<{ hammrId: number; externalId: string }> = []
): Promise<JournalEntry> {
  const journalEntry = createBaseJournalEntry(payroll);

  // Create user maps for lookup
  const { employeeMap, contractorMap, glAccountMap } = setupDataStructures(users, glAccountMappings);

  // Setup project tracking with "No Project" as default
  const projectTotals: Record<string, ProjectTotal> = {
    "No Project": createEmptyProjectTotal(),
  };

  // Process timesheet data into usable structures
  const {
    timesheetWagesByUser,
    timesheetWagesByProject,
    userTimesheetProjectMap,
    totalTimesheetWages,
    projectMap,
    hourlyEmployees,
    projectIdMapping,
    userTimesheetCounts,
    userProjectTimesheetHours,
  } = processTimesheetData(timesheets, projectMappings);

  // Process employee payroll items and distribute across projects
  processEmployeePayrollItems(
    payroll,
    employeeMap,
    hourlyEmployees,
    timesheetWagesByUser,
    timesheetWagesByProject,
    userTimesheetProjectMap,
    totalTimesheetWages,
    timesheets,
    projectTotals,
    userTimesheetCounts,
    userProjectTimesheetHours
  );

  // Process contractor payments
  processContractorPayments(
    payroll,
    contractorMap,
    hourlyEmployees,
    timesheetWagesByUser,
    userTimesheetProjectMap,
    timesheets,
    timesheetWagesByProject,
    projectTotals,
    userTimesheetCounts,
    userProjectTimesheetHours
  );

  // Process taxes and benefits for each project
  processProjectTaxesAndBenefits(projectTotals, payroll, companyBenefits, employeeBenefits);

  // Create journal entry line items for each project
  createJournalEntryLines(projectTotals, projectMap, payroll, glAccountMap, journalEntry, projectIdMapping);

  // Sort line items alphabetically by description
  sortJournalEntryLineItemsByDescription(journalEntry);

  // Validate that debits and credits balance
  if (!validateAndBalanceJournalEntry(journalEntry)) {
    throw new Error("Debits and credits do not balance.");
  }

  return journalEntry;
}

/**
 * Sets up necessary data structures for payroll processing
 * Creates maps for users, hourly employees, and GL accounts
 */
function setupDataStructures(users: User[], glAccountMappings: GlAccountMapping[]) {
  // Create maps for lookup
  const employeeMap = new Map<string, User>();
  const contractorMap = new Map<string, User>();

  // Track user mappings
  users.forEach((user) => {
    if (user.checkEmployeeId) employeeMap.set(user.checkEmployeeId, user);
    if (user.checkContractorId) contractorMap.set(user.checkContractorId, user);
  });

  // Create GL account mapping for lookup
  const glAccountMap: Record<string, string> = {};
  glAccountMappings.forEach((mapping) => {
    glAccountMap[mapping.payrollCategory] = mapping.accountId;
  });

  return { employeeMap, contractorMap, glAccountMap };
}

/**
 * Processes timesheet data into usable structures for payroll attribution
 */
function processTimesheetData(
  timesheets: Partial<ExtendedTimesheet>[],
  projectMappings: Array<{ hammrId: number; externalId: string }> = []
) {
  // Create a map of project IDs to project names and identify hourly employees
  const projectMap = new Map<string, string>();
  const hourlyEmployees = new Set<string>();
  const userTotalWages: Record<string, number> = {};
  const projectTotalWages: Record<string, number> = {};
  const userTimesheetProjectMap: Record<string, string[]> = {};

  // Track timesheets per user (regardless of wages)
  const userTimesheetCounts: Record<string, number> = {};
  const projectTimesheetCounts: Record<string, number> = {};
  const userProjectTimesheetHours: Record<string, Record<string, number>> = {};

  // Track internal to external project ID mapping
  const projectIdMapping = new Map<string, string>();

  // Initialize projectIdMapping from provided mappings
  projectMappings.forEach((mapping) => {
    projectIdMapping.set(mapping.hammrId.toString(), mapping.externalId);
  });

  // Process each timesheet to collect wage and project data
  timesheets.forEach((timesheet) => {
    const userId = timesheet.workerId?.toString() || "unknown";
    const projectId = timesheet.projectId?.toString() || "";
    const projectName = timesheet.project?.name || "Unknown Project";
    const timesheetWages =
      timesheet.totalWages !== undefined
        ? timesheet.totalWages
        : (timesheet.regularWages || 0) + (timesheet.overtimeWages || 0);

    // Calculate hours worked (for fallback distribution)
    const clockInTimestamp =
      timesheet.clockInTimestamp || (timesheet.clockIn ? new Date(timesheet.clockIn).getTime() : 0);
    const clockOutTimestamp =
      timesheet.clockOutTimestamp || (timesheet.clockOut ? new Date(timesheet.clockOut).getTime() : 0);
    const hoursWorked =
      clockInTimestamp && clockOutTimestamp ? (clockOutTimestamp - clockInTimestamp) / (1000 * 60 * 60) : 0;

    // Initialize tracking structures - store internal IDs in project map
    projectMap.set(projectId, projectName);
    hourlyEmployees.add(userId);

    if (!userTotalWages[userId]) {
      userTotalWages[userId] = 0;
      userTimesheetProjectMap[userId] = [];
      userTimesheetCounts[userId] = 0;
      userProjectTimesheetHours[userId] = {};
    }

    userTimesheetCounts[userId]++;

    if (!userTimesheetProjectMap[userId].includes(projectId)) {
      userTimesheetProjectMap[userId].push(projectId);
    }

    if (!projectTotalWages[projectId]) {
      projectTotalWages[projectId] = 0;
      projectTimesheetCounts[projectId] = 0;
    }

    projectTimesheetCounts[projectId]++;

    // Initialize hours tracking
    if (!userProjectTimesheetHours[userId][projectId]) {
      userProjectTimesheetHours[userId][projectId] = 0;
    }
    userProjectTimesheetHours[userId][projectId] += hoursWorked;

    // Calculate wages for this timesheet
    userTotalWages[userId] += timesheetWages;
    projectTotalWages[projectId] += timesheetWages;
  });

  const totalTimesheetWages = Object.values(projectTotalWages).reduce((sum, wages) => sum + wages, 0);

  return {
    timesheetWagesByUser: userTotalWages,
    timesheetWagesByProject: projectTotalWages,
    userTimesheetProjectMap,
    totalTimesheetWages,
    projectMap,
    hourlyEmployees,
    projectIdMapping,
    userTimesheetCounts,
    projectTimesheetCounts,
    userProjectTimesheetHours,
  };
}

/**
 * Calculates wages for a specific timesheet
 */
function getTimesheetWages(timesheet: Partial<ExtendedTimesheet>): number {
  return timesheet.totalWages !== undefined
    ? timesheet.totalWages
    : (timesheet.regularWages || 0) + (timesheet.overtimeWages || 0);
}

/**
 * Gets or creates a project total record
 */
function getOrCreateProjectTotal(
  projectId: string,
  projectTotals: Record<string, ProjectTotal>,
  timesheetWagesByProject: Record<string, number>
): ProjectTotal {
  // Initialize the project tracking if it doesn't exist
  if (!timesheetWagesByProject[projectId]) {
    timesheetWagesByProject[projectId] = 0;
  }

  if (!projectTotals[projectId]) {
    projectTotals[projectId] = createEmptyProjectTotal();
  }

  return projectTotals[projectId];
}

/**
 * Calculates project wages for a specific user and project
 */
function calculateProjectWagesForUser(
  timesheets: Partial<ExtendedTimesheet>[],
  userId: string,
  projectId: string
): number {
  const userProjectTimesheets = timesheets.filter(
    (ts) => ts.workerId?.toString() === userId && ts.projectId?.toString() === projectId
  );

  const totalWages = userProjectTimesheets.reduce((sum, ts) => sum + getTimesheetWages(ts), 0);

  return totalWages;
}

/**
 * Allocates a payroll item's wages and benefits to a project
 */
function allocatePayrollItemToProject(
  projectData: ProjectTotal,
  processedItem: ProcessedPayrollItem,
  projectWageRatio: number,
  allocateBenefits: boolean
) {
  // Distribute wages proportionally to this project
  projectData.wages += processedItem.grossWages * projectWageRatio;
  projectData.reimbursements += processedItem.reimbursements * projectWageRatio;

  // Always apply taxes proportionally to wages
  projectData.employerTaxes += processedItem.employerTaxes * projectWageRatio;
  projectData.employeeTaxes += processedItem.employeeTaxes * projectWageRatio;

  // Only allocate benefits if specified (to avoid double-counting)
  if (allocateBenefits) {
    projectData.employerBenefits += processedItem.employerBenefits;
    projectData.employeeBenefits += processedItem.employeeBenefits;
  }

  projectData.postTaxDeductions += processedItem.postTaxDeductions * projectWageRatio;
  projectData.garnishments += processedItem.garnishments * projectWageRatio;
  projectData.netPay += processedItem.netPay * projectWageRatio;

  // Track manual payments separately
  if (processedItem.isManualPayment) {
    projectData.manualPayments += processedItem.netPay * projectWageRatio;
  }
}

/**
 * Calculates project distribution ratios for a user based on hours if wages are zero
 */
function calculateHourBasedProjectRatios(
  userId: string,
  userProjectTimesheetHours: Record<string, Record<string, number>>
): Record<string, number> {
  const projectHours = userProjectTimesheetHours[userId] || {};
  const totalHours = Object.values(projectHours).reduce((sum, hours) => sum + hours, 0);

  if (totalHours <= 0) {
    return {};
  }

  const ratios: Record<string, number> = {};
  Object.entries(projectHours).forEach(([projectId, hours]) => {
    ratios[projectId] = hours / totalHours;
  });

  return ratios;
}

/**
 * Processes employee payroll items and distributes them across projects
 * based on timesheet data for hourly employees
 *
 * IMPORTANT CHANGES:
 * - The function now uses the presence of timesheets (userTimesheetCounts > 0) to determine if an employee has timesheets,
 *   not just whether they have timesheet wages > 0
 * - When an employee has timesheets with zero wages, the function now falls back to using hours-based distribution
 *   ratios rather than ignoring those timesheets
 * - The function now correctly handles cases where employees have timesheet entries in the system but
 *   those entries have $0.00 wages (timesheets entered without wage information)
 */
function processEmployeePayrollItems(
  payroll: Payroll,
  employeeMap: Map<string, User>,
  hourlyEmployees: Set<string>,
  timesheetWagesByUser: Record<string, number>,
  timesheetWagesByProject: Record<string, number>,
  userTimesheetProjectMap: Record<string, string[]>,
  totalTimesheetWages: number,
  timesheets: Partial<ExtendedTimesheet>[],
  projectTotals: Record<string, ProjectTotal>,
  userTimesheetCounts?: Record<string, number>,
  userProjectTimesheetHours?: Record<string, Record<string, number>>
) {
  let hourlyEmployeesWithTimesheets = 0;
  let hourlyEmployeesWithoutTimesheets = 0;
  let nonHourlyEmployees = 0;

  // Process each payroll item
  payroll.items.forEach((item: PayrollItem) => {
    const employeeId = item.employee;
    const user = employeeMap.get(employeeId);
    if (!user) return;

    const isHourlyEmployee = hourlyEmployees.has(user.id.toString());
    const processedItem = processPayrollItem(item as ExtendedPayrollItemForCalc);
    const userId = user.id.toString();
    const noProjectData = projectTotals["No Project"];

    if (isHourlyEmployee) {
      const userHasTimesheets = userTimesheetCounts && userTimesheetCounts[userId] > 0;
      const employeeTimesheetWages = timesheetWagesByUser[userId] || 0;

      if (userHasTimesheets) {
        hourlyEmployeesWithTimesheets++;
        const employeeProjects = userTimesheetProjectMap[userId] || [];
        let benefitsAllocated = false;

        // Determine project ratios - use hours-based if wages are zero
        const useHoursBasedDistribution =
          employeeTimesheetWages <= 0 && userProjectTimesheetHours && userProjectTimesheetHours[userId];
        let projectRatios: Record<string, number> = {};

        if (useHoursBasedDistribution && userProjectTimesheetHours) {
          projectRatios = calculateHourBasedProjectRatios(userId, userProjectTimesheetHours);
        }

        employeeProjects.forEach((projectId) => {
          const projectData = getOrCreateProjectTotal(projectId, projectTotals, timesheetWagesByProject);

          // Determine project ratio either by wages or hours
          let projectWageRatio = 0;
          if (useHoursBasedDistribution) {
            projectWageRatio = projectRatios[projectId] || 0;
          } else {
            const projectWagesForUser = calculateProjectWagesForUser(timesheets, userId, projectId);
            projectWageRatio = employeeTimesheetWages > 0 ? projectWagesForUser / employeeTimesheetWages : 0;
          }

          // Calculate global project ratio
          const projectGlobalRatio =
            totalTimesheetWages > 0 ? timesheetWagesByProject[projectId] / totalTimesheetWages : 0;

          projectData.timesheetRatio = projectGlobalRatio;

          // Store payroll item for later processing
          const newItemForProject: PayrollItemWithProjectInfo = {
            ...item, // Spread original PayrollItem
            projectRatio: projectWageRatio,
            isHourlyEmployee: true,
          };
          projectData.employeePayrollItems.push(newItemForProject);

          // Allocate wages and potentially benefits
          const shouldAllocateBenefits = !benefitsAllocated && projectWageRatio > 0;
          allocatePayrollItemToProject(projectData, processedItem, projectWageRatio, shouldAllocateBenefits);

          if (shouldAllocateBenefits) {
            benefitsAllocated = true;
          }
        });
      } else {
        // If hourly employee has no timesheets, add to "No Project"
        hourlyEmployeesWithoutTimesheets++;
        const newItemNoTimesheet: PayrollItemWithProjectInfo = {
          ...item,
          projectRatio: 1,
          isHourlyEmployee: true,
        };
        noProjectData.employeePayrollItems.push(newItemNoTimesheet);
        addItemToProject(noProjectData, processedItem);
      }
    } else {
      // For non-hourly employees, add to "No Project"
      nonHourlyEmployees++;
      const newItemNonHourly: PayrollItemWithProjectInfo = {
        ...item,
        projectRatio: 1,
        isHourlyEmployee: false,
      };
      noProjectData.employeePayrollItems.push(newItemNonHourly);
      addItemToProject(noProjectData, processedItem);
    }
  });

  return { hourlyEmployeesWithTimesheets, hourlyEmployeesWithoutTimesheets, nonHourlyEmployees };
}

/**
 * Processes contractor payments and distributes them across projects
 * similar to employee payroll items
 */
function processContractorPayments(
  payroll: Payroll,
  contractorMap: Map<string, User>,
  hourlyEmployees: Set<string>,
  timesheetWagesByUser: Record<string, number>,
  userTimesheetProjectMap: Record<string, string[]>,
  timesheets: Partial<ExtendedTimesheet>[],
  timesheetWagesByProject: Record<string, number>,
  projectTotals: Record<string, ProjectTotal>,
  userTimesheetCounts?: Record<string, number>,
  userProjectTimesheetHours?: Record<string, Record<string, number>>
) {
  if (!payroll.contractor_payments || payroll.contractor_payments.length === 0) {
    return; // Return an empty object or similar if no contractor payments
  }

  let hourlyContractorsWithTimesheets = 0;
  let hourlyContractorsWithoutTimesheets = 0;
  let nonHourlyContractors = 0;

  // Process contractor payments
  payroll.contractor_payments.forEach((contractorPayment) => {
    // Cast to the type with garnishments
    const extendedContractorPayment = contractorPayment as ExtendedContractorPayment;
    const contractorId = extendedContractorPayment.contractor;
    const user = contractorMap.get(contractorId);
    if (!user) return;

    const isHourlyContractor = hourlyEmployees.has(user.id.toString());
    const grossPay = parseFloat(extendedContractorPayment.amount || "0");
    const netPay = parseFloat(extendedContractorPayment.net_pay || "0");
    const reimbursements = parseFloat(extendedContractorPayment.reimbursement_amount || "0");
    const garnishments = extendedContractorPayment.garnishments
      ? extendedContractorPayment.garnishments.reduce(
          (sum: number, garnish: Garnishment) => sum + parseFloat(garnish.amount || "0"),
          0
        )
      : 0;

    // Calculate employer taxes from contractor payment
    let employerTaxes = 0;
    if (extendedContractorPayment.taxes) {
      employerTaxes = extendedContractorPayment.taxes
        .filter((tax: Tax) => tax.payer === "company")
        .reduce((sum: number, tax: Tax) => sum + parseFloat(tax.amount || "0"), 0);
    }

    const userId = user.id.toString();
    const noProjectData = projectTotals["No Project"];

    if (isHourlyContractor) {
      const userHasTimesheets = userTimesheetCounts && userTimesheetCounts[userId] > 0;
      const contractorTimesheetWages = timesheetWagesByUser[userId] || 0;

      if (userHasTimesheets) {
        hourlyContractorsWithTimesheets++;
        const contractorProjects = userTimesheetProjectMap[userId] || [];

        // Determine project ratios - use hours-based if wages are zero
        const useHoursBasedDistribution =
          contractorTimesheetWages <= 0 && userProjectTimesheetHours && userProjectTimesheetHours[userId];
        let projectRatios: Record<string, number> = {};

        if (useHoursBasedDistribution && userProjectTimesheetHours) {
          projectRatios = calculateHourBasedProjectRatios(userId, userProjectTimesheetHours);
        }

        contractorProjects.forEach((projectId) => {
          const projectData = getOrCreateProjectTotal(projectId, projectTotals, timesheetWagesByProject);

          // Determine project ratio either by wages or hours
          let projectRatio = 0;
          if (useHoursBasedDistribution) {
            projectRatio = projectRatios[projectId] || 0;
          } else {
            const projectWagesForUser = calculateProjectWagesForUser(timesheets, userId, projectId);
            projectRatio = contractorTimesheetWages > 0 ? projectWagesForUser / contractorTimesheetWages : 0;
          }

          // Add contractor payments to project
          projectData.contractorPayments += grossPay * projectRatio;
          projectData.reimbursements += reimbursements * projectRatio;
          projectData.garnishments += formatCurrency(garnishments * projectRatio);
          projectData.netPay += netPay * projectRatio;
          projectData.contractorNetPay += netPay * projectRatio;
          projectData.employerTaxes += employerTaxes * projectRatio;
        });
      } else {
        // No timesheets for this contractor - add to "No Project"
        hourlyContractorsWithoutTimesheets++;
        addContractorToNoProject(noProjectData, grossPay, reimbursements, garnishments, netPay, employerTaxes);
      }
    } else {
      // Non-hourly contractors go to "No Project"
      nonHourlyContractors++;
      addContractorToNoProject(noProjectData, grossPay, reimbursements, garnishments, netPay, employerTaxes);
    }
  });

  return { hourlyContractorsWithTimesheets, hourlyContractorsWithoutTimesheets, nonHourlyContractors };
}

/**
 * Helper to add contractor payments to the No Project category
 */
function addContractorToNoProject(
  noProjectData: ProjectTotal,
  grossPay: number,
  reimbursements: number,
  garnishments: number,
  netPay: number,
  employerTaxes: number // Added employerTaxes parameter
) {
  noProjectData.contractorPayments += grossPay;
  noProjectData.reimbursements += reimbursements;
  noProjectData.garnishments += formatCurrency(garnishments);
  noProjectData.netPay += netPay;
  noProjectData.contractorNetPay += netPay;
  noProjectData.employerTaxes += employerTaxes; // Add employer taxes here
}

/**
 * Processes taxes and benefits for each project
 * Categorizes taxes and benefits and stores them in project totals
 */
function processProjectTaxesAndBenefits(
  projectTotals: Record<string, ProjectTotal>,
  payroll: Payroll,
  companyBenefits: CompanyBenefit[],
  employeeBenefits: EmployeeBenefit[]
) {
  // Process taxes and benefits for each project using utility functions
  Object.entries(projectTotals).forEach(([_projectName, project]: [string, ProjectTotal]) => {
    // Skip processing only if both no employee items and no employer taxes (from contractors)
    if (project.employeePayrollItems.length === 0 && project.employerTaxes === 0) return;

    const projectPayroll: Payroll = {
      ...payroll,
      items: project.employeePayrollItems.map((item: PayrollItemWithProjectInfo) => ({
        ...item,
        // Scale net pay and other direct properties
        net_pay: (parseFloat(item.net_pay || "0") * item.projectRatio).toString(),

        // Scale individual earnings amounts
        earnings: item.earnings?.map((earning) => ({
          ...earning,
          amount: (parseFloat(earning.amount || "0") * item.projectRatio).toString(),
        })),

        // Scale individual tax amounts
        taxes:
          item.taxes?.map((tax: Tax) => ({
            ...tax,
            amount: (parseFloat(tax.amount || "0") * item.projectRatio).toString(),
          })) || [],

        // Scale individual benefit amounts
        benefits:
          item.benefits?.map((benefit: ExtendedCheckBenefit) => ({
            ...benefit,
            employee_contribution_amount: (
              parseFloat(benefit.employee_contribution_amount || "0") * item.projectRatio
            ).toString(),
            company_contribution_amount: (
              parseFloat(benefit.company_contribution_amount || "0") * item.projectRatio
            ).toString(),
            // Also scale these for consistency, though not directly used by processBenefitsForPayroll
            company_contribution: (parseFloat(benefit.company_contribution || "0") * item.projectRatio).toString(),
            employee_deduction: (parseFloat(benefit.employee_deduction || "0") * item.projectRatio).toString(),
          })) || [],
      })),
      totals: {
        company_taxes: project.employerTaxes.toString(),
      },
    };

    const { categorizedBenefits, totalEmployeeContributions, totalCompanyContributions } = processBenefitsForPayroll(
      projectPayroll,
      companyBenefits,
      employeeBenefits
    );

    project.benefitTotals = categorizedBenefits;

    // Ensure we're not double counting employer contributions
    project.employerBenefits = totalCompanyContributions;
    project.employeeBenefits = totalEmployeeContributions;
  });
}

/**
 * Sorts journal entry line items:
 * - Groups expense lines by base description (e.g., "Wages and Salaries").
 * - Within each group, puts the general line first, then project-specific lines alphabetically by project.
 * - Puts all liability lines (credits) after expense lines (debits), sorted alphabetically by description.
 */
export function sortJournalEntryLineItemsByDescription(journalEntry: JournalEntry): void {
  const expenseLines: JournalEntryLineItem[] = [];
  const liabilityLines: JournalEntryLineItem[] = [];
  const DEBIT_THRESHOLD = 0.001;

  journalEntry.line_items.forEach((item) => {
    if (item.total_amount > DEBIT_THRESHOLD) {
      expenseLines.push(item);
    } else {
      liabilityLines.push(item);
    }
  });

  const parseDescription = (description: string): { base: string; project: string | null } => {
    const parts = description.split(" - ");
    const NON_PROJECT_SUFFIXES = ["(EE contribution)", "(ER contribution)", "Liability", "Payable"];

    // Check if likely contains a project suffix
    if (parts.length > 1 && parts[0].trim() !== "") {
      const potentialProject = parts[parts.length - 1];
      const base = parts.slice(0, -1).join(" - ");

      // Check if the potential project part ends with any known non-project suffix
      const isNonProjectSuffix = NON_PROJECT_SUFFIXES.some((suffix) => potentialProject.endsWith(suffix));

      if (!isNonProjectSuffix) {
        return { base, project: potentialProject };
      }
    }

    // Otherwise, treat the whole description as the base
    return { base: description, project: null };
  };

  expenseLines.sort((a, b) => {
    const parsedA = parseDescription(a.description);
    const parsedB = parseDescription(b.description);

    // Sort by base description first
    const baseCompare = parsedA.base.localeCompare(parsedB.base);
    if (baseCompare !== 0) return baseCompare;

    // Then sort by project presence (general line with null project comes first)
    if (parsedA.project === null && parsedB.project !== null) return -1;
    if (parsedA.project !== null && parsedB.project === null) return 1;

    // Finally sort by project name alphabetically if both have projects
    if (parsedA.project !== null && parsedB.project !== null) {
      return parsedA.project.localeCompare(parsedB.project);
    }

    return 0; // Should only occur if descriptions are identical
  });

  liabilityLines.sort((a, b) => a.description.localeCompare(b.description));

  journalEntry.line_items = [...expenseLines, ...liabilityLines];
}

/**
 * Adds a processed payroll item's values to a project total
 */
export function addItemToProject(projectTotal: ProjectTotal, processedItem: ProcessedPayrollItem, ratio = 1) {
  projectTotal.wages += processedItem.grossWages * ratio;
  projectTotal.reimbursements += processedItem.reimbursements * ratio;
  projectTotal.employerTaxes += processedItem.employerTaxes * ratio;
  projectTotal.employerBenefits += processedItem.employerBenefits * ratio;
  projectTotal.employeeBenefits += processedItem.employeeBenefits * ratio;
  projectTotal.postTaxDeductions += processedItem.postTaxDeductions * ratio;
  projectTotal.managedPostTaxDeductions += processedItem.managedPostTaxDeductions * ratio;
  projectTotal.garnishments += processedItem.garnishments * ratio;
  projectTotal.netPay += processedItem.netPay * ratio;
  projectTotal.employeeTaxes += processedItem.employeeTaxes * ratio;

  // Track manual payments separately
  if (processedItem.isManualPayment) {
    projectTotal.manualPayments += processedItem.netPay * ratio;
  }
}
