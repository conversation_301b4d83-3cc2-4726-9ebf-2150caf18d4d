type SortOrder = "asc" | "desc";
type PropertyPath = string[];

export interface SortCriteria {
  path: PropertyPath;
  order: SortOrder;
}

const compareValues = (a: any, b: any): number => {
  if (a === undefined && b === undefined) return 0;
  if (a === undefined) return -1; // or 1 to place undefined at the end
  if (b === undefined) return 1; // or -1 to place undefined at the end
  if (a < b) return -1;
  if (a > b) return 1;

  return 0;
};

export const dynamicSort = <T>(items: T[], sortCriteria: SortCriteria[]): T[] => {
  return items.sort((a, b) => {
    for (const criterion of sortCriteria) {
      const aValue = getNestedValue(a, criterion.path);
      const bValue = getNestedValue(b, criterion.path);

      // Custom comparison for handling undefined
      const comparison = compareValues(aValue, bValue);

      if (comparison !== 0) {
        return criterion.order === "asc" ? comparison : -comparison;
      }
    }

    // if criteria matched equally
    return 0;
  });
};

export const getNestedValue = (obj: any, path: PropertyPath) => {
  return path.reduce((acc: any, part: string) => acc && acc[part], obj);
};

export const prefixCollectionValues = (collection: string[], prefix: string): string[] => {
  return collection.map((item) => {
    const prefixedItem = `${prefix}${item}`;

    return prefixedItem;
  });
};
