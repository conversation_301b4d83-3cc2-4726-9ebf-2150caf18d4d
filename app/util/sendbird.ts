import fetch from "node-fetch";
import { sendSlackMessage } from "@/util/slack";
import { User } from "../models";
const { SENDBIRD_APP_ID, SENDBIRD_API_KEY } = process.env;
const backendSlackChannel = "C047EG8B1CN";

export const createUserInSendbird = async (user: User): Promise<void> => {
  const nickname = user.dataValues.firstName + " " + user.dataValues.lastName;
  const sendbirdUser = {
    user_id: user.dataValues.id.toString(),
    nickname: nickname,
    profile_url: "",
  };

  const sendbirdResponse = await fetch(`https://api-${SENDBIRD_APP_ID}.sendbird.com/v3/users`, {
    method: "POST",
    body: JSON.stringify(sendbirdUser),
    headers: {
      "Content-Type": "application/json",
      "Api-Token": SENDBIRD_API_KEY,
    },
  });

  if (!sendbirdResponse.ok) {
    console.log("Failed to create user in Sendbird:", await sendbirdResponse.text());
    sendSlackMessage(`Failed to create user in Sendbird ${nickname}`, backendSlackChannel);
  }
};

export const updateUserInSendbird = async (user: User): Promise<void> => {
  const nickname = user.firstName + " " + user.lastName;
  const sendbirdUser = {
    nickname: nickname,
  };

  const sendbirdResponse = await fetch(
    `https://api-${SENDBIRD_APP_ID}.sendbird.com/v3/users/${user.id.toString()}`,
    {
      method: "PUT",
      body: JSON.stringify(sendbirdUser),
      headers: {
        "Content-Type": "application/json",
        "Api-Token": SENDBIRD_API_KEY,
      },
    }
  );

  if (!sendbirdResponse.ok) {
    console.log("Failed to update user in Sendbird:", await sendbirdResponse.text());
    sendSlackMessage(`Failed to update user in Sendbird ${nickname}`, backendSlackChannel);
  }
};

export const removeDeviceTokensFromSendbird = async (users: User[]): Promise<void> => {
  for (const user of users) {
    const userId = user.dataValues.id.toString();

    const deleteResponse = await fetch(
      `https://api-${SENDBIRD_APP_ID}.sendbird.com/v3/users/${userId}/push`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "Api-Token": SENDBIRD_API_KEY,
        },
      }
    );

    if (!deleteResponse.ok) {
      console.log("Failed to delete device tokens for user:", await deleteResponse.text());
    }

    // Wait for a short time to avoid hitting the rate limit
    await new Promise((resolve) => setTimeout(resolve, 500));
  }
};
