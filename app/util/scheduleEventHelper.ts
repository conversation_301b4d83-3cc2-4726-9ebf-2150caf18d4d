import { toEpoch } from "./dateHelper";

export function serializeScheduleEvent(scheduleEvent: any): any {
  const newScheduleEvent = { ...scheduleEvent };

  newScheduleEvent.startTime = toEpoch(newScheduleEvent.startTime);
  newScheduleEvent.endTime = toEpoch(newScheduleEvent.endTime);

  if (newScheduleEvent.project.location) {
    newScheduleEvent.project.location = newScheduleEvent.project.location.coordinates;
  }

  // Process notification
  const notification = serializeScheduleEventNotification(newScheduleEvent);
  if (notification) {
    newScheduleEvent.notification = notification;
  }

  // Make sure equipment is properly serialized
  if (newScheduleEvent.equipment && Array.isArray(newScheduleEvent.equipment)) {
    // Equipment is already an array, process each item
    newScheduleEvent.equipment = newScheduleEvent.equipment.map((equip: any) => {
      // Make sure each equipment has a category property
      if (equip.category) {
        // Send category directly instead of just categoryId
        return {
          ...equip,
          categoryName: equip.category.name,
        };
      }

      return equip;
    });
  } else if (newScheduleEvent.equipment) {
    // Convert non-array equipment to array (sequelize sometimes returns a single object)
    const equip = newScheduleEvent.equipment;
    const mappedEquip = equip.category
      ? {
          ...equip,
          categoryName: equip.category.name,
        }
      : equip;
    newScheduleEvent.equipment = [mappedEquip];
  } else {
    // Ensure equipment is an empty array if not present
    newScheduleEvent.equipment = [];
  }

  delete newScheduleEvent["scheduleEventNotifications"];

  return newScheduleEvent;
}

function serializeScheduleEventNotification(scheduleEvent: any): any {
  // for every schedule, only send one of two notifications in the following priority order:
  // - a future notification
  // - a previous notification
  const now = new Date();
  let selectedNotification = null;

  for (const notification of scheduleEvent.scheduleEventNotifications) {
    if (notification.scheduledAt > now) {
      selectedNotification = notification;
      break;
    } else if (notification.sentAt < now) {
      if (selectedNotification === null || notification.sentAt > selectedNotification.sentAt) {
        selectedNotification = notification;
      }
    }
  }

  if (selectedNotification) {
    selectedNotification.notificationScheduledAt = toEpoch(selectedNotification.scheduledAt);
    selectedNotification.sentAt = toEpoch(selectedNotification.sentAt);
    delete selectedNotification["scheduledAt"];
  }

  return selectedNotification;
}
