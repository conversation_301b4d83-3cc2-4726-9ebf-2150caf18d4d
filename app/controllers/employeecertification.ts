import { EmployeeCertificationService } from "@/services/employeecertification";
import { NextFunction, Request, Response } from "express";

interface QueryParams {
  userId?: number;
  expiredOrExpiring?: boolean;
}

export class EmployeeCertificationController {
  employeeCertificationService: EmployeeCertificationService;

  constructor() {
    this.employeeCertificationService = new EmployeeCertificationService();
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.delete = this.delete.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    try {
      const { title, objectId, userId, number, completionDate, expirationDate, issuingEntity } = req.body;

      const createdBy = req.session.user.id;
      const organizationId = req.session.user.organizationId;

      const employeeCertification = await this.employeeCertificationService.create({
        title,
        objectId,
        userId,
        createdBy,
        organizationId,
        number,
        completionDate,
        expirationDate,
        issuingEntity,
      });

      return res.json({
        data: {
          employeeCertification,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request<any, any, any, QueryParams>, res: Response, next: NextFunction) {
    try {
      const userId = req.query.userId;
      const expiredOrExpiring = req.query.expiredOrExpiring;
      const organizationId = req.session.user.organizationId;

      const certifications = await this.employeeCertificationService.findAll(organizationId, userId, expiredOrExpiring);

      return res.json({
        data: certifications,
      });
    } catch (err) {
      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const employeeCertificationId = parseInt(req.params.id, 10);

    try {
      await this.employeeCertificationService.markAsArchived({
        id: employeeCertificationId,
      });

      return res.json({});
    } catch (error) {
      next(error);
    }
  }
}
