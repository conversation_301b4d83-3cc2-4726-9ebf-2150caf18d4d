import { CreateOptions } from "sequelize";
import { NextFunction, Request, Response } from "express";
import { has } from "lodash";

import Classification from "@/models/classification";
import FringeBenefitClassification from "@/models/fringebenefitclassification";
import { ExtendedFringeBenefit } from "@/models/fringebenefit";
import { FringeBenefitsService } from "@/services/fringebenefits";
import { convertDatesToTimestamps } from "@/util/dateHelper";

export class FringeBenefitController {
  fringeBenefitsService: FringeBenefitsService;

  constructor() {
    this.fringeBenefitsService = new FringeBenefitsService();

    // methods
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const fringeBenefitObj = req.body;
    const organizationId = req.session.user.organizationId;

    const fringeBenefitPayload = {
      ...fringeBenefitObj,
      organizationId,
    };

    const fringeBenefitCreateOptions: CreateOptions = {
      returning: true,
    };

    if (has(fringeBenefitPayload, "fringeBenefitClassifications")) {
      // update fringeBenefitClassifications to include orgId
      fringeBenefitPayload.fringeBenefitClassifications = fringeBenefitPayload.fringeBenefitClassifications.map(
        (classification: any) => ({
          organizationId,
          startDate: classification.startDate || Date.now().valueOf(),
          ...classification,
        })
      );

      fringeBenefitCreateOptions.include = [
        {
          model: FringeBenefitClassification,
          as: "fringeBenefitClassifications",
        },
      ];
    }

    try {
      const fringeBenefit = await this.fringeBenefitsService.create(fringeBenefitPayload, fringeBenefitCreateOptions);

      res.json({
        data: {
          fringeBenefit,
        },
      });
    } catch (error) {
      console.log(error, "this is error");

      next(error);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    try {
      // really the only thing you can update is the name
      const fringeBenefit = await this.fringeBenefitsService.update(req.body, {
        where: {
          id: req.params.id,
        },
        returning: true,
      });

      res.json({
        data: {
          fringeBenefit,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const wageTableId = req.query.wageTableId ? Number(req.query.wageTableId) : undefined;

    const fringeBenefitsQuery: any = {
      where: {
        organizationId: req.session.user.organizationId,
      },
      include: [
        {
          model: FringeBenefitClassification,
          as: "fringeBenefitClassifications",
          include: [
            {
              model: Classification,
            },
          ],
        },
      ],
      raw: true,
      nest: true,
    };

    if (wageTableId) {
      fringeBenefitsQuery.where.wageTableId = wageTableId;
    }

    try {
      const fringeBenefits: ExtendedFringeBenefit[] = await this.fringeBenefitsService.findAll(fringeBenefitsQuery);

      res.json({
        data: {
          fringeBenefits: fringeBenefits.map((fringeBenefit) => convertDatesToTimestamps(fringeBenefit)),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    try {
      const fringeBenefit: ExtendedFringeBenefit = await this.fringeBenefitsService.findOne({
        where: {
          id: req.params.id,
          organizationId: req.session.user.organizationId,
        },
        raw: true,
        nest: true,
      });

      res.json({
        data: {
          fringeBenefit: convertDatesToTimestamps(fringeBenefit),
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
