import { NextFunction, Request, Response } from "express";
import { phone } from "phone";
import dayjs from "dayjs";
import dayjsPluginUTC from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import sequelize from "@/lib/sequelize";
import { UsersService } from "@/services/users";
import { CheckService } from "@/services/check";
import { CheckEarningRateManager } from "@/services/checkearningratemanager";
import { OrganizationsService } from "@/services/organizations";
import { EarningRateService } from "@/services/earningrate";
import { ServiceOrchestrator } from "@/services/orchestrator";
import { TimesheetsService } from "@/services/timesheets";
import { sendSMS } from "@/util/sendSMS";
import { sendSlackMessage } from "@/util/slack";
import { createUserInSendbird, updateUserInSendbird } from "@/util/sendbird";
import { CompanyBenefit, Project, ScheduleEvent, TimeSheet, User, WorkersCompCode } from "@/models";
import { Op } from "sequelize";
import { getActiveEarningRate } from "@/util/userHelper";
import { isTrueValue } from "@/util/typeHelpers";
import TimeOffPolicy from "@/models/timeoffpolicy";
import TimeOffPolicyService from "@/services/timeoffpolicy";
import { EmployeeBenefitsService } from "@/services/employeebenefits";
import { CheckEmployee } from "@/types/check";
import UserLocation from "@/models/userlocation";
import { DepartmentsService } from "@/services/departments";
import { RutterService } from "@/services/integrations/rutter";
import { IntegrationSettingsService } from "@/services/integrations/settings";
import { inngest } from "../queueing/client";

const customParseFormat = require("dayjs/plugin/customParseFormat");
const { ENVIRONMENT } = process.env;

dayjs.extend(customParseFormat);
dayjs.extend(dayjsPluginUTC);
dayjs.extend(timezone);

export class UsersController {
  usersService: UsersService;
  checkService: CheckService;
  organizationsService: OrganizationsService;
  earningRateService: EarningRateService;
  orchestratorService: ServiceOrchestrator;
  checkEarningRateManager: CheckEarningRateManager;
  timesheetsService: TimesheetsService;
  employeeBenefitService: EmployeeBenefitsService;
  timeOffPolicyService: TimeOffPolicyService;
  departmentService: DepartmentsService;
  rutterService: RutterService;
  integrationSettingsService: IntegrationSettingsService;

  constructor() {
    this.usersService = new UsersService();
    this.checkService = new CheckService();
    this.organizationsService = new OrganizationsService();
    this.earningRateService = new EarningRateService();
    this.orchestratorService = new ServiceOrchestrator();
    this.checkEarningRateManager = new CheckEarningRateManager();
    this.timesheetsService = new TimesheetsService();
    this.employeeBenefitService = new EmployeeBenefitsService();
    this.timeOffPolicyService = new TimeOffPolicyService();
    this.rutterService = new RutterService();
    this.integrationSettingsService = new IntegrationSettingsService();

    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.resendInvite = this.resendInvite.bind(this);
    this.sendOnboardingLink = this.sendOnboardingLink.bind(this);
    this.addToPayroll = this.addToPayroll.bind(this);
    this.getEmployeeBenefits = this.getEmployeeBenefits.bind(this);
  }

  async getWithSchedule(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const from = parseInt(req.query.from as string, 10);
    const to = parseInt(req.query.to as string, 10);

    const fromDate = from ? dayjs(from).toISOString() : undefined;
    const toDate = to ? dayjs(to).toISOString() : undefined;

    if (!fromDate || !toDate) {
      return res.status(400).json({
        error: "Missing from and to timestamps",
      });
    }

    try {
      let users: any = await User.findAll({
        where: {
          organizationId,
          isArchived: false,
        },
        attributes: ["id", "firstName", "lastName", "email", "phone", "position", "role", "workerClassification"],
        raw: false,
        include: [
          {
            model: ScheduleEvent,
            through: { attributes: [] },
            attributes: ["id", "startTime", "endTime"],
            required: false,
            where: {
              startTime: {
                [Op.gte]: fromDate,
                [Op.lt]: toDate,
              },
              isDeleted: false,
            },
          },
        ],
      });

      users = JSON.parse(JSON.stringify(users));
      users.forEach((user: any) => {
        user.scheduleEvents.forEach((scheduleEvent: any) => {
          scheduleEvent.startTime = dayjs(scheduleEvent.startTime).valueOf();
          scheduleEvent.endTime = dayjs(scheduleEvent.endTime).valueOf();
        });
      });

      return res.json({
        data: {
          users,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async getUsersLocations(request: Request, response: Response, next: NextFunction) {
    const organizationId = request.session.user.organizationId;

    try {
      const users = await User.findAll({
        where: {
          organizationId,
          isArchived: false,
        },
        include: [
          {
            model: TimeSheet,
            required: true,
            where: {
              clockOut: null,
              status: "CLOCKED_IN",
              isDeleted: false,
            },
            include: [
              {
                model: UserLocation,
                required: true,
                order: [["createdAt", "DESC"]],
                limit: 1,
                include: [
                  {
                    model: Project,
                  },
                ],
              },
            ],
          },
        ],
      });

      // We need to manually filter for empty `userLocations` because sequelize doesn't filter it for us
      // when using `require: true` and limit 1 together
      const usersWithLocations = users.filter((user) =>
        user.timesheets.some((timesheet) => timesheet?.userLocations.length)
      );

      return response.json({
        data: {
          users: usersWithLocations,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const checkEmployeeId = req.query.checkEmployeeId;
    const checkContractorId = req.query.checkContractorId;
    const phoneNumber = req.query.phoneNumber;
    const includeIsArchived = req.query.includeIsArchived;
    const workerClassification = req.query.workerClassification;
    const simple = req.query.simple === "true";

    const queryConditions: any = {};

    if (organizationId) {
      queryConditions.organizationId = organizationId;
    }

    if (checkEmployeeId) {
      queryConditions.check_employee_id = checkEmployeeId;
    }

    if (checkContractorId) {
      queryConditions.check_contractor_id = checkContractorId;
    }

    if (phoneNumber) {
      queryConditions.phone = phoneNumber;
    }

    if (includeIsArchived == null || includeIsArchived == "false") {
      queryConditions.isArchived = false;
    }

    if (workerClassification) {
      queryConditions.workerClassification = workerClassification;
    }

    try {
      const users = await this.usersService.findAll(
        {
          where: queryConditions,
          simple: simple,
        },
        req
      );

      return res.json({
        data: {
          users,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    const userIdInt = parseInt(req.params.id, 10);
    const organizationId = req.session.user.organizationId;
    const from = parseInt(req.query.from as string, 10);
    const fromDate = from && dayjs(from).toISOString();

    if (req.session.user.role === "WORKER" && req.session.user.id !== userIdInt) {
      return res.status(403).json({
        error: "You are not allowed to access this resource",
      });
    }

    try {
      const user: any = await this.usersService.findOne(
        {
          where: {
            id: userIdInt,
            organizationId,
          },
          timesheetOptions: {
            fromDate,
          },
        },
        req
      );

      if (!user) {
        return res.status(404).json({
          error: "User not found",
        });
      }

      return res.json({
        data: {
          user,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const {
      firstName,
      lastName,
      phoneNumber,
      role,
      position,
      hourlyRate,
      salary = null,
      weeklyHours = null,
      employeeId,
      checkEmployeeId,
      checkContractorId,
      email,
      gender,
      ethnicity,
      veteranStatus,
      workersCompCodeId,
      emergencyContactFirstName,
      emergencyContactLastName,
      emergencyContactRelationship,
      emergencyContactPhone,
      managerId,
      profilePhotoObjectId,
      workerClassification = "EMPLOYEE",
      shouldAddToPayroll = false,
      sendOnboardingLink = false,
      startDate = dayjs().format("YYYY-MM-DD"), // formatted as YYYY-MM-DD - defaults to today's date
      departmentId,
    } = req.body;

    const validRoles = ["ADMIN", "FOREMAN", "WORKER"];
    let hourlyRateFloat = parseFloat(hourlyRate);

    // reject if both hourlyRate and salary are provided
    if (hourlyRate && salary) {
      return res.status(400).json({
        error: "Cannot provide both hourlyRate and salary",
      });
    }

    if (salary && !weeklyHours) {
      return res.status(400).json({
        error: "Weekly hours is required when salary is provided",
      });
    }

    if (phoneNumber) {
      if (!phone(phoneNumber, { validateMobilePrefix: false, strictDetection: false }).isValid) {
        return res.status(400).json({
          error: "The provided phone number is not valid",
        });
      }

      const existingUser = await User.findOne({
        where: { phone: phoneNumber, organizationId },
      });

      if (existingUser) {
        return res.status(409).json({
          error: "User with this phone number already exists",
        });
      }
    }

    if (
      firstName.length == 0 ||
      lastName.length == 0 ||
      validRoles.indexOf(role) === -1 ||
      (hourlyRate && isNaN(hourlyRateFloat))
    ) {
      return res.status(400).json({
        error: "Missing or invalid params",
      });
    }

    if (isNaN(hourlyRateFloat)) {
      hourlyRateFloat = null;
    }

    const newUserData: any = {
      organizationId,
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      phone: phoneNumber,
      role,
      position,
      employeeId,
      workersCompCodeId,
      isClockInReminderEnabled: true,
      isClockOutReminderEnabled: true,
      clockInReminderAt: req.organization.timeTrackingSettings.clockInReminderAt,
      clockOutReminderAt: req.organization.timeTrackingSettings.clockOutReminderAt,
      status: "INVITED",
    };

    if (checkEmployeeId) {
      newUserData.checkEmployeeId = checkEmployeeId;
    }

    if (checkContractorId) {
      newUserData.checkContractorId = checkContractorId;
    }

    if (workerClassification) {
      newUserData.workerClassification = workerClassification;
    }

    if (email) {
      newUserData.email = email;
    }

    if (gender) {
      newUserData.gender = gender;
    }

    if (ethnicity) {
      newUserData.ethnicity = ethnicity;
    }

    if (veteranStatus) {
      newUserData.veteranStatus = veteranStatus;
    }

    if (emergencyContactFirstName) {
      newUserData.emergencyContactFirstName = emergencyContactFirstName.trim();
    }

    if (emergencyContactLastName) {
      newUserData.emergencyContactLastName = emergencyContactLastName.trim();
    }

    if (emergencyContactRelationship) {
      newUserData.emergencyContactRelationship = emergencyContactRelationship;
    }

    if (emergencyContactPhone) {
      newUserData.emergencyContactPhone = emergencyContactPhone;
    }

    if (managerId) {
      newUserData.managerId = managerId;
    }

    if (profilePhotoObjectId) {
      newUserData.profilePhotoObjectId = profilePhotoObjectId;
    }

    newUserData.departmentId = departmentId;

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      // hammr handles users as both employees and/or contractors
      const organization = req.organization;
      let newUser: User = await this.usersService.create(newUserData, { transaction });
      let regEarningRate;
      let otEarningRate;
      let dotEarningRate;
      // create earning rates for the user - if hourly
      const parsedStartDateTimestamp = dayjs.tz(startDate, organization.timezone).valueOf();
      if (hourlyRateFloat !== null) {
        const regEarningRateName = position ? `${position}` : "Regular";
        regEarningRate = await this.earningRateService.create(
          {
            name: regEarningRateName,
            amount: hourlyRateFloat.toFixed(2),
            period: "HOURLY",
            active: true,
            type: "REG",
            userId: newUser.id,
            organizationId,
            startDate: parsedStartDateTimestamp,
          },
          { transaction }
        );

        const otEarningRateName = position ? `${position} - Overtime` : "Overtime";
        otEarningRate = await this.earningRateService.create(
          {
            name: otEarningRateName,
            amount: (hourlyRateFloat * (organization?.overtimeSettings.overtimeMultiplier || 1.5)).toFixed(2),
            period: "HOURLY",
            active: true,
            type: "OT",
            userId: newUser.id,
            organizationId,
            startDate: parsedStartDateTimestamp,
          },
          { transaction }
        );

        const dotEarningRateName = position ? `${position} - Double Overtime` : "Double Overtime";
        dotEarningRate = await this.earningRateService.create(
          {
            name: dotEarningRateName,
            amount: (hourlyRateFloat * 2).toFixed(2),
            period: "HOURLY",
            active: true,
            type: "DOT",
            userId: newUser.id,
            organizationId,
            startDate: parsedStartDateTimestamp,
          },
          { transaction }
        );
      }

      // create earning rates for the user - if salaried
      if (salary !== null) {
        const regEarningRateName = position ? `${position}` : "Regular";
        regEarningRate = await this.earningRateService.create(
          {
            name: regEarningRateName,
            amount: salary.toFixed(2),
            period: "ANNUALLY",
            active: true,
            type: "REG",
            userId: newUser.id,
            organizationId,
            startDate: parsedStartDateTimestamp,
            weeklyHours,
          },
          { transaction }
        );
      }

      let checkEmployee;
      let checkContractor;
      // determine if downstream call to check is to employees or contractors
      // first determine if org is a payroll customer and if the user should be created in payroll
      if (shouldAddToPayroll && req.organization.isPayrollEnabled) {
        const extendedReqBody = { ...req.body, company: req?.checkCompanyId || null };
        if (workerClassification === "EMPLOYEE") {
          const potentialCheckPayload = this.checkService.handleCreateCheckPayload(
            extendedReqBody,
            "employees",
            req.method
          );

          if (Object.keys(potentialCheckPayload).length > 0) {
            // only make the call if there is something to update
            checkEmployee = await this.checkService.post<CheckEmployee>(`/employees`, potentialCheckPayload);

            // after the call to check, we need to update the checkEmployeeId on the user
            const updateResult = await this.usersService.update(
              { checkEmployeeId: checkEmployee.id },
              { where: { id: newUser.id }, transaction, returning: true }
            );

            // with returning: true, the updateResult is an array with the first element being the number of affected rows and the second element being the array of the updated rows
            newUser = updateResult[1][0];

            if (regEarningRate.period === "ANNUALLY") {
              // handle salary
              const salaryCheckEarningPayload = this.checkService.handleCreateCheckPayload(
                {
                  name: regEarningRate.name,
                  amount: `${regEarningRate.amount}`,
                  checkEmployeeId: newUser.checkEmployeeId,
                  weeklyHours: regEarningRate.weeklyHours,
                  period: "annually",
                },
                "earning_rates",
                "POST"
              );

              const salaryCheckEarningRate = await this.orchestratorService.forwardRequest(
                "check",
                "POST",
                "/earning_rates",
                salaryCheckEarningPayload
              );

              // Update hammr with the new check earning rate Id for salary
              await this.earningRateService.update(
                { checkEarningRateId: salaryCheckEarningRate.id },
                { where: { id: regEarningRate.id }, transaction }
              );
            } else {
              // handle hourly rates (existing code)
              const regCheckEarningPayload = this.checkService.handleCreateCheckPayload(
                {
                  name: regEarningRate.name,
                  amount: `${regEarningRate.amount}`,
                  checkEmployeeId: newUser.checkEmployeeId,
                  period: "hourly",
                },
                "earning_rates",
                "POST"
              );

              const otCheckEarningPayload = this.checkService.handleCreateCheckPayload(
                {
                  name: otEarningRate.name,
                  amount: `${otEarningRate.amount}`,
                  checkEmployeeId: newUser.checkEmployeeId,
                  period: "hourly",
                },
                "earning_rates",
                "POST"
              );

              const dotCheckEarningPayload = this.checkService.handleCreateCheckPayload(
                {
                  name: dotEarningRate.name,
                  amount: `${dotEarningRate.amount}`,
                  checkEmployeeId: newUser.checkEmployeeId,
                  period: "hourly",
                },
                "earning_rates",
                "POST"
              );

              const regCheckEarningRate = await this.orchestratorService.forwardRequest(
                "check",
                "POST",
                "/earning_rates",
                regCheckEarningPayload
              );
              const otCheckEarningRate = await this.orchestratorService.forwardRequest(
                "check",
                "POST",
                "/earning_rates",
                otCheckEarningPayload
              );
              const dotCheckEarningRate = await this.orchestratorService.forwardRequest(
                "check",
                "POST",
                "/earning_rates",
                dotCheckEarningPayload
              );

              // update hammr with these new check earning rate Ids
              await this.earningRateService.update(
                { checkEarningRateId: regCheckEarningRate.id },
                { where: { id: regEarningRate.id }, transaction }
              );
              await this.earningRateService.update(
                { checkEarningRateId: otCheckEarningRate.id },
                { where: { id: otEarningRate.id }, transaction }
              );
              await this.earningRateService.update(
                { checkEarningRateId: dotCheckEarningRate.id },
                { where: { id: dotEarningRate.id }, transaction }
              );
            }
          }
        } else if (workerClassification === "CONTRACTOR") {
          const potentialCheckPayload = this.checkService.handleCreateCheckPayload(
            extendedReqBody,
            "contractors",
            req.method
          );

          if (Object.keys(potentialCheckPayload).length > 0) {
            // only make the call if there is something to update
            checkContractor = await this.checkService.post(`/contractors`, potentialCheckPayload);
            // after the call to check, we need to update the checkContractorId on the user
            const updateResult = await this.usersService.update(
              { checkContractorId: checkContractor.id },
              { where: { id: newUser.id }, transaction, returning: true }
            );

            // with returning: true, the updateResult is an array with the first element being the number of affected rows and the second element being the array of the updated rows
            newUser = updateResult[1][0];

            // contractors don't have earning rates so we don't forward this to check
          }
        }
      }

      await transaction.commit();

      const integrationEmployeesSync = await this.integrationSettingsService.getIntegrationObjectSetting(
        organizationId,
        "EMPLOYEES"
      );

      if (integrationEmployeesSync?.integration.isEnabled && integrationEmployeesSync?.isEnabled) {
        await inngest.send({
          name: "employee/created",
          data: {
            userId: newUser.id,
          },
        });
      }

      // enroll users in all the available timeOff policies
      if (workerClassification === "EMPLOYEE") {
        const availablePolicies = await TimeOffPolicy.findAll({
          where: {
            organizationId: newUser.organizationId,
            addNewEmployeesAutomatically: true,
          },
        });
        await Promise.all(
          availablePolicies.map((policy) => this.timeOffPolicyService.enrollUsers(policy.id, [newUser.id], 0))
        );
      }

      await createUserInSendbird(newUser);

      if (phoneNumber && shouldAddToPayroll && sendOnboardingLink) {
        if (checkEmployee) {
          const checkEmployeeOnboardingLink = await this.checkService.post(
            `/employees/${checkEmployee.id}/onboard`,
            {}
          );

          await this.sendOnboardingLinkSMS(req, res, phoneNumber, checkEmployeeOnboardingLink.url);
        } else if (checkContractor) {
          const checkContractorOnboardingLink = await this.checkService.post(
            `/contractors/${checkContractor.id}/onboard`,
            {}
          );

          await this.sendOnboardingLinkSMS(req, res, phoneNumber, checkContractorOnboardingLink.url);
        }
      } else if (phoneNumber && !shouldAddToPayroll) {
        await this.sendInviteSMS(req, phoneNumber);
      }

      if (ENVIRONMENT === "PRODUCTION") {
        await sendSlackMessage(`New user ${firstName + " " + lastName} was added to ${req.organization.name}`);
      }

      return res.json({
        data: {
          user: {
            ...newUser.dataValues,
          },
        },
      });
    } catch (err) {
      console.log("something went wrong ", err);
      await transaction.rollback();

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const orgData = req.organization;
    const userIdInt = parseInt(req.params.id, 10);

    const {
      firstName,
      lastName,
      phoneNumber = null,
      role,
      hourlyRate = null,
      salary = null,
      weeklyHours,
      position,
      email,
      gender,
      ethnicity,
      veteranStatus,
      dob,
      address1,
      city,
      state,
      postalCode,
      emergencyContactFirstName,
      emergencyContactLastName,
      emergencyContactRelationship,
      emergencyContactPhone,
      clockInReminderAt,
      clockOutReminderAt,
      isClockInReminderEnabled = null,
      isClockOutReminderEnabled = null,
      employeeId = null,
      checkEmployeeId,
      workersCompCodeId,
      checkContractorId,
      workerClassification,
      isArchived,
      managerId,
      profilePhotoObjectId,
      startDate,
      departmentId,
    } = req.body;

    let payEffectiveDate = req.body.payEffectiveDate || null;

    if (req.session.user.role !== "ADMIN" && req.session.user.id !== userIdInt) {
      return res.status(403).json({
        error: "You are not allowed to update this resource",
      });
    }

    let hourlyRateFloat = parseFloat(hourlyRate);
    if (hourlyRate != null && hourlyRate.length == 0) {
      hourlyRateFloat = 0;
    }

    const validRoles = ["ADMIN", "FOREMAN", "WORKER"];
    if (role && validRoles.indexOf(role) === -1) {
      return res.status(400).json({
        error: "Invalid role",
      });
    }

    if (phoneNumber) {
      if (!phone(phoneNumber, { validateMobilePrefix: false, strictDetection: false }).isValid) {
        return res.status(400).json({
          error: "The provided phone number is not valid",
        });
      }

      const existingUser = await this.usersService.findOne(
        {
          where: { phone: phoneNumber, organizationId },
          simple: true,
        },
        req
      );

      if (existingUser && existingUser.id != userIdInt) {
        return res.status(409).json({
          error: "User already exists",
        });
      }
    }

    if (hourlyRate && isNaN(hourlyRateFloat)) {
      return res.status(400).json({
        error: "Invalid hourly rate",
      });
    }

    if (isNaN(hourlyRateFloat)) {
      hourlyRateFloat = null;
    }

    if (salary && isNaN(salary)) {
      return res.status(400).json({
        result: "error",
        error: "Invalid salary",
      });
    }

    if (weeklyHours && !salary) {
      return res.status(400).json({
        result: "error",
        error: "Weekly hours cannot be set without a salary",
      });
    }

    if (weeklyHours && isNaN(weeklyHours)) {
      return res.status(400).json({
        result: "error",
        error: "Invalid weekly hours",
      });
    }

    if (payEffectiveDate && isNaN(payEffectiveDate)) {
      return res.status(400).json({
        result: "failure",
        error: "payEffectiveDate is not a valid date",
      });
    }

    if (clockInReminderAt && !dayjs(clockInReminderAt, "HH:mm:ss").isValid()) {
      return res.status(400).json({
        error: "clockInReminderAt is not a valid time",
      });
    }

    if (clockOutReminderAt && !dayjs(clockOutReminderAt, "HH:mm:ss").isValid()) {
      return res.status(400).json({
        error: "clockOutReminderAt is not a valid time",
      });
    }

    let parsedIsClockInReminderEnabled: boolean = null;
    if (isClockInReminderEnabled != null) {
      parsedIsClockInReminderEnabled = isTrueValue(isClockInReminderEnabled);
    }

    let parsedIsClockOutReminderEnabled: boolean = null;
    if (isClockOutReminderEnabled != null) {
      parsedIsClockOutReminderEnabled = isTrueValue(isClockOutReminderEnabled);
    }

    // if any part of the address is provided, all parts must be provided
    if (address1 || city || state || postalCode) {
      // if address1, make sure city, state and postalCode are also provided
      if (!address1 || !city || !state || !postalCode) {
        return res.status(400).json({
          error: "City, State and Postal Code are required if an Address is provided",
        });
      }
    }

    const patchUpdates: any = { workersCompCodeId };
    if (firstName) {
      patchUpdates.firstName = firstName.trim();
    }

    if (lastName) {
      patchUpdates.lastName = lastName.trim();
    }

    if (phoneNumber !== null) {
      patchUpdates.phone = phoneNumber;
    }

    if (role) {
      patchUpdates.role = role;
    }

    if (hourlyRateFloat != null) {
      patchUpdates.hourlyRate = hourlyRateFloat;
    }

    if (position != null) {
      patchUpdates.position = position;
    }

    if (clockInReminderAt) {
      patchUpdates.clockInReminderAt = clockInReminderAt;
    }

    if (clockOutReminderAt) {
      patchUpdates.clockOutReminderAt = clockOutReminderAt;
    }

    if (parsedIsClockInReminderEnabled !== null) {
      patchUpdates.isClockInReminderEnabled = parsedIsClockInReminderEnabled;
    }

    if (parsedIsClockOutReminderEnabled !== null) {
      patchUpdates.isClockOutReminderEnabled = parsedIsClockOutReminderEnabled;
    }

    if (employeeId !== null) {
      patchUpdates.employeeId = employeeId;
    }

    if (checkEmployeeId) {
      patchUpdates.checkEmployeeId = checkEmployeeId;
    }

    if (checkContractorId) {
      patchUpdates.checkContractorId = checkContractorId;
    }

    if (workerClassification) {
      patchUpdates.workerClassification = workerClassification;
    }

    if (email) {
      patchUpdates.email = email;
    }

    if (gender) {
      patchUpdates.gender = gender;
    }

    if (ethnicity) {
      patchUpdates.ethnicity = ethnicity;
    }

    if (veteranStatus) {
      patchUpdates.veteranStatus = veteranStatus;
    }

    if (dob) {
      patchUpdates.dob = dob;
    }

    if (managerId) {
      patchUpdates.managerId = managerId;
    }

    if (profilePhotoObjectId) {
      patchUpdates.profilePhotoObjectId = profilePhotoObjectId;
    }

    if (isArchived != null) {
      patchUpdates.isArchived = isTrueValue(isArchived);
    }

    if (address1 && city && state && postalCode) {
      patchUpdates.address1 = address1;
      patchUpdates.city = city;
      patchUpdates.state = state;
      patchUpdates.postalCode = postalCode;
    }

    if (emergencyContactFirstName) {
      patchUpdates.emergencyContactFirstName = emergencyContactFirstName.trim();
    }

    if (emergencyContactLastName) {
      patchUpdates.emergencyContactLastName = emergencyContactLastName.trim();
    }

    if (emergencyContactRelationship) {
      patchUpdates.emergencyContactRelationship = emergencyContactRelationship;
    }

    if (emergencyContactPhone) {
      patchUpdates.emergencyContactPhone = emergencyContactPhone;
    }

    if (startDate) {
      patchUpdates.startDate = startDate;
    }

    if (isArchived === "true") {
      if (ENVIRONMENT === "PRODUCTION") {
        const organization = await this.organizationsService.findOne({ where: { id: organizationId } });
        const user = await this.usersService.findOne({ where: { id: userIdInt, organizationId }, simple: true }, req);
        sendSlackMessage(`User ${user.firstName + " " + user.lastName} was removed from ${organization.name}`);
      }
    }

    if (departmentId) {
      patchUpdates.departmentId = departmentId;
    }

    // start transaction
    const transaction = await sequelize.transaction();

    let user: User | undefined;
    try {
      const existingUser = await this.usersService.findOne({
        where: { id: userIdInt, organizationId },
        simple: true,
        transaction,
      });

      //  update hammr first - any downstream updates come after
      const updatedUser = await this.usersService.update(patchUpdates, {
        where: {
          id: userIdInt,
          organizationId,
        },
        transaction,
        returning: true,
      });

      // get existing earning rates
      const userEarningRates = await this.earningRateService.findAll({
        where: {
          userId: userIdInt,
          organizationId,
        },
        transaction,
      });

      // have existing earning rates available for reference
      const existingCurrentRegEarningRate = getActiveEarningRate(userEarningRates, "REG");
      const existingCurrentOtEarningRate = getActiveEarningRate(userEarningRates, "OT");
      const existingCurrentDotEarningRate = getActiveEarningRate(userEarningRates, "DOT");

      const regEarningRateName = position ? `${position}` : `${existingCurrentRegEarningRate?.name}` || "Regular";
      const otEarningRateName = position ? `${position} - Overtime` : existingCurrentOtEarningRate?.name ?? "Overtime";
      const dotEarningRateName = position
        ? `${position} - Double Overtime`
        : existingCurrentDotEarningRate?.name ?? "Double Overtime";

      // now compare if the hourlyRateFloat is different from the existing earning rates
      const ratesNeedUpdate =
        hourlyRateFloat !== null && hourlyRateFloat !== undefined
          ? existingCurrentRegEarningRate?.period === "ANNUALLY" || // need update if switching from salary to hourly
            parseFloat(existingCurrentRegEarningRate?.amount || "0") !== hourlyRateFloat
          : false;

      // for the most part, earning rates are immutable. these are the conditions where a new salary earning rate needs to be created
      const salaryNeedsUpdate =
        salary !== null && salary !== undefined
          ? existingCurrentRegEarningRate?.period === "HOURLY" || // need update if switching from hourly to salary
            parseFloat(existingCurrentRegEarningRate?.amount || "0") !== parseFloat(salary)
          : false;

      // works closely with salaryNeedsUpdate - but keep conditions separate for clarity
      const weeklyHoursNeedsUpdate =
        weeklyHours &&
        weeklyHours !== existingCurrentRegEarningRate?.weeklyHours &&
        existingCurrentRegEarningRate?.period === "ANNUALLY";

      // we assume position passed in should not be empty/null because you shouldn't "unset" a position
      const onlyJobTitlePositionNeedsUpdate =
        position &&
        position !== existingUser.position &&
        (hourlyRateFloat === null || hourlyRateFloat === undefined) &&
        (salary === null || salary === undefined);

      const oldEarningRateIds = [
        existingCurrentRegEarningRate?.id,
        existingCurrentOtEarningRate?.id,
        existingCurrentDotEarningRate?.id,
      ].filter(Boolean);

      const oldCheckEarningRateIds = [
        existingCurrentRegEarningRate?.checkEarningRateId,
        existingCurrentOtEarningRate ? existingCurrentOtEarningRate.checkEarningRateId : null,
        existingCurrentDotEarningRate ? existingCurrentDotEarningRate.checkEarningRateId : null,
      ].filter(Boolean);

      const orgTimezone = req.organization.timezone || "America/Los_Angeles"; // Default to PT if not set
      // convert current date to org's timezone and get start of day
      // current time in org's timezone
      const startOfDayInOrgTz = dayjs.tz(new Date(), orgTimezone).startOf("day");
      // generally we assume payEffectiveDate is REQUIRED, but if it's not provided, we use the start of day in the org's timezone
      // payEffectiveDate - take what's passed in or default to start of day in org's timezone
      payEffectiveDate = payEffectiveDate || startOfDayInOrgTz.valueOf();

      if (ratesNeedUpdate) {
        await this.timesheetsService.validateEffectiveDate(
          userIdInt,
          organizationId,
          new Date(payEffectiveDate),
          req.organization.timezone || "America/Los_Angeles",
          transaction
        );

        // handle the update of the earning rates for hammr first
        // check if oldEarningRateIds is empty, if it is, skip the deactivateCurrentRates call
        if (oldEarningRateIds.length > 0) {
          const endDateForOldEarningRate = payEffectiveDate - 1;
          await this.earningRateService.deactivateCurrentRates(
            oldEarningRateIds,
            endDateForOldEarningRate,
            transaction
          );
        }

        const newRatesDetails = [
          {
            name: regEarningRateName,
            amount: hourlyRateFloat.toFixed(2),
            type: "REG",
            effectiveStartDate: payEffectiveDate,
          },
          {
            name: otEarningRateName,
            amount: (hourlyRateFloat * 1.5).toFixed(2),
            type: "OT",
            effectiveStartDate: payEffectiveDate,
          },
          {
            name: dotEarningRateName,
            amount: (hourlyRateFloat * 2).toFixed(2),
            type: "DOT",
            effectiveStartDate: payEffectiveDate,
          },
        ];

        const newRates = await this.earningRateService.createNewRates(existingUser, newRatesDetails, transaction);

        // after new rates are created, timesheets need to be pointed to the new rates
        // update timesheets that have clockIn greater than payEffectiveDate
        // set their regEarningRateId and otEarningRateId to the new check earning rate ids
        await this.timesheetsService.update(
          {
            regEarningRateId: newRates.find((rate) => rate.type === "REG")?.id,
            otEarningRateId: newRates.find((rate) => rate.type === "OT")?.id,
            dotEarningRateId: newRates.find((rate) => rate.type === "DOT")?.id,
          },
          {
            where: {
              workerId: userIdInt,
              organizationId,
              clockIn: {
                [Op.gte]: payEffectiveDate,
              },
              status: {
                [Op.ne]: "PAID",
              },
            },
            transaction,
          }
        );

        if (orgData.isPayrollEnabled && existingUser.checkEmployeeId) {
          const newRateIdsInCheck = await this.checkEarningRateManager.synchronizeEarningRates(
            existingUser.checkEmployeeId,
            newRates,
            oldCheckEarningRateIds
          );

          await this.earningRateService.updateCheckEarningRateIds(
            newRates.map((rate) => rate.id),
            newRateIdsInCheck,
            transaction
          );
        }
      }

      // much of the operations for updating salary and weeklyHours is the same
      // the key differences is the new salary details payload where we're essentially deferring to req.body parameters. if salary is passed in, we use that. if not, we use the existing earning rate amount; likewise for weeklyHours
      if (salaryNeedsUpdate || weeklyHoursNeedsUpdate) {
        // handle the update of the earning rates for hammr first
        if (oldEarningRateIds.length > 0) {
          const newEffectiveEndDate = payEffectiveDate - 1;
          await this.earningRateService.deactivateCurrentRates(oldEarningRateIds, newEffectiveEndDate, transaction);
        }

        const newSalaryDetails = {
          amount: salary ?? existingCurrentRegEarningRate?.amount,
          name: regEarningRateName,
          period: "ANNUALLY",
          type: "REG",
          effectiveStartDate: payEffectiveDate,
          weeklyHours: weeklyHours ?? existingCurrentRegEarningRate?.weeklyHours,
        };

        const newSalary = await this.earningRateService.createNewSalaryRate(
          existingUser,
          newSalaryDetails,
          transaction
        );

        if (orgData.isPayrollEnabled && existingUser.checkEmployeeId) {
          const newSalaryInCheck = await this.checkEarningRateManager.synchronizeEarningRates(
            existingUser.checkEmployeeId,
            [newSalary],
            oldCheckEarningRateIds
          );

          await this.earningRateService.updateCheckEarningRateIds([newSalary.id], newSalaryInCheck, transaction);
        }
      }

      if (onlyJobTitlePositionNeedsUpdate && existingUser.checkEmployeeId && existingCurrentRegEarningRate) {
        await this.checkService.patch(`/earning_rates/${existingCurrentRegEarningRate.checkEarningRateId}`, {
          name: position,
        });

        if (existingCurrentOtEarningRate?.checkEarningRateId) {
          await this.checkService.patch(`/earning_rates/${existingCurrentOtEarningRate.checkEarningRateId}`, {
            name: `${position} - Overtime`,
          });
        }
      }

      // update check on user (employee or contractor changes) after leaf operations are complete
      if (existingUser.checkEmployeeId) {
        const checkEmployeePayload = this.checkService.handleCreateCheckPayload(patchUpdates, "employees", "PATCH");

        // update employee
        // only call this if there is something to update
        if (Object.keys(checkEmployeePayload).length > 0) {
          await this.checkService.patch(`/employees/${existingUser.checkEmployeeId}`, checkEmployeePayload);
        }
      } else if (existingUser.checkContractorId) {
        const checkContractorPayload = this.checkService.handleCreateCheckPayload(patchUpdates, "contractors", "PATCH");

        // or update contractor
        await this.checkService.patch(`/contractors/${existingUser.checkContractorId}`, checkContractorPayload);
      }

      await transaction.commit();

      if (updatedUser?.[1]?.[0]) {
        await updateUserInSendbird(updatedUser[1][0]);
      }

      user = updatedUser?.[1]?.[0];

      // We need the load the WorkersCompCode
      //  because it's necessary for the Web to show the "Washington State Fields" form
      //  in case they picked a Washington state workplace for the employee
      await user?.reload({
        include: [
          {
            model: WorkersCompCode,
            required: false,
            as: "workersCompCode",
          },
        ],
      });

      if (user) {
        const integrationEmployeesSync = await this.integrationSettingsService.getIntegrationObjectSetting(
          user.organizationId,
          "EMPLOYEES"
        );

        // update only if there is an integration enabled and only if some specific fields are changed.
        // This is because integrations don't care about other fields we have.
        if (
          integrationEmployeesSync?.integration.isEnabled &&
          integrationEmployeesSync?.isEnabled &&
          (firstName || lastName || typeof isArchived !== undefined)
        ) {
          await inngest.send({
            name: "employee/updated",
            data: {
              userId: user.id,
            },
          });
        }
      }

      return res.json({
        data: {
          user: user ?? {},
        },
      });
    } catch (error) {
      await transaction.rollback();
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async resendInvite(req: Request, res: Response) {
    const userIdInt = parseInt(req.params.id, 10);
    const organizationId = req.session.user.organizationId;
    const user = await this.usersService.findOne({ where: { id: userIdInt, organizationId } });
    if (!user) {
      return res.status(404).json({
        error: "User not found",
      });
    }

    if (user.phone !== null && user.phone !== "") {
      await this.sendInviteSMS(req, user.phone);
    }

    return res.json({});
  }

  async sendInviteSMS(req: Request, phoneNumber: any) {
    const organizationId = req.session.user.organizationId;

    const organization = req.organization;
    const currentUser = await this.usersService.findOne({
      where: { organizationId, id: req.session.user.id },
    });

    await sendSMS(
      phoneNumber,
      `${currentUser.firstName} ${currentUser.lastName} from ${organization.name} has invited you to download Hammr.\n\niPhone\nhammr.com/ios\n\nAndroid\nhammr.com/android`
    );
  }

  async sendOnboardingLink(req: Request, res: Response) {
    const userIdInt = parseInt(req.params.id, 10);
    const organizationId = req.session.user.organizationId;
    const user = await this.usersService.findOne({ where: { id: userIdInt, organizationId } });
    if (!user) {
      return res.status(404).json({
        error: "User not found",
      });
    }

    if (user.phone !== null && user.phone !== "") {
      const onboardingLink = req.body.onboardingLink;
      await this.sendOnboardingLinkSMS(req, res, user.phone, onboardingLink);
    }

    return res.json({});
  }

  private async sendOnboardingLinkSMS(req: Request, res: Response, phoneNumber: string, onboardingLink: string) {
    await sendSMS(
      phoneNumber,
      `Welcome to ${req.organization.name}. We use Hammr to process payroll securely. Complete your onboarding in just a few minutes by clicking this link: ${onboardingLink}`
    );
  }

  async addToPayroll(req: Request, res: Response, next: NextFunction) {
    const userIdInt = parseInt(req.params.id, 10);

    // validation
    if (req.session.user.role !== "ADMIN" && req.session.user.id !== userIdInt) {
      return res.status(403).json({
        error: "You are not allowed to update this resource",
      });
    }

    let hourlyRateFloat = parseFloat(req.body?.hourlyRate);
    if (req.body.hourlyRate != null && req.body.hourlyRate.length == 0) {
      hourlyRateFloat = 0;
    }

    if (req.body?.hourlyRate && isNaN(hourlyRateFloat)) {
      return res.status(400).json({
        error: "Invalid hourly rate",
      });
    }

    const extendedReqBody = { ...req.body, company: req?.checkCompanyId || null };

    const potentialCheckPayload = this.checkService.handleCreateCheckPayload(extendedReqBody, "employees", req.method);

    // start a transaction
    const transaction = await sequelize.transaction();
    let newUser: User;

    try {
      if (Object.keys(potentialCheckPayload).length > 0) {
        // only make the call if there is something to update
        // needs to grab all the data and then just call the check service
        const checkEmployee = await this.checkService.post(`/employees`, potentialCheckPayload);

        // after the call to check, we need to update the checkEmployeeId on the user
        // then update the checkEmployeeId on the user and then return the user object data
        const updateResult = await this.usersService.update(
          { checkEmployeeId: checkEmployee.id },
          { where: { id: userIdInt }, transaction, returning: true }
        );
        // with returning: true, the updateResult is an array with the first element being the number of affected rows and the second element being the array of the updated rows
        newUser = updateResult[1][0];
      }

      // if hourly rate, create reg and ot earning rates, then update the user with these new earning rates, sync / etc
      if (hourlyRateFloat !== null) {
        const organization = req.organization;

        // create earning rates for the user
        const regEarningRateName = req.body.position
          ? `${req.body.position}`
          : req.session.user.position
          ? `${req.session.user.position}`
          : "Regular";

        const regEarningRate = await this.earningRateService.create(
          {
            name: regEarningRateName,
            amount: hourlyRateFloat.toFixed(2),
            period: "HOURLY",
            active: true,
            type: "REG",
            userId: userIdInt,
            organizationId: req.session.user.organizationId,
          },
          { transaction }
        );

        const otEarningRateName = req.body.position
          ? `${req.body.position} - Overtime`
          : req.session.user.position
          ? `${req.session.user.position} - Overtime`
          : "Overtime";

        const otEarningRate = await this.earningRateService.create(
          {
            name: otEarningRateName,
            amount: (hourlyRateFloat * (organization.overtimeSettings.overtimeMultiplier || 1.5)).toFixed(2),
            period: "HOURLY",
            active: true,
            type: "OT",
            userId: userIdInt,
            organizationId: req.session.user.organizationId,
          },
          { transaction }
        );

        const dotEarningRateName = req.body.position
          ? `${req.body.position} - Double Overtime`
          : req.session.user.position
          ? `${req.session.user.position} - Double Overtime`
          : "Double Overtime";

        const dotEarningRate = await this.earningRateService.create(
          {
            name: dotEarningRateName,
            amount: (hourlyRateFloat * 2).toFixed(2),
            period: "HOURLY",
            active: true,
            type: "DOT",
            userId: userIdInt,
            organizationId: req.session.user.organizationId,
          },
          { transaction }
        );

        // foward the earning rates to check after creating payloads
        // reg earning rate
        const regCheckEarningPayload = this.checkService.handleCreateCheckPayload(
          {
            name: regEarningRate.name,
            amount: `${regEarningRate.amount}`,
            checkEmployeeId: newUser.checkEmployeeId,
            period: "hourly",
          },
          "earning_rates",
          "POST"
        );

        // ot earning rate
        const otCheckEarningPayload = this.checkService.handleCreateCheckPayload(
          {
            name: otEarningRate.name,
            amount: `${otEarningRate.amount}`,
            checkEmployeeId: newUser.checkEmployeeId,
            period: "hourly",
          },
          "earning_rates",
          "POST"
        );

        const dotCheckEarningPayload = this.checkService.handleCreateCheckPayload(
          {
            name: dotEarningRate.name,
            amount: `${dotEarningRate.amount}`,
            checkEmployeeId: newUser.checkEmployeeId,
            period: "hourly",
          },
          "earning_rates",
          "POST"
        );

        const regCheckEarningRate = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/earning_rates",
          regCheckEarningPayload
        );

        const otCheckEarningRate = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/earning_rates",
          otCheckEarningPayload
        );

        const dotCheckEarningRate = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/earning_rates",
          dotCheckEarningPayload
        );

        // update hammr with these new check earning rate Ids
        await this.earningRateService.update(
          { checkEarningRateId: regCheckEarningRate.id },
          { where: { id: regEarningRate.id }, transaction }
        );

        await this.earningRateService.update(
          { checkEarningRateId: otCheckEarningRate.id },
          { where: { id: otEarningRate.id }, transaction }
        );

        await this.earningRateService.update(
          { checkEarningRateId: dotCheckEarningRate.id },
          { where: { id: dotEarningRate.id }, transaction }
        );
      }

      // commit after all the updates
      transaction.commit();

      return res.json({
        data: {
          user: {
            ...newUser?.dataValues,
          },
        },
      });
    } catch (err) {
      console.log("something went wrong ", err);
      await transaction.rollback();

      next(err);
    }
  }

  async getEmployeeBenefits(req: Request, res: Response, next: NextFunction) {
    try {
      const organizationId = req.session.user.organizationId;

      const employeeBenefits = await this.employeeBenefitService.findAll({
        where: { userId: req.params.id, organizationId },
        include: [
          {
            model: CompanyBenefit,
            as: "companyBenefit",
          },
          {
            model: User,
          },
        ],
      });

      return res.json({
        data: {
          employeeBenefits,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
