import { NextFunction, Request, Response } from "express";
import { ProjectPhotosService } from "@/services/projectphotos";

export class ProjectPhotosController {
  projectPhotosService: ProjectPhotosService;

  constructor() {
    this.projectPhotosService = new ProjectPhotosService();
    this.create = this.create.bind(this);
    this.delete = this.delete.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    try {
      const { objectId, collectionId } = req.body;
      const createdBy = req.session.user.id;

      if (!objectId || !collectionId) {
        return res.status(400).json({
          error: "Missing required fields",
        });
      }

      const projectPhoto = await this.projectPhotosService.create({
        objectId,
        collectionId,
        createdBy,
      });

      return res.json({
        data: {
          projectPhoto,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const { id } = req.params;

    try {
      await this.projectPhotosService.delete(parseInt(id));

      return res.json({});
    } catch (error) {
      if (error.message === "Project photo not found") {
        return res.status(404).json({
          message: "Project photo not found",
        });
      }
      next(error);
    }
  }
}
