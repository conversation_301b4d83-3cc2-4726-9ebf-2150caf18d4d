import { NextFunction, Request, Response } from "express";
import { TimesheetAlertService } from "@/services/timesheetalert";
import dayjs from "dayjs";

export class TimesheetAlertController {
  timesheetAlertService: TimesheetAlertService;

  constructor() {
    this.timesheetAlertService = new TimesheetAlertService();
    this.update = this.update.bind(this);
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const { id } = req.params;
    const { isResolved, resolutionNote, resolutionDate } = req.body;
    const userId = req.session.user.id;

    if (isResolved === undefined && !resolutionNote) {
      return res.status(400).json({
        error: "isResolved or resolutionNote is required",
      });
    }

    const resolutionDateNum = parseInt(resolutionDate, 10);

    if (typeof resolutionDateNum !== "number" || isNaN(resolutionDateNum)) {
      return res.status(400).json({
        error: "resolutionDate must be a number",
      });
    }

    const parsedResolutionDate = resolutionDateNum ? dayjs(resolutionDateNum).toISOString() : undefined;

    const patchUpdates: any = {};

    patchUpdates.resolvedBy = userId;

    if (isResolved !== undefined) {
      patchUpdates.isResolved = isResolved;
    }

    if (resolutionNote) {
      patchUpdates.resolutionNote = resolutionNote;
    }

    if (parsedResolutionDate) {
      patchUpdates.resolutionDate = parsedResolutionDate;
    }

    try {
      await this.timesheetAlertService.update(patchUpdates, {
        where: {
          id: id,
        },
      });

      return res.json({});
    } catch (err) {
      next(err);
    }
  }
}
