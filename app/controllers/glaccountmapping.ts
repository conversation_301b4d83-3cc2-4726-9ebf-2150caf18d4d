import { NextFunction, Request, Response } from "express";
import { GlAccountMappingsService } from "@/services/glaccountmapping";
import { CheckService } from "@/services/check";
import { OrganizationsService } from "@/services/organizations";
import { CompanyBenefitsService } from "@/services/companybenefits";
import { Op } from "sequelize";
import { IntegrationUserToken } from "@/models";
import { triggerIncrementalSync } from "@/util/rutterHelper";

export class GlAccountMappingsController {
  glAccountMappingsService: GlAccountMappingsService;
  checkService: CheckService;
  organizationService: OrganizationsService;
  companyBenefitService: CompanyBenefitsService;

  constructor() {
    this.get = this.get.bind(this);
    this.getDepartmentsWithCustomMappings = this.getDepartmentsWithCustomMappings.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.upsertDepartmentMappings = this.upsertDepartmentMappings.bind(this);
    this.deleteDepartmentMappings = this.deleteDepartmentMappings.bind(this);
    this.triggerIncrementalSync = this.triggerIncrementalSync.bind(this);
    this.glAccountMappingsService = new GlAccountMappingsService();
    this.organizationService = new OrganizationsService();
    this.checkService = new CheckService();
    this.companyBenefitService = new CompanyBenefitsService();
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organization = req.organization;

    // get platform from query
    const platform = req.query.platform as string;
    let departmentIdParam: string | number | null; // Renamed for clarity and to avoid conflict with service param name
    if (req.query.departmentId === "ALL_CUSTOM_MAPPINGS") {
      departmentIdParam = "ALL_CUSTOM_MAPPINGS";
    } else {
      departmentIdParam = Number(req.query.departmentId) || null;
    }
    const integrationUserTokenId = Number(req.query.integrationUserTokenId);

    try {
      const companyBenefits = await this.companyBenefitService.findAll({
        where: {
          organizationId: organization.id,
          benefitStartDate: {
            [Op.lte]: new Date(),
          },
        },
      });

      const glAccountMappings = await this.glAccountMappingsService.findAll(
        organization.id,
        platform,
        integrationUserTokenId,
        companyBenefits,
        departmentIdParam // Pass the processed parameter here
      );

      if (!glAccountMappings) {
        // rutter connection not ready
        return res.status(202).json({
          data: null,
        });
      }

      return res.json({
        data: glAccountMappings,
      });
    } catch (err) {
      if (err.message === "expired") {
        return res.status(451).json({
          error: "Rutter connection expired. Please disable connection and re-authenticate.",
        });
      }

      next(err);
    }
  }

  async getDepartmentsWithCustomMappings(req: Request, res: Response, next: NextFunction) {
    const organization = req.organization;

    // get platform from query
    const platform = req.query.platform as string;
    const integrationUserTokenId = Number(req.query.integrationUserTokenId);

    try {
      const departments = await this.glAccountMappingsService.getDepartmentsWithCustomMappings(
        organization.id,
        platform,
        integrationUserTokenId
      );

      return res.json({ data: departments });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const userId = req.session.user.id;
    const organizationId = req.session.user.organizationId;

    try {
      await this.glAccountMappingsService.create(req.body, userId, organizationId);

      return res.status(201).json({
        data: {
          message: "Gl account mapping created successfully",
        },
      });
    } catch (err) {
      console.log(err);

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const glAccountMappingId = Number(req.params.id);

    try {
      const updatedGlAccountMapping = await this.glAccountMappingsService.update(req.body, glAccountMappingId);

      if (!updatedGlAccountMapping) {
        return res.status(404).json({
          error: "Gl account mapping not found",
        });
      }

      return res.status(200).json({
        data: {
          message: "Gl account mapping created successfully",
        },
      });
    } catch (err) {
      console.log(err);

      next(err);
    }
  }

  async upsertDepartmentMappings(req: Request, res: Response, next: NextFunction) {
    const userId = req.session.user.id;
    const organizationId = req.session.user.organizationId;
    const departmentId = Number(req.params.departmentId);
    const { mappings, platformId, integrationUserTokenId } = req.body;

    try {
      await this.glAccountMappingsService.upsertDepartmentMappings(
        mappings,
        departmentId,
        platformId,
        integrationUserTokenId,
        userId,
        organizationId
      );

      return res.status(200).json({
        data: {
          message: "Department GL account mappings updated successfully",
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async deleteDepartmentMappings(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const departmentId = Number(req.params.departmentId);

    try {
      await this.glAccountMappingsService.deleteDepartmentMappings(departmentId, organizationId);

      return res.status(200).json({
        data: {
          message: "Department GL account mappings deleted successfully",
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async triggerIncrementalSync(req: Request, res: Response, next: NextFunction) {
    const organization = req.organization;
    const integrationUserTokenId = Number(req.body.integrationUserTokenId);

    if (!integrationUserTokenId || isNaN(integrationUserTokenId)) {
      return res.status(400).json({ error: "Missing or invalid integrationUserTokenId in request body" });
    }

    try {
      const foundIntegrationUserToken = await IntegrationUserToken.findOne({
        where: {
          organizationId: organization.id,
          id: integrationUserTokenId,
        },
      });

      if (!foundIntegrationUserToken) {
        return res.status(404).json({ error: "Integration user token not found" });
      }

      await triggerIncrementalSync(foundIntegrationUserToken.accessToken);

      return res.status(200).json({
        data: {
          message: "Incremental sync triggered successfully",
        },
      });
    } catch (err) {
      if (err.message === "expired") {
        return res.status(451).json({
          error: "Rutter connection expired. Please disable connection and re-authenticate.",
        });
      }
      next(err);
    }
  }
}
