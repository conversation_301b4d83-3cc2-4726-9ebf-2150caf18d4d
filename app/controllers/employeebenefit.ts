import { NextFunction, Request, Response } from "express";
import dayjs from "dayjs";

import { EmployeeBenefit } from "@/models";
import { EmployeeBenefitsService } from "@/services/employeebenefits";
import { CompanyBenefitsService } from "@/services/companybenefits";
import { UsersService } from "@/services/users";
import { ServiceOrchestrator } from "@/services/orchestrator";
import { CheckService } from "@/services/check";
import { convertDatesToTimestamps } from "@/util/dateHelper";
import sequelize from "@/lib/sequelize";

export class EmployeeBenefitController {
  orchestratorService: ServiceOrchestrator;
  employeeBenefitService: EmployeeBenefitsService;
  userService: UsersService;
  companyBenefitService: CompanyBenefitsService;
  checkService: CheckService;

  constructor() {
    this.employeeBenefitService = new EmployeeBenefitsService();
    this.userService = new UsersService();
    this.companyBenefitService = new CompanyBenefitsService();
    this.checkService = new CheckService();
    this.orchestratorService = new ServiceOrchestrator();

    // methods
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
    this.deactivate = this.deactivate.bind(this);
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    const id = parseInt(req?.params?.id, 10);

    try {
      const employeeBenefit = await this.employeeBenefitService.findOne({
        where: {
          id,
          organizationId: req.session.user.organizationId,
        },
      });

      // convert timestamps manually here
      let timestampEmployeeBenefit = null;
      if (employeeBenefit) {
        timestampEmployeeBenefit = {
          ...employeeBenefit.dataValues,
          benefitStartDate: employeeBenefit?.benefitStartDate || null,
          benefitEndDate: employeeBenefit?.benefitEndDate || null,
        };
      }

      return res.json({
        data: {
          employeeBenefit: timestampEmployeeBenefit,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const userId = parseInt(req.query.userId as string, 10);
    const companyBenefitId = parseInt(req.query.companyBenefitId as string, 10);

    // this needs to be scoped to user's organizattion
    const where: any = {
      organizationId: req.session.user.organizationId,
    };

    if (userId !== undefined && !isNaN(userId)) {
      where.userId = userId;
    }

    if (companyBenefitId !== undefined && !isNaN(companyBenefitId)) {
      where.companyBenefitId = companyBenefitId;
    }

    try {
      const employeeBenefits = await this.employeeBenefitService.findAll({
        where,
      });

      let timestampEmployeeBenefits: Partial<EmployeeBenefit>[] = [];
      if (employeeBenefits) {
        timestampEmployeeBenefits = employeeBenefits.map((employeeBenefit) => {
          // convert timestamps manually here
          return {
            ...employeeBenefit.dataValues,
            benefitStartDate: employeeBenefit?.benefitStartDate || null,
            benefitEndDate: employeeBenefit?.benefitEndDate || null,
          };
        });
      }

      return res.json({
        data: {
          employeeBenefits: timestampEmployeeBenefits,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const { userId } = req.body;

    const targetedUserId = parseInt(userId, 10);

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      const employeeBenefit = await this.employeeBenefitService.create(
        {
          ...req.body,
          userId: targetedUserId,
          organizationId: req.session.user.organizationId,
        },
        { transaction }
      );

      const targetedBenefitData = await this.companyBenefitService.findOne({
        where: {
          id: req.body.companyBenefitId,
        },
        transaction,
      });

      let checkBenefit;
      if (req.organization.isPayrollEnabled && req.organization.checkCompanyId && req.targetedUser.checkEmployeeId) {
        // create check for company benefit - after transforming payload
        const checkEmployeeBenefitPayload = this.checkService.handleCreateCheckPayload(
          {
            ...req.body,
            checkEmployeeId: req?.targetedUser?.checkEmployeeId,
            checkCompanyBenefitId: targetedBenefitData?.checkCompanyBenefitId,
          },
          "benefits",
          "POST"
        );

        checkBenefit = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/benefits",
          checkEmployeeBenefitPayload
        );

        // update company benefit with checkId
        await this.employeeBenefitService.update(
          { checkBenefitId: checkBenefit.id },
          { where: { id: employeeBenefit.id }, transaction }
        );
      }

      await transaction.commit();
      const timestampEmployeeBenefitResults = convertDatesToTimestamps(employeeBenefit);

      return res.json({
        data: {
          employeeBenefit: timestampEmployeeBenefitResults.dataValues,
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }

  // this endpoint strictly does versioning
  async update(req: Request, res: Response, next: NextFunction) {
    const employeeBenefitId = parseInt(req.params.id, 10);
    const employeeBenefitObj = req.body;
    const { effectiveDate, changeReason } = req.body; // effectiveDate should be a string YYYY-MM-DD
    // start transaction
    const transaction = await sequelize.transaction();

    const dayjsEffectiveDate = dayjs(effectiveDate);
    let endDateForCurrentBenefit = dayjsEffectiveDate.subtract(1, "day").format("YYYY-MM-DD");

    try {
      const existingEmployeeBenefit = await this.employeeBenefitService.findOne({
        where: {
          id: employeeBenefitId,
        },
        transaction,
      });

      // if endDateForCurrentBenefit is going to be before start date of current benefit
      // set endDateForCurrentBenefit to today's date
      if (endDateForCurrentBenefit < existingEmployeeBenefit.benefitStartDate) {
        // if start date of current benefit is in the future, set end date to start date
        // to avoid it being before start date
        if (dayjs(existingEmployeeBenefit.benefitStartDate).isAfter(dayjs())) {
          endDateForCurrentBenefit = dayjs(existingEmployeeBenefit.benefitStartDate).format("YYYY-MM-DD");
        } else {
          endDateForCurrentBenefit = dayjs().format("YYYY-MM-DD");
        }
      }

      const updatedExistingEmployeeBenefitResults = await this.employeeBenefitService.update(
        { benefitEndDate: endDateForCurrentBenefit, changeReason },
        {
          where: {
            id: employeeBenefitId,
          },
          returning: true,
          transaction,
        }
      );

      const updatedExistingEmployeeBenefit = (updatedExistingEmployeeBenefitResults as any)[1]?.[0]?.dataValues || {};
      const persistedEmployeeBenefitsData = {
        name: updatedExistingEmployeeBenefit.name,
        metadata: updatedExistingEmployeeBenefit.metadata,
        userId: updatedExistingEmployeeBenefit.userId,
        companyBenefitId: updatedExistingEmployeeBenefit.companyBenefitId,
      };

      // create new employee benefit with the new effective date
      const newEmployeeBenefit = await this.employeeBenefitService.create(
        {
          ...persistedEmployeeBenefitsData,
          ...employeeBenefitObj,
          benefitStartDate: effectiveDate,
          changeReason: null,
          organizationId: req.session.user.organizationId,
        },
        { transaction }
      );

      if (req.organization.isPayrollEnabled && req.organization.checkCompanyId) {
        const targetedBenefitData = await this.companyBenefitService.findOne({
          where: {
            id: updatedExistingEmployeeBenefit.companyBenefitId,
          },
          transaction,
        });

        // update old employee benefit to be inactive / set end date
        const updateCurrentCheckEmployeeBenefitPayload = this.checkService.handleCreateCheckPayload(
          {
            benefitEndDate: endDateForCurrentBenefit,
            benefitStartDate: updatedExistingEmployeeBenefit.benefitStartDate,
          },
          "benefits",
          "PATCH"
        );

        await this.orchestratorService.forwardRequest(
          "check",
          "PATCH",
          `/benefits/${updatedExistingEmployeeBenefit.checkBenefitId}`,
          updateCurrentCheckEmployeeBenefitPayload
        );

        // create a new employee benefit in check
        const checkEmployeeBenefitPayload = this.checkService.handleCreateCheckPayload(
          {
            name: persistedEmployeeBenefitsData.name, // this will get overwritten by the new employee benefit if it is different
            ...employeeBenefitObj,
            benefitStartDate: effectiveDate,
            checkEmployeeId: req.targetedUser.checkEmployeeId, // this is required
            checkCompanyBenefitId: targetedBenefitData.checkCompanyBenefitId,
          },
          "benefits",
          "POST"
        );

        const newCheckEmployeeBenefit = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/benefits",
          checkEmployeeBenefitPayload
        );

        // update company benefit with checkId
        await this.employeeBenefitService.update(
          { checkBenefitId: newCheckEmployeeBenefit.id },
          { where: { id: newEmployeeBenefit.id }, transaction }
        );
      }

      await transaction.commit();

      return res.json({
        data: {
          employeeBenefit: newEmployeeBenefit,
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }

  async deactivate(req: Request, res: Response, next: NextFunction) {
    const benefitId = parseInt(req.params.id, 10);
    const { effectiveDate = new Date()?.toISOString()?.split("T")[0], changeReason } = req.body;

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      const updatedBenefit = await this.employeeBenefitService.update(
        {
          benefitEndDate: effectiveDate,
          changeReason,
        },
        {
          where: {
            id: benefitId,
          },
          transaction,
          returning: true,
        }
      );

      const returningUpdatedExistingEmployeeBenefit = (updatedBenefit as any)[1]?.[0]?.dataValues || {};

      // if payroll is enabled, update check
      if (
        req.organization.isPayrollEnabled &&
        req.organization.checkCompanyId &&
        returningUpdatedExistingEmployeeBenefit.checkBenefitId
      ) {
        // forward request to check
        const checkEmployeeBenefitPayload = this.checkService.handleCreateCheckPayload(
          {
            benefitEndDate: effectiveDate,
          },
          "benefits",
          "PATCH"
        );

        await this.orchestratorService.forwardRequest(
          "check",
          "PATCH",
          `/benefits/${returningUpdatedExistingEmployeeBenefit.checkBenefitId}`,
          checkEmployeeBenefitPayload
        );
      }

      await transaction.commit();

      return res.json({
        data: {
          message: "Successfully deactivated employee benefit",
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }
}
