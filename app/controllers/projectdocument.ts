import { NextFunction, Request, Response } from "express";
import { ProjectDocumentService } from "@/services/projectdocument";
import { Project, User } from "../models";
import { Op, Sequelize } from "sequelize";

export class ProjectDocumentController {
  projectDocumentService: ProjectDocumentService;

  constructor() {
    this.projectDocumentService = new ProjectDocumentService();
    this.get = this.get.bind(this);
    // the getSSR method is used in order to use ag-grid client/server pagination and filtering
    this.getSSR = this.getSSR.bind(this);
    this.create = this.create.bind(this);
    this.delete = this.delete.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    try {
      const { name, objectId, projectId } = req.body;
      const createdBy = req.session.user.id;
      const organizationId = req.session.user.organizationId;

      if (!objectId || !projectId) {
        return res.status(400).json({
          error: "Missing required fields",
        });
      }

      const projectDocument = await this.projectDocumentService.create({
        name,
        objectId,
        projectId,
        createdBy,
        organizationId,
      });

      return res.json({
        data: {
          projectDocument,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const projectId = parseInt(req.query.projectId as string, 10);
    const offset = parseInt(req.query.offset as string, 10) || 0;
    const limit = parseInt(req.query.limit as string, 10) || 40;

    if (offset < 0 || limit < 0) {
      return res.status(400).json({
        error: "Invalid offset or limit",
      });
    }

    try {
      const where: any = {
        organizationId,
        isDeleted: false,
      };

      if (projectId !== undefined && !isNaN(projectId)) {
        where.projectId = projectId;
      }

      const projectDocuments = await this.projectDocumentService.findAll({
        where,
        offset,
        limit,
        order: [["name", "ASC"]],
      });

      const totalCount = await this.projectDocumentService.count({
        where: where,
      });

      return res.json({
        data: {
          totalCount,
          projectDocuments,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async getSSR(req: Request, res: Response, next: NextFunction) {
    const calculatedLimit = parseInt(req.body.endRow as string) - parseInt(req.body.startRow as string);
    const organizationId = req.session.user.organizationId;
    const projectId = parseInt(req.body.projectId as string, 10);
    const offset = parseInt(req.body.startRow as string, 10) || 0;
    const limit = calculatedLimit || 40;

    if (offset < 0 || limit < 0) {
      return res.status(400).json({
        error: "Invalid offset or limit",
      });
    }

    let sortArray = [["name", "ASC"]];

    if (req.body.sortModel.length > 0) {
      const allSortOptions = req.body.sortModel.map((agGridSort: { colId: string; sort: string }) => {
        const key = agGridSort.colId;
        const direction = agGridSort.sort.toUpperCase();

        if (key === "project.name") {
          return [{ model: Project }, "name", direction];
        }

        if (key === "userName") {
          return [{ model: User }, "firstName", direction];
        }

        return [key, direction];
      });

      sortArray = [allSortOptions[0]];
    }

    try {
      const where: any = {
        organizationId,
        isDeleted: false,
      };

      if (req.body.filterModel) {
        if (req.body.filterModel.projects) {
          where["$project.id$"] = {
            [Op.in]: req.body.filterModel.projects,
          };
        }

        if (typeof req.body.filterModel.projectArchived === "boolean") {
          where["$project.is_archived$"] = req.body.filterModel.projectArchived;
        }

        if (req.body.filterModel.users) {
          where["$user.id$"] = {
            [Op.in]: req.body.filterModel.users,
          };
        }

        if (req.body.filterModel.searchTerm) {
          where[Op.or] = [
            { "$project.name$": { [Op.iLike]: `%${req.body.filterModel.searchTerm}%` } },
            { name: { [Op.iLike]: `%${req.body.filterModel.searchTerm}%` } },
            { "$user.first_name$": { [Op.iLike]: `%${req.body.filterModel.searchTerm}%` } },
            { "$user.last_name$": { [Op.iLike]: `%${req.body.filterModel.searchTerm}%` } },
            Sequelize.where(
              Sequelize.fn("concat", Sequelize.col("user.first_name"), " ", Sequelize.col("user.last_name")),
              {
                [Op.iLike]: `%${req.body.filterModel.searchTerm}%`,
              }
            ),
          ];
        }
      }

      if (projectId !== undefined && !isNaN(projectId)) {
        where.projectId = projectId;
      }

      const projectDocuments = await this.projectDocumentService.findAll({
        where,
        offset,
        limit,
        order: sortArray,
      });

      const totalCount = await this.projectDocumentService.count({
        where: where,
      });

      return res.json({
        data: {
          rows: projectDocuments,
          totalCount,
        },
      });
    } catch (err) {
      console.log("something went wrong ", err);

      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const projectDocumentId = parseInt(req.params.id, 10);

    if (!projectDocumentId) {
      return res.status(400).json({
        error: "Missing required fields",
      });
    }

    try {
      await this.projectDocumentService.markAsDeleted({
        id: projectDocumentId,
      });

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }
}
