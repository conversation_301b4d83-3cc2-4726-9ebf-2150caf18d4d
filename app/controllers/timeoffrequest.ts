import { NextFunction, Request, Response } from "express";
import TimeOffRequestService from "../services/timeoffrequest";
import { TimeOffRequestStatus } from "@/models/timeoffrequest";
import HTTPError from "../errors/HTTPError";

interface QueryParams {
  userId?: number | number[];
  status?: TimeOffRequestStatus | TimeOffRequestStatus[];
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

export default class TimeOffRequestController {
  timeOffRequestService = new TimeOffRequestService();

  constructor() {
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.list = this.list.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.approve = this.approve.bind(this);
    this.decline = this.decline.bind(this);
    this.getUserRequests = this.getUserRequests.bind(this);
    this.getMyRequests = this.getMyRequests.bind(this);
  }

  async create(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffRequestService.create(request.session.user, request.body);

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffRequestService.update(Number(request.params.id), request.body);

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async get(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffRequestService.get(Number(request.params.id));

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async list(request: Request<any, any, any, QueryParams>, response: Response, next: NextFunction) {
    try {
      const userId = request.query.userId
        ? Array.isArray(request.query.userId)
          ? request.query.userId
          : [request.query.userId]
        : undefined;
      const status = request.query.status
        ? Array.isArray(request.query.status)
          ? request.query.status
          : [request.query.status]
        : undefined;

      const result = await this.timeOffRequestService.list(request.organization.id, {
        userId,
        status,
        startDate: request.query.startDate,
        endDate: request.query.endDate,
        page: request.query.page,
        limit: request.query.limit,
      });

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async getUserRequests(request: Request<any, any, any, QueryParams>, response: Response, next: NextFunction) {
    try {
      const userId = [Number(request.params.userId)];
      const status = request.query.status
        ? Array.isArray(request.query.status)
          ? request.query.status
          : [request.query.status]
        : undefined;

      const results = await this.timeOffRequestService.list(request.organization.id, {
        userId,
        status,
        page: request.query.page,
        limit: request.query.limit,
      });

      return response.json({
        data: results,
      });
    } catch (error) {
      next(error);
    }
  }

  async getMyRequests(request: Request<any, any, any, QueryParams>, response: Response, next: NextFunction) {
    try {
      const userId = [request.session.user.id];
      const status = request.query.status
        ? Array.isArray(request.query.status)
          ? request.query.status
          : [request.query.status]
        : undefined;

      const results = await this.timeOffRequestService.list(request.organization.id, {
        userId,
        status,
        page: request.query.page,
        limit: request.query.limit,
      });

      return response.json({
        data: results,
      });
    } catch (error) {
      next(error);
    }
  }

  async approve(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffRequestService.approve(Number(request.params.id), request.session.user.id);

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async decline(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffRequestService.decline(
        Number(request.params.id),
        request.session.user.id,
        request.body.declineNotes
      );

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async delete(request: Request, response: Response, next: NextFunction) {
    try {
      const timeOffRequest = await this.timeOffRequestService.get(Number(request.params.id));

      if (timeOffRequest.status === TimeOffRequestStatus.PAID) {
        throw new HTTPError(400, "Cannot delete a paid Time-Off Request");
      }

      const result = await this.timeOffRequestService.delete(Number(request.params.id));

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
}
