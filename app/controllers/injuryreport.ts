import { Request, Response, NextFunction } from "express";
import { InjuryReportService } from "@/services/injuryreport";
import sequelize from "@/lib/sequelize";

export class InjuryReportController {
  injuryReportService: InjuryReportService;

  constructor() {
    this.injuryReportService = new InjuryReportService();
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.list = this.list.bind(this);
  }

  async list(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { page, limit, search, isResolved, startDate, endDate, userId } = req.query;

    try {
      const result = await this.injuryReportService.list({
        organizationId,
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search as string | undefined,
        isResolved: isResolved ? isResolved === "true" : undefined,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        userId: userId ? parseInt(userId as string, 10) : undefined,
      });

      return res.json({
        data: result.data,
        pagination: result.pagination,
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const timesheetId = parseInt(req.query.timesheetId as string, 10);

    try {
      const injuryReports = await this.injuryReportService.findByTimesheet(timesheetId);

      return res.json({
        data: {
          injuryReports,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { note, timesheetId, userId, injuryPhotos } = req.body;

    const transaction = await sequelize.transaction();

    try {
      const injuryReport = await this.injuryReportService.create(
        {
          organizationId,
          note,
          timesheetId,
          userId,
          createdBy: req.session.user.id,
        },
        transaction
      );

      // Add photos
      for (const objectId of injuryPhotos) {
        await this.injuryReportService.addPhoto(
          {
            objectId,
            injuryReportId: injuryReport.id,
            createdBy: req.session.user.id,
          },
          transaction
        );
      }

      await transaction.commit();

      const allTimesheetReports = await this.injuryReportService.findByTimesheet(timesheetId);
      const newReport = allTimesheetReports.find((report) => report.id === injuryReport.id);

      return res.json({
        data: {
          injuryReport: newReport,
        },
      });
    } catch (err) {
      await transaction.rollback();
      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const { id } = req.params;
    const { resolvedBy, resolutionNote, resolutionDate } = req.body;
    const transaction = await sequelize.transaction();

    try {
      const injuryReport = await this.injuryReportService.update(
        {
          id: parseInt(id, 10),
          resolvedBy,
          resolutionNote,
          resolutionDate,
          isResolved: true,
        },
        transaction
      );

      await transaction.commit();

      return res.json({
        data: {
          injuryReport,
        },
      });
    } catch (err) {
      await transaction.rollback();
      next(err);
    }
  }
}
