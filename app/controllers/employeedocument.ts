import { NextFunction, Request, Response } from "express";
import { EmployeeDocumentService } from "@/services/employeedocument";

export class EmployeeDocumentController {
  employeeDocumentService: EmployeeDocumentService;

  constructor() {
    this.employeeDocumentService = new EmployeeDocumentService();
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.delete = this.delete.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    try {
      const { name, objectId, userId } = req.body;
      const createdBy = req.session.user.id;
      const organizationId = req.session.user.organizationId;

      const employeeDocument = await this.employeeDocumentService.create({
        name,
        objectId,
        userId,
        createdBy,
        organizationId,
      });

      return res.json({
        data: {
          employeeDocument,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.query.userId as string, 10);
      const organizationId = req.session.user.organizationId;

      const documents = await this.employeeDocumentService.findAll({
        organizationId,
        userId,
        isArchived: false,
      });

      return res.json({
        data: documents,
      });
    } catch (err) {
      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const employeeDocumentId = parseInt(req.params.id, 10);

    try {
      await this.employeeDocumentService.markAsArchived({
        id: employeeDocumentId,
      });

      return res.json({});
    } catch (error) {
      next(error);
    }
  }
}
