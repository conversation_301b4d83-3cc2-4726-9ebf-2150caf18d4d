import { NextFunction, Request, Response } from "express";
import { createOrUpdateTimesheetResponse, TimesheetsService } from "@/services/timesheets";
import { TimesheetHistoryService } from "@/services/timesheethistory";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import sequelize from "@/lib/sequelize";
import { Op } from "sequelize";
import Timesheet from "@/models/timesheet";

dayjs.extend(utc);

interface QueryParams {
  from?: number;
  to?: number;
  userId?: string;
  isResolved?: boolean;
  status?: string;
  projectId?: string;
  costCodeId?: string;
  offsetTimestamp?: number;
  offsetId?: number;
  pageSize?: number;
  equipmentId?: string;
}

export class TimesheetsController {
  timesheetsService: TimesheetsService;
  timesheetHistoryService: TimesheetHistoryService;

  constructor() {
    this.timesheetsService = new TimesheetsService();
    this.timesheetHistoryService = new TimesheetHistoryService();

    this.approveTimesheets = this.approveTimesheets.bind(this);
    this.unApproveTimesheets = this.unApproveTimesheets.bind(this);
    this.get = this.get.bind(this);
    this.createBulk = this.createBulk.bind(this);
    this.create = this.create.bind(this);
    this.bulkDelete = this.bulkDelete.bind(this);
  }

  async get(request: Request<never, never, never, QueryParams>, response: Response, next: NextFunction) {
    const {
      from,
      to,
      userId,
      isResolved,
      status,
      projectId,
      costCodeId,
      offsetTimestamp,
      offsetId,
      pageSize,
      equipmentId,
    } = request.query;
    const organizationId = request.organization.id;
    if (!organizationId) {
      return response.status(400).json({
        error: "Missing organizationId in session",
      });
    }

    try {
      const result = await this.timesheetsService.list(
        request.organization,
        {
          from,
          to,
          userId,
          isResolved,
          status,
          projectId,
          costCodeId,
          offsetTimestamp,
          offsetId,
          pageSize,
          equipmentId,
        },
        request.session.user.role !== "ADMIN" && Number(userId) !== request.session.user.id
      );

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async approveTimesheets(req: Request, res: Response, next: NextFunction) {
    const timesheetIds: number[] = req.body.timesheetIds;
    const organizationId = req.session.user.organizationId;

    try {
      // fetch all the existing timesheet data
      const existingTimesheets = await this.timesheetsService.findAll({
        where: {
          id: timesheetIds,
          organizationId: organizationId,
          status: "SUBMITTED", // the only status eligible for approval
        },
      });

      const updatedTimesheets = await this.updateStatus(
        organizationId,
        req.session.user.id,
        existingTimesheets,
        "APPROVED"
      );

      return res.json({ data: { updatedTimesheets } });
    } catch (error) {
      next(error);
    }
  }

  async unApproveTimesheets(req: Request, res: Response, next: NextFunction) {
    const timesheetIds: number[] = req.body.timesheetIds;
    const organizationId = req.session.user.organizationId;

    try {
      const existingTimesheets = await this.timesheetsService.findAll({
        where: {
          id: timesheetIds,
          organizationId: organizationId,
          status: {
            [Op.not]: "PAID", // the only status eligible for approval
          },
        },
      });

      const updatedTimesheets = await this.updateStatus(
        organizationId,
        req.session.user.id,
        existingTimesheets,
        "SUBMITTED"
      );

      return res.json({ data: { updatedTimesheets } });
    } catch (error) {
      next(error);
    }
  }

  async updateStatus(organizationId: number, userId: number, timesheets: Timesheet[], status: Timesheet["status"]) {
    const updatedTimesheets: any[] = await this.timesheetsService.bulkUpdate(
      { status: status, approvedAt: status === "APPROVED" ? dayjs.utc().toDate() : null },
      {
        where: {
          id: timesheets.map((item) => item.id),
          organizationId: organizationId,
        },
        returning: true,
      }
    );

    // add timesheet history on approval
    // updateTimesheets due to returning = true should return a tuple and we want to ensure that the second element is not empty
    if (updatedTimesheets && updatedTimesheets.length > 1 && updatedTimesheets[1]?.length > 0) {
      // loop through all the updated timesheets and create a timesheet history object for status change
      for (const timesheet of updatedTimesheets[1]) {
        const existingTimesheet = timesheets.find((t) => t.id === timesheet.id);
        const timesheetHistoryObj = this.timesheetHistoryService.formatTimesheetHistory(
          existingTimesheet.dataValues,
          { status: status },
          "UPDATE",
          userId
        );

        if (timesheetHistoryObj.history && timesheetHistoryObj.history !== "{}") {
          await this.timesheetHistoryService.create(timesheetHistoryObj);
        }
      }
    }

    return updatedTimesheets;
  }

  async createBulk(request: Request, response: Response, next: NextFunction) {
    try {
      const timesheets = await this.timesheetsService.createBulk(
        request.organization,
        request.session.user,
        request.body
      );

      return response.json({
        data: {
          timesheets,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async create(request: Request, response: Response, next: NextFunction) {
    const organization = request.organization;

    const transaction = await sequelize.transaction();

    try {
      const timesheet = await this.timesheetsService.create(
        organization,
        request.session.user,
        request.body,
        transaction
      );
      await transaction.commit();

      return response.json({
        data: await createOrUpdateTimesheetResponse(timesheet.id, organization.id),
      });
    } catch (error) {
      await transaction.rollback();
      console.error("Failed to create Timesheet:", error);
      next(error);
    }
  }

  async bulkDelete(req: Request, res: Response, next: NextFunction) {
    const { timesheetIds } = req.body;
    const organizationId = req.session.user.organizationId;

    try {
      const transaction = await sequelize.transaction();

      try {
        await this.timesheetsService.bulkUpdate(
          { isDeleted: true },
          {
            where: {
              id: timesheetIds,
              organizationId: organizationId,
              status: {
                [Op.not]: "PAID",
              },
            },
            transaction,
          }
        );

        await transaction.commit();

        return res.json({});
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      next(error);
    }
  }
}
