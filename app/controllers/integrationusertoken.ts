import { NextFunction, Request, Response } from "express";
import { GlAccountMapping, IntegrationSyncHistory, IntegrationUserToken, User } from "@/models";
import { createJournalEntry, exchangePublicTokenForAccessToken } from "@/util/rutterHelper";
import GlAccountMappingSetting, { GL_ACCOUNT_MAPPING_SETTINGS, CONSOLIDATE_BY } from "@/models/glaccountmappingsetting";
import { INTEGRATION_SUPPORTED_PLATFORMS, PROVIDERS, PROVIDERS_MAPPING } from "@/models/integrationusertoken";
import { IntegrationUserTokensService } from "@/services/integrationusertokenservice";
import { CheckService } from "@/services/check";
import { validateRequiredGlAccountMappings } from "@/util/accountingUtil";
import { stringify } from "csv-stringify/sync";
import { DepartmentsService } from "@/services/departments";
import { OrganizationsService } from "@/services/organizations";
import { CompanyBenefitsService } from "@/services/companybenefits";
import { EmployeeBenefitsService } from "@/services/employeebenefits";
import { Op } from "sequelize";
import { QuickBooksOnlineService } from "@/services/integrations/quickBooksOnline";
import { JournalEntryLineItem } from "@/types/journalEntry";
import { RutterService } from "@/services/integrations/rutter";

export class IntegrationUserTokensController {
  integrationUserTokensService: IntegrationUserTokensService;
  checkService: CheckService;
  departmentsService: DepartmentsService;
  organizationService: OrganizationsService;
  companyBenefitService: CompanyBenefitsService;
  employeeBenefitService: EmployeeBenefitsService;
  quickBooksOnlineService: QuickBooksOnlineService;
  rutterService: RutterService;

  constructor() {
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.delete = this.delete.bind(this);
    this.createJournalEntry = this.createJournalEntry.bind(this);
    this.updateJournalEntry = this.updateJournalEntry.bind(this);
    this.exportPayrollJournalEntryToCsv = this.exportPayrollJournalEntryToCsv.bind(this);

    this.integrationUserTokensService = new IntegrationUserTokensService();
    this.checkService = new CheckService();
    this.departmentsService = new DepartmentsService();
    this.organizationService = new OrganizationsService();
    this.companyBenefitService = new CompanyBenefitsService();
    this.employeeBenefitService = new EmployeeBenefitsService();
    this.quickBooksOnlineService = new QuickBooksOnlineService();
    this.rutterService = new RutterService();
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    try {
      const integrationUserTokens = await IntegrationUserToken.findAll({
        where: {
          organizationId,
        },
      });

      return res.json({
        data: {
          integrationUserTokens: integrationUserTokens,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  /**
   * Creates a new integration user token based on provider type
   * Handles different flows for Rutter vs Manual providers
   */
  async create(req: Request, res: Response, next: NextFunction) {
    const userId = req.session.user.id;
    const organizationId = req.session.user.organizationId;
    const { publicToken, provider, platform } = req.body;

    try {
      let integrationUserToken: IntegrationUserToken;

      if (provider === PROVIDERS_MAPPING.RUTTER) {
        // exchange public token for access token
        const response = await exchangePublicTokenForAccessToken(publicToken);

        const { access_token, store_unique_name, platform } = response;

        // check if we have an integration for the same platform that is disabled
        const existingIntegration = await IntegrationUserToken.findOne({
          where: {
            organizationId,
            platform,
            storeUniqueId: store_unique_name,
            isEnabled: false,
          },
        });

        if (existingIntegration) {
          // enable the existing integration
          integrationUserToken = await existingIntegration.update({
            isEnabled: true,
            accessToken: access_token,
            createdBy: userId,
          });
        } else {
          // save user token with storeUniqueId
          integrationUserToken = await IntegrationUserToken.create({
            createdBy: userId,
            organizationId,
            platform,
            storeUniqueId: store_unique_name,
            accessToken: access_token,
            provider,
          });
        }
      } else if (provider === PROVIDERS_MAPPING.MANUAL) {
        // check if we have an integration for the same platform that is disabled
        const existingIntegration = await IntegrationUserToken.findOne({
          where: {
            organizationId,
            platform,
            isEnabled: false,
          },
        });

        if (existingIntegration) {
          integrationUserToken = await existingIntegration.update({
            isEnabled: true,
            createdBy: userId,
          });
        } else {
          integrationUserToken = await IntegrationUserToken.create({
            createdBy: userId,
            organizationId,
            platform,
            accessToken: null,
            provider,
          });
        }
      }

      // create default gl account mapping settings

      await GlAccountMappingSetting.create({
        createdBy: userId,
        organizationId,
        integrationUserTokenId: integrationUserToken.id,
        consolidateJournalEntryBy: GL_ACCOUNT_MAPPING_SETTINGS.consolidateJournalEntryBy.values[0].value,
        entryDate: GL_ACCOUNT_MAPPING_SETTINGS.entryDate.values[0].value,
      });

      return res.status(201).json({
        data: {
          message: "User token created successfully",
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const integrationUserTokenId = Number(req.params.id);

    try {
      await this.integrationUserTokensService.disable(integrationUserTokenId, organizationId);

      return res.status(200).json({
        data: {
          message: "User token disabled successfully",
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async getProviders(req: Request, res: Response) {
    return res.json({
      data: PROVIDERS,
    });
  }

  /**
   * Creates or previews a journal entry from payroll data
   * When preview=true, returns calculated entry without calling rutter api or saving to db
   */
  async createJournalEntry(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const preview = req.query.preview === "true";
    const integrationUserTokenId = req.body.integrationUserTokenId;

    try {
      const { payrollId, platform } = req.body;
      const payroll = await this.checkService.get(`/payrolls/${payrollId}`);

      // check that payroll status is paid
      if (payroll.status !== "paid") {
        return res.status(400).json({
          error: "Payroll status is not paid",
        });
      }

      // get integration user token
      const integrationUserToken = await IntegrationUserToken.findOne({
        where: {
          organizationId: organizationId,
          platform,
          isEnabled: true,
          id: integrationUserTokenId,
        },
      });

      if (!integrationUserToken) {
        return res.status(404).json({
          error: "Integration user token not found",
        });
      }

      // get gl account mappings settings
      const glAccountMappingSettings = await GlAccountMappingSetting.findOne({
        where: {
          organizationId: organizationId,
          integrationUserTokenId: integrationUserToken.id,
        },
      });

      // if glAccountMappingSettings is empty, return an error
      if (!glAccountMappingSettings) {
        return res.status(400).json({
          error: "No GL account mappings settings found",
        });
      }

      // Determine the where clause for fetching GL account mappings
      const glAccountMappingsWhereClause: {
        organizationId: number;
        integrationUserTokenId: number;
        departmentId?: number | null;
      } = {
        organizationId: organizationId,
        integrationUserTokenId: integrationUserToken.id,
      };

      if (glAccountMappingSettings.consolidateJournalEntryBy !== CONSOLIDATE_BY.DEPARTMENT) {
        glAccountMappingsWhereClause.departmentId = null;
      }
      // When consolidating by department, departmentId is intentionally omitted from the where clause
      // to fetch all mappings (default and department-specific). The service layer
      // (mapPayrollToJournalEntryPerDepartment) will then resolve the correct account.

      // get gl account mappings
      const glAccountMappings = await GlAccountMapping.findAll({
        where: glAccountMappingsWhereClause,
      });

      const companyBenefits = await this.companyBenefitService.findAll({
        where: {
          organizationId,
          benefitStartDate: {
            [Op.lte]: new Date(),
          },
        },
      });

      const employeeBenefits = await this.employeeBenefitService.findAll({
        where: {
          organizationId,
        },
      });

      const { isValid } = await validateRequiredGlAccountMappings(
        integrationUserToken.id,
        organizationId,
        companyBenefits
      );

      if (!isValid) {
        return res.status(400).json({
          error: `Please go to the Account Mappings tab and map payroll items to your GL accounts.`,
        });
      }

      // Generate journal entry based on consolidation type
      let journalEntryBody;
      try {
        journalEntryBody = await this.rutterService.generateJournalEntry(
          payroll,
          glAccountMappings,
          glAccountMappingSettings,
          integrationUserToken,
          organizationId,
          companyBenefits,
          employeeBenefits,
          req.organization
        );
      } catch (error) {
        return res.status(400).json({
          error: error.message,
        });
      }

      // Filter out zero-amount entries
      journalEntryBody.journal_entry.line_items = journalEntryBody.journal_entry.line_items.filter(
        (item: JournalEntryLineItem) => item.total_amount !== 0
      );

      if (preview) {
        return res.json({
          data: journalEntryBody,
        });
      }

      let response;

      if (integrationUserToken.platform === INTEGRATION_SUPPORTED_PLATFORMS.QUICKBOOKS.id) {
        try {
          response = await this.quickBooksOnlineService.createJournalEntry(
            integrationUserToken.organizationId,
            journalEntryBody.journal_entry,
            glAccountMappings
          );

          // create sync history entry
          const syncHistory = await IntegrationSyncHistory.create({
            integrationUserTokenId: integrationUserToken.id,
            payrollId,
          });

          return res.json({
            data: {
              response,
              syncHistory,
            },
          });
        } catch (error) {
          return res.status(400).json({
            error: error.message || "Failed to create journal entry in QuickBooks",
          });
        }
      } else {
        response = await createJournalEntry(integrationUserToken.accessToken, journalEntryBody);
      }

      // create sync history entry
      const syncHistory = await IntegrationSyncHistory.create({
        integrationUserTokenId: integrationUserToken.id,
        payrollId,
      });

      return res.json({
        data: {
          response,
          syncHistory,
        },
      });
    } catch (err) {
      if (err.message === "expired") {
        return res.status(451).json({
          error: "Rutter connection expired. Please disable connection and re-authenticate.",
        });
      }

      if (err.message === "empty") {
        return res.status(400).json({
          error: "No employees or contractors with departments found for this payroll.",
        });
      }

      next(err);
    }
  }

  /**
   * Retries failed journal entry creation and updates sync history
   * Similar to createJournalEntry but for previously failed entries
   */
  async updateJournalEntry(req: Request, res: Response, next: NextFunction) {
    try {
      const { payrollId, syncHistoryId, integrationUserTokenId, platform } = req.body;
      const organizationId = req.session.user.organizationId;

      const payroll = await this.checkService.get(`/payrolls/${payrollId}`);

      // check that payroll status is paid
      if (payroll.status !== "paid") {
        return res.status(400).json({
          error: "Payroll status is not paid",
        });
      }

      // get integration user token
      const integrationUserToken = await IntegrationUserToken.findOne({
        where: {
          organizationId: organizationId,
          platform,
          id: integrationUserTokenId,
        },
      });

      if (!integrationUserToken) {
        return res.status(404).json({
          error: "Integration user token not found",
        });
      }

      // get gl account mappings settings
      const glAccountMappingSettings = await GlAccountMappingSetting.findOne({
        where: {
          organizationId: organizationId,
          integrationUserTokenId: integrationUserToken.id,
        },
      });

      // if glAccountMappingSettings is empty, return an error
      if (!glAccountMappingSettings) {
        return res.status(400).json({
          error: "No GL account mappings settings found",
        });
      }

      // Determine the where clause for fetching GL account mappings
      const glAccountMappingsWhereClause: {
        organizationId: number;
        integrationUserTokenId: number;
        departmentId?: number | null;
      } = {
        organizationId: organizationId,
        integrationUserTokenId: integrationUserToken.id,
      };

      if (glAccountMappingSettings.consolidateJournalEntryBy !== CONSOLIDATE_BY.DEPARTMENT) {
        glAccountMappingsWhereClause.departmentId = null;
      }
      // When consolidating by department, departmentId is intentionally omitted from the where clause
      // to fetch all mappings (default and department-specific). The service layer
      // (mapPayrollToJournalEntryPerDepartment) will then resolve the correct account.

      // get gl account mappings
      const glAccountMappings = await GlAccountMapping.findAll({
        where: glAccountMappingsWhereClause,
      });

      const companyBenefits = await this.companyBenefitService.findAll({
        where: {
          organizationId,
          benefitStartDate: {
            [Op.lte]: new Date(),
          },
        },
      });

      const employeeBenefits = await this.employeeBenefitService.findAll({
        where: {
          organizationId,
        },
      });

      // all payroll categories must be mapped to a GL account based on PAYROLL_CATEGORY_TYPES
      const { isValid } = await validateRequiredGlAccountMappings(
        integrationUserToken.id,
        organizationId,
        companyBenefits
      );

      if (!isValid) {
        return res.status(400).json({
          error: `Please go to the Account Mappings tab and map payroll items to your GL accounts.`,
        });
      }

      // Generate journal entry based on consolidation type
      let journalEntryBody;
      try {
        journalEntryBody = await this.rutterService.generateJournalEntry(
          payroll,
          glAccountMappings,
          glAccountMappingSettings,
          integrationUserToken,
          organizationId,
          companyBenefits,
          employeeBenefits,
          req.organization
        );
      } catch (error) {
        return res.status(400).json({
          error: error.message,
        });
      }

      // Filter out zero-amount entries
      journalEntryBody.journal_entry.line_items = journalEntryBody.journal_entry.line_items.filter(
        (item: JournalEntryLineItem) => item.total_amount !== 0
      );

      let response;
      if (integrationUserToken.platform === INTEGRATION_SUPPORTED_PLATFORMS.QUICKBOOKS.id) {
        try {
          response = await this.quickBooksOnlineService.createJournalEntry(
            integrationUserToken.organizationId,
            journalEntryBody.journal_entry,
            glAccountMappings
          );

          // Only update sync history if the journal entry creation was successful
          await IntegrationSyncHistory.update(
            {
              failedAt: null,
            },
            { where: { id: syncHistoryId, payrollId } }
          );

          return res.json({
            data: {
              response,
              syncHistory: null,
            },
          });
        } catch (error) {
          return res.status(400).json({
            error: error.message || "Failed to create journal entry in QuickBooks",
          });
        }
      }

      response = await createJournalEntry(integrationUserToken.accessToken, journalEntryBody);

      // update sync history entry
      const syncHistory = await IntegrationSyncHistory.update(
        {
          failedAt: null,
        },
        { where: { id: syncHistoryId, payrollId } }
      );

      return res.json({
        data: {
          response,
          syncHistory,
        },
      });
    } catch (err) {
      if (err.message === "expired") {
        return res.status(451).json({
          error: "Rutter connection expired. Please disable connection and re-authenticate.",
        });
      }

      if (err.message === "empty") {
        return res.status(400).json({
          error: "No employees or contractors with departments found for this payroll.",
        });
      }

      next(err);
    }
  }

  async getSupportedPlatforms(req: Request, res: Response) {
    let supportedPlatforms = Object.values(INTEGRATION_SUPPORTED_PLATFORMS);

    if (!shouldShowWorkmansDashboardIntegration(req.organization.id)) {
      supportedPlatforms = supportedPlatforms.filter((item) => item.id !== "WORKMANS_DASHBOARD");
    }

    return res.json({
      data: supportedPlatforms,
    });
  }

  /**
   * Exports journal entry data to CSV format for Foundation integration
   * Transforms journal entries to match Foundation's expected format
   */
  async exportPayrollJournalEntryToCsv(req: Request, res: Response, next: NextFunction) {
    try {
      const { payrollId } = req.body;
      const organizationId = req.session.user.organizationId;

      const payroll = await this.checkService.get(`/payrolls/${payrollId}`);
      if (payroll.status !== "paid") {
        return res.status(400).json({ error: "Payroll must be paid." });
      }

      const integrationUserToken = await IntegrationUserToken.findOne({
        where: { organizationId, platform: "FOUNDATION" },
      });

      if (!integrationUserToken) {
        return res.status(404).json({ error: "Integration user token not found" });
      }

      const glAccountMappings = await GlAccountMapping.findAll({
        where: {
          organizationId,
          integrationUserTokenId: integrationUserToken.id,
        },
      });

      const companyBenefits = await this.companyBenefitService.findAll({
        where: {
          organizationId,
          benefitStartDate: {
            [Op.lte]: new Date(),
          },
        },
      });

      const { isValid } = await validateRequiredGlAccountMappings(
        integrationUserToken.id,
        organizationId,
        companyBenefits
      );

      if (!isValid) {
        return res.status(400).json({
          error: `Please go to the Account Mappings tab and map payroll items to your GL accounts.`,
        });
      }

      const glAccountMappingSettings = await GlAccountMappingSetting.findOne({
        where: {
          organizationId,
          integrationUserTokenId: integrationUserToken.id,
        },
      });
      if (!glAccountMappingSettings) {
        return res.status(400).json({ error: "No GL account mappings settings found" });
      }

      let users: User[] = [];
      let consolidateByEmployee = false;
      if (
        glAccountMappingSettings.consolidateJournalEntryBy ===
        GL_ACCOUNT_MAPPING_SETTINGS.consolidateJournalEntryBy.values[1].value
      ) {
        consolidateByEmployee = true;
        users = await User.findAll({ where: { organizationId } });
      }

      const employeeBenefits = await this.employeeBenefitService.findAll({
        where: {
          organizationId,
        },
      });

      // 5) Build the journal entry for Foundation (attach cost classes, etc.)
      const { journal_entry } = await this.integrationUserTokensService.mapJournalEntryForFoundation(
        payroll,
        glAccountMappings,
        consolidateByEmployee,
        users,
        companyBenefits,
        employeeBenefits
      );

      const rows = journal_entry.line_items.map((line) => {
        const dbCr = line.total_amount >= 0 ? "D" : "C";
        const amount = Math.abs(line.total_amount).toFixed(2);

        return [
          /* Account      */ line.account_id,
          /* Division1    */ "",
          /* Division2    */ "",
          /* Division3    */ "",
          /* Division4    */ "",
          /* Db/Cr        */ dbCr,
          /* Amount       */ amount,
          /* Description  */ line.description || "",
          /* Units        */ "",
          /* Job          */ "",
          /* Phase        */ "",
          /* Cost Code    */ "",
          /* Cost Class   */ line.foundationCostClass || "",
        ];
      });

      const columns = [
        "Account",
        "Division1",
        "Division2",
        "Division3",
        "Division4",
        "Db/Cr",
        "Amount",
        "Description",
        "Units",
        "Job",
        "Phase",
        "Cost Code",
        "Cost Class",
      ];
      const csv = stringify(rows, { header: true, columns });

      res.attachment("foundation_journal_entry.csv").send(csv);
    } catch (err) {
      next(err);
    }
  }
}

/**
 * This function was specially written for one single organization for now.
 * Only specific organizations listed in a comma separated string
 *    inside env var `WORKMANS_DASHBOARD_ORGANIZATION_WHITELIST`are allowed to see this integration
 */
function shouldShowWorkmansDashboardIntegration(organizationId: number) {
  const environment = process.env.ENVIRONMENT;
  const allowedOrganizations = String(process.env.WORKMANS_DASHBOARD_ORGANIZATION_WHITELIST ?? "").split(",");

  return (
    environment === "DEVELOPMENT" ||
    ((environment === "PRODUCTION" || environment === "STAGING") &&
      allowedOrganizations.includes(organizationId.toString()))
  );
}
