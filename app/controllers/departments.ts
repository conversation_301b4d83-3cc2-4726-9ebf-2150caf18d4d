import { Request, Response, NextFunction } from "express";
import { DepartmentsService } from "@/services/departments";
import { User } from "../models";
import { UsersService } from "@/services/users";
export class DepartmentsController {
  departmentsService: DepartmentsService;
  usersService: UsersService;

  constructor() {
    this.departmentsService = new DepartmentsService();
    this.usersService = new UsersService();
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.updateDepartmentMember = this.updateDepartmentMember.bind(this);
    this.bulkAssignDepartmentMembers = this.bulkAssignDepartmentMembers.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    try {
      const departments = await this.departmentsService.findAll(organizationId);

      return res.json({
        data: {
          departments,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { name, departmentMembers }: { name: string; departmentMembers: number[] } = req.body;

    try {
      const department = await this.departmentsService.create({
        organizationId,
        name,
        createdBy: req.session.user.id,
      });

      if (departmentMembers?.length) {
        await this.usersService.update({ departmentId: department.id }, { where: { id: departmentMembers } });
      }

      return res.json({
        data: {
          department: {
            ...department.dataValues,
          },
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const departmentId = parseInt(req.params.id, 10);
    const { name, departmentMembers }: { name: string; departmentMembers: number[] } = req.body;

    try {
      const department = await this.departmentsService.findOne(departmentId);

      if (!department) {
        return res.status(404).json({ error: "Department not found" });
      }

      if (name) {
        await this.departmentsService.update({ name }, { where: { id: departmentId, organizationId } });
      }

      if (departmentMembers) {
        // Get current members
        const existingMembers = await this.departmentsService.getMembersFromDepartment(departmentId);

        const existingUserIds = existingMembers.map((member: User) => member.id);
        const membersToAdd = departmentMembers.filter((id: number) => !existingUserIds.includes(id));
        const userIdsToRemove = existingUserIds.filter((id: number) => !departmentMembers.includes(id));

        // Update memberships using the new service method
        if (membersToAdd.length > 0) {
          await this.departmentsService.updateMembers(departmentId, membersToAdd);
        }

        if (userIdsToRemove.length > 0) {
          await this.departmentsService.updateMembers(null, userIdsToRemove);
        }
      }

      return res.json({});
    } catch (error) {
      next(error);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const departmentId = parseInt(req.params.id, 10);

    try {
      // Remove department from all users
      await this.usersService.update({ departmentId: null }, { where: { departmentId } });

      // Delete the department
      await this.departmentsService.delete({
        id: departmentId,
      });

      return res.json({});
    } catch (error) {
      next(error);
    }
  }

  async updateDepartmentMember(req: Request, res: Response, next: NextFunction) {
    const { userId, departmentId } = req.body;

    try {
      await this.departmentsService.updateMembers(departmentId, [userId]);

      return res.json({});
    } catch (error) {
      next(error);
    }
  }

  async bulkAssignDepartmentMembers(req: Request, res: Response, next: NextFunction) {
    const { departmentId, userIds } = req.body;

    try {
      await this.departmentsService.updateMembers(departmentId, userIds);

      return res.json({});
    } catch (error) {
      next(error);
    }
  }
}
