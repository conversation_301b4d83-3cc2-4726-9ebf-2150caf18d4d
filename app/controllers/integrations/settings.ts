import { NextFunction, Request, Response } from "express";
import { IntegrationSettingsService } from "@/services/integrations/settings";
import { IntegrationUserToken } from "@/models";

export class IntegrationSettingsController {
  private integrationSettingsService: IntegrationSettingsService;

  constructor() {
    this.integrationSettingsService = new IntegrationSettingsService();
    this.getSettings = this.getSettings.bind(this);
    this.updateSetting = this.updateSetting.bind(this);
  }

  async getSettings(request: Request, response: Response, next: NextFunction) {
    try {
      const integration = await IntegrationUserToken.findOne({
        where: {
          platform: request.params.platform,
          organizationId: request.organization.id,
          isEnabled: true,
        },
      });

      const settings = await this.integrationSettingsService.getSettings(integration?.id);

      return response.json({
        data: { settings },
      });
    } catch (error) {
      next(error);
    }
  }

  async updateSetting(request: Request, response: Response, next: NextFunction) {
    try {
      const integration = await IntegrationUserToken.findOne({
        where: {
          platform: request.params.platform,
          organizationId: request.organization.id,
          isEnabled: true,
        },
      });

      const settingData = request.body;
      const setting = await this.integrationSettingsService.createOrUpdateSetting(integration?.id, settingData);

      return response.json({
        data: { setting },
      });
    } catch (error) {
      next(error);
    }
  }
}
