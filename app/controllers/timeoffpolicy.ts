import { NextFunction, Request, Response } from "express";
import TimeOffPolicyService from "../services/timeoffpolicy";

export default class TimeOffPolicyController {
  timeOffPolicyService = new TimeOffPolicyService();

  constructor() {
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.list = this.list.bind(this);
    this.userPolicies = this.userPolicies.bind(this);
    this.myPolicies = this.myPolicies.bind(this);
    this.enrollUsers = this.enrollUsers.bind(this);
    this.removeUserEnrollment = this.removeUserEnrollment.bind(this);
    this.update = this.update.bind(this);
    this.archive = this.archive.bind(this);
    this.unarchive = this.unarchive.bind(this);
    this.timeOffBalances = this.timeOffBalances.bind(this);
  }

  async create(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffPolicyService.create({
        ...request.body,
        organizationId: request.organization.id,
      });

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffPolicyService.update(Number(request.params.id), request.body);

      return response.json({ data: result });
    } catch (error) {
      next(error);
    }
  }

  async get(request: Request, response: Response, next: NextFunction) {
    try {
      const timeOffPolicy = await this.timeOffPolicyService.get(Number(request.params.id));

      const usersWithTimeOffSummary = await Promise.all(
        timeOffPolicy.users.map(async (user) => {
          const timeOffSummary = await this.timeOffPolicyService.getTimeOffSummary(timeOffPolicy.id, user.id);

          return {
            ...user.toJSON(),
            timeOffSummary,
          };
        })
      );

      return response.json({ data: { timeOffPolicy: { ...timeOffPolicy.toJSON(), users: usersWithTimeOffSummary } } });
    } catch (error) {
      next(error);
    }
  }

  async list(request: Request<any, any, any, { includeArchived?: boolean }>, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffPolicyService.list(request.organization.id, request.query.includeArchived);

      return response.json({ data: { timeOffPolicies: result } });
    } catch (error) {
      next(error);
    }
  }

  async userPolicies(
    request: Request<
      any,
      any,
      any,
      {
        includeArchived?: boolean;
      }
    >,
    response: Response,
    next: NextFunction
  ) {
    try {
      const result = await this.timeOffPolicyService.userPolicies(
        parseInt(request.params.userId),
        request.query.includeArchived
      );

      return response.json({ data: { timeOffPolicies: result } });
    } catch (error) {
      next(error);
    }
  }

  async myPolicies(
    request: Request<
      any,
      any,
      any,
      {
        includeArchived?: boolean;
      }
    >,
    response: Response,
    next: NextFunction
  ) {
    try {
      const results = await this.timeOffPolicyService.userPolicies(
        request.session.user.id,
        request.query.includeArchived
      );

      return response.json({
        data: { timeOffPolicies: results },
      });
    } catch (error) {
      next(error);
    }
  }

  async enrollUsers(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffPolicyService.enrollUsers(
        Number(request.params.id),
        request.body.userIds,
        request.body.startingBalance
      );

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async removeUserEnrollment(request: Request, response: Response, next: NextFunction) {
    try {
      const result = await this.timeOffPolicyService.removeUserEnrollment(
        Number(request.params.id),
        Number(request.params.userId)
      );

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async archive(request: Request, response: Response, next: NextFunction) {
    try {
      const id = parseInt(request.params.id);
      const result = await this.timeOffPolicyService.archive(id);

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async unarchive(request: Request, response: Response, next: NextFunction) {
    try {
      const id = parseInt(request.params.id);
      const result = await this.timeOffPolicyService.unarchive(id);

      return response.json({
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  async timeOffBalances(request: Request, response: Response, next: NextFunction) {
    try {
      const year = parseInt(request.params.year);
      const results = await this.timeOffPolicyService.timeOffBalances(
        request.organization.id,
        request.organization.checkCompanyId,
        year
      );

      return response.json({
        data: results,
      });
    } catch (error) {
      next(error);
    }
  }
}
