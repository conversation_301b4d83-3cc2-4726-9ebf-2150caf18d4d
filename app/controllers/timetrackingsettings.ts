import { NextFunction, Request, Response } from "express";
import TimeTrackingSettings from "@/models/timetrackingsettings";

export class TimeTrackingSettingsController {
  constructor() {
    this.update = this.update.bind(this);
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      const organizationId = request.organization.id;
      const timeTrackingSettings = await TimeTrackingSettings.findOne({
        where: { organizationId },
      });

      if (!timeTrackingSettings) {
        return response.status(404).json({
          error: "Time tracking settings not found.",
        });
      }

      await timeTrackingSettings.update(request.body);

      return response.json({});
    } catch (error) {
      next(error);
    }
  }
}
