import { NextFunction, Request, Response } from "express";
import fetch from "node-fetch";
const { SENDBIRD_API_TOKEN, SENDBIRD_APP_ID } = process.env;

export class ChatController {
  constructor() {
    this.update = this.update.bind(this);
    this.getToken = this.getToken.bind(this);
    this.createAnnouncement = this.createAnnouncement.bind(this);
    this.getAnnouncements = this.getAnnouncements.bind(this);
  }

  async getToken(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.session.user.id;
      const { url, headers } = getSendbirdRequestOptions(`/v3/users/${userId}/token`);

      const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000;
      const expiresAt = Date.now() + thirtyDaysInMilliseconds;

      const response = await fetch(url, {
        method: "POST",
        headers: headers,
        body: JSON.stringify({ expires_at: expiresAt }),
      });

      if (!response.ok) {
        next(new Error(`Error issuing session token: ${response.statusText}`));
      }

      const responseData = await response.json();
      const { token, expires_at } = responseData;

      return res.json({
        data: {
          token,
          expires_at,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    try {
      const channelUrl = req.params.channelUrl;
      const userId = req.session.user.id;
      const { url: membersUrl, headers: membersHeaders } = getSendbirdRequestOptions(
        `/v3/group_channels/${channelUrl}/members`
      );

      const membersResponse = await fetch(membersUrl, { headers: membersHeaders });

      if (!membersResponse.ok) {
        const errorText = await membersResponse.text();
        console.error(`Error fetching members: ${errorText}`);
        throw new Error(`Error fetching members: ${membersResponse.statusText}`);
      }

      const membersData = await membersResponse.json();

      const isOperator = membersData.members.some(
        (member: { user_id: string; role: string }) =>
          member.user_id === userId.toString() && member.role === "operator"
      );

      if (isOperator) {
        const isHideAction = req.path.includes("/hide");
        const action = isHideAction ? "hide" : "unhide";
        const method = isHideAction ? "PUT" : "DELETE";
        const body = isHideAction
          ? JSON.stringify({ should_hide_all: true })
          : JSON.stringify({ should_unhide_all: true });
        const { url, headers } = getSendbirdRequestOptions(`/v3/group_channels/${channelUrl}/hide`);

        const response = await fetch(url, {
          method: method,
          headers: headers,
          ...(body && { body: body }),
        });

        if (!response.ok) {
          throw new Error(`Error ${action} channel: ${response.statusText}`);
        }

        return res.json({});
      } else {
        res.status(403).json({
          error: "User is not an operator",
        });
      }
    } catch (error) {
      next(error);
    }
  }

  async createAnnouncement(req: Request, res: Response, next: NextFunction) {
    try {
      const { message, receiverUserIds, scheduledAt } = req.body as {
        message: string;
        receiverUserIds: string[];
        scheduledAt?: number;
      };
      const senderUserId = req.session.user.id;

      const { url, headers } = getSendbirdRequestOptions(`/v3/announcements`);

      const response = await fetch(url, {
        method: "POST",
        headers: headers,
        body: JSON.stringify({
          target_at: "target_users_only_channels",
          target_list: receiverUserIds?.map((userId) => userId.toString()),
          create_channel: true,
          create_channel_options: {
            custom_type: "personal",
            distinct: true,
          },
          target_channel_type: "distinct",
          message: {
            user_id: senderUserId?.toString(),
            content: message,
            type: "MESG",
            custom_type: "text",
            scheduled_at: scheduledAt ?? Math.floor(Date.now() / 1000), // Unix timestamp in seconds
          },
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error creating annoucement: ${errorText}`);
        throw new Error(`Unable to create annoucement: ${response.statusText}`);
      }

      const result = await response.json();

      return res.json(result);
    } catch (error) {
      next(error);
    }
  }

  async getAnnouncements(req: Request, res: Response, next: NextFunction) {
    try {
      const { url, headers } = getSendbirdRequestOptions(`/v3/announcements/${req.params.id}`);

      const response = await fetch(url, {
        method: "GET",
        headers: headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error fetching annoucements: ${errorText}`);
        throw new Error(`Unable to fetch annoucements: ${response.statusText}`);
      }

      const result = await response.json();

      return res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

function getSendbirdRequestOptions(endpoint: string) {
  const sendbirdApiToken = SENDBIRD_API_TOKEN;
  const sendbirdAppId = SENDBIRD_APP_ID;
  const baseUrl = `https://api-${sendbirdAppId}.sendbird.com`;
  const url = `${baseUrl}${endpoint}`;

  const headers = {
    "Content-Type": "application/json; charset=utf8",
    "Api-Token": sendbirdApiToken,
  };

  return { url, headers };
}
