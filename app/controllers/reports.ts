import { Op } from "sequelize";
import dayjs from "dayjs";
import { NextFunction, Request, Response } from "express";
import { CheckService } from "@/services/check";
import { WageTableService } from "@/services/wagetable";
import { TimesheetsService } from "@/services/timesheets";
import { ReportsService } from "@/services/reports";
import { prefixCollectionValues } from "@/util/collectionHelpers";

import { ExtendedUserClassification } from "@/models/userclassification";
import { ExtendedFringeBenefitClassification } from "@/models/fringebenefitclassification";
import { ExtendedTimesheet } from "@/models/timesheet";
import { wageTableInclude } from "@/controllers/wagetable";

import { getNearestFullWeekIntervals } from "@/util/dateHelper";

import {
  Classification,
  FringeBenefit,
  FringeBenefitClassification,
  TimeSheet,
  User,
  UserClassification,
} from "@/models";
import { attributeHourlyFringeContribution } from "@/util/financial";
import { getTimesheetIncludes } from "@/util/timesheetHelper";
import { ExtendedOrganization } from "@/models/organization";
import { REPORT_FORMAT } from "@/models/reports";
import HTTPError from "../errors/HTTPError";
import { Payroll, PayrollItem } from "@/types/check";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import WorkforceDemographicsReport from "@/services/reports/WorkforceDemographics";
const { ENVIRONMENT } = process.env;

dayjs.extend(utc);
dayjs.extend(timezone);

export class ReportsController {
  checkService: CheckService;
  timesheetsService: TimesheetsService;
  wageTableService: WageTableService;
  reportsService: ReportsService;

  constructor() {
    this.checkService = new CheckService();
    this.timesheetsService = new TimesheetsService();
    this.wageTableService = new WageTableService();
    this.reportsService = new ReportsService();

    this.generateCertifiedPayrollReport = this.generateCertifiedPayrollReport.bind(this);
    this.AA202 = this.AA202.bind(this);
    this.generate401kReport = this.generate401kReport.bind(this);
  }

  async generateCertifiedPayrollReport(req: Request, res: Response, next: NextFunction) {
    const {
      projectId,
      signatoryName,
      signatoryTitle,
      reportFormat,
      reportType = null,
      isLastCertifiedPayrollReport = false,
      remarks = "",
      payrollWeekEnding,
    } = req.body;

    const project = req.targetedProject;
    const organization = req.organization as ExtendedOrganization;

    // essentially payrollWeekEnding should be (YYYY-MM-DD) format that is the "to" or "end" date used in the request
    // we need to get this sort of within the payday range - probably add 7 days to payroll_before since we know these are weekly reports
    // ideally we restrict this to "valid" payroll dates selectable on the client that Check has - how? not sure yet
    const checkCompanyId = organization.checkCompanyId;

    try {
      if (checkCompanyId) {
        // need several data fetches from Check
        // get company data from check via checkCompanyId
        const checkCompany = await this.checkService.get(`/companies/${checkCompanyId}`);

        const payroll = req.targetedPayroll;

        // payrollWeekEnding is passed by the client in cases where it's different from the payroll.period_end
        // this would happen when user is generating a report for a bi-weekly payroll and selects one of the weeks from the range
        const determinedPayrollWeekEnding = payrollWeekEnding ? payrollWeekEnding : payroll.period_end; // since this is timezone agnostic - parse via orgs timezone

        const timesheetsToTimestamp = dayjs
          .tz(determinedPayrollWeekEnding, organization.timezone)
          .endOf("day")
          .valueOf();
        // we always set from timestamp to 6 days before the end timestamp because most certified payroll reports are for a 7 day period
        const timesheetsFromTimestamp = dayjs
          .tz(determinedPayrollWeekEnding, organization.timezone)
          .subtract(6, "day")
          .startOf("day")
          .valueOf();

        const paySchedule = organization.paySchedule;
        const startDayOfWeek =
          paySchedule.payFrequency === "weekly" ? organization.overtimeSettings.weekStartDay : "MONDAY";
        const adjustedDates = getNearestFullWeekIntervals(
          timesheetsFromTimestamp,
          timesheetsToTimestamp,
          startDayOfWeek
        );

        // get all the employees given a chosen payroll - need bulk get employees
        const employeeIds = payroll.items.map((item: any) => item.employee);

        const listOfEmployeeIds = prefixCollectionValues(employeeIds, "id=");

        const bulkEmployeeQueryString = listOfEmployeeIds.join("&");

        const employeesData = await this.checkService.getAllEmployees(organization, bulkEmployeeQueryString);

        // from hammr's side we need a lot of data as well
        const userClassificationsData: ExtendedUserClassification[] = await UserClassification.findAll({
          where: { wageTableId: project.wageTableId, organizationId: organization.id },
          include: [
            ...wageTableInclude({}),
            {
              model: Classification,
              required: false,
              include: [
                {
                  model: FringeBenefitClassification,
                  attributes: ["amount"],
                  required: false,
                  include: [
                    {
                      model: FringeBenefit, // including individual attributes removed because it would not include them - some sequelize limitation
                    },
                  ],
                },
              ],
            },
          ],
        });

        const fromDate = adjustedDates?.start ? adjustedDates.start.toDate() : undefined;
        const toDate = adjustedDates?.end ? adjustedDates.end.toDate() : undefined;

        const timesheetIncludes = getTimesheetIncludes(fromDate, toDate);

        const projectStartDate = req.targetedProjectStartDate;

        const determinedPayrollNumber = this.reportsService.determinePayrollNumber(
          projectStartDate,
          payrollWeekEnding,
          organization?.paySchedule?.payFrequency || "weekly",
          organization.timezone,
          isLastCertifiedPayrollReport
        );

        const timesheetsData: ExtendedTimesheet[] = await this.timesheetsService.findAll({
          where: {
            clockIn: {
              [Op.between]: [adjustedDates.start.toISOString(), adjustedDates.end.toISOString()],
            },
            isDeleted: false,
            organizationId: organization.id,
          },
          order: [
            [User, "id", "ASC"],
            ["clockIn", "ASC"],
          ],
          include: timesheetIncludes,
        });

        // this is where some reports start to deviate
        // for WH-347, continue with report even if no timesheets are found
        if (timesheetsData.length === 0 && reportFormat !== REPORT_FORMAT.DepartmentOfLabor) {
          return res.status(404).json({
            error: `There was no work performed on ${project.name} in the selected period.`,
          });
        }

        const noWorkWeek = timesheetsData.length === 0;

        const fringeBenefitData = await FringeBenefitClassification.findAll({
          where: {
            organizationId: organization.id,
          },
          include: [
            {
              model: Classification,
              required: true,
            },
            {
              model: FringeBenefit,
              required: true,
            },
          ],
        });

        const joinedTimesheetsData = timesheetsData.map((timesheet) => {
          let plainTimesheet: any = timesheet;

          if (timesheet instanceof TimeSheet) {
            plainTimesheet = timesheet.get({ plain: true });
          }

          const classification = plainTimesheet.userClassification?.classification;
          if (classification) {
            const fringeBenefitClassifications = (fringeBenefitData as ExtendedFringeBenefitClassification[])
              .filter((fbc) => {
                if (fbc.classification?.id !== classification.id) {
                  return false;
                }

                return (
                  plainTimesheet.clockIn >= (fbc.startDate ?? -Infinity) &&
                  plainTimesheet.clockIn <= (fbc.endDate ?? Infinity)
                );
              })
              .map((fbc) => ({
                id: fbc.fringeBenefit?.id,
                name: fbc.fringeBenefit?.name,
                category: fbc.fringeBenefit?.category,
                amount: fbc.amount,
              }));

            return {
              ...plainTimesheet,
              fringeBenefitClassifications,
            };
          }

          // attribute fringeHourlyContribution if user.employeeBenefits exists and length > 0
          if (
            plainTimesheet.userClassificationId &&
            plainTimesheet.user.employeeBenefits &&
            plainTimesheet.user.employeeBenefits.length > 0
          ) {
            const attributedBenefits = attributeHourlyFringeContribution(
              plainTimesheet.userClassification,
              plainTimesheet.user.employeeBenefits,
              plainTimesheet.userClassification.classification.fringeBenefitClassifications,
              organization.paySchedule.payFrequency
            );

            // important to note that this "enhances" the data with a fringeHourlyContribution property
            // essentially downstream, we'll eventually attribute timesheets and for PW projects, it implies we'll need to calculate cashFringe - fringeHourlyContribution is a pre-req to calculating the cashFringe
            plainTimesheet.user.employeeBenefits = attributedBenefits;
          }

          return plainTimesheet;
        });

        const enrichedTimesheets = this.timesheetsService.enrichTimesheets(
          joinedTimesheetsData,
          organization.paySchedule.payFrequency
        );

        const flattendAttributedTimesheets = this.timesheetsService.generateFlattenedAttributedTimesheets(
          enrichedTimesheets,
          adjustedDates.start.valueOf(),
          organization
        ) as ExtendedTimesheet[];

        // we temporarily want to support a specific customer to generate certified payroll reports for draft payrolls
        const supportDraftPayrolls = ENVIRONMENT === "PRODUCTION" ? organization.id === 76 : true;
        // timesheets that need to be included in the report
        const eligibleAttributedTimesheets = flattendAttributedTimesheets.filter((timesheet) => {
          return (
            timesheet.clockInTimestamp >= timesheetsFromTimestamp &&
            timesheet.clockInTimestamp <= timesheetsToTimestamp &&
            (supportDraftPayrolls || (timesheet.status === "PAID" && timesheet.checkPayrollId === payroll.id)) &&
            timesheet.projectId === projectId
          );
        });

        // all timesheets across all projects in the date range
        const eligibleAllProjectsAttributedTimesheets = flattendAttributedTimesheets.filter((timesheet) => {
          return (
            timesheet.clockInTimestamp >= timesheetsFromTimestamp &&
            timesheet.clockInTimestamp <= timesheetsToTimestamp &&
            (supportDraftPayrolls || (timesheet.status === "PAID" && timesheet.checkPayrollId === payroll.id))
          );
        });

        const extendedUserClassifications = this.wageTableService.extendWageTableData(
          userClassificationsData,
          organization?.overtimeSettings?.overtimeMultiplier,
          organization?.paySchedule?.payFrequency
        );

        const organizedCertifiedPayrollData = this.reportsService.stitchCertifiedPayrollData(
          {
            signatoryName,
            signatoryTitle,
            company: checkCompany,
            payroll,
            project,
            organization,
            employees: employeesData.length > 0 ? employeesData : [],
            enrichedTimesheets: eligibleAttributedTimesheets,
            enrichedTimesheetsAllProjects: eligibleAllProjectsAttributedTimesheets,
            userClassifications: extendedUserClassifications,
            payrollNumber: determinedPayrollNumber,
            noWorkWeek,
            payrollWeekEnding: determinedPayrollWeekEnding,
            remarks,
            isLastCertifiedPayrollReport,
          },
          organization.timezone
        );

        const reporter =
          this.reportsService.reports[reportFormat as keyof typeof REPORT_FORMAT] ?? this.reportsService.reports.JSON;

        const result = await new reporter(res).report(organization, organizedCertifiedPayrollData, reportType);

        return result;
      } else {
        return res.status(400).json({
          error: "Payroll company id required.",
        });
      }
    } catch (err) {
      if (err instanceof HTTPError) {
        return res.status(err.statusCode).json(err.toJSON());
      }

      console.log("error", err);

      next(err);
    }
  }

  async generate401kReport(req: Request, res: Response, next: NextFunction) {
    // should pass in a checkPayrollId (just payrollId)
    const { payrollId: checkPayrollId } = req.body;
    const organization = req.organization as ExtendedOrganization;

    try {
      // query the check payroll id
      const checkPayroll: Payroll = await this.checkService.get(`/payrolls/${checkPayrollId}`);
      // need to query hammr as well for employee data - no employee names
      const allCheckEmployeeIds = checkPayroll.items.map((item: PayrollItem) => item.employee);

      const allHammrUserDataFromCheckEmployeeIds = await User.findAll({
        where: {
          checkEmployeeId: {
            [Op.in]: allCheckEmployeeIds,
          },
        },
      });

      const report = this.reportsService.build401kReport(
        checkPayroll,
        organization,
        allHammrUserDataFromCheckEmployeeIds
      );

      // return as attachment
      return res.attachment("401k-report.csv").send(report);
    } catch (err) {
      console.log("error", err);
      next(err);
    }
  }

  async AA202(request: Request, response: Response, next: NextFunction) {
    try {
      const organization = request.organization;
      const reporter = new WorkforceDemographicsReport(response);

      return await reporter.report(organization, request.body);
    } catch (err) {
      next(err);
    }
  }
}
