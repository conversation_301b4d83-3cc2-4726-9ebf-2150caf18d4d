import { NextFunction, Request, Response } from "express";
import EmployeeReimbursement from "@/models/employeereimbursement";

export const setEmployeeReimbursement = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId, reimbursementAmount } = req.body;

    // Check if user already has a reimbursement
    const existingReimbursement = await EmployeeReimbursement.findOne({
      where: { userId },
    });

    if (existingReimbursement) {
      try {
        // update if exists
        existingReimbursement.reimbursementAmount = reimbursementAmount;
        await existingReimbursement.save();

        return res.status(200).json(existingReimbursement);
      } catch (error) {
        console.error("Error updating employee reimbursement:", error);
        next(error);
      }
    }

    try {
      const reimbursement = await EmployeeReimbursement.create({
        userId,
        reimbursementAmount,
        organizationId: req.organization.id,
      });

      return res.status(201).json(reimbursement);
    } catch (error) {
      console.error("Error creating employee reimbursement:", error);
      next(error);
    }
  } catch (error) {
    console.error("Error handling employee reimbursement:", error);
    next(error);
  }
};
