import { NextFunction, Request, Response } from "express";
import sequelize from "@/lib/sequelize";
import { CompanyBenefitsService } from "@/services/companybenefits";
import { ServiceOrchestrator } from "@/services/orchestrator";
import { CheckService } from "@/services/check";
import { convertDatesToTimestamps } from "@/util/dateHelper";
import { EmployeeBenefitsService } from "@/services/employeebenefits";

export class CompanyBenefitController {
  orchestratorService: ServiceOrchestrator;
  companyBenefitService: CompanyBenefitsService;
  employeeBenefitService: EmployeeBenefitsService;
  checkService: CheckService;

  constructor() {
    this.companyBenefitService = new CompanyBenefitsService();
    this.employeeBenefitService = new EmployeeBenefitsService();
    this.checkService = new CheckService();
    this.orchestratorService = new ServiceOrchestrator();

    // methods
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    const id = parseInt(req.params.id, 10);

    try {
      const companyBenefit = await this.companyBenefitService.findOne({
        where: {
          id,
          organizationId: req.session.user.organizationId,
        },
      });

      const timestampCompanyBenefitResults = convertDatesToTimestamps(companyBenefit);

      return res.json({
        data: {
          companyBenefit: timestampCompanyBenefitResults,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    try {
      let companyBenefits = await this.companyBenefitService.findAll({
        where: {
          organizationId,
        },
      });

      // run the results through
      companyBenefits = companyBenefits.map((companyBenefit) => {
        return convertDatesToTimestamps(companyBenefit);
      });

      return res.json({
        data: {
          companyBenefits,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const companyBenefitObj = req.body;

    // need to attach users's organizationId to the companyBenefitObj
    companyBenefitObj.organizationId = req.session.user.organizationId;

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      const companyBenefit = await this.companyBenefitService.create(companyBenefitObj, {
        transaction,
      });

      let checkCompanyBenefit;
      if (req.organization.isPayrollEnabled && req.organization.checkCompanyId) {
        // create check for company benefit - after transforming payload
        const checkCompanyBenefitPayload = this.checkService.handleCreateCheckPayload(
          { ...req.body, checkCompanyId: req.organization.checkCompanyId },
          "company_benefits",
          "POST"
        );

        checkCompanyBenefit = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/company_benefits",
          checkCompanyBenefitPayload
        );

        // update company benefit with checkId
        await this.companyBenefitService.update(
          { checkCompanyBenefitId: checkCompanyBenefit.id },
          { where: { id: companyBenefit.id }, transaction }
        );
      }

      await transaction.commit();
      const timestampCompanyBenefitResults = convertDatesToTimestamps(companyBenefit);

      return res.json({
        data: {
          companyBenefit: timestampCompanyBenefitResults.dataValues,
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const companyBenefitId = parseInt(req.params.id, 10);
    const companyBenefitObj = req.body;

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      const companyBenefit = await this.companyBenefitService.update(companyBenefitObj, {
        where: {
          id: companyBenefitId,
        },
        returning: true,
        transaction,
      });

      // if req.body contains benefitEndDate, we need to update the employee benefits that are associated with this company benefit
      // we don't need to update the employee benefit in Check because Check automatically excludes employee benefits from payroll
      // that are linked to a company benefit that has an end date
      if (req.body.benefitEndDate) {
        await this.employeeBenefitService.update(
          { benefitEndDate: req.body.benefitEndDate },
          {
            where: {
              companyBenefitId: companyBenefitId,
              benefitEndDate: null, // Only update employee benefits that don't already have an end date
            },
            transaction,
          }
        );
      }

      if (req.organization.isPayrollEnabled && req.organization.checkCompanyId) {
        // update company benefit for check
        const checkCompanyBenefitPayload = this.checkService.handleCreateCheckPayload(
          { ...req.body, checkCompanyId: req.organization.checkCompanyId },
          "company_benefits",
          "PATCH"
        );

        const returningCompanyBenefit = (companyBenefit as any)[1]?.[0]?.dataValues || {};

        await this.orchestratorService.forwardRequest(
          "check",
          "PATCH",
          `/company_benefits/${returningCompanyBenefit.checkCompanyBenefitId}`,
          checkCompanyBenefitPayload
        );
      }

      await transaction.commit();

      return res.json({
        data: {
          companyBenefit,
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }
}
