import { NextFunction, Request, Response } from "express";

import sequelize from "@/lib/sequelize";
import { OrganizationsService } from "@/services/organizations";
import { CheckService } from "@/services/check";

export class OrganizationsController {
  organizationsService: OrganizationsService;
  checkService: CheckService;

  constructor() {
    this.organizationsService = new OrganizationsService();
    this.checkService = new CheckService();

    // method this binds
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    const organizationId = parseInt(req.params.id, 10);

    try {
      const organization = await this.organizationsService.findOne({
        where: {
          id: organizationId,
        },
      });

      return res.json({
        data: {
          organization,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    const { name, slug, checkCompanyId, isRegisteredOnCheck } = req.body;

    const patchUpdates: any = {};

    if (name) {
      patchUpdates.name = name;
    }

    if (slug) {
      patchUpdates.slug = slug;
    }

    if (checkCompanyId) {
      patchUpdates.checkCompanyId = checkCompanyId;
    }

    if (isRegisteredOnCheck) {
      patchUpdates.isRegisteredOnCheck = isRegisteredOnCheck;
    }

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      const existingOrg = await this.organizationsService.findOne({
        where: {
          id: organizationId,
        },
        transaction,
      });

      await this.organizationsService.update(patchUpdates, {
        where: {
          id: organizationId,
        },
        transaction,
      });

      if (existingOrg.checkCompanyId) {
        const potentialCheckPayload = this.checkService.handleCreateCheckPayload(req.body, "companies", req.method);

        // downstream check api call
        if (Object.keys(potentialCheckPayload).length > 0) {
          await this.checkService.patch(`/companies/${existingOrg.checkCompanyId}`, potentialCheckPayload);
        }
      }

      await transaction.commit();

      return res.json({});
    } catch (error) {
      await transaction.rollback();
      console.log("something went wrong ", error);

      next(error);
    }
  }
}
