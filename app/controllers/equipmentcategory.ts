import { NextFunction, Request, Response } from "express";
import { EquipmentCategoryService } from "@/services/equipmentcategory";

export class EquipmentCategoryController {
  equipmentCategoryService: EquipmentCategoryService;

  constructor() {
    this.equipmentCategoryService = new EquipmentCategoryService();

    // method this binds
    this.get = this.get.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    try {
      const categories = await this.equipmentCategoryService.findAll({
        where: {
          organizationId,
        },
        order: [["name", "ASC"]],
      });

      return res.json({
        data: {
          categories,
        },
      });
    } catch (err) {
      next(err);
    }
  }
}
