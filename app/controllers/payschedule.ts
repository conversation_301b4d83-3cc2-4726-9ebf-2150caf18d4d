import { NextFunction, Request, Response } from "express";
import HTTPError from "../errors/HTTPError";
import { CheckService } from "@/services/check";
import PaySchedule from "@/models/payschedule";
import { beautifyError } from "@/util/utils";

// used to make error messages user friendly
const checkFieldsMappings = {
  pay_frequency: "Pay Frequency",
  first_payday: "First Payday",
  first_period_end: "First Period End",
  second_payday: "Second Payday",
  company: "Company",
};

export class PayScheduleController {
  checkService: CheckService;

  constructor() {
    this.checkService = new CheckService();

    this.get = this.get.bind(this);
    this.update = this.update.bind(this);
  }

  async get(request: Request, response: Response, next: NextFunction) {
    try {
      const organizationId = request.session.user?.organizationId;

      const paySchedule = await PaySchedule.findOne({
        where: { organizationId },
      });

      if (!paySchedule) {
        throw new HTTPError(404, "Pay schedule not found");
      }

      return response.json({
        data: paySchedule,
      });
    } catch (error) {
      next(error);
    }
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      const organizationId = request.session.user?.organizationId as number;
      const organization = request.organization;
      const checkCompanyId = organization.checkCompanyId as string;

      const { payFrequency, firstPayday, secondPayday, firstPeriodEnd, isActive = true } = request.body;

      // for non-payroll companies, we don't need to sync with Check
      if (!organization.isPayrollEnabled) {
        // update existing pay schedule with new values
        const existingPaySchedule = await PaySchedule.findOne({
          where: {
            organizationId,
            isActive: true,
          },
        });

        if (existingPaySchedule) {
          existingPaySchedule.setAttributes(request.body);
          await existingPaySchedule.save();
        }

        return response.json({
          data: existingPaySchedule,
        });
      }

      let existingPaySchedule = await PaySchedule.findOne({
        where: {
          organizationId,
          payFrequency,
        },
      });

      const payScheduleData = {
        pay_frequency: payFrequency,
        first_payday: firstPayday,
        first_period_end: firstPeriodEnd,
        second_payday: secondPayday,
        company: checkCompanyId,
      };

      let checkResponse;
      try {
        /**
         * Created or updates a pay schedule in Check
         *
         * Check doesn't allow updating a pay schedule already used in a payroll.
         * Because of this, when we get a specific error about it being used in a payroll,
         *  we just create a new pay schedule instead of updating the existing one.
         *  Note that there will be multiple pay schedules with the same pay frequency
         */

        // If we have an existing schedule in our DB with a Check ID, attempt to update it
        if (existingPaySchedule?.checkPayScheduleId) {
          checkResponse = await this.checkService.patch(
            `/pay_schedules/${existingPaySchedule.checkPayScheduleId}`,
            payScheduleData
          );
        } else {
          // If no existing schedule or no Check ID, create a new one
          checkResponse = await this.checkService.post("/pay_schedules", payScheduleData);
        }
      } catch (error) {
        if (error.input_errors?.[0]?.message === "Cannot update pay schedule that has been associated with a payroll") {
          // If we can't update the existing schedule, create a new one
          checkResponse = await this.checkService.post("/pay_schedules", payScheduleData);
          existingPaySchedule = undefined;
        } else if ("input_errors" in error && error.input_errors?.length) {
          throw new HTTPError(400, beautifyError(error.input_errors[0].message, checkFieldsMappings), {
            details: error,
          });
        } else {
          throw new HTTPError(400, error.message, { details: error });
        }
      }

      // Now sync our database with Check's response
      const payScheduleDbData = {
        firstPayday,
        firstPeriodEnd,
        secondPayday,
        isActive,
        checkPayScheduleId: checkResponse.id,
      };

      let updatedPaySchedule: PaySchedule;
      if (existingPaySchedule) {
        existingPaySchedule.setAttributes(payScheduleDbData);
        updatedPaySchedule = await existingPaySchedule.save();
      } else {
        updatedPaySchedule = await PaySchedule.create({
          organizationId: Number(organizationId),
          payFrequency,
          ...payScheduleDbData,
        });
      }

      return response.json({
        data: updatedPaySchedule,
      });
    } catch (error) {
      next(error);
    }
  }
}
