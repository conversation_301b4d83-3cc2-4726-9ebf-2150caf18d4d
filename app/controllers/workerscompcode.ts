import { NextFunction, Request, Response } from "express";
import { WorkersCompCodeService } from "@/services/workerscompcode";

export class WorkersCompCodeController {
  private workersCompCodeService: WorkersCompCodeService;

  constructor() {
    this.workersCompCodeService = new WorkersCompCodeService();
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.assignCostCodes = this.assignCostCodes.bind(this);
    this.assignWorkers = this.assignWorkers.bind(this);
  }

  async get(request: Request, response: Response, next: NextFunction) {
    try {
      const { includeArchived } = request.query;
      const organizationId = request.organization.id;
      const workersCompCodes = await this.workersCompCodeService.findAll(organizationId, includeArchived === "true");

      return response.json({
        data: workersCompCodes,
      });
    } catch (error) {
      next(error);
    }
  }

  async create(request: Request, response: Response, next: NextFunction) {
    try {
      const { name, code } = request.body;
      const organizationId = request.organization.id;

      const workersComCode = await this.workersCompCodeService.create({
        name,
        code,
        organizationId,
      });

      return response.json({
        data: workersComCode,
      });
    } catch (error) {
      next(error);
    }
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      const { id } = request.params;
      const { name, code, isArchived } = request.body;
      const organizationId = request.organization.id;

      const updatedCode = await this.workersCompCodeService.update(Number(id), organizationId, {
        name,
        code,
        isArchived,
      });

      return response.json({
        data: updatedCode,
      });
    } catch (error) {
      next(error);
    }
  }

  async assignCostCodes(request: Request, response: Response, next: NextFunction) {
    try {
      const { id } = request.params;
      const { costCodeIds } = request.body;
      const organizationId = request.organization.id;

      await this.workersCompCodeService.assignCostCodes(Number(id), organizationId, costCodeIds);

      return response.json({});
    } catch (error) {
      next(error);
    }
  }

  async assignWorkers(request: Request, response: Response, next: NextFunction) {
    try {
      const assignments = request.body as { userId: number; workersCompCodeId: string | number | null }[];
      const organizationId = request.organization.id;

      await this.workersCompCodeService.assignWorkers(organizationId, assignments);

      return response.json({});
    } catch (error) {
      next(error);
    }
  }
}
