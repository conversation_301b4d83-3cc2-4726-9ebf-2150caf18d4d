import { Request, Response, NextFunction } from "express";
import { CrewsService } from "@/services/crews";
import { CrewsMembersService } from "@/services/crewmembers";

export class CrewsController {
  crewsService: CrewsService;
  crewMembersService: CrewsMembersService;

  constructor() {
    this.crewsService = new CrewsService();
    this.crewMembersService = new CrewsMembersService();
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    try {
      const crews = await this.crewsService.findAll(organizationId);

      return res.json({
        data: {
          crews,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { name, crewMembers = null, crewLead = null } = req.body;

    if (!name) {
      return res.status(400).json({
        error: "Missing name",
      });
    }

    if (crewLead !== null) {
      if (isNaN(crewLead)) {
        return res.status(400).json({
          error: "crewLead must be a number",
        });
      }
    }

    let crewMembersNumbers: number[] = [];
    if (crewMembers !== null && crewMembers.length > 0) {
      const crewMembersStrings = crewMembers.split(",");
      crewMembersNumbers = crewMembersStrings.map(Number).filter((value: number) => !isNaN(value));
    }

    try {
      const newCrew: any = await this.crewsService.create({
        organizationId,
        name,
        crewLead,
      });

      for (const crewMember of crewMembersNumbers) {
        await this.crewMembersService.create({
          userId: crewMember,
          crewId: newCrew.id,
        });
      }

      return res.json({
        data: {
          crew: {
            ...newCrew.dataValues,
          },
        },
      });
    } catch (err) {
      console.log("something went wrong ", err);

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const crewId = parseInt(req.params.id, 10);

    const { name = null, crewMembers = null, crewLead = null } = req.body;

    if (crewLead !== null) {
      if (isNaN(crewLead)) {
        return res.status(400).json({
          error: "crewLead must be a number",
        });
      }
    }

    const patchUpdates: any = {};
    if (name) {
      patchUpdates.name = name;
    }

    if (crewLead) {
      patchUpdates.crewLead = crewLead;
    }

    try {
      await this.crewsService.update(patchUpdates, {
        where: {
          id: crewId,
        },
      });

      let crewMembersNumbers: number[] = [];
      if (crewMembers !== null && crewMembers.length > 0) {
        const crewMembersStrings = crewMembers.split(",");
        crewMembersNumbers = crewMembersStrings.map(Number).filter((value: number) => !isNaN(value));
      }

      // Update crew members
      let userIdsToRemove: number[] = [];
      let userIdsToAdd: number[] = [];
      if (crewMembersNumbers !== null && crewMembersNumbers.length > 0) {
        const existingCrewMembers = await this.crewMembersService.findAll({
          where: {
            crewId: crewId,
          },
        });

        const existingUserIds = existingCrewMembers.map((crewMember) => crewMember.userId);
        userIdsToRemove = existingUserIds.filter((id: number) => !crewMembersNumbers.includes(id));
        userIdsToAdd = crewMembersNumbers.filter((id) => !existingUserIds.includes(id));

        for (const userId of userIdsToRemove) {
          await this.crewMembersService.delete({
            userId: userId,
          });
        }

        for (const userId of userIdsToAdd) {
          await this.crewMembersService.create({
            userId: userId,
            crewId: crewId,
          });
        }
      }

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const crewId = parseInt(req.params.id, 10);

    try {
      await this.crewMembersService.delete({
        crewId: crewId,
      });

      await this.crewsService.delete({
        id: crewId,
      });

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }
}
