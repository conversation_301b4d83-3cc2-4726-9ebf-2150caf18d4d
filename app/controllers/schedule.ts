import { NextFunction, Request, Response } from "express";
import { ScheduleService } from "@/services/schedule";
import { UserScheduleEventService } from "@/services/userscheduleevent";
import { Op } from "sequelize";
import dayjs from "dayjs";
import { ScheduleEventNotificationService } from "@/services/scheduleeventnotification";
import { applyDateTimeChanges, calculateDateShift, isEventSingleDay } from "@/util/dateHelper";
import { UserScheduleEvent } from "../models";
import HTTPError from "../errors/HTTPError";
import { EquipmentScheduleEventService } from "@/services/equipmentscheduleevent";

export class ScheduleController {
  scheduleService: ScheduleService;
  userScheduleEventService: UserScheduleEventService;
  scheduleEventNotificationService: ScheduleEventNotificationService;
  equipmentScheduleEventService: EquipmentScheduleEventService;

  constructor() {
    this.scheduleService = new ScheduleService();
    this.userScheduleEventService = new UserScheduleEventService();
    this.scheduleEventNotificationService = new ScheduleEventNotificationService();
    this.equipmentScheduleEventService = new EquipmentScheduleEventService();

    // method this binds
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.deleteMany = this.deleteMany.bind(this);

    this.sendUpdateNotifications = this.sendUpdateNotifications.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const from = parseInt(req.query.from as string, 10);
    const to = parseInt(req.query.to as string, 10);
    const userId = parseInt(req.query.userId as string, 10);

    if (req.session.user.role === "WORKER") {
      if (userId == undefined || userId !== req.session.user.id) {
        return res.status(403).json({
          error: "userId missing or not matching current user",
        });
      }
    }

    const fromDate = from ? dayjs(from).toISOString() : undefined;
    const toDate = to ? dayjs(to).toISOString() : undefined;

    if (fromDate === undefined || toDate === undefined) {
      return res.status(400).json({
        message: "Invalid date range",
      });
    }

    const dateFilter: any = { [Op.between]: [fromDate, toDate] };
    const scheduleFilter: any = {
      organizationId,
      [Op.or]: [{ startTime: dateFilter }, { endTime: dateFilter }],
      isDeleted: false,
    };

    // if userId is passed, filter schedule events only for that user
    if (userId) {
      const userScheduleEvents = await UserScheduleEvent.findAll({
        where: { userId },
        attributes: ["scheduleEventId"],
      });

      const scheduleEventIds = userScheduleEvents.map((userScheduleEvent) => userScheduleEvent.scheduleEventId);
      scheduleFilter.id = scheduleEventIds;
    }

    try {
      // Get schedule events with equipment
      const scheduleEvents = await this.scheduleService.findAll({
        where: scheduleFilter,
      });

      return res.json({
        data: {
          scheduleEvents,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const {
      startTime,
      endTime,
      note,
      userIds,
      costCodeId,
      projectId,
      linkedEventId,
      notificationScheduledAt,
      notifyViaText,
      notifyViaPush,
      equipmentIds,
    } = req.body;

    const startTimeDate = new Date(parseInt(startTime));
    const endTimeDate = new Date(parseInt(endTime));
    if (startTimeDate >= endTimeDate) {
      return res.status(400).json({
        error: "Start time cannot be after end time",
      });
    }

    let userIdNumbers: number[] = [];
    if (userIds !== undefined && userIds.length > 0) {
      const userIdStrings = userIds.split(",");
      userIdNumbers = userIdStrings.map(Number).filter((value: number) => !isNaN(value));
    }

    let equipmentIdNumbers: number[] = [];
    if (equipmentIds !== undefined && equipmentIds.length > 0) {
      const equipmentIdStrings = equipmentIds.split(",");
      equipmentIdNumbers = equipmentIdStrings.map(Number).filter((value: number) => !isNaN(value));
    }

    let projectIdInt: number = null;
    if (!projectId) {
      return res.status(400).json({
        error: "Missing projectId in request body",
      });
    } else {
      if (isNaN(parseInt(projectId))) {
        return res.status(400).json({
          error: "Invalid projectId in request body",
        });
      } else {
        projectIdInt = parseInt(projectId);
      }
    }

    let costCodeIdInt: number = null;
    if (costCodeId) {
      if (isNaN(parseInt(costCodeId))) {
        return res.status(400).json({
          error: "Invalid costCodeId in request body",
        });
      } else {
        costCodeIdInt = parseInt(costCodeId);
      }
    }

    let notificationScheduledAtDate;

    try {
      notificationScheduledAtDate = await this.scheduleEventNotificationService.parseNotificationScheduledAt(
        notificationScheduledAt
      );
    } catch {
      return res.status(400).json({
        error: "Invalid notificationScheduledAt in request body",
      });
    }

    try {
      const scheduleObject = {
        organizationId,
        startTime: startTimeDate,
        endTime: endTimeDate,
        note,
        costCodeId: costCodeIdInt,
        projectId: projectIdInt,
        linkedEventId: linkedEventId ?? null,
        createdBy: req.session.user.id,
      };
      const newScheduleEvent = await this.scheduleService.create(scheduleObject, userIdNumbers);

      if (notificationScheduledAtDate) {
        await this.scheduleEventNotificationService.create({
          scheduleEventId: newScheduleEvent.id,
          notifyViaText: notifyViaText ?? true,
          notifyViaPush: notifyViaPush ?? true,
          userIds: userIdNumbers,
          scheduledAt: notificationScheduledAtDate,
          notificationType: "CREATED",
        });
      }

      // After creating the ScheduleEvent, add users to the event
      for (const userId of userIdNumbers) {
        await this.userScheduleEventService.create({
          userId: userId,
          scheduleEventId: newScheduleEvent.id,
        });
      }

      // Add equipment to the event
      for (const equipmentId of equipmentIdNumbers) {
        await this.equipmentScheduleEventService.create({
          equipmentId: equipmentId,
          scheduleEventId: newScheduleEvent.id,
        });
      }

      // Fetch the complete schedule event with all associations
      const completeScheduleEvent = await this.scheduleService.findOne({
        where: {
          id: newScheduleEvent.id,
        },
      });

      return res.json({
        data: {
          scheduleEvent: completeScheduleEvent,
        },
      });
    } catch (err) {
      if (err instanceof HTTPError) {
        return res.status(err.statusCode).json(err.toJSON());
      }
      console.log("something went wrong ", err);

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const scheduleEventId = parseInt(req.params.id, 10);
    const {
      startTime,
      endTime,
      note,
      userIds,
      costCodeId,
      projectId,
      notificationScheduledAt,
      notifyViaText = null,
      notifyViaPush = null,
      linkedEventId,
      addUsers,
      removeUsers,
      timezone,
      equipmentIds,
    } = req.body;

    let startTimeDate: Date,
      endTimeDate: Date = null;
    if (startTime) {
      startTimeDate = new Date(parseInt(startTime));
    }

    if (endTime) {
      endTimeDate = new Date(parseInt(endTime));
    }

    if (startTimeDate && endTimeDate && startTimeDate >= endTimeDate) {
      return res.status(400).json({
        error: "Start time cannot be after end time",
      });
    }

    let notificationScheduledAtDate;
    // mobile apps don't support setting notification time yet, so it will be undefined for requests from mobile
    try {
      notificationScheduledAtDate = await this.scheduleEventNotificationService.parseNotificationScheduledAt(
        notificationScheduledAt
      );
    } catch {
      return res.status(400).json({
        error: "Invalid notificationScheduledAt in request body",
      });
    }

    let userIdNumbers: number[] = null;
    if (userIds !== undefined && userIds.length > 0) {
      const userIdStrings = userIds.split(",");
      userIdNumbers = userIdStrings.map(Number).filter((value: number) => !isNaN(value));
    }

    let equipmentIdNumbers: number[] = null;
    if (equipmentIds !== undefined && equipmentIds.length > 0) {
      const equipmentIdStrings = equipmentIds.split(",");
      equipmentIdNumbers = equipmentIdStrings.map(Number).filter((value: number) => !isNaN(value));
    }

    let costCodeIdInt: number = null;
    if (costCodeId) {
      if (isNaN(parseInt(costCodeId))) {
        return res.status(400).json({
          error: "Invalid costCodeId in request body",
        });
      } else {
        costCodeIdInt = parseInt(costCodeId);
      }
    }

    let projectIdInt: number = null;
    if (projectId) {
      if (isNaN(parseInt(projectId))) {
        return res.status(400).json({
          error: "Invalid projectId in request body",
        });
      } else {
        projectIdInt = parseInt(projectId);
      }
    }

    const patchUpdates: any = {};
    if (startTimeDate) {
      patchUpdates.startTime = startTimeDate;
    }

    if (endTimeDate) {
      patchUpdates.endTime = endTimeDate;
    }

    if (note) {
      patchUpdates.note = note;
    }

    if (costCodeIdInt) {
      patchUpdates.costCodeId = costCodeIdInt;
    }

    if (projectIdInt) {
      patchUpdates.projectId = projectIdInt;
    }

    let mappedEventsToUpdatePayload = [];
    let usersToAddCollection = [];
    let usersToRemoveCollection = [];

    // implies multi schedule update
    if (linkedEventId) {
      const orgId = req.session.user.organizationId;

      // this has a call to a service - wrap in try catch
      try {
        mappedEventsToUpdatePayload = await this.generateMultiEventUpdatePayloads({
          organizationId: orgId,
          timezone,
          scheduleEventId,
          patchUpdates,
          startTimeDate,
          endTimeDate,
          linkedEventId,
          notificationDateTime: notificationScheduledAtDate,
        });
      } catch (err) {
        next(err);
      }
    } else {
      // implies single event update
      mappedEventsToUpdatePayload = [
        {
          id: scheduleEventId,
          updatedNotificationDateTime: notificationScheduledAtDate,
          ...patchUpdates,
        },
      ];
    }

    try {
      // this block of code handles legacy updates to schedule events where userIds is used instead of addUsers and removeUsers. should have specific errors staging if doing multi event update (linkedEventId is not null), then addUsers and removeUsers should be used instead of userIds
      let userIdsToRemove: number[] = [];
      let userIdsToAdd: number[] = [];
      let equipmentIdsToRemove: number[] = [];
      let equipmentIdsToAdd: number[] = [];

      if (userIdNumbers) {
        await this.scheduleService.update(patchUpdates, userIdNumbers, {
          where: {
            id: scheduleEventId,
          },
        });
        // Update user schedule events
        const existingUserScheduleEvents = await this.userScheduleEventService.findAll({
          where: {
            scheduleEventId: scheduleEventId,
          },
        });

        if (userIdNumbers !== null && userIdNumbers.length > 0) {
          const existingUserIds = existingUserScheduleEvents.map((event) => event.userId);
          userIdsToRemove = existingUserIds.filter((id: number) => !userIdNumbers.includes(id));
          userIdsToAdd = userIdNumbers.filter((id) => !existingUserIds.includes(id));
          for (const userId of userIdsToRemove) {
            await this.userScheduleEventService.delete(userId, scheduleEventId);
          }
          for (const userId of userIdsToAdd) {
            await this.userScheduleEventService.create({
              userId: userId,
              scheduleEventId: scheduleEventId,
            });
          }
        }

        // Cascade the update to any scheduled notifications for this event
        await this.scheduleEventNotificationService.handleScheduledNotificationsForEventUpdate(
          scheduleEventId,
          userIdsToAdd,
          userIdsToRemove,
          notificationScheduledAtDate,
          notifyViaText,
          notifyViaPush
        );
      } else {
        // we want to make sure only one invocation of update is called depending on legacy or new update
        if (addUsers) {
          usersToAddCollection = addUsers.split(",").map((e: string) => parseInt(e));
        }

        if (removeUsers) {
          usersToRemoveCollection = removeUsers.split(",").map((e: string) => parseInt(e));
        }

        await this.scheduleService.updateScheduleEventsAndUserScheduleEvents({
          scheduleEventsToUpdate: mappedEventsToUpdatePayload,
          usersToAdd: usersToAddCollection,
          usersToRemove: usersToRemoveCollection,
        });

        // handle notification updates for multiday events
        // To handle notification updates - will reuse handleScheduledNotificationsForEventUpdate but loop and pass diff params
        for (const event of mappedEventsToUpdatePayload) {
          await this.scheduleEventNotificationService.handleScheduledNotificationsForEventUpdate(
            event.id,
            usersToAddCollection,
            usersToRemoveCollection,
            event.updatedNotificationDateTime,
            notifyViaText,
            notifyViaPush
          );
        }
      }

      // if a blank string is provided remove all equipments
      if (equipmentIds === "") {
        await this.equipmentScheduleEventService.deleteAllEquipmentsFromScheduleEvent({
          scheduledEventId: scheduleEventId,
        });
      } else if (equipmentIdNumbers !== null) {
        // Update equipment schedule events if equipmentIds is provided
        const existingEquipmentScheduleEvents = await this.equipmentScheduleEventService.findAll({
          where: {
            scheduleEventId: scheduleEventId,
          },
        });

        const existingEquipmentIds = existingEquipmentScheduleEvents.map((event) => event.equipmentId);
        equipmentIdsToRemove = existingEquipmentIds.filter((id: number) => !equipmentIdNumbers.includes(id));
        equipmentIdsToAdd = equipmentIdNumbers.filter((id) => !existingEquipmentIds.includes(id));

        for (const equipmentId of equipmentIdsToRemove) {
          await this.equipmentScheduleEventService.delete(equipmentId, scheduleEventId);
        }

        for (const equipmentId of equipmentIdsToAdd) {
          await this.equipmentScheduleEventService.create({
            equipmentId: equipmentId,
            scheduleEventId: scheduleEventId,
          });
        }
      }

      // Fetch the updated schedule event with all associations
      const updatedScheduleEvent = await this.scheduleService.findOne({
        where: {
          id: scheduleEventId,
        },
      });

      if (updatedScheduleEvent === null) {
        return res.status(404).json({
          error: `The event does not exist.`,
        });
      }

      return res.json({
        data: {
          scheduleEvent: updatedScheduleEvent,
        },
      });
    } catch (error) {
      if (error instanceof HTTPError) {
        return res.status(error.statusCode).json(error.toJSON());
      }
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const scheduleEventId = parseInt(req.params.id, 10);

    try {
      await this.scheduleService.delete({
        where: {
          id: scheduleEventId,
        },
      });

      await this.scheduleEventNotificationService.handleScheduledNotificationsForEventDelete(scheduleEventId);

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async deleteMany(req: Request, res: Response, next: NextFunction) {
    const linkedEventId = req.query.linkedEventId;
    const organizationId = req.session.user.organizationId;

    if (linkedEventId === undefined) {
      return res.status(400).json({
        error: "Missing linkedEventId in request params",
      });
    }

    // query for all schedule events with the linkedEventId before deletion
    const scheduleEvents = await this.scheduleService.findAll({
      where: {
        linkedEventId,
        organizationId,
      },
    });

    try {
      const deleted = await this.scheduleService.delete({
        where: {
          linkedEventId,
        },
      });

      // handle pending notifications
      for (const scheduleEvent of scheduleEvents) {
        const pendingNotification = await this.scheduleEventNotificationService.fetchPendingNotification(
          scheduleEvent.id
        );
        if (pendingNotification) {
          await this.scheduleEventNotificationService.deletePendingNotifications(scheduleEvent.id);
        }
      }

      return res.json({
        deletedCount: deleted,
      });
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async sendUpdateNotifications(req: Request, res: Response, next: NextFunction) {
    const scheduleEventId = parseInt(req.params.id, 10);
    if (scheduleEventId === undefined) {
      return res.status(400).json({
        error: "Missing scheduleEventId in request params",
      });
    }

    try {
      await this.scheduleEventNotificationService.createNotificationBasedOnPastNotification(scheduleEventId, "UPDATED");

      return res.json();
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async generateMultiEventUpdatePayloads(data: Record<string, any>) {
    const {
      organizationId,
      timezone,
      scheduleEventId,
      patchUpdates,
      startTimeDate,
      endTimeDate,
      linkedEventId,
      notificationDateTime,
    } = data;

    if (!organizationId || !timezone || !scheduleEventId || !patchUpdates || !linkedEventId) {
      throw new Error(
        'Required data missing for multi event update: "organizationId", "timezone", "scheduleEventId", "patchUpdates", "linkedEventId"'
      );
    }

    const eventUpdatePayloads = [];

    // need the current event selected to calculate shifts
    const currentEvent = await this.scheduleService.findOne({ where: { id: scheduleEventId } });
    // calculate any date shifts
    const changedEventOriginallySingleDay = isEventSingleDay(currentEvent.startTime, currentEvent.endTime, timezone);
    const startTimeDateShift = calculateDateShift(timezone, currentEvent.startTime, startTimeDate || null);
    const endTimeDateShift = calculateDateShift(timezone, currentEvent.endTime, endTimeDate || null);

    // first query for all events with the linkedEventId
    const scheduleEventsToUpdate = await this.scheduleService.findAll({
      where: {
        linkedEventId,
        organizationId,
      },
    });

    // calculate the date shift for notifications once based on passed in scheduleEventId and notificationDateTime
    const anchorPendingNotification = await this.scheduleEventNotificationService.fetchPendingNotification(
      scheduleEventId
    );

    let notificationTimeDateShift: Record<string, number> = { days: null, hours: null, minutes: null };
    if (anchorPendingNotification) {
      notificationTimeDateShift = calculateDateShift(
        timezone,
        anchorPendingNotification.scheduledAt,
        notificationDateTime
      );
    }

    // loop through the events and apply the date shifts including potentially for notifications
    for (const event of scheduleEventsToUpdate) {
      const isExistingEventSingleDay = isEventSingleDay(event.startTime, event.endTime, timezone);

      const potentialNewStartDateTime = applyDateTimeChanges(
        timezone,
        event.startTime,
        isExistingEventSingleDay,
        changedEventOriginallySingleDay,
        startTimeDateShift
      );

      const potentialNewEndDateTime = applyDateTimeChanges(
        timezone,
        event.endTime,
        isExistingEventSingleDay,
        changedEventOriginallySingleDay,
        endTimeDateShift
      );

      const pendingNotification = await this.scheduleEventNotificationService.fetchPendingNotification(event.id);
      let newNotificationDateTime = undefined;

      if (pendingNotification) {
        // if there is a pending notification, then we need to update it
        newNotificationDateTime = applyDateTimeChanges(
          timezone,
          pendingNotification.scheduledAt,
          isExistingEventSingleDay,
          changedEventOriginallySingleDay,
          notificationTimeDateShift
        );
        // need to handle scenario to update the notification time for the event that is being updated
        // regardless of whether it has pending notification or not
        // this is an edge case for needing an anchor notification to calculate a date shift
      } else if (!pendingNotification && notificationDateTime && scheduleEventId === event.id) {
        newNotificationDateTime = notificationDateTime;
      }

      eventUpdatePayloads.push({
        ...patchUpdates,
        id: event.id,
        startTime: potentialNewEndDateTime > potentialNewStartDateTime ? potentialNewStartDateTime : event.startTime,
        endTime: potentialNewEndDateTime > potentialNewStartDateTime ? potentialNewEndDateTime : event.endTime,
        updatedNotificationDateTime: notificationDateTime === null ? null : newNotificationDateTime,
      });
    }

    return eventUpdatePayloads;
  }
}
