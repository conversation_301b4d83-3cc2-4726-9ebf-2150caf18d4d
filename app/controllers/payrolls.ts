import { NextFunction, Request, Response } from "express";
import { Op } from "sequelize";

import { CONTRACTOR_PAYMENT_CODE, PayloadEarning, PayloadReimbursement, PayrollsService } from "@/services/payrolls";
import { CheckService } from "@/services/check";
import { TimesheetsService } from "@/services/timesheets";
import TimeOffRequestService from "@/services/timeoffrequest";
import { TimesheetHistoryService } from "@/services/timesheethistory";

import Timesheet, { ExtendedTimesheet, UserEarningsSummary } from "@/models/timesheet";
import { TimeOffRequestStatus } from "@/models/timeoffrequest";
import { EarningRate, User } from "@/models";

import { convertDatesToTimestamps } from "@/util/dateHelper";
import {
  categorizeUserEarningsSummaries,
  getPayrollPeriodDates,
  mapExistingPayrollToPayloadData,
} from "@/util/payrollHelpers";

import { Payroll } from "@/types/check";

import sequelize from "@/lib/sequelize";
import { ExtendedOrganization } from "@/models/organization";
import { IntegrationSettingsService } from "@/services/integrations/settings";
import * as Sentry from "@sentry/node";
import { inngest } from "../queueing/client";
import { chunk } from "lodash";

export interface PayrollStitchedData {
  payrollId: string;
  periodStart: string;
  periodEnd: string;
  approvalDeadline: string;
  attributedTimesheets: ExtendedTimesheet[];
  hourlyEmployeeSummaries: UserEarningsSummary[];
  contractorSummaries: UserEarningsSummary[];
  salariedSummaries: UserEarningsSummary[];
  payroll: Payroll;
}

export interface UpdatePayrollPayload {
  periodStart?: string;
  periodEnd?: string;
  payday?: string;
  processingPeriod?: string;
  type?: string;
  payFrequency?: string;
  fundingMethod?: string;
  paySchedule?: string;
  earnings?: PayloadEarning[];
  reimbursements?: PayloadReimbursement[];
  // salariedEmployeesToSkip is an array of Check employee ids
  salariedEmployeesToSkip?: string[];
  paymentMethodOverride?: Record<string, string>;
}

export const salariedInclude = (periodStartTimestamp: number, periodEndTimestamp: number) => {
  return [
    {
      model: EarningRate,
      as: "earningRates",
      where: {
        type: "REG",
        period: "ANNUALLY",
        startDate: {
          [Op.lte]: periodEndTimestamp,
        },
        [Op.or]: [{ endDate: null }, { endDate: { [Op.gte]: periodStartTimestamp } }],
      },
      required: true,
    },
  ];
};

export class PayrollsController {
  private payrollsService: PayrollsService;
  private timesheetsService: TimesheetsService;
  private checkService: CheckService;
  private timeOffRequestService: TimeOffRequestService;
  private timesheetHistoryService: TimesheetHistoryService;
  private integrationSettingsService: IntegrationSettingsService;

  constructor() {
    this.payrollsService = new PayrollsService();
    this.timesheetsService = new TimesheetsService();
    this.checkService = new CheckService();
    this.timeOffRequestService = new TimeOffRequestService();
    this.timesheetHistoryService = new TimesheetHistoryService();
    this.integrationSettingsService = new IntegrationSettingsService();

    // bind methods
    this.create = this.create.bind(this);
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
    this.approve = this.approve.bind(this);
  }

  /**
   * Handles the request lifecycle for retrieving a single payroll.
   *
   * This method performs the following steps:
   * 1. Fetches the payroll from Check using the checkPayrollId.
   * 2. Reconciles any newly approved timesheets that should be part of this payroll.
   * 3. Processes and transforms the data into the shape expected by Check.
   * 4. Reconstructs reimbursement data for both employees and contractors.
   *
   * Note: We don't store reimbursement data locally, so when it comes back from Check,
   * we deconstruct and recreate it for both employees and contractors.
   *
   * This complex process ensures that the payroll data is up-to-date and correctly
   * formatted before being sent back to the client or used for further processing.
   */
  async getOne(req: Request, res: Response, next: NextFunction) {
    const payrollId = req.params.id;
    const organization = req.organization as ExtendedOrganization;

    try {
      const payroll = await this.payrollsService.getPayroll(payrollId);

      const payrollPeriodDates = await getPayrollPeriodDates(
        payroll.period_start,
        payroll.period_end,
        organization.paySchedule,
        organization.overtimeSettings.weekStartDay,
        organization.timezone
      );

      const { periodStartTimestamp, periodEndTimestamp } = payrollPeriodDates;

      const salariedEmployeesToSkip = payroll.metadata.salariedEmployeesToSkip
        ? JSON.parse(payroll.metadata.salariedEmployeesToSkip)
        : [];

      const payrollData = await this.payrollsService.processPayrollData({
        payroll,
        organization,
        periodDates: payrollPeriodDates,
        type: payroll.type,
        salariedEmployeesToSkip,
      });

      const { hourlyEmployeeSummaries, contractorSummaries } = categorizeUserEarningsSummaries(
        payrollData.userEarningsSummaries
      );

      const mappedExistingPayrollDataAsPayload = mapExistingPayrollToPayloadData(payrollData.payroll);

      const bulkEmployeeQueryString = this.payrollsService.generateBulkEmployeeQueryString({
        timesheets: payrollData.timesheets,
        salariedEmployees: payrollData.salariedEmployeeSummaries,
        earnings: mappedExistingPayrollDataAsPayload?.earnings || [],
        reimbursements: mappedExistingPayrollDataAsPayload?.reimbursements || [],
        timeOffRequests: payrollData.timeOffRequests,
      });

      const bulkContractorQueryString = this.payrollsService.generateBulkContractorQueryString({
        timesheets: payrollData.timesheets,
        reimbursements: mappedExistingPayrollDataAsPayload?.reimbursements || [],
      });

      // query all hammr user data for organization - this will be so we can "create" a summary object if needed such as the event where an non-hourly earning is added to an employee that doesn't have a summary object yet
      const allHammerUserData = await User.findAll({
        where: {
          organizationId: organization.id,
        },
        include: [
          {
            model: EarningRate,
            as: "earningRates",
            where: {
              active: true,
            },
            required: true,
          },
        ],
      });

      const allEmployeesData = await this.checkService.getAllEmployees(organization, bulkEmployeeQueryString);

      const allContractorsData = await this.checkService.getAllContractors(organization, bulkContractorQueryString);

      const extendedContractorSummaries = this.payrollsService.stitchReimbursementsToUserEarningsSummaries(
        contractorSummaries,
        payrollData.payroll,
        "contractor"
      );

      const latestPayroll = await this.payrollsService.handleUpdatePayroll({
        payrollId: payroll.id,
        existingPayrollData: payroll,
        payloadData: mappedExistingPayrollDataAsPayload,
        organization,
        checkEmployeeData: allEmployeesData?.length > 0 ? allEmployeesData : [],
        checkContractorData: allContractorsData?.length > 0 ? allContractorsData : [],
        timeOffRequests: payrollData.timeOffRequests,
        hourlyEmployeeSummaries,
        contractorSummaries: extendedContractorSummaries,
        salariedEmployeeSummaries: payrollData.salariedEmployeeSummaries,
        periodStartTimestamp,
        periodEndTimestamp,
        salariedEmployeesToSkip,
        perDiemReimbursements: payrollData.perDiemReimbursements,
      });

      const enhancedStitchedData = {
        ...this.payrollsService.stitchPayrollData({ ...payrollData, payroll: latestPayroll }, allHammerUserData),
        hasMissingPayrollUsers: payrollData.hasMissingPayrollUsers,
        usersMissingCheckIds: payrollData.usersMissingCheckIds,
        employeesNotFinishedOnboarding: payrollData.employeesNotFinishedOnboarding,
        contractorsNotFinishedOnboarding: payrollData.contractorsNotFinishedOnboarding,
        hasUnapprovedTimesheets: payrollData.hasUnapprovedTimesheets,
      };

      delete enhancedStitchedData.attributedTimesheets;

      return res.json({
        data: convertDatesToTimestamps(enhancedStitchedData),
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const { ...payloadData } = req.body;
    const organization = req.organization as ExtendedOrganization;
    const payrollType = payloadData?.type || "regular";

    const payrollPeriodDates = await getPayrollPeriodDates(
      payloadData.periodStart,
      payloadData.periodEnd,
      organization.paySchedule,
      organization.overtimeSettings.weekStartDay,
      organization.timezone
    );

    const { periodStartTimestamp, periodEndTimestamp } = payrollPeriodDates;

    try {
      const payrollData = await this.payrollsService.processPayrollData({
        organization,
        periodDates: payrollPeriodDates,
        type: payrollType,
      });

      const { hourlyEmployeeSummaries, contractorSummaries } = categorizeUserEarningsSummaries(
        payrollData.userEarningsSummaries
      );

      // should stop the process if there are no eligible attributed timesheets or salaried employees - this is a check for regular payrolls - offcycle could be empty
      if (
        payrollType === "regular" &&
        payrollData.timesheets.length === 0 &&
        payrollData.salariedEmployeeSummaries.length === 0 &&
        payrollData.timeOffRequests.length === 0
      ) {
        return res.status(400).json({
          error: "There are no approved timesheets or salaried employees to pay in this pay period",
        });
      }

      const bulkEmployeeQueryString = this.payrollsService.generateBulkEmployeeQueryString({
        timesheets: payrollData.timesheets,
        salariedEmployees: payrollData.salariedEmployeeSummaries,
        timeOffRequests: payrollData.timeOffRequests,
      });

      const bulkContractorQueryString = this.payrollsService.generateBulkContractorQueryString({
        timesheets: payrollData.timesheets,
        reimbursements: [],
      });

      // we query check for list of employees because of workplace requirement
      const allEmployeesData = await this.checkService.getAllEmployees(organization, bulkEmployeeQueryString);

      // query check for contractors
      const allContractorsData = await this.checkService.getAllContractors(organization, bulkContractorQueryString);

      // on web there is the ability to have contractor payments - src/pages/timesheets/index.tsx - it's an optional parameter for creating a payroll
      const createdCheckPayroll = await this.payrollsService.handleCreatePayroll({
        payloadData,
        organization,
        checkEmployeeData: allEmployeesData?.length > 0 ? allEmployeesData : [],
        checkContractorData: allContractorsData?.length > 0 ? allContractorsData : [],
        timeOffRequests: payrollData.timeOffRequests,
        hourlyEmployeeSummaries,
        contractorSummaries,
        salariedEmployeeSummaries: payrollData.salariedEmployeeSummaries,
        periodStartTimestamp,
        periodEndTimestamp,
      });

      const stitchedData = this.payrollsService.stitchPayrollData({
        ...payrollData,
        salariedEmployeeSummaries: payrollData.salariedEmployeeSummaries,
        payroll: createdCheckPayroll,
      });

      const extendedStitchedData = {
        ...stitchedData,
        hasUnapprovedTimesheets: payrollData.hasUnapprovedTimesheets,
      };

      delete extendedStitchedData.attributedTimesheets;

      return res.json({
        data: convertDatesToTimestamps(extendedStitchedData),
      });
    } catch (err) {
      next(err);
    }
  }

  // based on product spec - used to add reimbursement(s) to a payroll
  async update(req: Request, res: Response, next: NextFunction) {
    // send me the checkEmployeeId so I don't have to query the user object for it
    const { paymentMethodOverride, ...payloadData }: UpdatePayrollPayload = req.body;
    const payrollId = req.params.id;
    const organization = req.organization as ExtendedOrganization;

    try {
      const payroll = await this.payrollsService.getPayroll(payrollId);

      const payrollPeriodDates = await getPayrollPeriodDates(
        payroll.period_start,
        payroll.period_end,
        organization.paySchedule,
        organization.overtimeSettings.weekStartDay,
        organization.timezone
      );

      const { periodStartTimestamp, periodEndTimestamp } = payrollPeriodDates;

      const salariedEmployeesToSkip = payroll.metadata.salariedEmployeesToSkip
        ? JSON.parse(payroll.metadata.salariedEmployeesToSkip)
        : [];

      const additionalContractorPayments = payloadData?.earnings.filter(
        (earning: PayloadEarning) => earning.type === CONTRACTOR_PAYMENT_CODE
      );

      const payrollData = await this.payrollsService.processPayrollData({
        payroll,
        organization,
        periodDates: payrollPeriodDates,
        type: payroll.type,
        salariedEmployeesToSkip,
      });

      const { hourlyEmployeeSummaries, contractorSummaries } = categorizeUserEarningsSummaries(
        payrollData.userEarningsSummaries
      );

      const bulkEmployeeQueryString = this.payrollsService.generateBulkEmployeeQueryString({
        timesheets: payrollData.timesheets,
        salariedEmployees: payrollData.salariedEmployeeSummaries,
        earnings: payloadData?.earnings || [],
        reimbursements: payloadData?.reimbursements || [],
        timeOffRequests: payrollData.timeOffRequests,
      });

      const allEmployeesData = await this.checkService.getAllEmployees(organization, bulkEmployeeQueryString);

      const bulkContractorQueryString = this.payrollsService.generateBulkContractorQueryString({
        timesheets: payrollData.timesheets,
        reimbursements: payloadData?.reimbursements || [],
        additionalContractorPayments: additionalContractorPayments,
      });

      const allContractorsData = await this.checkService.getAllContractors(organization, bulkContractorQueryString);

      const updatedPayroll = await this.payrollsService.handleUpdatePayroll({
        payrollId: payroll.id,
        existingPayrollData: payroll,
        payloadData,
        organization,
        checkEmployeeData: allEmployeesData?.length > 0 ? allEmployeesData : [],
        checkContractorData: allContractorsData?.length > 0 ? allContractorsData : [],
        timeOffRequests: payrollData.timeOffRequests,
        hourlyEmployeeSummaries: hourlyEmployeeSummaries,
        contractorSummaries: contractorSummaries,
        salariedEmployeeSummaries: payrollData.salariedEmployeeSummaries,
        periodStartTimestamp,
        periodEndTimestamp,
        salariedEmployeesToSkip: payloadData?.salariedEmployeesToSkip || null,
        paymentMethodOverride: paymentMethodOverride || null,
        perDiemReimbursements: payrollData.perDiemReimbursements,
      });

      return res.json({
        data: updatedPayroll,
      });
    } catch (err) {
      next(err);
    }
  }

  async approve(req: Request, res: Response, next: NextFunction) {
    // gross amount is the current amount the client sees
    const payrollId = req.params.id;
    const organization = req.organization as ExtendedOrganization;
    const userId = req.session.user.id;

    // start transaction
    const transaction = await sequelize.transaction();

    try {
      const payroll = await this.payrollsService.getPayroll(payrollId);
      const payrollLastUpdatedAt: string = payroll.metadata?.updatedAt; // this is a string representation of a timestamp

      const payrollPeriodDates = await getPayrollPeriodDates(
        payroll.period_start,
        payroll.period_end,
        organization.paySchedule,
        organization.overtimeSettings.weekStartDay,
        organization.timezone
      );

      const payrollData = await this.payrollsService.processPayrollData({
        payroll,
        organization,
        periodDates: payrollPeriodDates,
        type: payroll.type,
      });

      // Convert payrollLastUpdatedAt to a Date object, or use current time if it's empty
      const payrollLastUpdatedDate = payrollLastUpdatedAt ? new Date(parseInt(payrollLastUpdatedAt, 10)) : new Date();

      // check if any of the timesheets are approved after the payroll was last updated
      const areAnyTimesheetsApprovedAfterPayrollUpdated = payrollData.timesheets.some(
        (timesheet) => timesheet.approvedAt && new Date(timesheet.approvedAt) > payrollLastUpdatedDate
      );

      if (areAnyTimesheetsApprovedAfterPayrollUpdated) {
        return res.status(409).json({
          error: "Timesheets have been approved after the payroll was last updated",
        });
      }

      // if there are salaried employees to skip, we need to filter them out of the timesheets to eventually be marked PAID
      const salariedEmployeesToSkip = payroll?.metadata?.salariedEmployeesToSkip
        ? JSON.parse(payroll.metadata.salariedEmployeesToSkip)
        : [];

      // add payrollId to all timesheets as part of this payroll
      // filter out any timesheets that are part of salaried employees to skip
      const timesheetIdsToUpdate = payrollData.timesheets
        .filter((timesheet) => !salariedEmployeesToSkip.includes(timesheet.user.checkEmployeeId))
        .map((timesheet) => timesheet.id);

      // call check's payroll approval endpoint
      const checkPayroll = await this.checkService.post(`/payrolls/${payrollId}/approve`, {});

      if (checkPayroll && checkPayroll?.approved_at) {
        // Fetch timesheets before updating them to get their previous state for timesheet history
        const existingTimesheets = await Timesheet.findAll({
          where: {
            id: { [Op.in]: timesheetIdsToUpdate },
          },
          transaction,
        });

        // update timesheets to PAID status
        await this.timesheetsService.update(
          { status: "PAID", checkPayrollId: payrollId },
          {
            where: {
              id: { [Op.in]: timesheetIdsToUpdate },
            },
            transaction,
          }
        );

        await Promise.all(
          existingTimesheets.map(async (timesheet) => {
            const timesheetHistoryObj = this.timesheetHistoryService.formatTimesheetHistory(
              timesheet.dataValues,
              { status: "PAID" },
              "UPDATE",
              userId
            );

            if (timesheetHistoryObj.history && timesheetHistoryObj.history !== "{}") {
              await this.timesheetHistoryService.create(timesheetHistoryObj, transaction);
            }
          })
        );

        try {
          // Sync timesheets into Quickbooks (for now). In the future, we will sync will every active integration
          const integrationEmployeesSync = await this.integrationSettingsService.getIntegrationObjectSetting(
            req.organization.id,
            "TIMESHEETS"
          );

          if (integrationEmployeesSync?.integration.isEnabled && integrationEmployeesSync?.isEnabled) {
            // splitting timesheets in 100 items chunks when calling the `integration/timesheets-sync` Inngest event due to this Express JS error:
            // {"expected":136760,"length":136760,"limit":102400,"message":"request entity too large","type":"entity.too.large"}
            chunk(timesheetIdsToUpdate, 100).forEach((chunk) => {
              inngest.send({
                name: "integration/timesheets-sync",
                data: {
                  timesheetIds: chunk,
                  organizationId: organization.id,
                },
              });
            });
          }
        } catch (error) {
          Sentry.captureException(error);
          console.log("Error sending timesheets to Inngest", error, timesheetIdsToUpdate);
        }

        // also update time off requests to PAID status
        await this.timeOffRequestService.staticUpdate(
          { status: TimeOffRequestStatus.PAID, checkPayrollId: payrollId },
          {
            where: {
              id: { [Op.in]: payrollData.timeOffRequests.map((timeOffRequest) => timeOffRequest.id) },
            },
            transaction,
          }
        );
      }

      await transaction.commit();

      return res.json({
        data: {
          payroll: checkPayroll,
        },
      });
    } catch (err) {
      // rollback transaction
      await transaction.rollback();

      next(err);
    }
  }
}
