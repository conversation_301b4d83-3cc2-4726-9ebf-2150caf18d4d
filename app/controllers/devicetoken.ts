import { NextFunction, Request, Response } from "express";
import { DeviceTokensService } from "@/services/devicetoken";

export class DeviceTokenController {
  deviceTokensService: DeviceTokensService;

  constructor() {
    this.deviceTokensService = new DeviceTokensService();

    this.create = this.create.bind(this);
    this.delete = this.delete.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const { token, platform } = req.body;
    const userId = req.session.user.id;

    if (!token || !platform) {
      return res.status(400).json({
        error: "Missing token or platform",
      });
    }

    const validPlatforms = ["IOS", "ANDROID"];
    if (platform && validPlatforms.indexOf(platform) === -1) {
      return res.status(400).json({
        error: "Invalid platform",
      });
    }

    if (!userId) {
      return res.status(400).json({
        error: "Missing user id in session",
      });
    }

    try {
      // check if device token already exists
      const existingDeviceToken = await this.deviceTokensService.findOne({
        token,
      });

      if (existingDeviceToken) {
        return res.json({
          data: {
            deviceToken: {
              ...existingDeviceToken.dataValues,
            },
          },
        });
      } else {
        const deviceToken: any = await this.deviceTokensService.create({
          token,
          userId,
          platform,
        });

        return res.json({
          data: {
            deviceToken: {
              ...deviceToken.dataValues,
            },
          },
        });
      }
    } catch (err) {
      console.log("something went wrong ", err);

      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        error: "Missing token",
      });
    }

    try {
      await this.deviceTokensService.delete({
        token: token,
      });

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }
}
