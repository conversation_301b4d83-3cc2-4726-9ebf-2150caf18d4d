import { NextFunction, Request, Response } from "express";
import { UserLocationService } from "@/services/userlocation";

export class UserLocationController {
  userLocationService: UserLocationService;

  constructor() {
    this.userLocationService = new UserLocationService();
    this.create = this.create.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    try {
      const organizationId = req.session.user.organizationId;
      const userLocation = await this.userLocationService.create({
        ...req.body,
        organizationId,
      });

      return res.json({
        data: { userLocation },
      });
    } catch (err) {
      next(err);
    }
  }
}
