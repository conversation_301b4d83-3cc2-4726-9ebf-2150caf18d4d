import { NextFunction, Request, Response } from "express";
import { round } from "lodash";
import sequelize from "@/lib/sequelize";
import { Op } from "sequelize";
import { Classification, User, UserClassification } from "../models";
import { convertDatesToTimestamps } from "@/util/dateHelper";
import { ExtendedUserClassification } from "@/models/userclassification";
import dayjs from "dayjs";
import { TimesheetsService } from "@/services/timesheets";

export class UserClassificationController {
  timesheetsService: TimesheetsService;

  constructor() {
    this.timesheetsService = new TimesheetsService();
    this.assign = this.assign.bind(this);
    this.unassign = this.unassign.bind(this);
    this.get = this.get.bind(this);
    this.update = this.update.bind(this);
  }

  async assign(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const organizationId = req.session.user.organizationId;
    const {
      classificationId,
      basePay = "0.00",
      fringePay = "0.00",
      userIds,
      startDate = new Date(),
      endDate = null,
    } = req.body;

    try {
      const classification = await Classification.findOne({
        where: { id: classificationId, organizationId },
        transaction,
      });

      // avoid assigning users if they are already assigned to that classification
      const alreadyAssignedUsers = await UserClassification.findAll({
        where: {
          classificationId,
          userId: userIds,
          organizationId,
          [Op.or]: [{ endDate: { [Op.gte]: new Date() } }, { endDate: null }],
        },
        raw: true,
        transaction,
      });

      const existingUserIds = alreadyAssignedUsers.map((au) => au.userId);
      const newUserIds = userIds.filter((userId: number) => !existingUserIds.includes(userId));

      await UserClassification.bulkCreate(
        newUserIds.map((userId: number) => ({
          classificationId,
          userId,
          wageTableId: classification.wageTableId,
          organizationId,
          basePay,
          fringePay,
          startDate,
          endDate,
        })),
        { transaction }
      );

      transaction.commit();

      return res.json({});
    } catch (err) {
      await transaction.rollback();
      next(err);
    }
  }

  async unassign(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const organizationId = req.session.user.organizationId;
    const { userClassificationIds, endDate = new Date() } = req.body;

    try {
      await UserClassification.update(
        {
          endDate,
        },
        {
          where: {
            id: { [Op.in]: userClassificationIds },
            organizationId,
          },
          transaction,
        }
      );

      await transaction.commit();

      return res.json({});
    } catch (err) {
      await transaction.rollback();
      next(err);
    }
  }

  // by default gets all user classifications for org - can filter by "active" via query parameter
  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const where: any = { organizationId };
    const active = req.query.active === "true";

    const wageTableId = parseInt(req.query.wageTableId as string, 10);

    if (wageTableId !== undefined && !isNaN(wageTableId)) {
      where.wageTableId = wageTableId;
    }

    const userIds: string = req.query.userIds ? (req.query.userIds as string) : null;
    const userIdArray = userIds ? userIds.split(",").map((id) => parseInt(id, 10)) : [];

    if (userIdArray != null && userIdArray.length > 0) {
      where.userId = { [Op.in]: userIdArray };
    }

    if (active) {
      where.startDate = { [Op.lte]: new Date() };
      // translates to: WHERE (endDate IS NULL OR endDate > current_timestamp)
      where.endDate = { [Op.or]: [null, { [Op.gt]: new Date() }] };
    }

    try {
      const userClassifications: ExtendedUserClassification[] = await UserClassification.findAll({
        where,
        include: [
          {
            model: Classification,
            where: {
              startDate: {
                [Op.lte]: new Date(),
              },
              // there's a case where a user may be assigned and then unassigned and then assigned again which will result in them
              // having a UserClassification record with an endDate, we want to ignore those as they are effectively unassigned for that classification
              [Op.or]: [{ endDate: { [Op.gte]: new Date() } }, { endDate: null }],
            },
            attributes: ["id", "name", "basePay", "fringePay"],
          },
          {
            model: User,
            attributes: ["id", "firstName", "lastName"],
          },
        ],
      });

      return res.json({
        data: {
          userClassifications: userClassifications.map((uc: ExtendedUserClassification) => {
            const convertedUc = convertDatesToTimestamps(uc.toJSON());

            return {
              ...convertedUc,
              classification: convertedUc.classification || null,
              user: convertedUc.user || null,
            };
          }),
        },
      });
    } catch (err) {
      console.log(err);

      next(err);
    }
  }

  /**
   * Update user classifications (Bulk update) - takes a collection of userClassifications
   * @param req - request body
   * @param res - response body
   */
  async update(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const organizationId = req.session.user.organizationId;
    const { userClassifications } = req.body;
    const effectiveDate = new Date(req.body.effectiveDate || new Date());
    const effectiveDateTimestamp = dayjs.tz(effectiveDate, req.organization.timezone).startOf("day").valueOf();
    const effectiveDateMinusOneMillisecond = effectiveDateTimestamp - 1;

    try {
      const updatedClassifications: any[] = [];
      const newClassifications = [];

      for (const obj of userClassifications) {
        const { userClassificationId, basePay = "0.00", fringePay = "0.00", ...updateData } = obj;

        // fetch the existing user classification and associated classification
        const existingUserClassification: ExtendedUserClassification = await UserClassification.findOne({
          where: { id: userClassificationId, organizationId },
          include: [{ model: Classification }],
          transaction,
        });

        if (!existingUserClassification) {
          throw new Error(`User classification with id ${userClassificationId} not found`);
        }

        const classification = existingUserClassification.classification;

        // parse basePay and fringePay to numbers, handling missing or malformed values
        const parsedBasePay = parseFloat(basePay) || 0;
        const parsedFringePay = parseFloat(fringePay) || 0;
        const parsedClassificationBasePay = parseFloat(classification.basePay) || 0;
        const parsedClassificationFringePay = parseFloat(classification.fringePay) || 0;

        // Fix floating-point precision issues by rounding to 2 decimal places
        const totalUserPay = round(parsedBasePay + parsedFringePay, 2);
        const totalClassificationPay = round(parsedClassificationBasePay + parsedClassificationFringePay, 2);

        // validation: ensure that the total of basePay and fringePay is not less than the classification numbers
        if (totalUserPay < totalClassificationPay) {
          throw new Error(
            `Total of basePay (${parsedBasePay}) and fringePay (${parsedFringePay}) cannot be less than the classification's total (${totalClassificationPay})`
          );
        }

        await this.timesheetsService.validateEffectiveDate(
          existingUserClassification.userId,
          organizationId,
          effectiveDate,
          req.organization.timezone,
          transaction
        );

        // update the existing user classification end date
        const [updatedCount, updatedUserClassifications] = await UserClassification.update(
          { endDate: effectiveDateMinusOneMillisecond },
          {
            where: { id: userClassificationId, organizationId },
            returning: true,
            transaction,
          }
        );

        if (updatedCount === 0) {
          throw new Error(`User classification with id ${userClassificationId} not found`);
        }

        updatedClassifications.push(updatedUserClassifications[0]);

        // create a new user classification with the new data
        const newClassification = await UserClassification.create(
          {
            ...updateData,
            id: undefined, // ensure a new ID is generated
            startDate: effectiveDate,
            endDate: null,
            basePay, // make sure the new basePay and fringePay are used
            fringePay,
            classificationId: classification.id,
            userId: existingUserClassification.userId,
            wageTableId: classification.wageTableId,
            organizationId,
          },
          { transaction }
        );

        newClassifications.push(newClassification);

        await this.timesheetsService.update(
          {
            userClassificationId: newClassification.id,
          },
          {
            where: {
              workerId: existingUserClassification.userId,
              organizationId,
              clockIn: {
                [Op.gte]: effectiveDate,
              },
              status: {
                [Op.ne]: "PAID",
              },
            },
            transaction,
          }
        );
      }

      await transaction.commit();
      res.json({
        data: {
          updatedClassifications,
          newClassifications,
        },
      });
    } catch (err) {
      console.log(err);

      await transaction.rollback();

      next(err);
    }
  }
}
