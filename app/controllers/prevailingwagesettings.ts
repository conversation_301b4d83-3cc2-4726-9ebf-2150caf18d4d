import { NextFunction, Request, Response } from "express";
import PrevailingWageSettings from "@/models/prevailingwagesettings";

export class PrevailingWageSettingsController {
  constructor() {
    this.update = this.update.bind(this);
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      await PrevailingWageSettings.update(request.body, {
        where: { organizationId: request.organization.id },
      });

      return response.json({});
    } catch (error) {
      next(error);
    }
  }
}
