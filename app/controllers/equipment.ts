import { NextFunction, Request, Response } from "express";
import { EquipmentService } from "@/services/equipment";
import { EquipmentCategoryService } from "@/services/equipmentcategory";
import EquipmentCategory from "@/models/equipmentcategory";
import {
  TimeSheet,
  User,
  Project,
  UserLocation,
  Equipment,
  Organization,
  FringeBenefitClassification,
  Classification,
  FringeBenefit,
  ScheduleEvent,
  ScheduleEventNotification,
} from "../models";
import { TimesheetsService } from "@/services/timesheets";
import { Op } from "sequelize";
import EquipmentScheduleEvent from "@/models/equipmentscheduleevent";
import { getTimesheetIncludes } from "@/util/timesheetHelper";
import dayjs from "dayjs";

export class EquipmentController {
  equipmentService: EquipmentService;
  equipmentCategoryService: EquipmentCategoryService;
  timesheetsService: TimesheetsService;

  constructor() {
    this.equipmentService = new EquipmentService();
    this.equipmentCategoryService = new EquipmentCategoryService();
    this.timesheetsService = new TimesheetsService();

    // method this binds
    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
  }

  async getTimesheetsData(equipment: Equipment, organization: Organization) {
    const timesheets = equipment.timesheets ?? [];
    // Get the first clock-in date for this project

    if (timesheets.length === 0) {
      return [];
    }

    const firstClockInDate = await this.timesheetsService.getFirstClockInDate(equipment.id, "equipmentId");

    // Enrich the timesheets with additional data
    const enrichedTimesheets = this.timesheetsService.enrichTimesheets(
      timesheets,
      organization.paySchedule.payFrequency
    );

    const fringeBenefitData = await FringeBenefitClassification.findAll({
      where: {
        organizationId: organization.id,
      },
      include: [
        {
          model: Classification,
          required: true,
        },
        {
          model: FringeBenefit,
          required: true,
        },
      ],
    });

    // sequencing this, we need to enrich the timesheets with fringe benefit classifications then do additional enrichment for histories/fringeHourlyContribution/etc
    const joinedTimesheetsData = this.timesheetsService.enrichTimesheetsWithFringeBenefitClassifications(
      enrichedTimesheets,
      fringeBenefitData
    );

    // Generate flattened and attributed timesheets
    const attributedTimesheets = this.timesheetsService.generateFlattenedAttributedTimesheets(
      joinedTimesheetsData,
      firstClockInDate.getTime(),
      organization
    );

    return attributedTimesheets;
  }

  mapEquipment(equipment: Equipment) {
    const mostRecentTimesheet = equipment.timesheets?.sort((a, b) => b.clockIn - a.clockIn)[0];

    const lastUsedBy = mostRecentTimesheet
      ? mostRecentTimesheet?.user?.firstName + " " + mostRecentTimesheet?.user?.lastName
      : null;
    const lastUsedOn = mostRecentTimesheet?.clockIn;
    const lastUsedProject = mostRecentTimesheet?.project?.name;
    const lastUsedLocation = mostRecentTimesheet?.userLocation?.findOne(
      (location: UserLocation) => location.locationEvent === "CLOCK_IN"
    );

    return {
      ...equipment,
      categoryName: equipment.category.name,
      lastUsedBy: lastUsedBy,
      lastUsedOn: lastUsedOn,
      lastUsedProject: lastUsedProject,
      lastUsedLocation: lastUsedLocation,
    };
  }

  getEquipmentSqIncludeObject() {
    const today = new Date();
    const oneYearAgo = dayjs(today).subtract(1, "year").toDate();

    return [
      {
        model: EquipmentCategory,
        as: "category",
        attributes: ["id", "name"],
      },
      {
        model: TimeSheet,
        as: "timesheets",
        required: false,
        include: [
          ...getTimesheetIncludes(oneYearAgo, today),
          {
            model: UserLocation,
          },
        ],
      },
    ];
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { includeIsArchived, categoryIds } = req.query;

    const calculatedCategoryIds = categoryIds ? (categoryIds as string).split(",").map(Number) : [];

    try {
      const equipment = await this.equipmentService.findAll({
        where: {
          organizationId,
          ...(includeIsArchived ? {} : { isArchived: false }),
          ...(calculatedCategoryIds.length > 0 ? { categoryId: { [Op.in]: calculatedCategoryIds } } : {}),
        },
        include: this.getEquipmentSqIncludeObject(),
        order: [["name", "ASC"]],
      });

      const equipmentWithTimesheetsData = await Promise.all(
        equipment.map(async (equipment: Equipment) => {
          const timesheetsData = await this.getTimesheetsData(equipment, req.organization);

          return this.mapEquipment({ ...equipment.dataValues, timesheets: timesheetsData });
        })
      );

      return res.json({
        data: {
          equipment: equipmentWithTimesheetsData,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    const equipmentId = parseInt(req.params.id, 10);

    try {
      const equipment = await this.equipmentService.findOne({
        where: {
          id: equipmentId,
        },
        include: [
          ...this.getEquipmentSqIncludeObject(),
          {
            model: EquipmentScheduleEvent,
            include: [
              {
                model: ScheduleEvent,
                include: [
                  {
                    model: User,
                    through: { attributes: [] as string[] },
                    attributes: ["id", "firstName", "lastName", "email", "position", "phone"],
                    required: true,
                  },
                  {
                    model: Project,
                    attributes: [
                      "id",
                      "name",
                      "address",
                      "location",
                      "isGeofenced",
                      "geofenceRadius",
                      "isPrevailingWage",
                      "wageTableId",
                    ],
                    required: true,
                  },
                  {
                    model: ScheduleEventNotification,
                    attributes: ["id", "sentAt", "scheduledAt", "notifyViaText", "notifyViaPush"],
                  },
                ],
              },
            ],
          },
        ],
      });

      const timesheetsData = await this.getTimesheetsData(equipment, req.organization);

      return res.json({
        data: {
          equipment: this.mapEquipment({ ...equipment.dataValues, timesheets: timesheetsData }),
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { name, categoryName, year, hourlyCost } = req.body;

    try {
      // Find or create the category
      const category = await this.equipmentCategoryService.findOrCreate(
        categoryName.trim().toLowerCase(),
        organizationId
      );

      const equipment = await this.equipmentService.create({
        name,
        categoryId: category.id,
        year: year ? parseInt(year, 10) : null,
        hourlyCost: parseFloat(hourlyCost),
        organizationId,
        isArchived: false,
      });

      const equipmentWithCategory = await this.equipmentService.findOne({
        where: { id: equipment.id },
        include: {
          model: EquipmentCategory,
          as: "category",
          attributes: ["id", "name"],
        },
      });

      return res.json({
        data: {
          equipment: this.mapEquipment(equipmentWithCategory.dataValues),
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const equipmentId = parseInt(req.params.id, 10);
    const { name, categoryName, year, hourlyCost, isArchived } = req.body;

    try {
      const updateData: any = {};

      if (name !== undefined) updateData.name = name;

      // Handle category updates if categoryName provided
      if (categoryName !== undefined) {
        const category = await this.equipmentCategoryService.findOrCreate(categoryName, organizationId);
        updateData.categoryId = category.id;
      }

      if (year !== undefined) updateData.year = year ? parseInt(year, 10) : null;
      if (hourlyCost !== undefined) updateData.hourlyCost = parseFloat(hourlyCost);
      if (isArchived !== undefined) updateData.isArchived = isArchived;

      await this.equipmentService.update(updateData, {
        where: {
          id: equipmentId,
          organizationId,
        },
      });

      const updatedEquipment = await this.equipmentService.findOne({
        where: {
          id: equipmentId,
          organizationId,
        },
        include: this.getEquipmentSqIncludeObject(),
      });

      return res.json({
        data: {
          equipment: this.mapEquipment(updatedEquipment.dataValues),
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const equipmentId = parseInt(req.params.id, 10);

    try {
      // Set isArchived to true instead of actually deleting
      await this.equipmentService.update(
        { isArchived: true },
        {
          where: {
            id: equipmentId,
            organizationId,
          },
        }
      );

      return res.json({
        data: {
          message: "Equipment deleted successfully",
        },
      });
    } catch (err) {
      next(err);
    }
  }
}
