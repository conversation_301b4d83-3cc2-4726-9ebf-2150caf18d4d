import { OvertimeSettingsService } from "@/services/overtimesettings";
import { NextFunction, Request, Response } from "express";

export class OvertimeSettingsController {
  overtimeSettingsService: OvertimeSettingsService;

  constructor() {
    this.overtimeSettingsService = new OvertimeSettingsService();

    this.update = this.update.bind(this);
    this.create = this.create.bind(this);
    this.delete = this.delete.bind(this);
    this.findAll = this.findAll.bind(this);
    this.findOne = this.findOne.bind(this);
  }

  async findAll(request: Request, response: Response, next: NextFunction) {
    try {
      const { query } = request;

      // Build the where clause with allowed filter parameters
      const whereClause: any = {
        organizationId: request.organization.id, // Always filter by organization for security
      };

      // Add other filters from query parameters if they exist
      // Add specific filters you want to allow
      const allowedFilters = [
        "name",
        "weeklyOvertimeEnabled",
        "dailyOvertimeEnabled",
        "dailyOvertimeThreshold",
        "weeklyOvertimeThreshold",
        "overtimeMultiplier",
        "weekStartDay",
        "dailyDoubleOvertimeEnabled",
        "dailyDoubleOvertimeThreshold",
        "overtimeDays",
        "doubleOvertimeDays",
        "isActive",
      ];

      for (const key of allowedFilters) {
        if (query[key] !== undefined) {
          // Convert string 'true'/'false' to boolean for boolean fields
          if (key.includes("Enabled")) {
            const value = query[key];
            if (typeof value === "string") {
              whereClause[key] = value.toLowerCase() === "true";
            } else if (Array.isArray(value) && value.length > 0 && typeof value[0] === "string") {
              // Handle case where the query parameter is an array
              whereClause[key] = value[0].toLowerCase() === "true";
            }
          } else {
            whereClause[key] = query[key];
          }
        }
      }

      const overtimeSettings = await this.overtimeSettingsService.findAll({
        where: whereClause,
      });

      return response.json({
        data: {
          overtimeSettings,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async findOne(request: Request, response: Response, next: NextFunction) {
    try {
      const overTimeSettingsId = parseInt(request.params.id);

      if (isNaN(overTimeSettingsId) || overTimeSettingsId <= 0) {
        return response.status(400).json({ error: "Invalid overtime settings ID" });
      }

      const overtimeSettings = await this.overtimeSettingsService.findOne({
        where: {
          id: overTimeSettingsId,
          organizationId: request.organization.id,
        },
      });

      if (!overtimeSettings) {
        return response.status(404).json({ error: "Overtime settings not found" });
      }

      return response.json({
        data: {
          overtimeSettings,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async update(request: Request, response: Response, next: NextFunction) {
    try {
      // if we are updating the global settings, "name" of overtime settings = default, then it should cascade the update to all the children overtime settings.
      const overTimeSettingsId = parseInt(request.params.id);

      if (isNaN(overTimeSettingsId) || overTimeSettingsId <= 0) {
        return response.status(400).json({ error: "Invalid overtime settings ID" });
      }

      const result = await this.overtimeSettingsService.update(
        overTimeSettingsId,
        request.body,
        request.organization.id
      );

      if (!result) {
        return response.status(404).json({ error: "Overtime settings not found or not part of your organization" });
      }

      return response.json({
        data: {
          overtimeSettings: result,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async create(request: Request, response: Response, next: NextFunction) {
    try {
      // what we want to do with creation is to mirror the default global settings to the new overtime settings instance.
      const { overtimeDistribution } = request.organization.overtimeSettings;

      const newOvertimeSettings = await this.overtimeSettingsService.create({
        ...request.body,
        organizationId: request.organization.id,
        overtimeDistribution,
      });

      return response.json({
        data: {
          overtimeSettings: newOvertimeSettings,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async delete(request: Request, response: Response, next: NextFunction) {
    try {
      const overTimeSettingsId = parseInt(request.params.id);

      if (isNaN(overTimeSettingsId) || overTimeSettingsId <= 0) {
        return response.status(400).json({ error: "Invalid overtime settings ID" });
      }

      const deleted = await this.overtimeSettingsService.delete(overTimeSettingsId, request.organization.id);

      if (!deleted) {
        return response.status(404).json({ error: "Overtime settings not found or not part of your organization" });
      }

      return response.json({ success: true });
    } catch (error) {
      next(error);
    }
  }
}
