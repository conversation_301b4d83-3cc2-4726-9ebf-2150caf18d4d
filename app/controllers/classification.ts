import { NextFunction, Request, Response } from "express";
import { CreateOptions, Op } from "sequelize";
import dayjs from "dayjs";
import sequelize from "@/lib/sequelize";

import { ClassificationService } from "@/services/classification";
import { UserClassificationsService } from "@/services/userclassifications";
import { ServiceOrchestrator } from "@/services/orchestrator";
import { TimesheetsService } from "@/services/timesheets";
import { CheckService } from "@/services/check";
import { Classification, UserClassification, FringeBenefitClassification } from "@/models";

import { convertDatesToTimestamps } from "@/util/dateHelper";
import { FringeBenefitClassificationsService } from "@/services/fringebenefitclassifications";

export class ClassificationController {
  classificationService: ClassificationService;
  userClassificationService: UserClassificationsService;
  checkService: CheckService;
  orchestratorService: ServiceOrchestrator;
  fringeBenefitClassificationsService: FringeBenefitClassificationsService;
  timesheetsService: TimesheetsService;

  constructor() {
    this.classificationService = new ClassificationService();
    this.userClassificationService = new UserClassificationsService();
    this.checkService = new CheckService();
    this.orchestratorService = new ServiceOrchestrator();
    this.fringeBenefitClassificationsService = new FringeBenefitClassificationsService();
    this.timesheetsService = new TimesheetsService();
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.update = this.update.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const where: any = { organizationId };

    const wageTableId = parseInt(req.query.wageTableId as string, 10);

    if (wageTableId !== undefined && !isNaN(wageTableId)) {
      where.wageTableId = wageTableId;
    }

    try {
      const classifications = await this.classificationService.findAll({
        where,
        raw: true,
      });

      const timestampClassifications = convertDatesToTimestamps(classifications);

      return res.json({
        data: {
          classifications: timestampClassifications,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const { isPayrollEnabled, checkCompanyId, id: organizationId } = req.organization;

    const { name, fringeBenefitClassifications, ...body } = req.body;

    try {
      const classification = await this.classificationService.create(
        { name, ...body, organizationId },
        { transaction }
      );

      if (isPayrollEnabled && checkCompanyId) {
        // query for existing list of classification names
        const existingClassifications = await this.classificationService.findAll({
          where: {
            name: name,
            organizationId,
          },
        });

        let existingClassification: Partial<Classification> = {};
        let regCheckEarningCode: any = {};
        let otCheckEarningCode: any = {};
        let checkDotEarningCode: any = {};
        if (existingClassifications.length > 0) {
          // we will need to take the existing checkRegEarningCodeId and checkOtEarningCodeId and re-use these for the new classification
          existingClassification = existingClassifications[0];
        } else {
          // we call check to create these new earning code and assign them to the new classification
          // create check earning codes - one for hourly and one for ot
          const checkRegEarningCodePayload = this.checkService.handleCreateCheckPayload(
            { ...req.body, checkCompanyId, type: "hourly" },
            "earning_codes",
            "POST"
          );

          const checkOtEarningCodePayload = this.checkService.handleCreateCheckPayload(
            { ...req.body, checkCompanyId, type: "overtime" },
            "earning_codes",
            "POST"
          );

          const checkDotEarningCodePayload = this.checkService.handleCreateCheckPayload(
            { ...req.body, checkCompanyId, type: "double_overtime" },
            "earning_codes",
            "POST"
          );

          regCheckEarningCode = await this.orchestratorService.forwardRequest(
            "check",
            "POST",
            "/earning_codes",
            checkRegEarningCodePayload
          );

          otCheckEarningCode = await this.orchestratorService.forwardRequest(
            "check",
            "POST",
            "/earning_codes",
            checkOtEarningCodePayload
          );

          checkDotEarningCode = await this.orchestratorService.forwardRequest(
            "check",
            "POST",
            "/earning_codes",
            checkDotEarningCodePayload
          );
        }

        // // update hammr with these new check earning codes
        await this.classificationService.update(
          {
            checkRegEarningCodeId:
              existingClassifications.length > 0
                ? existingClassification.checkRegEarningCodeId
                : regCheckEarningCode.id,
            checkOtEarningCodeId:
              existingClassifications.length > 0 ? existingClassification.checkOtEarningCodeId : otCheckEarningCode.id,
            checkDotEarningCodeId:
              existingClassifications.length > 0
                ? existingClassification.checkDotEarningCodeId
                : checkDotEarningCode.id,
          },
          {
            where: {
              id: classification.id,
            },
            transaction,
          }
        );
      }

      let fringeBenefitsData;

      if (fringeBenefitClassifications && fringeBenefitClassifications.length) {
        const fringeBenefitClassificationCreateOptions: CreateOptions = {
          returning: true,
          transaction: transaction,
        };
        fringeBenefitsData = await Promise.all(
          fringeBenefitClassifications.map(async ({ amount, fringeBenefitId, startDate }: any) => {
            const fringeBenefitClassificationObj = {
              amount,
              fringeBenefitId,
              classificationId: classification.id,
              organizationId,
              startDate,
            };

            return this.fringeBenefitClassificationsService.create(
              fringeBenefitClassificationObj,
              fringeBenefitClassificationCreateOptions
            );
          })
        );
      }

      await transaction.commit();

      const timestampClassification = convertDatesToTimestamps(classification);

      return res.json({
        data: {
          classification: timestampClassification.dataValues,
          fringes: fringeBenefitsData,
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }

  /*

    annotating this so we have context
    * classification is versioned so anytime an edit is made this method call will create a new classification
    * classification maps to check's earning codes therefore if the name changes we need to create new earning codes - this is conditioned based on the organization being a payroll user/customer with a checkCompanyId
    * essentially references to the checkEarningCode need to be maintained throughout any versioned classification edits

  */
  async update(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const id = parseInt(req.params.id, 10);

    const classificationObject = req.body;
    const { effectiveDate, name } = req.body;
    const { isPayrollEnabled, checkCompanyId } = req.organization;
    const effectiveDateMinusOneMillisecond = new Date(effectiveDate).getTime() - 1;

    const hasClassificationNameChanged = name !== req.targetedClassification.name;

    try {
      // before attempting to make any changes, we need to check if the effectiveDate collides with any timesheets paid out already
      // we need to query all user classifications belonging to this classification
      // and have the classifications via userClassificationId to query the timesheets
      const timesheets = await this.timesheetsService.findAll({
        where: {
          status: "PAID",
          organizationId: req.organization.id,
          clockIn: {
            [Op.gt]: effectiveDate,
          },
        },
        include: [
          {
            model: UserClassification,
            required: true,
            where: {
              classificationId: id,
            },
          },
        ],
        order: [["clockIn", "DESC"]],
      });

      if (timesheets.length > 0) {
        return res.status(400).json({
          error: `Effective Date cannot be earlier than ${dayjs(timesheets[0].clockIn)
            .tz(req.organization.timezone || "America/Los_Angeles")
            .format(
              "MMM D, YYYY"
            )}, as some employees have been paid at the old rate until this date. Please select a later date.`,
        });
      }

      const existingClassification = await this.classificationService.update(
        { endDate: new Date(effectiveDateMinusOneMillisecond) },
        {
          where: {
            id,
          },
          returning: true,
          transaction,
        }
      );

      const returningExistingClassification = (existingClassification as any)[1]?.[0]?.dataValues || {};

      const persistedClassificationData = {
        name: returningExistingClassification.name,
        basePay: returningExistingClassification.basePay,
        fringePay: returningExistingClassification.fringePay,
        wageTableId: returningExistingClassification.wageTableId,
        organizationId: returningExistingClassification.organizationId,
        checkRegEarningCodeId: returningExistingClassification.checkRegEarningCodeId,
        checkOtEarningCodeId: returningExistingClassification.checkOtEarningCodeId,
      };

      // if the name changed, we create new check earning codes and update the new classification
      // if not, existingClassification check ids will transfer to the new classification
      if (hasClassificationNameChanged && isPayrollEnabled && checkCompanyId) {
        // before we create we need to check if a classification with the same name already exists
        const existingClassifications = await this.classificationService.findAll({
          where: {
            name,
            organizationId: req.organization.id,
          },
        });

        let existingClassification: Partial<Classification> = {};
        let regCheckEarningCode: any = {};
        let otCheckEarningCode: any = {};
        let checkDotEarningCode: any = {};

        if (existingClassifications.length > 0) {
          // we will need to take the existing checkRegEarningCodeId and checkOtEarningCodeId and re-use these for the new classification
          existingClassification = existingClassifications[0];
        } else {
          const checkRegEarningCodePayload = this.checkService.handleCreateCheckPayload(
            { ...classificationObject, checkCompanyId, type: "hourly" },
            "earning_codes",
            "POST"
          );

          const checkOtEarningCodePayload = this.checkService.handleCreateCheckPayload(
            { ...classificationObject, checkCompanyId, type: "overtime" },
            "earning_codes",
            "POST"
          );

          const checkDotEarningCodePayload = this.checkService.handleCreateCheckPayload(
            { ...classificationObject, checkCompanyId, type: "double_overtime" },
            "earning_codes",
            "POST"
          );

          regCheckEarningCode = await this.orchestratorService.forwardRequest(
            "check",
            "POST",
            "/earning_codes",
            checkRegEarningCodePayload
          );

          otCheckEarningCode = await this.orchestratorService.forwardRequest(
            "check",
            "POST",
            "/earning_codes",
            checkOtEarningCodePayload
          );

          checkDotEarningCode = await this.orchestratorService.forwardRequest(
            "check",
            "POST",
            "/earning_codes",
            checkDotEarningCodePayload
          );
        }

        classificationObject.checkRegEarningCodeId =
          existingClassifications.length > 0 ? existingClassification.checkRegEarningCodeId : regCheckEarningCode.id;
        classificationObject.checkOtEarningCodeId =
          existingClassifications.length > 0 ? existingClassification.checkOtEarningCodeId : otCheckEarningCode.id;
        classificationObject.checkDotEarningCodeId =
          existingClassifications.length > 0 ? existingClassification.checkDotEarningCodeId : checkDotEarningCode.id;
      }

      const newClassification = await this.classificationService.create(
        {
          ...persistedClassificationData,
          ...classificationObject,
          startDate: effectiveDate,
        },
        { transaction }
      );

      // needs to update all the existing user classifications with the old id to have an endDate
      await UserClassification.update(
        { endDate: effectiveDateMinusOneMillisecond },
        { where: { classificationId: id }, transaction }
      );

      /*
        after creating a new classification version, we need to:
        1. Create new UserClassifications for all users who had the old classification
        2. Update unpaid timesheets to reference these new UserClassifications

        The process:
        - Bulk create new UserClassifications and get back the created records
        - For each old UserClassification, find its corresponding new one (matched by userId)
        - Update all future, unpaid timesheets to reference the new UserClassification

        Important conditions:
        - Only update timesheets with clockIn after effectiveDate
        - Skip any PAID timesheets to maintain payment history
        - Maintain transaction consistency throughout
      */

      const oldUserClassifications = await this.userClassificationService.findAll({
        where: { classificationId: id },
        raw: true,
      });

      // create new UserClassifications and store the result
      const newUserClassifications = await UserClassification.bulkCreate(
        oldUserClassifications.map((uc) => ({
          ...uc,
          classificationId: newClassification.id,
          basePay: newClassification.basePay,
          fringePay: newClassification.fringePay,
          startDate: effectiveDate,
        })),
        { transaction, returning: true }
      );

      // update timesheets for each user, mapping old to new classifications
      await Promise.all(
        oldUserClassifications.map(async (oldUC) => {
          const newUC = newUserClassifications.find((nuc) => nuc.userId === oldUC.userId);

          if (newUC) {
            await this.timesheetsService.update(
              { userClassificationId: newUC.id },
              {
                where: {
                  userClassificationId: oldUC.id,
                  clockIn: {
                    [Op.gt]: effectiveDate,
                  },
                  status: {
                    [Op.ne]: "PAID",
                  },
                },
                transaction,
              }
            );
          }
        })
      );

      // Fetch existing FringeBenefitClassifications
      const existingFringeBenefitClassifications = await FringeBenefitClassification.findAll({
        where: { classificationId: id },
        raw: true,
      });

      // Create new FringeBenefitClassifications
      if (existingFringeBenefitClassifications.length > 0) {
        await FringeBenefitClassification.bulkCreate(
          existingFringeBenefitClassifications.map((fbc) => ({
            ...fbc,
            classificationId: newClassification.id,
            startDate: effectiveDate,
          })),
          { transaction }
        );
      }

      await transaction.commit();

      return res.json({
        data: {
          classification: newClassification,
        },
      });
    } catch (err) {
      await transaction.rollback();

      next(err);
    }
  }
}
