import { NextFunction, Request, Response } from "express";
import { FeatureFlagsService } from "@/services/featureflags";
import { removeDeviceTokensFromSendbird } from "@/util/sendbird";
import { User } from "../models";
import { OrganizationsService } from "@/services/organizations";

export class FeatureFlagsController {
  featureFlagsService: FeatureFlagsService;
  organizationsService: OrganizationsService;

  constructor() {
    this.featureFlagsService = new FeatureFlagsService();
    this.organizationsService = new OrganizationsService();
    this.update = this.update.bind(this);
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    const { isPayrollEnabled = null, isSchedulingEnabled = null, isMessagingEnabled = null } = req.body;

    const patchUpdates: any = {};

    if (isPayrollEnabled !== null) {
      // only update to true if there is a checkCompanyId set for the organization
      // otherwise the web app will break if checkCompanyId is not set
      if (isPayrollEnabled) {
        const organization = await this.organizationsService.findOne({
          where: {
            id: organizationId,
          },
        });

        if (organization.checkCompanyId === null || organization.checkCompanyId.length === 0) {
          res.status(400).json({
            error: "Cannot enable payroll without a check company ID",
          });

          return;
        }
      }
      patchUpdates.isPayrollEnabled = isPayrollEnabled;
    }

    if (isSchedulingEnabled !== null) {
      patchUpdates.isSchedulingEnabled = isSchedulingEnabled;
    }

    if (isMessagingEnabled !== null) {
      patchUpdates.isMessagingEnabled = isMessagingEnabled;

      if (isMessagingEnabled == "false") {
        const users: any = await User.findAll({
          where: {
            organizationId,
          },
        });

        removeDeviceTokensFromSendbird(users);
      }
    }

    try {
      await this.featureFlagsService.update(patchUpdates, {
        where: {
          organizationId,
        },
      });

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }
}
