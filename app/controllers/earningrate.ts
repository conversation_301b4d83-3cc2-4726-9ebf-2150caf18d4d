import { NextFunction, Request, Response } from "express";

import { EarningRateService } from "@/services/earningrate";
import { ServiceOrchestrator } from "@/services/orchestrator";
import { CheckService } from "@/services/check";
import sequelize from "@/lib/sequelize";

export class EarningRatesController {
  orchestratorService: ServiceOrchestrator;
  earningRateService: EarningRateService;
  checkService: CheckService;

  constructor() {
    this.earningRateService = new EarningRateService();
    this.orchestratorService = new ServiceOrchestrator();
    // this is really only used to generate payload for check service - can be refatored later
    this.checkService = new CheckService();

    // method this binds
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
  }

  async getOne(req: Request, res: Response, next: NextFunction) {
    const id = req.params.id;

    try {
      const earningRate = await this.earningRateService.findOne({
        where: {
          id,
        },
      });

      return res.json({
        data: {
          earningRate,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const userId = parseInt(req.query.userId as string, 10);
    const organizationId = req.session.user.organizationId;

    try {
      const earningRates = await this.earningRateService.findAll({
        where: {
          organizationId,
          userId,
        },
      });

      return res.json({
        data: {
          earningRates,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  // this might need an update to handle salaried earning rates
  async create(req: Request, res: Response, next: NextFunction) {
    // amount should be sent as "string" with 2 decimals
    const { name, amount, userId, type = "REG", startDate = null, endDate = null, metadata = {} } = req.body;

    const parsedStartDate = startDate ? parseInt(startDate, 10) : null;
    const parsedEndDate = endDate ? parseInt(endDate, 10) : null;
    const orgId = req.session.user.organizationId;

    // start transaction
    const transaction = await sequelize.transaction();
    let checkEarningRate: any;

    try {
      const existinActivegEarningRate = await this.earningRateService.findOne({
        where: {
          userId,
          type,
          active: true,
        },
        transaction,
      });

      const earningRate = await this.earningRateService.create(
        {
          name,
          amount,
          period: "HOURLY",
          active: true,
          type,
          startDate: parsedStartDate,
          endDate: parsedEndDate,
          metadata,
          userId,
          organizationId: orgId,
        },
        { transaction }
      );

      // conditional this - only if the org is payrollEnabled
      if (req.organization.isPayrollEnabled) {
        const user = req.targetedUser; // assumption is that within the middleware stack, validation would have attached this to the req object

        // tag downstream existing active check earning rate to inactive
        if (existinActivegEarningRate) {
          await this.checkService.patch(`/earning_rates/${existinActivegEarningRate.checkEarningRateId}`, {
            active: false,
          });
        }

        // forward to orchestrator
        const checkEarningRatePayload = this.checkService.handleCreateCheckPayload(
          { ...req.body, checkEmployeeId: user.checkEmployeeId, period: "hourly" },
          "earning_rates",
          "POST"
        );

        checkEarningRate = await this.orchestratorService.forwardRequest(
          "check",
          "POST",
          "/earning_rates",
          checkEarningRatePayload
        );
      }

      // if there is an existing active earning rate, update hammr's version of earning rate to inactive and set end time if they dont have one
      if (existinActivegEarningRate) {
        const newEndDate = new Date().toISOString();
        await this.earningRateService.update(
          {
            active: false,
            // Use Sequelize.literal to conditionally set endDate only if it's currently null
            endDate: sequelize.literal(`CASE WHEN "end_date" IS NULL THEN '${newEndDate}' ELSE "end_date" END`),
          },
          { where: { id: existinActivegEarningRate.id }, transaction }
        );
      }

      // will need to update earning rate with checkEarningRateId
      const updateObj: any = {};

      if (checkEarningRate) {
        updateObj.checkEarningRateId = checkEarningRate.id;
      }

      const finalEarningRate: any[] = await this.earningRateService.update(updateObj, {
        where: { id: earningRate.id },
        transaction,
        returning: true,
      });

      await transaction.commit();

      return res.json({
        data: {
          earningRate: finalEarningRate[1][0],
        },
      });
    } catch (err) {
      await transaction.rollback();

      console.error("Error creating earning rate:", err);

      next(err);
    }
  }
}
