import { NextFunction, Request, Response } from "express";
import { runStripeIntegration } from "@/crons/stripe";

export class StripeController {
  constructor() {
    this.get = this.get.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const numberOfDaysInRequest = Number(req.query.numberOfDays);
    const numberOfDays = numberOfDaysInRequest ?? 1;

    try {
      await runStripeIntegration(numberOfDays);

      return res.json({});
    } catch (err) {
      next(err);
    }
  }
}
