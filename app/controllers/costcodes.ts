import { NextFunction, Request, Response } from "express";
import { CostCodesService } from "@/services/costcodes";
import * as Sentry from "@sentry/node";
export class CostCodesController {
  costCodesService: CostCodesService;

  constructor() {
    this.costCodesService = new CostCodesService();

    // method this binds
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.updateWorkersCompCode = this.updateWorkersCompCode.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    try {
      const costCodes = await this.costCodesService.findAll({
        where: {
          organizationId,
        },
      });

      return res.json({
        data: {
          costCodes,
        },
      });
    } catch (err) {
      Sentry.captureException(err);

      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { name, number } = req.body;

    if (!name) {
      return res.status(400).json({
        error: "Missing name",
      });
    }

    try {
      const newCostCode: any = await this.costCodesService.create({
        organizationId,
        name,
        number,
      });

      return res.json({
        data: {
          costCode: {
            ...newCostCode.dataValues,
          },
        },
      });
    } catch (err) {
      console.log("something went wrong ", err);

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const costCodeId = parseInt(req.params.id, 10);

    const { name, number } = req.body;

    const patchUpdates: any = {};
    if (name) {
      patchUpdates.name = name;
    }

    if (number != null) {
      patchUpdates.number = number;
    }

    try {
      await this.costCodesService.update(patchUpdates, {
        where: {
          id: costCodeId,
        },
      });

      return res.json({});
    } catch (error) {
      console.log("something went wrong ", error);

      next(error);
    }
  }

  async updateWorkersCompCode(req: Request, res: Response) {
    const costCodeId = parseInt(req.params.id, 10);
    const workersCompCodeId = req.body.workersCompCodeId ? parseInt(req.body.workersCompCodeId, 10) : null;

    await this.costCodesService.updateWorkersCompCode(costCodeId, workersCompCodeId);

    return res.json({});
  }
}
