import { NextFunction, Request, Response } from "express";
import { GlAccountMappingSettingsService } from "@/services/glaccountmappingsettings";

export class GlAccountMappingSettingsController {
  glAccountMappingSettingsService: GlAccountMappingSettingsService;

  constructor() {
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.glAccountMappingSettingsService = new GlAccountMappingSettingsService();
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;

    const integrationUserTokenId = Number(req.query.integrationUserTokenId);

    try {
      const glAccountMappingSettings = await this.glAccountMappingSettingsService.getOne(
        organizationId,
        integrationUserTokenId
      );

      return res.json({
        data: glAccountMappingSettings,
      });
    } catch (err) {
      console.log(err);

      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const userId = req.session.user.id;
    const organizationId = req.session.user.organizationId;

    try {
      const newGlAccountMappingSetting = await this.glAccountMappingSettingsService.create(
        req.body,
        userId,
        organizationId
      );

      if (!newGlAccountMappingSetting) {
        return res.status(400).json({
          error: "Failed to create gl account mapping setting",
        });
      }

      return res.status(201).json({
        data: {
          message: "Gl account mapping setting created successfully",
        },
      });
    } catch (err) {
      console.log(err);

      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const userId = req.session.user.id;
    const organizationId = req.session.user.organizationId;
    const glAccountMappingSettingId = Number(req.params.id);

    try {
      const updatedGlAccountMappingSetting = await this.glAccountMappingSettingsService.update(
        req.body,
        userId,
        glAccountMappingSettingId,
        organizationId
      );

      if (!updatedGlAccountMappingSetting) {
        return res.status(404).json({
          error: "Gl account mapping setting not found",
        });
      }

      return res.status(200).json({
        data: {
          message: "Gl account mapping setting updated successfully",
        },
      });
    } catch (err) {
      console.log(err);

      next(err);
    }
  }
}
