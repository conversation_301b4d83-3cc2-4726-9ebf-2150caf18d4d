import { CreateOptions, Op, UpdateOptions } from "sequelize";
import dayjs from "dayjs";
import { NextFunction, Request, Response } from "express";

import sequelize from "@/lib/sequelize";
import { Classification, FringeBenefit } from "@/models";
import { convertDatesToTimestamps } from "@/util/dateHelper";
import { FringeBenefitClassificationsService } from "@/services/fringebenefitclassifications";

export class FringeBenefitClassificationController {
  fringeBenefitClassificationsService: FringeBenefitClassificationsService;

  constructor() {
    this.fringeBenefitClassificationsService = new FringeBenefitClassificationsService();

    // methods
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const { fringeBenefitClassifications } = req.body;

    const fringeBenefitClassificationCreateOptions: CreateOptions = {
      returning: true,
    };

    try {
      const fringeBenefitsData = await Promise.all(
        fringeBenefitClassifications.map(async ({ amount, fringeBenefitId, classificationId, startDate }: any) => {
          const fringeBenefitClassificationObj = {
            amount,
            fringeBenefitId,
            classificationId,
            organizationId,
            startDate,
          };

          return this.fringeBenefitClassificationsService.create(
            fringeBenefitClassificationObj,
            fringeBenefitClassificationCreateOptions
          );
        })
      );

      res.json({
        data: {
          fringeBenefitClassifications: fringeBenefitsData,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async get(req: Request, res: Response) {
    // query parameter can accept either fringeBenefitId/classificationId/wageTableId
    const organizationId = req.session.user.organizationId;

    // Parse query parameters
    const fringeBenefitId = req.query.fringeBenefitId ? Number(req.query.fringeBenefitId) : undefined;
    const classificationId = req.query.classificationId ? Number(req.query.classificationId) : undefined;
    const wageTableId = req.query.wageTableId ? Number(req.query.wageTableId) : undefined;

    const currentDate = new Date();

    const fringeBenefitClassificationQuery: any = {
      where: {
        organizationId,
      },
      include: [
        {
          model: FringeBenefit,
        },
        {
          model: Classification,
          where: {
            startDate: {
              [Op.lte]: currentDate,
            },
            [Op.or]: [{ endDate: { [Op.gte]: currentDate } }, { endDate: null }],
          },
        },
      ],
      raw: true,
      nest: true,
    };

    if (fringeBenefitId) {
      fringeBenefitClassificationQuery.where.fringeBenefitId = fringeBenefitId;
    }

    if (classificationId) {
      fringeBenefitClassificationQuery.where.classificationId = classificationId;
    }

    if (wageTableId) {
      fringeBenefitClassificationQuery.include[0].where = { wageTableId };
    }

    const fringeBenefitClassifications = await this.fringeBenefitClassificationsService.findAll(
      fringeBenefitClassificationQuery
    );

    res.json({
      data: {
        fringeBenefitClassifications: fringeBenefitClassifications.map((fringeBenefitClassification) =>
          convertDatesToTimestamps(fringeBenefitClassification)
        ),
      },
    });
  }

  async getOne(req: Request, res: Response) {
    const fringeBenefitClassificationId = req.params.id;

    const fringeBenefitClassification = await this.fringeBenefitClassificationsService.findOne({
      where: {
        id: fringeBenefitClassificationId,
        organizationId: req.session.user.organizationId,
      },
      raw: true,
      nest: true,
    });

    res.json({
      data: {
        fringeBenefitClassification: convertDatesToTimestamps(fringeBenefitClassification),
      },
    });
  }

  // with versioning for fringe benefit classification, we want to set the end date of the current classification
  // then create a new one with the updated values
  async update(req: Request, res: Response, next: NextFunction) {
    const fringeBenefitClassificationId = req.params.id;
    const currentTimestamp = dayjs().valueOf();

    const timestampForEnd = dayjs(currentTimestamp).subtract(1, "millisecond").valueOf();
    const timestampForNewStart = dayjs(currentTimestamp).valueOf();

    // start transaction
    const transaction = await sequelize.transaction();

    const fringeBenefitClassificationUpdateOptions: UpdateOptions = {
      where: {
        id: fringeBenefitClassificationId,
      },
      transaction,
      returning: true,
    };

    try {
      const oldFringeBenefitClassification: any = await this.fringeBenefitClassificationsService.update(
        {
          endDate: timestampForEnd,
        },
        fringeBenefitClassificationUpdateOptions
      );

      const oldFringeBenefitClassificationDataValues = oldFringeBenefitClassification[1][0].dataValues;

      const newFringeBenefitClassificationPayload = {
        ...req.body,
        fringeBenefitId: oldFringeBenefitClassificationDataValues?.fringeBenefitId,
        classificationId: oldFringeBenefitClassificationDataValues?.classificationId,
        organizationId: oldFringeBenefitClassificationDataValues?.organizationId,
        startDate: timestampForNewStart,
        endDate: null,
      };

      const newFringeBenefitClassification = await this.fringeBenefitClassificationsService.create(
        newFringeBenefitClassificationPayload,
        { transaction }
      );

      await transaction.commit();

      res.json({
        data: {
          fringeBenefitClassification: convertDatesToTimestamps(newFringeBenefitClassification.dataValues),
        },
      });
    } catch (error) {
      await transaction.rollback();

      next(error);
    }
  }
}
