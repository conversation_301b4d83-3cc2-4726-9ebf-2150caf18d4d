import { NextFunction, Request, Response } from "express";
import { DailyReportService } from "@/services/daily-reports/dailyreport";
import { uploadFileToS3 } from "./sts";
import Project from "@/models/project";
import dayjs from "dayjs";
import { TimesheetListResponse, TimesheetsService } from "@/services/timesheets";
import { convertMinutesToHoursWorked } from "@/util/timeHelper";
import { WeatherService } from "@/services/weather";
import { v4 as uuidv4 } from "uuid";
import { Op, Sequelize } from "sequelize";
import User from "@/models/user";
import DailyReport from "@/models/dailyreport";

export class DailyReportController {
  dailyReportService: DailyReportService;
  timesheetsService: TimesheetsService;
  weatherService: WeatherService;

  constructor() {
    this.dailyReportService = new DailyReportService();
    this.timesheetsService = new TimesheetsService();
    this.weatherService = new WeatherService();
    this.create = this.create.bind(this);
    this.list = this.list.bind(this);
  }

  async create(req: Request, res: Response, next: NextFunction) {
    try {
      const { date, summary, injuryNotes, equipmentNotes, materialNotes, signOffName, projectId, selectedPhotoIds } =
        req.body;

      const createdBy = req.session.user.id;
      const organizationId = req.session.user.organizationId;
      let timezone = req.organization.timezone;
      const reportDate = dayjs.tz(date, timezone);

      const dailyReportObj = {
        date: reportDate.toDate(),
        summary,
        injuryNotes,
        equipmentNotes,
        materialNotes,
        signOffName,
        projectId,
        createdBy,
        organizationId,
      };

      const project = await Project.findOne({
        where: {
          organizationId,
          id: projectId,
        },
        raw: false,
        nest: true,
      });

      let stationMetadata;
      let weatherData;

      const isDayInFuture = reportDate.isAfter(dayjs(), "day");

      if (!isDayInFuture) {
        try {
          stationMetadata = await this.weatherService.getStationData({
            address: {
              coordinates: {
                long: project.location?.coordinates[0],
                lat: project.location?.coordinates[1],
              },
              streetAddress: project.address,
            },
          });

          timezone = stationMetadata.timezone;

          weatherData = await this.weatherService.getWeatherData({
            stationMetadata,
            date,
            timezone,
          });
        } catch (err) {
          console.log("err", err);
        }
      }

      const startOfDay = reportDate.startOf("day");
      const endOfDay = reportDate.endOf("day");

      const timesheets: TimesheetListResponse = await this.timesheetsService.list(
        req.organization,
        {
          from: startOfDay.toDate().getTime(),
          to: endOfDay.toDate().getTime(),
          projectId: String([projectId]),
        },
        false
      );

      const totalHours = convertMinutesToHoursWorked(timesheets.totalMinutes);

      const pdf = await this.dailyReportService.generatePdf({
        dailyReport: dailyReportObj,
        organization: req.organization,
        selectedPhotoIds,
        project,
        timesheets: timesheets.timesheets,
        totalHours,
        weatherData,
        timezone,
      });

      const dateString = dayjs(dailyReportObj.date).toISOString().split("T")[0];

      const reportName = `${dateString}-daily-report-${uuidv4()}.pdf`;

      await uploadFileToS3({
        file: pdf.content,
        key: reportName,
        organizationId,
        directory: "daily-reports",
      });

      const dailyReport = await this.dailyReportService.create({ ...dailyReportObj, objectId: reportName });

      return res.json({
        data: {
          dailyReport,
        },
      });
    } catch (err) {
      console.log(err, "err");
      next(err);
    }
  }

  async list(req: Request, res: Response, next: NextFunction) {
    try {
      // Get pagination parameters from query string
      const page = parseInt(req.query.page as string) || 1;
      const pageSize = parseInt(req.query.pageSize as string) || 100;
      const offset = (page - 1) * pageSize;
      const limit = pageSize;
      const organizationId = req.session.user.organizationId;

      if (offset < 0 || limit < 0) {
        return res.status(400).json({
          error: "Invalid page or pageSize",
        });
      }

      // Get sorting parameters from query string
      let sortArray: any = [["createdAt", "DESC"]];
      const sortField = req.query.sortField as string;
      const sortDirection = (req.query.sortDirection as string)?.toUpperCase() || "ASC";

      if (sortField) {
        if (sortField === "project") {
          sortArray = [[{ model: Project }, "name", sortDirection]];
        } else if (sortField === "createdBy") {
          sortArray = [[{ model: User }, "firstName", sortDirection]];
        } else if (sortField === "reportDate") {
          sortArray = [["date", sortDirection]];
        } else if (sortField === "createdAt") {
          sortArray = [["createdAt", sortDirection]];
        } else {
          sortArray = [[sortField, sortDirection]];
        }
      }

      const where: any = {
        organizationId,
      };

      // Handle projectIds parameter for filtering
      if (req.query.projectIds) {
        const projectIds = (req.query.projectIds as string).split(",").map((id) => parseInt(id.trim(), 10));
        where.projectId = {
          [Op.in]: projectIds.filter((id) => !isNaN(id)),
        };
      }

      // Handle userIds parameter for filtering
      if (req.query.userIds) {
        const userIds = (req.query.userIds as string).split(",").map((id) => parseInt(id.trim(), 10));
        where.createdBy = {
          [Op.in]: userIds.filter((id) => !isNaN(id)),
        };
      }

      // Handle search parameter
      if (req.query.search) {
        const searchTerm = req.query.search as string;
        where[Op.or] = [
          { "$project.name$": { [Op.iLike]: `%${searchTerm}%` } },
          { summary: { [Op.iLike]: `%${searchTerm}%` } },
          { injuryNotes: { [Op.iLike]: `%${searchTerm}%` } },
          { equipmentNotes: { [Op.iLike]: `%${searchTerm}%` } },
          { materialNotes: { [Op.iLike]: `%${searchTerm}%` } },
          { signOffName: { [Op.iLike]: `%${searchTerm}%` } },
          Sequelize.where(Sequelize.fn("TO_CHAR", Sequelize.col("date"), "MM/DD/YYYY"), {
            [Op.iLike]: `%${searchTerm}%`,
          }),
          Sequelize.where(
            Sequelize.fn("concat", Sequelize.col("user.first_name"), " ", Sequelize.col("user.last_name")),
            {
              [Op.iLike]: `%${searchTerm}%`,
            }
          ),
        ];
      }

      const dailyReports = await this.dailyReportService.findAll({
        where,
        offset,
        limit,
        order: sortArray,
        include: [
          {
            model: Project,
            attributes: ["id", "name"],
            required: true,
          },
          {
            model: User,
            attributes: ["id", "firstName", "lastName"],
            required: true,
          },
        ],
      });

      const allProjectsWithDailyReports = await Project.findAll({
        where: {
          organizationId,
        },
        include: [
          {
            model: DailyReport,
            attributes: ["id"],
            required: true,
          },
        ],
      });

      const allUsersWithDailyReports = await User.findAll({
        where: {
          organizationId,
        },
        include: [
          {
            model: DailyReport,
            attributes: ["id"],
            required: true,
          },
        ],
      });

      const totalCount = await this.dailyReportService.count({
        where: where,
        include: [
          {
            model: Project,
            attributes: ["id", "name"],
            required: true,
          },
          {
            model: User,
            attributes: ["id", "firstName", "lastName"],
            required: true,
          },
        ],
      });

      return res.json({
        data: {
          dailyReports,
          totalCount,
          availableProjectsToFilter: allProjectsWithDailyReports,
          availableUsersToFilter: allUsersWithDailyReports,
        },
      });
    } catch (err) {
      console.log("Error in daily report list: ", err);
      next(err);
    }
  }
}
