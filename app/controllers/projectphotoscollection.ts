import { NextFunction, Request, Response } from "express";
import { ProjectPhotosCollectionService } from "@/services/projectphotoscollection";
import { Op } from "sequelize";

export class ProjectPhotosCollectionController {
  projectPhotosCollectionService: ProjectPhotosCollectionService;

  constructor() {
    this.projectPhotosCollectionService = new ProjectPhotosCollectionService();
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const offset = parseInt(req.query.offset as string, 10) || 0;
    const limit = parseInt(req.query.limit as string, 10) || 20;
    const projectArchived =
      req.query.projectArchived === "true" ? true : req.query.projectArchived === "false" ? false : undefined;

    let projectIds: number[] = [];
    let userIds: number[] = [];
    let timesheetIds: number[] = [];

    if (req.query.projectId) {
      projectIds = Array.isArray(req.query.projectId)
        ? req.query.projectId.map((id) => parseInt(id as string, 10)).filter((id) => !isNaN(id))
        : (req.query.projectId as string)
            .split(",")
            .map((id) => parseInt(id, 10))
            .filter((id) => !isNaN(id));
    }

    if (req.query.userId) {
      userIds = Array.isArray(req.query.userId)
        ? req.query.userId.map((id) => parseInt(id as string, 10)).filter((id) => !isNaN(id))
        : (req.query.userId as string)
            .split(",")
            .map((id) => parseInt(id, 10))
            .filter((id) => !isNaN(id));
    }

    if (req.query.timesheetId) {
      timesheetIds = Array.isArray(req.query.timesheetId)
        ? req.query.timesheetId.map((id) => parseInt(id as string, 10)).filter((id) => !isNaN(id))
        : (req.query.timesheetId as string)
            .split(",")
            .map((id) => parseInt(id, 10))
            .filter((id) => !isNaN(id));
    }

    if (offset < 0 || limit < 0) {
      return res.status(400).json({
        error: "Invalid offset or limit",
      });
    }

    try {
      const where: any = {
        organizationId,
      };

      if (projectIds.length) {
        where.projectId = { [Op.in]: projectIds };
      }

      if (userIds.length) {
        where.created_by = { [Op.in]: userIds };
      }

      if (timesheetIds.length) {
        where.timesheetId = { [Op.in]: timesheetIds };
      }

      if (typeof projectArchived === "boolean") {
        where["$project.is_archived$"] = projectArchived;
      }

      const projectPhotosCollections = await this.projectPhotosCollectionService.findAll(
        {
          where,
          offset,
          limit,
          order: [["createdAt", "DESC"]],
        },
        true
      );

      const totalCount = await this.projectPhotosCollectionService.count({
        orgId: organizationId,
        projectIds,
        userIds,
        projectArchived,
      });

      return res.json({
        data: {
          totalCount,
          projectPhotosCollections,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    try {
      const { projectId, timesheetId, note } = req.body;
      const createdBy = req.session.user.id;

      if (!projectId) {
        return res.status(400).json({
          error: "Missing required fields",
        });
      }

      const newprojectPhotosCollection = await this.projectPhotosCollectionService.create({
        projectId,
        timesheetId,
        note,
        createdBy,
        organizationId,
      });

      const projectPhotosCollections = await this.projectPhotosCollectionService.findAll(
        {
          where: { id: newprojectPhotosCollection.id },
        },
        false
      );

      if (projectPhotosCollections.length === 0) {
        return res.status(400).json({
          error: "Something went wrong",
        });
      }

      const projectPhotosCollection = projectPhotosCollections[0];

      return res.json({
        data: {
          projectPhotosCollection,
        },
      });
    } catch (err) {
      next(err);
    }
  }
}
