import { NextFunction, Request, Response } from "express";

import { IntegrationSyncHistoryService } from "@/services/integrationsynchistory";
import { IntegrationUserTokensService } from "@/services/integrationusertokenservice";
import { IntegrationSyncHistory, IntegrationUserToken, User, EarningRate } from "../models";
import GlAccountMapping, { PAYROLL_CATEGORY_TYPES } from "@/models/glaccountmapping";
import { GL_ACCOUNT_MAPPING_SETTINGS } from "@/models/glaccountmappingsetting";
import GlAccountMappingSetting from "@/models/glaccountmappingsetting";
import { IntegrationUserTokensController } from "./integrationusertoken";
import { Op } from "sequelize";
import { createJournalEntry, getConnectionStatus } from "@/util/rutterHelper";
import { CompanyBenefitsService } from "@/services/companybenefits";
import { EmployeeBenefitsService } from "@/services/employeebenefits";
import { OrganizationsService } from "@/services/organizations";
import { OBJECT_TYPES } from "@/models/integrationobjectsetting";
import IntegrationObjectSetting from "@/models/integrationobjectsetting";

export class IntegrationSyncHistoryController {
  integrationSyncHistoryService: IntegrationSyncHistoryService;
  integrationUserTokenService: IntegrationUserTokensService;
  integrationUserTokenController: IntegrationUserTokensController;
  companyBenefitService: CompanyBenefitsService;
  employeeBenefitService: EmployeeBenefitsService;
  organizationsService: OrganizationsService;
  constructor() {
    this.get = this.get.bind(this);
    this.checkWebhook = this.checkWebhook.bind(this);
    this.integrationSyncHistoryService = new IntegrationSyncHistoryService();
    this.integrationUserTokenService = new IntegrationUserTokensService();
    this.integrationUserTokenController = new IntegrationUserTokensController();
    this.companyBenefitService = new CompanyBenefitsService();
    this.employeeBenefitService = new EmployeeBenefitsService();
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const integrationUserTokenId = Number(req.params.integrationUserTokenId);

    try {
      const integrationUserToken = await this.integrationUserTokenService.getById(integrationUserTokenId);

      if (!integrationUserToken) {
        return res.status(404).json({
          error: "Integration user token not found",
        });
      }

      const syncHistories = await this.integrationSyncHistoryService.findAll(integrationUserToken.id);

      return res.json({
        data: syncHistories,
      });
    } catch (err) {
      next(err);
    }
  }

  async checkWebhook(req: Request, res: Response) {
    try {
      const headers = req.headers;

      // get Check-Topic header
      const checkTopic = headers["check-topic"];

      // if checkTopic is payroll, process it
      if (checkTopic === "payroll") {
        const data = req.body.data;

        const payrollStatus = data.status;

        // only proceed if status is paid
        if (payrollStatus === "paid") {
          // get company id from data
          const companyId = data.company;
          const payrollId = data.id;

          // get org based on company id
          const organization = await this.organizationsService.findOne({
            where: {
              checkCompanyId: companyId,
            },
            include: [
              {
                association: "timeTrackingSettings",
                required: true,
              },
              {
                association: "overtimeSettings",
                required: true,
              },
              {
                association: "prevailingWageSettings",
                required: true,
              },
            ],
          });

          if (!organization) {
            throw new Error(`Company not found for id: ${companyId}`);
          }

          // check if this org has any integrations that are enabled and have auto sync enabled
          const integrationUserTokens = await IntegrationUserToken.findAll({
            where: {
              organizationId: organization.id,
              isEnabled: true,
              accessToken: {
                [Op.ne]: null, // manual integrations are not synced automatically
              },
            },
          });

          for (const integrationUserToken of integrationUserTokens) {
            // First check if the user has auto sync enabled for this integration
            const glAccountMappingSettings = await GlAccountMappingSetting.findOne({
              where: {
                organizationId: organization.id,
                integrationUserTokenId: integrationUserToken.id,
              },
            });

            if (!glAccountMappingSettings) {
              continue;
            }

            if (glAccountMappingSettings.autoSync === false) {
              continue;
            }

            // get the sync history for this integration user token
            const previousSyncHistory = await IntegrationSyncHistory.findOne({
              where: {
                integrationUserTokenId: integrationUserToken.id,
                payrollId,
              },
            });

            if (previousSyncHistory) {
              continue;
            }

            // before we proceed, check if the connection is still valid
            const connectionStatus = await getConnectionStatus(integrationUserToken.accessToken);

            if (!connectionStatus) {
              await this.integrationSyncHistoryService.createFailedSyncHistoryEntry(integrationUserToken.id, payrollId);
              throw new Error(`No connection status found for integration user token: ${integrationUserToken.id}`);
            }

            if (!connectionStatus.is_healthy) {
              await this.integrationSyncHistoryService.createFailedSyncHistoryEntry(integrationUserToken.id, payrollId);
              throw new Error(
                `Rutter connection for this integration is not healthy. IntegrationUserTokenId: ${integrationUserToken.id}`
              );
            }

            // get gl account mappings
            const glAccountMappings = await GlAccountMapping.findAll({
              where: {
                organizationId: organization.id,
                integrationUserTokenId: integrationUserToken.id,
              },
            });

            if (glAccountMappings.length === 0) {
              await this.integrationSyncHistoryService.createFailedSyncHistoryEntry(integrationUserToken.id, payrollId);
              throw new Error(`No GL account mappings found for organization: ${organization.id}`);
            }

            if (glAccountMappings.length !== PAYROLL_CATEGORY_TYPES.length) {
              throw new Error(
                `Not all payroll categories are mapped to a GL account for organization: ${organization.id}`
              );
            }

            const companyBenefits = await this.companyBenefitService.findAll({
              where: {
                organizationId: organization.id,
              },
            });

            const employeeBenefits = await this.employeeBenefitService.findAll({
              where: {
                organizationId: organization.id,
              },
            });

            let journalEntryBody;
            if (
              glAccountMappingSettings.consolidateJournalEntryBy ===
              GL_ACCOUNT_MAPPING_SETTINGS.consolidateJournalEntryBy.values[2].value
            ) {
              const users = await User.findAll({
                where: {
                  organizationId: organization.id,
                },
              });

              journalEntryBody = await this.integrationUserTokenService.mapPayrollToJournalEntryPerDepartment(
                data,
                glAccountMappings,
                users,
                companyBenefits,
                employeeBenefits
              );
            } else if (
              glAccountMappingSettings.consolidateJournalEntryBy ===
              GL_ACCOUNT_MAPPING_SETTINGS.consolidateJournalEntryBy.values[3].value
            ) {
              // Check if project syncing is enabled
              const projectSyncSetting = await IntegrationObjectSetting.findOne({
                where: {
                  integrationUserTokenId: integrationUserToken.id,
                  objectType: OBJECT_TYPES.PROJECTS,
                  isEnabled: true,
                },
              });

              if (!projectSyncSetting) {
                await this.integrationSyncHistoryService.createFailedSyncHistoryEntry(
                  integrationUserToken.id,
                  payrollId
                );
                continue;
              }

              const users = await User.findAll({
                where: {
                  organizationId: organization.id,
                },
                include: [
                  {
                    model: EarningRate,
                    required: false,
                    where: {
                      active: true,
                    },
                  },
                ],
              });

              journalEntryBody = await this.integrationUserTokenService.mapPayrollToJournalEntryPerProject(
                data,
                glAccountMappings,
                users,
                companyBenefits,
                employeeBenefits,
                organization
              );
            } else {
              journalEntryBody = await this.integrationUserTokenService.mapPayrollToJournalEntry(
                data,
                glAccountMappings,
                companyBenefits,
                employeeBenefits
              );
            }

            // we need to remove any 0 amounts from the journal entry
            journalEntryBody.journal_entry.line_items = journalEntryBody.journal_entry.line_items.filter(
              (item: any) => item.total_amount !== 0
            );

            try {
              await createJournalEntry(integrationUserToken.accessToken, journalEntryBody);
            } catch (err) {
              await this.integrationSyncHistoryService.createFailedSyncHistoryEntry(integrationUserToken.id, payrollId);
              continue;
            }

            // create sync history entry
            await this.integrationSyncHistoryService.create(integrationUserToken.id, payrollId);
          }
        }
      }

      return res.status(200).json({});
    } catch (err) {
      // return success so that the webhook is not retried
      return res.status(200).json({});
    }
  }
}
