import { Op } from "sequelize";
import { NextFunction, Request, Response } from "express";
import {
  Classification,
  CompanyBenefit,
  EmployeeBenefit,
  FringeBenefit,
  FringeBenefitClassification,
  User,
  UserClassification,
} from "@/models";
import { WageTableService } from "@/services/wagetable";
import { ExtendedUserClassification } from "@/models/userclassification";
import { UserClassificationsService } from "@/services/userclassifications";
import { convertDatesToTimestamps } from "@/util/dateHelper";
import { calculateCashFringeDiff, attributeHourlyFringeContribution } from "@/util/financial";
import sequelize from "@/lib/sequelize";
import { CheckService } from "@/services/check";
import { TimesheetsService } from "@/services/timesheets";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

export const wageTableInclude = ({ startDate = null, endDate = null }) => [
  {
    model: User,
    attributes: [
      "id",
      "firstName",
      "lastName",
      "checkEmployeeId",
      "phone",
      "workerClassification",
      "position",
      "gender",
      "ethnicity",
    ],
  },
  {
    model: User,
    attributes: [] as string[],
    include: [
      {
        model: EmployeeBenefit,
        required: false,
        where: {
          benefitStartDate: {
            [Op.lte]: endDate ? endDate : new Date(),
          },
          [Op.or]: [{ benefitEndDate: null }, { benefitEndDate: { [Op.gte]: startDate ? startDate : new Date() } }],
        },
        include: [
          {
            model: CompanyBenefit,
            attributes: [
              "name",
              "benefitProviderName",
              "benefitProviderAddress",
              "benefitProviderPhone",
              "category",
            ] as string[],
            as: "companyBenefit",
            where: {
              is_approved_fringe: true,
            },
          },
        ],
      },
    ],
  },
];

export class WageTableController {
  wageTableService: WageTableService;
  userClassificationsService: UserClassificationsService;
  checkService: CheckService;
  timesheetsService: TimesheetsService;

  constructor() {
    this.wageTableService = new WageTableService();
    this.userClassificationsService = new UserClassificationsService();
    this.checkService = new CheckService();
    this.timesheetsService = new TimesheetsService();

    // methods
    this.create = this.create.bind(this);
    this.get = this.get.bind(this);
    this.getOne = this.getOne.bind(this);
    this.update = this.update.bind(this);
    this.getBenefitsStatement = this.getBenefitsStatement.bind(this);
    this.getBenefitsReport = this.getBenefitsReport.bind(this);
  }

  private transformArray = (userClass: ExtendedUserClassification, payFrequency: string) => {
    const attributedBenefits = attributeHourlyFringeContribution(
      userClass,
      userClass.user.employeeBenefits,
      userClass.classification.fringeBenefitClassifications,
      payFrequency
    );

    const transformedUserClass = {
      id: userClass.id,
      basePay: userClass.basePay,
      fringePay: userClass.fringePay,
      wageTableId: userClass.wageTableId,
      userId: userClass.userId,
      classificationId: userClass.classificationId,
      startDate: userClass.startDate,
      endDate: userClass.endDate,
      user: {
        id: userClass.user.id,
        firstName: userClass.user.firstName,
        lastName: userClass.user.lastName,
      },
      classification: {
        id: userClass.classification.id,
        name: userClass.classification.name,
        basePay: parseFloat(userClass.classification.basePay).toFixed(2),
        fringePay: parseFloat(userClass.classification.fringePay).toFixed(2),
        startDate: userClass.classification.startDate,
        endDate: userClass.classification.endDate,
      },
      fringeBenefits: userClass.classification.fringeBenefitClassifications.map((fbc) => ({
        id: fbc.fringeBenefit.id,
        name: fbc.fringeBenefit.name,
        amount: fbc.amount,
        category: fbc.fringeBenefit.category,
        startDate: fbc.startDate,
        endDate: fbc.endDate,
      })),
      cashFringe: "0",
      employeeBenefits: attributedBenefits,
    };

    // at the end we need to calculate the cash fringe which is the userClass.classification.fringePay minus sum of all fringe benefits and minus the sum of all employee benefits
    const cashFringeDiff = calculateCashFringeDiff(
      transformedUserClass?.fringePay,
      transformedUserClass?.fringeBenefits,
      transformedUserClass?.employeeBenefits
    );

    transformedUserClass.cashFringe = ((cashFringeDiff * 100) / 100).toFixed(2);

    // so if we encounter a benefit with contributionType DYNAMIC, we need assign the cash fringe calculation to this benefit
    transformedUserClass.employeeBenefits = transformedUserClass.employeeBenefits.map((eb) => {
      if (eb.contributionType === "DYNAMIC") {
        const returnedEb = {
          ...eb,
          // not adding new properties and re-using fringeHourlyContribution keeps web the same - probably a good thing we can leave this here
          fringeHourlyContribution: transformedUserClass.cashFringe,
        };

        // set cashFringe to 0.00
        transformedUserClass.cashFringe = "0.00";

        return returnedEb;
      }

      return {
        ...eb,
        fringeHourlyContribution: eb.fringeHourlyContribution.toFixed(2),
      };
    }) as any;

    return transformedUserClass;
  };

  async getOne(req: Request, res: Response, next: NextFunction) {
    const id = parseInt(req.params.id, 10);

    try {
      const wageTable = await this.wageTableService.findOne({
        where: {
          id,
        },
      });

      return res.json({
        data: {
          wageTable: wageTable?.dataValues || null,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async get(req: Request, res: Response, next: NextFunction) {
    const organizationId = req.session.user.organizationId;
    const query: any = { organizationId };
    try {
      const wageTables = await this.wageTableService.findAll({
        where: query,
      });

      return res.json({
        data: {
          wageTables,
        },
      });
    } catch (err) {
      next(err);
    }
  }

  async create(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const organizationId = req.session.user.organizationId;

    try {
      const wageTable = await this.wageTableService.create({ ...req.body, organizationId }, { transaction });

      await transaction.commit();
      const timestampWageTable = convertDatesToTimestamps(wageTable);

      return res.json({
        data: {
          wageTable: timestampWageTable.dataValues,
        },
      });
    } catch (err) {
      await transaction.rollback();
      next(err);
    }
  }

  async update(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelize.transaction();
    const id = parseInt(req.params.id, 10);

    try {
      const wageTable = await this.wageTableService.update(req.body, {
        where: {
          id,
        },
        transaction,
        returning: true,
      });

      await transaction.commit();

      return res.json({
        data: {
          wageTable: wageTable[1][0] || {},
        },
      });
    } catch (err) {
      await transaction.rollback();
      next(err);
    }
  }

  async getBenefitsStatement(req: Request, res: Response) {
    const wageTableId = parseInt(req.params.id, 10);
    const organization = req.organization;

    try {
      const wageTableData: ExtendedUserClassification[] = await UserClassification.findAll({
        where: {
          wageTableId: wageTableId,
          organizationId: organization.id,
          startDate: {
            [Op.lte]: new Date(),
          },
          [Op.or]: [{ endDate: { [Op.gte]: new Date() } }, { endDate: null }],
        },
        include: [
          ...wageTableInclude({}),
          {
            model: Classification,
            // only return active classifications since we don't want to show inactive classifications on the fringe benefit statement page
            where: {
              startDate: {
                [Op.lte]: new Date(),
              },
              [Op.or]: [{ endDate: { [Op.gte]: new Date() } }, { endDate: null }],
            },
            include: [
              {
                model: FringeBenefitClassification,
                where: {
                  startDate: {
                    [Op.lte]: new Date(),
                  },
                  [Op.or]: [{ endDate: null }, { endDate: { [Op.gte]: new Date() } }],
                },
                attributes: ["amount"],
                required: false,
                include: [
                  {
                    model: FringeBenefit,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
        ],
      });

      // transform the result to wanted structure
      // we can flatten this structure as well
      const transformedData = wageTableData.map((userClass) =>
        this.transformArray(userClass, organization.paySchedule.payFrequency)
      );

      return res.json({
        data: {
          wageTableData: convertDatesToTimestamps(transformedData),
        },
      });
    } catch (error) {
      console.error("Error fetching wage table data:", error);
    }
  }

  async getBenefitsReport(req: Request, res: Response) {
    const wageTableId = parseInt(req.params.id, 10);
    const organization = req.organization;
    const checkCompanyId = req.organization.checkCompanyId;
    const checkCompany = await this.checkService.get(`/companies/${checkCompanyId}`);

    const { projectId, weekEnding } = req.query;

    const timesheetsTo = dayjs.tz(parseInt(weekEnding as string), checkCompany.timezone).endOf("day");
    const timesheetsFrom = timesheetsTo.subtract(6, "day").startOf("day");

    try {
      const wageTableData: ExtendedUserClassification[] = await UserClassification.findAll({
        where: {
          wageTableId: wageTableId,
          organizationId: organization.id,
          startDate: {
            [Op.lte]: timesheetsTo.toDate(),
          },
          [Op.or]: [{ endDate: { [Op.gte]: timesheetsFrom.toDate() } }, { endDate: null }],
        },
        include: [
          ...wageTableInclude({ startDate: timesheetsFrom.toDate(), endDate: timesheetsTo.toDate() }),
          {
            model: Classification,
            required: false,
            include: [
              {
                model: FringeBenefitClassification,
                where: {
                  startDate: {
                    [Op.lte]: timesheetsTo.toDate(),
                  },
                  [Op.or]: [{ endDate: null }, { endDate: { [Op.gte]: timesheetsFrom.toDate() } }],
                },
                required: false,
                include: [
                  {
                    model: FringeBenefit,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
        ],
      });

      // transform the result to wanted structure
      // we can flatten this structure as well
      const transformedData = wageTableData.map((userClass) =>
        this.transformArray(userClass, organization.paySchedule.payFrequency)
      );

      const companyName = checkCompany.legal_name;
      const companyAddress = `${checkCompany.address.line1}${
        checkCompany.address.line2 ? ", " + checkCompany.address.line2 : ""
      }, ${checkCompany.address.city}, ${checkCompany.address.state} ${checkCompany.address.postal_code}, ${
        checkCompany.address.country
      }`;

      const userTimesheets = await this.timesheetsService.findAll({
        where: {
          organizationId: organization.id,
          projectId,
          workerId: { [Op.in]: transformedData.map((userClass) => userClass.user.id) },
          clockIn: { [Op.gte]: timesheetsFrom.toDate() },
        },
      });

      const filteredData = transformedData.filter((userClass) =>
        userTimesheets.some((timesheet) => timesheet.workerId === userClass.user.id)
      );

      return res.json({
        data: {
          wageTableData: convertDatesToTimestamps(filteredData),
          companyName,
          companyAddress,
        },
      });
    } catch (error) {
      console.error("Error fetching wage table data:", error);
    }
  }
}
