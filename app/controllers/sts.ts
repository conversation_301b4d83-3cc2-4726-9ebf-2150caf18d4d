import * as Sentry from "@sentry/node";
import { Request, Response } from "express";
const AWS = require("aws-sdk");
AWS.config.update({ region: "us-west-1" });
const sts = new AWS.STS();
const ENVIRONMENT = process.env.ENVIRONMENT;
export const bucket = ENVIRONMENT === "PRODUCTION" ? "hammr-customer-files-production" : "hammr-customer-files-staging";

export class STSController {
  constructor() {
    this.get = this.get.bind(this);
  }

  async get(req: Request, res: Response) {
    const organizationId = req.session.user.organizationId;
    try {
      const credentials = await generateSTSToken(organizationId);

      if (credentials) {
        return res.json(credentials);
      }
    } catch (err) {
      console.error("Error generating STS token:", err);
      // manually capture error with Sentry here
      Sentry.captureException(err);
      res.status(500).send({
        error: "Failed to generate STS token",
      });
    }
  }
}

export async function generateSTSToken(organizationId: number) {
  const params = {
    DurationSeconds: 3600 * 36, // 36 hours is the maximum allowed by AWS
    Name: "s3-uploads",
    Policy: JSON.stringify({
      Version: "2012-10-17",
      Statement: [
        {
          Sid: "PutObjectPermission",
          Effect: "Allow",
          Action: ["s3:PutObject", "s3:GetObject"],
          Resource: [`arn:aws:s3:::${bucket}/${organizationId}/*`],
        },
      ],
    }),
  };

  try {
    const data = await sts.getFederationToken(params).promise();

    return data.Credentials;
  } catch (err) {
    console.error("Error generating STS token:", err);

    return null;
  }
}

export async function uploadFileToS3({
  file,
  key,
  organizationId,
  directory,
}: {
  file: Buffer;
  key: string;
  organizationId: number;
  directory?: string;
}) {
  const s3 = new AWS.S3();
  const params = {
    Bucket: `${bucket}/${organizationId}/${directory}`,
    Key: key,
    Body: file,
  };

  const data = await s3.putObject(params).promise();

  return data;
}

export async function getSignedUrl(params: any) {
  const s3 = new AWS.S3();
  const url = await new Promise((resolve, reject) => {
    s3.getSignedUrl("getObject", params, (err: any, url: string) => {
      if (err) reject(err);
      else resolve(url);
    });
  });

  return url;
}
