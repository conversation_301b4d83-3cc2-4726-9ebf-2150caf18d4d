{"name": "hammer-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "concurrently \"npm run start:inngest:dev\" \"nodemon app/index.ts\"", "dev:debug": "DEBUG=express:* nodemon app/index.ts", "build": "tsc -p tsconfig.production.json && tsc-alias -p tsconfig.production.json && cpx \"app/**/*.handlebars\" dist/ --preserve && cpx \"app/**/*.xlsx\" dist/ --preserve && npm run sentry:sourcemaps", "start": "node dist/index.js", "start:inngest:dev": "npx inngest-cli@latest dev -u http://localhost:3000/api/v1/inngest", "build-documentation": "./node_modules/.bin/apidoc -i ./app/routes/ -o api-docs", "test": "vitest", "coverage": "vitest run --coverage", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org hammr-inc --project backend ./dist && sentry-cli sourcemaps upload --org hammr-inc --project backend ./dist", "setup-tests": "chmod +x ./scripts/setup-test-db.sh && ./scripts/setup-test-db.sh"}, "lint-staged": {"app/**/*.{js,jsx,ts,tsx}": ["npx prettier --write", "npx eslint --fix"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@sentry/cli": "^2.39.1", "@sentry/node": "^8.42.0", "@sentry/profiling-node": "^8.42.0", "@turf/turf": "^6.5.0", "@types/express": "^4.17.14", "@types/node": "^18.7.23", "@types/sequelize": "^4.28.14", "apidoc": "^0.53.1", "aws-sdk": "^2.1462.0", "connect-sqlite3": "^0.9.13", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-stringify": "^6.2.3", "dayjs": "^1.11.12", "dotenv": "^16.0.3", "dotenv-expand": "^9.0.0", "exceljs": "^4.4.0", "express": "^4.18.1", "express-bearer-token": "^2.4.0", "express-rate-limit": "^6.7.0", "express-session": "^1.17.3", "firebase-admin": "^11.10.1", "handlebars": "^4.7.8", "http-proxy-middleware": "^2.0.6", "inngest": "^3.34.1", "joi": "^17.13.1", "jsreport": "^4.5.0", "loader-utils": ">=2.0.4", "lodash": "^4.17.21", "node-apn": "^3.0.0", "node-cron": "^3.0.2", "node-fetch": "^2.6.9", "nodemon": "^2.0.20", "otp-generator": "^4.0.0", "parseurl": "^1.3.3", "pdf-merger-js": "^4.3.1", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "phone": "^3.1.31", "puppeteer": "22.15.0", "sequelize": "^6.23.2", "stripe": "^13.8.0", "ts-node": "^10.9.1", "twilio": "^3.83.0", "typescript": "^4.8.4", "uuid": "^11.1.0", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.12", "@types/express-session": "^1.17.5", "@types/geojson": "^7946.0.10", "@types/jsreport": "^2.9.4", "@types/node-fetch": "^2.6.2", "@types/parseurl": "^1.3.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "@vitest/coverage-v8": "^3.0.9", "concurrently": "^9.1.2", "cpx": "^1.5.0", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "husky": "^8.0.2", "light-my-request": "^6.3.0", "prettier": "^2.8.0", "sequelize-cli": "^6.5.1", "tsc-alias": "^1.8.2", "tsconfig-paths": "^4.1.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9"}}