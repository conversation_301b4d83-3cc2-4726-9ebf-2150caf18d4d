import { afterEach, describe, expect, it } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import TimeTrackingSettings from "@/models/timetrackingsettings";

describe("TimeTrackingSettingsController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("PATCH /time-tracking-settings", () => {
    it("should update time tracking settings with valid data", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "/time-tracking-settings",
        payload: {
          allowWorkersToAddEditTime: false,
          areBreaksPaid: true,
          breakOptions: [0, 15, 30, 45],
          clockInReminderAt: "09:00:00",
          clockOutReminderAt: "17:00:00",
          isClockinClockoutPhotosEnabled: true,
        },
      });

      expect(response.statusCode).toBe(200);

      const updatedSettings = await TimeTrackingSettings.findOne({
        where: { organizationId: testOrganization.id },
      });

      expect(updatedSettings?.allowWorkersToAddEditTime).toBe(false);
      expect(updatedSettings?.areBreaksPaid).toBe(true);
      expect(updatedSettings?.breakOptions).toEqual([0, 15, 30, 45]);
      expect(updatedSettings?.clockInReminderAt).toBe("09:00:00");
      expect(updatedSettings?.clockOutReminderAt).toBe("17:00:00");
      expect(updatedSettings?.isClockinClockoutPhotosEnabled).toBe(true);
    });

    it("should return 400 for invalid clockInReminderAt time format", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "time-tracking-settings",
        payload: {
          clockInReminderAt: "invalid-time",
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "clockInReminderAt must be in HH:mm:ss+HH format",
      });
    });

    it("should return 400 for invalid clockOutReminderAt time format", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "time-tracking-settings",
        payload: {
          clockOutReminderAt: "25:00:00", // Invalid hour
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "clockOutReminderAt must be in HH:mm:ss format",
      });
    });

    it("should handle empty breakOptions", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "time-tracking-settings",
        payload: {
          breakOptions: [],
        },
      });

      expect(response.statusCode).toBe(200);

      const updatedSettings = await TimeTrackingSettings.findOne({
        where: { organizationId: testOrganization.id },
      });
      expect(updatedSettings?.breakOptions).toEqual([]);
    });

    it("should return 400 for invalid breakOptions format", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "time-tracking-settings",
        payload: {
          breakOptions: ["15", "30", "invalid"],
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "breakOptions must be an array of numbers",
      });
    });

    it("should return 400 for invalid realtimeBreakStartReminderAt format", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "time-tracking-settings",
        payload: {
          realtimeBreakStartReminderAt: "invalid-time",
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "realtimeBreakStartReminderAt must be in HH:mm:ss+HH format",
      });
    });
  });
});
