import { afterEach, beforeEach, describe, expect, it, MockedFunction, vi } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import CompanyBenefit from "@/models/companybenefit";
import EmployeeBenefit from "@/models/employeebenefit";
import Organization from "@/models/organization";
import { CheckService } from "@/services/check";
import fetch from "node-fetch";
import WorkersCompCode from "@/models/workersCompCode";
import { Express } from "express";

const mockCheckWAWorkplace = {
  id: "workplace_123",
  address: {
    line1: "123 Main St",
    city: "Seattle",
    state: "WA",
    postal_code: "98101",
    country: "US",
  },
};

const mockCheckNonWAWorkplace = {
  id: "workplace_1234",
  address: {
    state: "CA",
    country: "US",
  },
};

const mockCheckEmployee = {
  id: "check_emp_123",
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  workplaces: [mockCheckNonWAWorkplace.id],
};

describe("UsersController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  vi.mock("node-fetch");
  (fetch as any as MockedFunction<any>).mockImplementation((url: string) => {
    if (url.includes("sendbird")) {
      return { ok: true };
    }
  });

  describe("GET /users/:id/employee-benefits", () => {
    it("should return employee benefits for a user", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a test company benefit
      const companyBenefit = await CompanyBenefit.create({
        name: "Test Company Benefit",
        category: "MEDICAL",
        contributionType: "PERCENT",
        companyContributionPercent: 80,
        employeeContributionPercent: 20,
        isApprovedFringe: true,
        organizationId: testOrganization.id,
      });

      // Create a test employee benefit
      const employeeBenefit = await EmployeeBenefit.create({
        name: "Test Employee Benefit",
        contributionType: "PERCENT",
        companyContributionPercent: 80,
        employeeContributionPercent: 20,
        contributionFrequency: "MONTHLY",
        benefitStartDate: new Date().toISOString().split("T")[0],
        companyBenefitId: companyBenefit.id,
        userId: testUser.id,
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `users/${testUser.id}/employee-benefits`,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);

      expect(responseBody.data).toBeDefined();
      expect(responseBody.data.employeeBenefits).toBeDefined();
      expect(responseBody.data.employeeBenefits.length).toBe(1);

      const returnedBenefit = responseBody.data.employeeBenefits[0];
      expect(returnedBenefit.id).toBe(employeeBenefit.id);
      expect(returnedBenefit.name).toBe("Test Employee Benefit");
      expect(returnedBenefit.contributionType).toBe("PERCENT");
      expect(returnedBenefit.companyContributionPercent).toBe(80);
      expect(returnedBenefit.employeeContributionPercent).toBe(20);

      // Verify company benefit data
      expect(returnedBenefit.companyBenefit).toBeDefined();
      expect(returnedBenefit.companyBenefit.id).toBe(companyBenefit.id);
      expect(returnedBenefit.companyBenefit.name).toBe("Test Company Benefit");
      expect(returnedBenefit.companyBenefit.isApprovedFringe).toBe(true);
    });

    it("should return empty array when user has no benefits", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "GET",
        url: `users/${testUser.id}/employee-benefits`,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.data.employeeBenefits).toEqual([]);
    });

    it("should not return benefits from other organizations", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create another organization's benefit
      const otherOrg = await Organization.create({
        name: "Other Org",
      });

      const otherCompanyBenefit = await CompanyBenefit.create({
        name: "Other Company Benefit",
        category: "MEDICAL",
        organizationId: otherOrg.id,
        contributionType: "PERCENT",
      });

      await EmployeeBenefit.create({
        name: "Other Employee Benefit",
        companyBenefitId: otherCompanyBenefit.id,
        userId: testUser.id,
        organizationId: otherOrg.id,
        contributionType: "PERCENT",
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `users/${testUser.id}/employee-benefits`,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.data.employeeBenefits).toEqual([]);
    });
  });

  describe("PATCH /users/:id", () => {

    it("should save the new user details", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const checkService = {
        loadAllPaginatedData: vi.spyOn(CheckService.prototype, "loadAllPaginatedData").mockResolvedValueOnce([]),
        get: vi.spyOn(CheckService.prototype, "get").mockResolvedValueOnce(mockCheckEmployee),
        patch: vi.spyOn(CheckService.prototype, "patch"),
      };

      const workersCompCode = await WorkersCompCode.create({
        name: 'workers comp code',
        code: "23",
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/users/${testUser.id}`,
        payload: {
          workersCompCodeId: workersCompCode.id
        },
      });

      expect(response.statusCode).toBe(200);

      // TODO add checks for more fields to see if they were correctly saved in DB
    });

  });

  describe("POST /users", () => {

    const basicFields = {
      shouldAddToPayroll: true,
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      workplaces: ["workplace_123"],
      role: "WORKER",
      hourlyRate: 20,
    };

    it("should create user", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const checkService = {
        post: vi.spyOn(CheckService.prototype, "post").mockResolvedValue(mockCheckEmployee),
      };

      const workersCompCode = await WorkersCompCode.create({
        name: "Valid Code",
        code: "123-456",
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: "/users",
        payload: {
          ...basicFields,
          organizationId: testOrganization.id,
          workersCompCodeId: workersCompCode.id,
          companyDefinedAttributes: [
            { name: "wa_ltc_exemption", value: "false", effective_start: "2024-01-01" },
            { name: "labor_and_industries_total_rate", value: "1.1593" },
            { name: "wa_soc_code", value: "12-3456" },
            { name: "labor_and_industries_employee_rate", value: "0.2500" },
            { name: "wa_corporate_officer", value: "false" },
          ],
        },
      });

      expect(response.statusCode).toBe(200);
      expect(checkService.post).toHaveBeenCalledWith("/employees", expect.objectContaining({
        first_name: "John",
        last_name: "Doe",
        workplaces: ["workplace_123"],
      }));
    });

  });

});
