import { afterEach, describe, expect, it, vi } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import WorkersCompCode from "@/models/workersCompCode";
import CostCode from "@/models/costcode";
import User from "@/models/user";
import { CheckService } from "@/services/check";

describe("WorkersCompCodeController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
    vi.clearAllMocks();
  });

  describe("GET /workers-comp-codes", () => {
    it("should return only unarchived worker comp codes by default", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      await WorkersCompCode.create({
        name: "Active Code",
        code: "AC1",
        organizationId: testOrganization.id,
        isArchived: false,
      });

      await WorkersCompCode.create({
        name: "Archived Code",
        code: "AC2",
        organizationId: testOrganization.id,
        isArchived: true,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: "/workers-comp-codes",
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload).data;
      expect(data.length).toBe(1);
      expect(data[0].name).toBe("Active Code");
    });

    it("should return both archived and un-archived results includeArchived is true", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      await WorkersCompCode.create({
        name: "Active Code",
        code: "AC1",
        organizationId: testOrganization.id,
        isArchived: false,
      });

      await WorkersCompCode.create({
        name: "Archived Code",
        code: "AC2",
        organizationId: testOrganization.id,
        isArchived: true,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: "/workers-comp-codes?includeArchived=true",
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload).data;
      expect(data.length).toBe(2);
    });

  });

  describe("POST /workers-comp-codes", () => {
    it("should create a new worker comp code", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "POST",
        url: "/workers-comp-codes",
        payload: {
          name: "New Code",
          code: "NC1",
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload).data;
      expect(data.name).toBe("New Code");
      expect(data.code).toBe("NC1");
      expect(data.isArchived).toBe(false);
    });
  });

  describe("PATCH /workers-comp-codes/:id", () => {
    it("should update an existing worker comp code including archive status", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const workersCompCode = await WorkersCompCode.create({
        name: "Original Name",
        code: "ON1",
        organizationId: testOrganization.id,
        isArchived: false,
      });

      const costCode = await CostCode.create({
        name: "Cost Code",
        number: "CC1",
        organizationId: testOrganization.id,
        workersCompCodeId: workersCompCode.id,
      });

      await testUser.update({ workersCompCodeId: workersCompCode.id });

      expect(testUser?.workersCompCodeId).toBe(workersCompCode.id);
      expect(costCode?.workersCompCodeId).toBe(workersCompCode.id);

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/workers-comp-codes/${workersCompCode.id}`,
        payload: {
          name: "Updated Name",
          code: "UN1",
          isArchived: true,
        },
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload).data;
      expect(data.name).toBe("Updated Name");
      expect(data.code).toBe("UN1");
      expect(data.isArchived).toBe(true);

      // Verify it doesn't show up in regular GET
      const getResponse = await makeRequest(app, {
        method: "GET",
        url: "/workers-comp-codes",
      });

      const getData = JSON.parse(getResponse.payload).data;
      expect(getData.length).toBe(0);

      // Verify that references were cleaned up
      await testUser.reload();
      const updatedCostCode = await CostCode.findByPk(costCode.id);

      expect(testUser?.workersCompCodeId).toBeNull();
      expect(updatedCostCode?.workersCompCodeId).toBeNull();
    });

    it("should return 404 for non-existent worker comp code", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "/workers-comp-codes/999999",
        payload: {
          name: "Updated Name",
          code: "UN1",
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });

  });

  describe("PATCH /workers-comp-codes/:id/cost-codes", () => {
    it("should assign multiple cost codes to a worker comp code", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Code",
        code: "TC1",
        organizationId: testOrganization.id,
      });

      const costCode1 = await CostCode.create({
        name: "Cost Code 1",
        number: "CC1",
        organizationId: testOrganization.id,
      });

      const costCode2 = await CostCode.create({
        name: "Cost Code 2",
        number: "CC2",
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/workers-comp-codes/${workersCompCode.id}/cost-codes`,
        payload: {
          costCodeIds: [costCode1.id, costCode2.id],
        },
      });

      expect(response.statusCode).toBe(200);

      // Verify cost codes were assigned
      const updatedCostCode1 = await CostCode.findByPk(costCode1.id);
      const updatedCostCode2 = await CostCode.findByPk(costCode2.id);

      expect(updatedCostCode1?.workersCompCodeId).toBe(workersCompCode.id);
      expect(updatedCostCode2?.workersCompCodeId).toBe(workersCompCode.id);
    });

    it("should unassign existing cost codes and assign new ones", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Code",
        code: "TC1",
        organizationId: testOrganization.id,
      });

      // Create three cost codes
      const costCode1 = await CostCode.create({
        name: "Cost Code 1",
        number: "CC1",
        organizationId: testOrganization.id,
        workersCompCodeId: workersCompCode.id,  // Initially assigned
      });

      const costCode2 = await CostCode.create({
        name: "Cost Code 2",
        number: "CC2",
        organizationId: testOrganization.id,
        workersCompCodeId: workersCompCode.id,  // Initially assigned
      });

      const costCode3 = await CostCode.create({
        name: "Cost Code 3",
        number: "CC3",
        organizationId: testOrganization.id,
      });

      // Reassign to only costCode1 and costCode3
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/workers-comp-codes/${workersCompCode.id}/cost-codes`,
        payload: {
          costCodeIds: [costCode1.id, costCode3.id],
        },
      });

      expect(response.statusCode).toBe(200);

      // Verify assignments
      const updatedCostCode1 = await CostCode.findByPk(costCode1.id);
      const updatedCostCode2 = await CostCode.findByPk(costCode2.id);
      const updatedCostCode3 = await CostCode.findByPk(costCode3.id);

      expect(updatedCostCode1?.workersCompCodeId).toBe(workersCompCode.id);
      expect(updatedCostCode2?.workersCompCodeId).toBeNull();  // Should be unassigned
      expect(updatedCostCode3?.workersCompCodeId).toBe(workersCompCode.id);
    });

    it("should unassign all cost codes when empty array is provided", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Code",
        code: "TC1",
        organizationId: testOrganization.id,
      });

      // Create cost codes with initial assignments
      const costCode1 = await CostCode.create({
        name: "Cost Code 1",
        number: "CC1",
        organizationId: testOrganization.id,
        workersCompCodeId: workersCompCode.id,
      });

      const costCode2 = await CostCode.create({
        name: "Cost Code 2",
        number: "CC2",
        organizationId: testOrganization.id,
        workersCompCodeId: workersCompCode.id,
      });

      // Send empty array to unassign all
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/workers-comp-codes/${workersCompCode.id}/cost-codes`,
        payload: {
          costCodeIds: [],
        },
      });

      expect(response.statusCode).toBe(200);

      // Verify all assignments were removed
      const updatedCostCode1 = await CostCode.findByPk(costCode1.id);
      const updatedCostCode2 = await CostCode.findByPk(costCode2.id);

      expect(updatedCostCode1?.workersCompCodeId).toBeNull();
      expect(updatedCostCode2?.workersCompCodeId).toBeNull();
    });

    it("should return 404 for non-existent worker comp code", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "/workers-comp-codes/999999/cost-codes",
        payload: {
          costCodeIds: [1, 2],
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });

    it("should return 400 for non-existent cost codes", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Code",
        code: "TC1",
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/workers-comp-codes/${workersCompCode.id}/cost-codes`,
        payload: {
          costCodeIds: [999999, 999998],
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
    });

    it("should validate all cost code IDs exist", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create worker comp code
      const workersCompCode = await WorkersCompCode.create({
        name: "Test Code",
        code: "TC1",
        organizationId: testOrganization.id,
      });

      // Create one valid cost code
      const costCode = await CostCode.create({
        name: "Cost Code 1",
        number: "CC1",
        organizationId: testOrganization.id,
      });

      // Test with one valid and one non-existent cost code ID
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/workers-comp-codes/${workersCompCode.id}/cost-codes`,
        payload: {
          costCodeIds: [costCode.id, 99999], // 99999 is non-existent
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload).error).toEqual("costCodeIds contains an invalid value");

      // Verify no assignments were made
      const updatedCostCode = await CostCode.findByPk(costCode.id);
      expect(updatedCostCode?.workersCompCodeId).toBeNull();
    });

  });

  describe("PATCH /workers-comp-codes/assign-workers", () => {
    it("should assign workers to different worker comp codes", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({});

      const workersCompCode1 = await WorkersCompCode.create({
        name: "Code 1",
        code: "WC1",
        organizationId: testOrganization.id,
      });

      const workersCompCode2 = await WorkersCompCode.create({
        name: "Code 2",
        code: "WC2",
        organizationId: testOrganization.id,
      });

      const user1 = await User.create({
        firstName: "John",
        lastName: "Doe",
        organizationId: testOrganization.id,
        role: "WORKER",
      });

      const user2 = await User.create({
        firstName: "Jane",
        lastName: "Doe",
        organizationId: testOrganization.id,
        role: "WORKER",
      });

      const user3 = await User.create({
        firstName: "Jim",
        lastName: "Doe",
        organizationId: testOrganization.id,
        role: "WORKER",
      });

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "/workers-comp-codes/assign-workers",
        payload: [
          { userId: user1.id, workersCompCodeId: workersCompCode1.id },
          { userId: user2.id, workersCompCodeId: workersCompCode2.id },
          { userId: user3.id, workersCompCodeId: null },  // Remove worker comp code
        ],
      });

      expect(response.statusCode).toBe(200);

      // Verify assignments
      const updatedUser1 = await User.findByPk(user1.id);
      const updatedUser2 = await User.findByPk(user2.id);
      const updatedUser3 = await User.findByPk(user3.id);

      expect(updatedUser1?.workersCompCodeId).toBe(workersCompCode1.id);
      expect(updatedUser2?.workersCompCodeId).toBe(workersCompCode2.id);
      expect(updatedUser3?.workersCompCodeId).toBeNull();
    });

    it("should return 400 for non-existent worker comp codes", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const user = await User.create({
        firstName: "John",
        lastName: "Doe",
        organizationId: testOrganization.id,
        role: "WORKER",
      });

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "/workers-comp-codes/assign-workers",
        payload: [
          { userId: user.id, workersCompCodeId: 99999 },
        ],
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload).error).toEqual("[0].workersCompCodeId contains an invalid value");
    });

    it("should return 400 for non-existent users", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Code",
        code: "TC1",
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "/workers-comp-codes/assign-workers",
        payload: [
          { userId: 99999, workersCompCodeId: workersCompCode.id },
        ],
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload).error).toEqual("One or more users not found");
    });

    describe("Specific functionality for employees that belongs to a workplace in the Washington state", () => {

      const mockCheckEmployee = {
        first_name: "John",
        last_name: "Doe",
        workplaces: ["workplace_123"],
        id: "123abc",
      };

      const mockCheckWAWorkplace = {
        id: "workplace_123",
        address: {
          line1: "123 Main St",
          city: "Seattle",
          state: "WA",
          postal_code: "98101",
          country: "US",
        },
      };

      const mockCheckNonWAWorkplace = {
        id: "workplace_1234",
        address: {
          state: "CA",
          country: "US",
        },
      };

      it("should not allow non-6-digit codes for WA state employees", async () => {
        const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
        cleanup = testCleanup;

        const checkService = {
          loadAllPaginatedData: vi.spyOn(CheckService.prototype, "loadAllPaginatedData").mockImplementation(async (endpoint: string) => {
            if (endpoint.startsWith("/employees")) {
              return [mockCheckEmployee];
            }
            if (endpoint.startsWith("/workplaces")) {
              return [mockCheckWAWorkplace, mockCheckNonWAWorkplace];
            }

            return undefined;
          }),
          patch: vi.spyOn(CheckService.prototype, "patch").mockRejectedValueOnce({
            type: "validation_error",
            message: "Please correct the required fields and try again.",
            input_errors: [
              {
                message: "Invalid value for 'wa_risk_class_code': Value must be a valid Washington risk class code",
                field: "value",
                field_path: [
                  "company_defined_attributes",
                  "0",
                  "value",
                ],
              },
            ],
          }),
        };

        const workersCompCode = await WorkersCompCode.create({
          name: "Invalid Code",
          code: "123-45",
          organizationId: testOrganization.id,
        });

        const response = await makeRequest(app, {
          method: "PATCH",
          url: "/workers-comp-codes/assign-workers",
          payload: [
            { userId: testUser.id, workersCompCodeId: workersCompCode.id },
          ],
          throwOnError: false,
        });

        expect(response.statusCode).toBe(400);
        const responseBody = JSON.parse(response.payload);
        expect(responseBody.error.input_errors[0].message).toBe("Invalid value for 'wa_risk_class_code': Value must be a valid Washington risk class code");
        expect(checkService.loadAllPaginatedData).toHaveBeenCalledTimes(2);
        expect(checkService.patch).toHaveBeenCalledWith(
          `/employees/${testUser.checkEmployeeId}/company_defined_attributes`, {
            company_defined_attributes: [
              { name: "wa_risk_class_code", value: workersCompCode.code },
            ],
          },
        );
      });

      it("should update Check risk class code for WA state employees", async () => {
        const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
        cleanup = testCleanup;

        const checkService = {
          loadAllPaginatedData: vi.spyOn(CheckService.prototype, "loadAllPaginatedData").mockImplementation(async (endpoint: string) => {
            if (endpoint.startsWith("/employees")) {
              return [mockCheckEmployee];
            }
            if (endpoint.startsWith("/workplaces")) {
              return [mockCheckWAWorkplace, mockCheckNonWAWorkplace];
            }
            return undefined;
          }),
          patch: vi.spyOn(CheckService.prototype, "patch").mockResolvedValueOnce({}),
        };

        const workersCompCode = await WorkersCompCode.create({
          name: "Valid Code",
          code: "123-456",
          organizationId: testOrganization.id,
        });

        const response = await makeRequest(app, {
          method: "PATCH",
          url: "/workers-comp-codes/assign-workers",
          payload: [
            { userId: testUser.id, workersCompCodeId: workersCompCode.id.toString() },
          ],
        });

        expect(response.statusCode).toBe(200);

        expect(checkService.loadAllPaginatedData).toHaveBeenCalledTimes(2);
        expect(checkService.patch).toHaveBeenCalledWith(
          `/employees/${testUser.checkEmployeeId}/company_defined_attributes`, {
            company_defined_attributes: [
              { name: "wa_risk_class_code", value: workersCompCode.code },
            ],
          },
        );
      });
    });

  });
});
