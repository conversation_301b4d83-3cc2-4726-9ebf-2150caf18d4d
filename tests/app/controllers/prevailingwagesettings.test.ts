import { afterEach, describe, expect, it } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import PrevailingWageSettings from "@/models/prevailingwagesettings";

describe("PrevailingWageSettingsController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("PATCH /prevailing-wage-settings", () => {
    it("should update prevailing wage settings with valid data", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "prevailing-wage-settings",
        payload: {
          allowCustomPrevailingWagePerEmployee: true,
        },
      });

      expect(response.statusCode).toBe(200);

      const updatedSettings = await PrevailingWageSettings.findOne({
        where: { organizationId: testOrganization.id },
      });

      expect(updatedSettings?.allowCustomPrevailingWagePerEmployee).toBe(true);
    });

    it("should return 400 for invalid data type", async () => {
      const { app } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: "prevailing-wage-settings",
        payload: {
          allowCustomPrevailingWagePerEmployee: "invalid",
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "allowCustomPrevailingWagePerEmployee must be a boolean",
      });
    });
  });
});
