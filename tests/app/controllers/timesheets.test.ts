import { afterEach, describe, expect, it } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import { CostCode, EarningRate, Project, TimeSheet, TimesheetHistory, WorkersCompCode } from "@/models";
import dayjs from "dayjs";
import CompanyBenefit from "@/models/companybenefit";
import { ExtendedTimesheet } from "@/models/timesheet";

describe("TimesheetsController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("POST /timesheets", () => {
    it("should create a new timesheet with valid data", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create test project
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create test cost code
      const costCode = await CostCode.create({
        name: "Test Cost Code",
        organizationId: testOrganization.id,
      });

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Workers Comp",
        code: "WC1",
        organizationId: testOrganization.id,
      });

      const clockIn = dayjs().subtract(2, "hour").valueOf();
      const clockOut = dayjs().subtract(1, "hour").valueOf();

      const response = await makeRequest(app, {
        method: "POST",
        url: "timesheets",
        payload: {
          userId: testUser.id,
          projectId: project.id,
          costCodeId: costCode.id,
          description: "Test timesheet",
          clockIn,
          clockOut,
          clockInLocation: {
            horizontalAccuracy: 10,
            loggedAt: dayjs().valueOf(),
            locationEvent: "CLOCK_IN",
            lat: 37.811463,
            long: -121.28921,
            platform: "IOS",
            locationPermission: "WHEN_IN_USE",
            appState: "FOREGROUND",
            preciseLocationEnabled: true,
            userId: testUser.id,
            speed: 0.5124441,
          },
          breakDuration: 3600,
          isManual: true,
          workersCompCodeId: workersCompCode.id,
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);

      // Verify timesheet was created with correct data
      const timesheet = await TimeSheet.findOne({
        where: {
          id: responseBody.data.id,
        },
      });

      expect(timesheet).not.toBeNull();
      expect(timesheet?.workerId).toBe(testUser.id);
      expect(timesheet?.projectId).toBe(project.id);
      expect(timesheet?.costCodeId).toBe(costCode.id);
      expect(timesheet?.description).toBe("Test timesheet");
      expect(timesheet?.breakDuration).toBe(3600);
      expect(timesheet?.isManual).toBe(true);
      expect(timesheet?.status).toBe("SUBMITTED");
      expect(timesheet?.workersCompCodeId).toBe(workersCompCode.id);
    });
  });

  describe("PATCH /timesheets/:id", () => {
    it("should update timesheet and create history including workers comp code changes", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      await testOrganization.timeTrackingSettings.update({
        areRealtimeBreaksEnabled: false,
      });

      // Create test project
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create new project for update
      const newProject = await Project.create({
        name: "New Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create test cost codes
      const costCode = await CostCode.create({
        name: "Test Cost Code",
        organizationId: testOrganization.id,
      });

      const newCostCode = await CostCode.create({
        name: "New Cost Code",
        organizationId: testOrganization.id,
      });

      // Create timesheet
      const clockIn = dayjs().subtract(2, "hour");
      const clockOut = dayjs().subtract(1, "hour");

      const timesheet = await TimeSheet.create({
        workerId: testUser.id,
        projectId: project.id,
        costCodeId: costCode.id,
        organizationId: testOrganization.id,
        clockIn: clockIn.toDate(),
        clockOut: clockOut.toDate(),
        description: "Original description",
        breakDuration: 15,
        status: "SUBMITTED",
        isManual: true,
        createdBy: testUser.id,
      });

      // Update the timesheet
      const newClockIn = clockIn.add(15, "minute").valueOf();
      const newClockOut = clockOut.add(15, "minute").valueOf();

      const payload = {
        projectId: newProject.id,
        costCodeId: newCostCode.id,
        clockIn: newClockIn,
        clockOut: newClockOut,
        breakDuration: 3600,
      };

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `timesheets/${timesheet.id}`,
        payload,
      });

      expect(response.statusCode).toBe(200);

      // Verify timesheet was updated
      const updatedTimesheet = await TimeSheet.findByPk(timesheet.id);
      expect(updatedTimesheet?.projectId).toBe(newProject.id);
      expect(updatedTimesheet?.costCodeId).toBe(newCostCode.id);
      expect(updatedTimesheet?.breakDuration).toBe(3600);

      // Verify timesheet history was created
      const historyItems = await TimesheetHistory.findAll({
        where: {
          timesheetId: timesheet.id,
        },
      });

      expect(historyItems).to.have.length(1);
      const history = historyItems[0];
      expect(history).not.toBeNull();
      expect(history?.type).toBe("UPDATE");
      expect(history?.createdBy).toBe(testUser.id);

      // Parse history JSON and verify changes
      const historyData = JSON.parse(history?.history);

      expect(historyData.projectId.prev).toBe("Test Project");
      expect(historyData.projectId.new).toBe("New Project");

      expect(historyData.clockIn.prev).toBe(timesheet.clockIn.getTime());
      expect(historyData.clockIn.new).toBe(newClockIn);

      expect(historyData.clockOut.prev).toBe(timesheet.clockOut.getTime());
      expect(historyData.clockOut.new).toBe(newClockOut);

      expect(historyData.costCodeId.prev).toBe(costCode.name);
      expect(historyData.costCodeId.new).toBe(newCostCode.name);

      expect(historyData.breakDuration.prev).toBe("0.25 mins");
      expect(historyData.breakDuration.new).toBe("60 mins");

      expect(Object.keys(historyData).length).toBe(Object.keys(payload).length);
    });

    it("should allow setting workersCompCodeId to null and record history", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create test project
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create test cost code
      const costCode = await CostCode.create({
        name: "Test Cost Code",
        organizationId: testOrganization.id,
      });

      // Create initial workers comp code
      const initialWorkersCompCode = await WorkersCompCode.create({
        name: "Initial Workers Comp",
        code: "WC1",
        organizationId: testOrganization.id,
      });

      // Create timesheet with workers comp code
      const clockIn = dayjs().subtract(2, "hour");
      const clockOut = dayjs().subtract(1, "hour");

      const timesheet = await TimeSheet.create({
        workerId: testUser.id,
        projectId: project.id,
        costCodeId: costCode.id,
        organizationId: testOrganization.id,
        clockIn: clockIn.toDate(),
        clockOut: clockOut.toDate(),
        description: "Original description",
        breakDuration: 15,
        status: "SUBMITTED",
        isManual: true,
        createdBy: testUser.id,
        workersCompCodeId: initialWorkersCompCode.id,
      });

      // Update the timesheet, setting workersCompCodeId to null
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `timesheets/${timesheet.id}`,
        payload: {
          workersCompCodeId: null,
        },
      });

      expect(response.statusCode).toBe(200);

      // Verify timesheet was updated
      const updatedTimesheet = await TimeSheet.findByPk(timesheet.id);
      expect(updatedTimesheet?.workersCompCodeId).toBeNull();

      // Verify timesheet history was created
      const history = await TimesheetHistory.findOne({
        where: {
          timesheetId: timesheet.id,
        },
        order: [["createdAt", "DESC"]],
      });

      expect(history).not.toBeNull();
      expect(history?.type).toBe("UPDATE");
      expect(history?.createdBy).toBe(testUser.id);

      // Parse history JSON and verify changes
      const historyData = JSON.parse(history?.history);

      expect(historyData.workersCompCodeId.prev).toBe(initialWorkersCompCode.name);
      expect(historyData.workersCompCodeId.new).toBeUndefined();
      expect(Object.keys(historyData).length).toBe(1);
    });
  });

  describe("GET /timesheets", () => {
    it("should return timesheets within date range with correct data", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create test project
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create test cost code
      const costCode = await CostCode.create({
        name: "Test Cost Code",
        organizationId: testOrganization.id,
      });

      // Create a test company benefit
      await CompanyBenefit.create({
        name: "Test Company Benefit",
        category: "MEDICAL",
        contributionType: "PERCENT",
        companyContributionPercent: 80,
        employeeContributionPercent: 20,
        isApprovedFringe: true,
        organizationId: testOrganization.id,
      });

      const workersCompCode = await WorkersCompCode.create({
        name: "Test Workers Comp",
        code: "WC1",
        organizationId: testOrganization.id,
      });

      // Create timesheet within the date range we'll query
      const now = dayjs();
      const clockIn = now.subtract(30, "minutes");
      const clockOut = now;

      // Create test earning rates
      const testRegEarningRate = await EarningRate.create({
        name: "Regular Rate",
        organizationId: testOrganization.id,
        rate: 20,
      });

      const testOtEarningRate = await EarningRate.create({
        name: "Overtime Rate",
        organizationId: testOrganization.id,
        rate: 30,
      });

      const timesheet = await TimeSheet.create({
        workerId: testUser.id,
        projectId: project.id,
        costCodeId: costCode.id,
        organizationId: testOrganization.id,
        regEarningRateId: testRegEarningRate.id,
        otEarningRateId: testOtEarningRate.id,
        clockIn: clockIn.toDate(),
        clockOut: clockOut.toDate(),
        description: "Test timesheet",
        breakDuration: 0,
        status: "SUBMITTED",
        isManual: true,
        createdBy: testUser.id,
        workersCompCodeId: workersCompCode.id,
      });

      // add an older timesheet that should not be returned by the endpoint
      await TimeSheet.create({
        workerId: testUser.id,
        projectId: project.id,
        costCodeId: costCode.id,
        organizationId: testOrganization.id,
        regEarningRateId: testRegEarningRate.id,
        otEarningRateId: testOtEarningRate.id,
        clockIn: clockIn.subtract(1, "day").toDate(),
        clockOut: clockOut.subtract(1, "day").toDate(),
        description: "Test timesheet",
        breakDuration: 0,
        status: "SUBMITTED",
        isManual: true,
        createdBy: testUser.id,
        workersCompCodeId: workersCompCode.id,
      });

      // Query for timesheets
      const from = clockIn.subtract(1, "hour").valueOf();
      const to = clockOut.add(2, "hour").valueOf();

      const response = await makeRequest(app, {
        method: "GET",
        url: `timesheets?from=${from}&to=${to}`,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);

      expect(responseBody.data).toBeDefined();
      expect(responseBody.data.timesheets).toBeDefined();
      expect(responseBody.data.timesheets.length).toBe(1);

      const returnedTimesheet = responseBody.data.timesheets[0];
      expect(returnedTimesheet.id).toBe(timesheet.id);
      expect(returnedTimesheet.workerId).toBe(testUser.id);
      expect(returnedTimesheet.projectId).toBe(project.id);
      expect(returnedTimesheet.costCodeId).toBe(costCode.id);
      expect(returnedTimesheet.workersCompCodeId).toBe(workersCompCode.id);
      expect(returnedTimesheet.description).toBe("Test timesheet");
      expect(returnedTimesheet.status).toBe("SUBMITTED");
      expect(returnedTimesheet.isManual).toBe(true);

      // Verify summary data
      expect(responseBody.data.totalMinutes).toBeDefined();
      expect(responseBody.data.totalWages).toBeDefined();
      expect(responseBody.data.summary).toBeDefined();
    });
  });

  describe("POST /timesheets/bulk", () => {
    it("should create multiple timesheets successfully", async () => {
      const { app, testOrganization, testUser } = await setupTestEnvironment();
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      const mockTimesheets = [
        {
          userId: testUser.id,
          projectId: project.id,
          clockIn: dayjs().valueOf(),
          clockOut: dayjs().add(1, "hour").valueOf(),
          isManual: true,
          breaks: [
            {
              start: dayjs().add(1, "minute").valueOf(),
              end: dayjs().add(2, "minutes").valueOf(),
            },
          ],
        },
        {
          userId: testUser.id,
          projectId: project.id,
          clockIn: dayjs().valueOf(),
          clockOut: dayjs().add(1, "hour").valueOf(),
          isManual: true,
          breaks: [
            {
              start: dayjs().add(1, "minute").valueOf(),
            },
          ],
        },
      ];

      const response = await makeRequest(app, {
        method: "POST",
        url: "timesheets/bulk",
        payload: mockTimesheets,
      });

      expect(response.statusCode).toBe(200);
      const results = JSON.parse(response.payload) as { data: { timesheets: ExtendedTimesheet[] } };

      expect(results.data.timesheets.length).toBe(2);
      results.data.timesheets.forEach((timesheet) => {
        expect(timesheet?.project.id).toBe(project.id);
        expect(timesheet?.isManual).toBe(true);
        expect(timesheet?.status).toBe("SUBMITTED");
        expect(timesheet?.breaks.length).toBe(1);
      });
    });

    it("should rollback all timesheets if one fails", async () => {
      const { app, testOrganization, testUser } = await setupTestEnvironment();
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      const mockTimesheets = [
        {
          userId: testUser.id,
          projectId: project.id,
          clockIn: dayjs().valueOf(),
          clockOut: dayjs().add(1, "hour").valueOf(),
          isManual: true,
        },
        {
          userId: testUser.id,
          projectId: project.id,
          clockIn: dayjs().valueOf(),
          clockOut: dayjs().add(1, "hour").valueOf(),
          isManual: true,
          breaks: [
            {
              start: "invalid Value",
            },
          ],
        },
      ];

      const response = await makeRequest(app, {
        method: "POST",
        url: "timesheets/bulk",
        payload: mockTimesheets,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(500);
      expect(await TimeSheet.count({ where: { organizationId: testOrganization.id } })).toBe(0);
    });
  });
});
