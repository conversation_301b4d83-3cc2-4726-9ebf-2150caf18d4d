import { afterAll, beforeAll, describe, expect, it, MockedFunction, vi } from "vitest";
import User from "@/models/user";
import Organization from "@/models/organization";
import Project from "@/models/project";
import TimeSheet from "@/models/timesheet";
import { CostCode, EarningRate, UserClassification } from "@/models";
import fetch from "node-fetch";
import { CheckCompany, CheckEmployee, Payroll } from "@/types/check";
import { PaginatedResponse } from "@/services/check";
import CompanyBenefit from "@/models/companybenefit";
import EmployeeBenefit from "@/models/employeebenefit";
import Classification from "@/models/classification";
import FringeBenefit from "@/models/fringebenefit";
import FringeBenefitClassification from "@/models/fringebenefitclassification";
import WageTable from "@/models/wagetable";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import { Report } from "@/services/reports/Report";
import { Express } from "express";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import { REPORT_FORMAT } from "@/models/reports";

dayjs.extend(isoWeek);

const checkCompany: CheckCompany & Record<string, any> = {
  "id": "com_kABXp29C1cbWsOzA1ttP",
  "legal_name": "Dunder Mifflin",
  "trade_name": "Dunder Mifflin1",
  "other_business_name": null,
  "business_type": "sole_proprietorship",
  "industry_type": "general_construction_or_general_contracting",
  "website": null,
  "email": null,
  "phone": "**********",
  "pay_frequency": "weekly",
  "bank_accounts": ["bnk_Eth0pFIotPke2w0bT2py"],
  "active": true,
  "address": {
    "line1": "123 Main St",
    "line2": null,
    "city": "Los Angeles",
    "state": "CA",
    "postal_code": "90012",
    "country": "US",
  },
  "principal_place_of_business": null,
  "onboard": {
    "status": "needs_attention",
    "blocking_steps": [],
    "remaining_steps": ["setup_parameters", "filing_authorization"],
    "setup_parameters": [{ "id": "jur_n8L4UB5wHlxJQ8XXkcJh", "name": "Washington", "status": "needs_attention" }],
    "filing_authorization": [{
      "id": "frm_uP74bYv5UpdMTWfvQ0vx",
      "name": "Washington L&I Authorization Form",
      "status": "needs_attention",
    }, {
      "id": "frm_gXoVP5dP5y0Fdi3xAL3U",
      "name": "Washington Power of Attorney for Unemployment Insurance",
      "status": "needs_attention",
    }, {
      "id": "frm_a8YRfMtXHLbfwm6bXlRS",
      "name": "Washington Paid Family and Medical Leave Power of Attorney",
      "status": "needs_attention",
    }],
  },
  "implementation": { "status": "needs_attention", "remaining_steps": { "kyb": { "status": "four_day_approved" } } },
  "in_good_standing": true,
  "processing_period": "four_day",
  "start_date": "2023-03-01",
  "metadata": {},
  "employee_w2_delivery_method": null,
  "contractor_1099_delivery_method": null,
  "credit_approval_status": null,
  "credit_approval_details": null,
};

describe("POST /reports/certified-payroll", () => {
  let cleanup: () => void;
  let app: Express;
  let testUser: User;
  let testOrganization: Organization;
  let testProject: Project;
  let testWageTable: WageTable;
  let testClassification: Classification;
  let testUserClassification: UserClassification;
  let testFringeBenefit: FringeBenefit;
  let testCompanyBenefit: CompanyBenefit;
  let testEmployeeBenefit: EmployeeBenefit;
  let testTimesheet1: TimeSheet;

  beforeAll(async () => {
    const testSetup = await setupTestEnvironment({
      userProps: {
        checkEmployeeId: "emp_123",
        position: "Project Manager",
        workerClassification: "EMPLOYEE",
        isArchived: false,
      },
      organizationProps: {
        checkCompanyId: "TEST123",
        isRegisteredOnCheck: true,
      },
    });

    app = testSetup.app;
    cleanup = testSetup.cleanup;
    testUser = testSetup.testUser;
    testOrganization = testSetup.testOrganization;

    // Create a test wage table
    testWageTable = await WageTable.create({
      name: "Test Wage Table",
      description: "Test wage table description",
      organizationId: testOrganization.id,
    });

    // Create a test project with wage table
    testProject = await Project.create({
      name: "Test Project",
      organizationId: testOrganization.id,
      status: "ACTIVE",
      customerName: "Test Customer",
      address: "123 Test St, Test City, TS 12345",
      projectNumber: "PROJ-001",
      startDate: new Date(),
      isGeofenced: false,
      isArchived: false,
      isPrevailingWage: true,
      prevailingWageCategory: "FEDERAL",
      prevailingWageState: "CA",
      prevailingWageDirProjectId: "DIR-001",
      prevailingWageAwardingBody: "Test Awarding Body",
      prevailingWagePrimeContractorName: "Test Prime Contractor",
      prevailingWageBidAwardDate: new Date(),
      prevailingWageIsSubcontractor: false,
      wageTableId: testWageTable.id,
    });

    // Create a test cost code
    const testCostCode = await CostCode.create({
      name: "Test Cost Code",
      organizationId: testOrganization.id,
    });

    // Create a test classification
    testClassification = await Classification.create({
      name: "Test Classification",
      basePay: "25.00",
      fringePay: "5.00",
      startDate: new Date(),
      wageTableId: testWageTable.id,
      organizationId: testOrganization.id,
    });

    // Create a test user classification
    testUserClassification = await UserClassification.create({
      basePay: "25.00",
      fringePay: "5.00",
      startDate: new Date(),
      classificationId: testClassification.id,
      userId: testUser.id,
      wageTableId: testWageTable.id,
      organizationId: testOrganization.id,
    });

    // Create test earning rates
    const testRegEarningRate = await EarningRate.create({
      name: "Regular Rate",
      organizationId: testOrganization.id,
      rate: 20,
    });

    const testOtEarningRate = await EarningRate.create({
      name: "Overtime Rate",
      organizationId: testOrganization.id,
      rate: 30,
    });

    // Create a test timesheet
    testTimesheet1 = await TimeSheet.create({
      organizationId: testOrganization.id,
      workerId: testUser.id,
      projectId: testProject.id,
      costCodeId: testCostCode.id,
      userClassificationId: testUserClassification.id,
      description: "Test timesheet",
      clockIn: new Date(2023, 4, 1, 9, 0), // May 1, 2023, 9:00 AM
      clockOut: new Date(2023, 4, 1, 17, 0), // May 1, 2023, 5:00 PM
      isManual: false,
      status: "PAID",
      createdBy: testUser.id,
      breakDuration: 30,
      clockedOutBySwitch: false,
      regEarningRateId: testRegEarningRate.id,
      otEarningRateId: testOtEarningRate.id,
    });

    // Create a test fringe benefit
    testFringeBenefit = await FringeBenefit.create({
      name: "Test Fringe Benefit",
      category: "TRAINING_FUND_CONTRIBUTION",
      wageTableId: testWageTable.id,
      organizationId: testOrganization.id,
    });

    // Create a test fringe benefit classification
    await FringeBenefitClassification.create({
      amount: "2.50",
      startDate: new Date(),
      fringeBenefitId: testFringeBenefit.id,
      classificationId: testClassification.id,
      organizationId: testOrganization.id,
    });

    // Create a test company benefit
    testCompanyBenefit = await CompanyBenefit.create({
      name: "Test Company Benefit",
      category: "MEDICAL",
      contributionType: "PERCENT",
      companyContributionPercent: 80,
      employeeContributionPercent: 20,
      isApprovedFringe: true,
      organizationId: testOrganization.id,
    });

    // Create a test employee benefit
    testEmployeeBenefit = await EmployeeBenefit.create({
      name: "Test Employee Benefit",
      contributionType: "PERCENT",
      companyContributionPercent: 80,
      employeeContributionPercent: 20,
      benefitStartDate: new Date().toISOString().split("T")[0],
      companyBenefitId: testCompanyBenefit.id,
      userId: testUser.id,
      organizationId: testOrganization.id,
    });
  });

  afterAll(async () => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("input validation", () => {
    const validPayload = {
      projectId: 1,
      payrollId: "PAY123",
      signatoryName: "John Doe",
      signatoryTitle: "Project Manager",
      reportFormat: REPORT_FORMAT.LCPTracker,
      isLastCertifiedPayrollReport: true,
      remarks: "Test remarks",
    };

    const testValidationError = async (app: Express, field: string, errorMessage: string) => {
      const invalidPayload = { ...validPayload };
      delete invalidPayload[field as keyof typeof invalidPayload];

      const response = await makeRequest(app, {
        method: "POST",
        url: "report/certified-payroll",
        payload: invalidPayload,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe(errorMessage);
    };

    it("should validate projectId is required", async () => {
      await testValidationError(app, "projectId", "projectId is required");
    });

    it("should validate payrollId is required", async () => {
      await testValidationError(app, "payrollId", "payrollId is required");
    });

    it("should validate signatoryName is required", async () => {
      await testValidationError(app, "signatoryName", "signatoryName is required");
    });

    it("should validate signatoryTitle is required", async () => {
      await testValidationError(app, "signatoryTitle", "signatoryTitle is required");
    });

    it("should validate reportFormat is required", async () => {
      await testValidationError(app, "reportFormat", "reportFormat is required");
    });

    it("should validate reportFormat is valid", async () => {
      const invalidPayload = { ...validPayload, reportFormat: "INVALID_FORMAT" };
      const response = await makeRequest(app, {
        method: "POST",
        url: "report/certified-payroll",
        payload: invalidPayload,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe(`reportFormat must be one of [${Object.keys(REPORT_FORMAT).join(", ")}, xml, csv]`);
    });

    // it("should allow isLastCertifiedPayrollReport to be optional", async () => {
    //   vi.mock("node-fetch");
    //   (fetch as any as MockedFunction<any>).mockResolvedValue({ json: () => new Promise((resolve) => resolve({ test: "123ad" })) });
    //
    //   const payloadWithoutOptional = { ...validPayload };
    //   delete payloadWithoutOptional.isLastCertifiedPayrollReport;
    //
    //   const response = await inject(app, {
    //     method: "POST",
    //     url: "report/certified-payroll",
    //     headers: {
    //       "Cookie": `user_sid=${sessionCookie}`,
    //       "Content-type": "application/json",
    //       "x-api-key": process.env.API_PUBLIC_TOKEN,
    //     },
    //     payload: JSON.stringify(payloadWithoutOptional),
    //   });
    //
    //   expect(response.statusCode).not.toBe(400);
    // });
    //
    // it("should allow remarks to be optional", async () => {
    //   vi.mock("node-fetch");
    //   (fetch as any as MockedFunction<any>).mockResolvedValue({ json: () => new Promise((resolve) => resolve({ test: "123ad" })) });
    //
    //
    //   const payloadWithoutOptional = { ...validPayload };
    //   delete payloadWithoutOptional.remarks;
    //
    //   const response = await inject(app, {
    //     method: "POST",
    //     url: "report/certified-payroll",
    //     headers: {
    //       "Cookie": `user_sid=${sessionCookie}`,
    //       "Content-type": "application/json",
    //       "x-api-key": process.env.API_PUBLIC_TOKEN,
    //     },
    //     payload: JSON.stringify(payloadWithoutOptional),
    //   });
    //
    //   expect(response.statusCode).not.toBe(400);
    // });

  });

  describe("creates a report", () => {
    it("should accept a valid payload and return correct data", async () => {
      const validPayload = {
        projectId: testProject.id,
        payrollId: "PAY123",
        signatoryName: "John Doe",
        signatoryTitle: "Project Manager",
        reportFormat: REPORT_FORMAT.JSON,
        isLastCertifiedPayrollReport: true,
        remarks: "Test remarks",
      };

      // Set clockIn to the current date at 9:00 AM
      const clockIn = dayjs().startOf("day").hour(9);

      // Set clockOut to 8 hours after clockIn
      const clockOut = clockIn.add(8, "hour");

      // Update the testTimesheet with the new dates
      await testTimesheet1.update({
        clockIn: clockIn.toDate(),
        clockOut: clockOut.toDate(),
      });

      // Create payroll period based on the timesheet
      const payrollStartDate = clockIn.subtract(3, "day").startOf("week");
      const payrollEndDate = clockOut.add(3, "day").endOf("day");

      const hoursWorked = clockOut.diff(clockIn, "hour");
      const grossEarnings = calculateGrossEarnings(testUserClassification.basePay, hoursWorked);
      const { employeeTaxes, companyTaxes } = calculateTaxes(grossEarnings);
      const {
        employeeBenefits,
        companyBenefits,
      } = calculateBenefits(testCompanyBenefit, testEmployeeBenefit, grossEarnings);

      const netPay = grossEarnings - employeeTaxes - employeeBenefits;
      const totalLiability = grossEarnings + companyTaxes + companyBenefits;
      const cashRequirement = netPay + companyBenefits;

      const payroll: Payroll = {
        "id": "pay_K73ThaExoYKGMdGFweeH",
        "company": testOrganization.checkCompanyId,
        "period_start": payrollStartDate.format("YYYY-MM-DD"),
        "period_end": payrollEndDate.format("YYYY-MM-DD"),
        "approval_deadline": "2024-10-26T00:00:00Z",
        "approved_at": "2024-10-08T18:46:07.853503Z",
        "payday": payrollEndDate.add(7, "days").format("YYYY-MM-DD"),
        "status": "paid",
        "managed": true,
        "type": "regular",
        "pay_frequency": "weekly",
        "pay_schedule": null,
        "off_cycle_options": null,
        "totals": {
          "employee_gross": grossEarnings.toFixed(2),
          "employee_reimbursements": "0.00",
          "employee_net": netPay.toFixed(2),
          "employee_taxes": employeeTaxes.toFixed(2),
          "employee_benefits": employeeBenefits.toFixed(2),
          "post_tax_deductions": "0.00",
          "contractor_gross": "0.00",
          "contractor_reimbursements": "0.00",
          "contractor_net": "0.00",
          "company_taxes": companyTaxes.toFixed(2),
          "company_benefits": companyBenefits.toFixed(2),
          "liability": totalLiability.toFixed(2),
          "cash_requirement": cashRequirement.toFixed(2),
        },
        "items": [
          {
            "id": "itm_RNURMnXrzzs24xBsKlJX",
            "payroll": "pay_K73ThaExoYKGMdGFweeH",
            "employee": testUser.checkEmployeeId,
            "payment_method": "direct_deposit",
            "pto_balance_hours": null,
            "sick_balance_hours": null,
            "net_pay": netPay.toFixed(2),
            "earnings": [{
              "amount": grossEarnings.toFixed(2),
              "hours": hoursWorked,
              "type": "regular",
              "code": null,
              "description": "Regular Pay",
              "earning_code": "erc_Kuw0rX5uhvgG1kstedUb",
              "earning_rate": testUserClassification.basePay,
              "workplace": "wrk_74c4dnaw7U3F1Hhy05ef",
            }],
            "reimbursements": [],
            "taxes": [
              {
                "tax": "tax_ImvSF9CTuMdokf0uwx5x",
                "description": "Employer FICA Tax",
                "amount": (companyTaxes / 2).toFixed(2),
                "payer": "company",
                "remittable": true,
              },
              {
                "tax": "tax_O3f21hkS1cvHBZTa61BO",
                "description": "Employer Medicare Tax",
                "amount": (companyTaxes / 2).toFixed(2),
                "payer": "company",
                "remittable": true,
              },
              {
                "tax": "tax_ibU8cGhC5OlpOjoQFIXV",
                "description": "FICA",
                "amount": (employeeTaxes / 2).toFixed(2),
                "payer": "employee",
                "remittable": true,
              },
              {
                "tax": "tax_1XHqR9Qf5t18SD2sfYEe",
                "description": "Medicare",
                "amount": (employeeTaxes / 2).toFixed(2),
                "payer": "employee",
                "remittable": true,
              },
            ],
            "benefits": [{
              "benefit": testCompanyBenefit.category,
              "description": `${testCompanyBenefit.name} - ${testEmployeeBenefit.name}`,
              "employee_contribution_amount": employeeBenefits.toFixed(2),
              "company_contribution_amount": companyBenefits.toFixed(2),
              employee_contribution_percent: '',
              company_contribution_percent: '',
              company_period_amount: '',
            }],
            "benefit_overrides": [],
            "post_tax_deductions": [],
            "post_tax_deduction_overrides": [],
            "warnings": [],
          },
        ],
        "contractor_payments": [],
        "metadata": { "updatedAt": "*************" },
      };
      const checkEmployee: PaginatedResponse<CheckEmployee> = {
        "next": null,
        "previous": null,
        "results": [{
          "id": "emp_nio1fYMaw8pxcK9nN1kl",
          "first_name": "Jim",
          "last_name": "Halpert",
          "middle_name": null,
          "email": "<EMAIL>",
          "dob": "1982-01-01",
          "bank_accounts": ["bnk_55thqaQHlL7BwzVFVM7F"],
          "ssn_last_four": "2222",
          // "ssn_validation_status": "pending",
          "payment_method_preference": "direct_deposit",
          // "default_net_pay_split": "nps_hDGCY1WzTeSNw354w3oi",
          "company": "com_kABXp29C1cbWsOzA1ttP",
          "workplaces": ["wrk_74c4dnaw7U3F1Hhy05ef"],
          "primary_workplace": "wrk_74c4dnaw7U3F1Hhy05ef",
          "start_date": "2023-02-01",
          // "termination_date": null,
          "active": true,
          "residence": {
            "line1": "2 Embarcadero Center, 8th Floor",
            "line2": null,
            "city": "San Francisco",
            "state": "CA",
            "postal_code": "94111",
            "country": "US",
          },
          // "w2_electronic_consent_provided": true,
          "metadata": {
            "primary_earning_rate": "rte_W113AZ8EWiM3mqs3aflS",
            "primary_ot_earning_rate": "rte_5Qa0XcUrvgwF5EYSWCRA",
          },
          "onboard": { "status": "completed", "blocking_steps": [], "remaining_steps": [] },
        }],
      };

      vi.mock("node-fetch");
      const payrollEndpoint = `/payrolls/${validPayload.payrollId}`;
      const checkCompanyEndpoint = `/companies/${testOrganization.checkCompanyId}`;
      const checkEmployeeEndpoint = `/employees?id=${testUser.checkEmployeeId}`;
      (fetch as any as MockedFunction<any>).mockImplementation((url: string) => {
        const responses = {
          [payrollEndpoint]: payroll,
          [checkCompanyEndpoint]: checkCompany,
          [checkEmployeeEndpoint]: checkEmployee,
        };

        const response = Object.entries(responses).find(([endpoint]) => url.endsWith(endpoint))?.[1];

        return {
          json: () => new Promise(resolve => resolve(response)),
        };
      });
      const response = await makeRequest(app, {
        method: "POST",
        url: "report/certified-payroll",
        payload: validPayload,
      });

      const responseBody = JSON.parse(response.payload) as ReturnType<Report["transform"]>;
      expect(response.statusCode, JSON.stringify(responseBody)).toBe(200);

      // Assert company details
      expect(responseBody.name).toBe("Dunder Mifflin");
      expect(responseBody.company).toEqual({
        fullAddress: "123 Main St, Los Angeles, CA 90012, US",
        line1: "123 Main St",
        line2: null,
        fullStreet: "123 Main St",
        city: "Los Angeles",
        state: "CA",
        zip: "90012",
      });

      // Assert project details
      expect(responseBody.isSubcontractor).toBe(false);
      expect(responseBody.payrollNumber).toBe("2 (FINAL)");
      expect(responseBody.projectLocation).toBe("Test Project - 123 Test St, Test City, TS 12345");
      expect(responseBody.projectNumber).toBe("PROJ-001");
      expect(responseBody.projectName).toBe("Test Project");

      // Assert dates
      expect(responseBody.endWeekDate).toBe(payrollEndDate.format("MM-DD-YYYY"));
      expect(responseBody.weekDates).toHaveLength(7);
      expect(responseBody.reportDate).toBe(dayjs().format("MM-DD-YYYY"));

      // Assert signatory details
      expect(responseBody.signerName).toBe("John Doe");
      expect(responseBody.signerTitle).toBe("Project Manager");

      // Assert fringe benefits
      expect(responseBody.areFringeBenefitsPaidToPlans).toBe(true);
      expect(responseBody.areFringeBenefitsPaidInCash).toBe(false);

      // Assert remarks
      expect(responseBody.remarks).toBe("Test remarks");

      // Assert timesheet details
      expect(responseBody.timesheets).toHaveLength(1);
      const timesheet = responseBody.timesheets[0];

      expect(timesheet.employee.name).toBe("John Doe");
      expect(timesheet.employee.firstName).toBe("John");
      expect(timesheet.employee.lastName).toBe("Doe");
      expect(timesheet.employee.checkEmployeeId).toBe("emp_123");
      expect(timesheet.jobClassification).toBe("Test Classification");
      expect(timesheet.jobClassificationId).toBe(testClassification.id);

      // Assert hours
      expect(timesheet.regularHours).toHaveLength(7);
      expect(timesheet.overtimeHours).toHaveLength(7);
      expect(timesheet.totalRegularHours).toBe("7.98");
      expect(timesheet.totalOvertimeHours).toBe("0.00");

      // Assert rates and earnings
      expect(timesheet.baseRate).toBe("25.00");
      expect(timesheet.cashFringes).toBe("0.00");
      expect(timesheet.overtimeRate).toBe("37.50");
      expect(parseFloat(timesheet.grossEarned)).toBeCloseTo(221, 0);

      // Assert deductions
      expect(timesheet.deductions).toHaveLength(4);
      const totalDeductions = timesheet.deductions.reduce((sum, deduction) => sum + parseFloat(deduction.amount), 0);
      expect(totalDeductions).toBeCloseTo(110.6, 2);

      // Assert net wages
      expect(parseFloat(timesheet.netWagesPaid)).toBeCloseTo(netPay, 2);
    });
  });
});

function calculateGrossEarnings(basePay: string, hoursWorked: number): number {
  return parseFloat(basePay) * hoursWorked;
}

function calculateTaxes(grossEarnings: number): { employeeTaxes: number; companyTaxes: number } {
  const ficaRate = 0.062;
  const medicareRate = 0.0145;
  const totalTaxRate = ficaRate + medicareRate;

  const employeeTaxes = grossEarnings * totalTaxRate;
  const companyTaxes = grossEarnings * totalTaxRate;

  return { employeeTaxes, companyTaxes };
}

function calculateBenefits(
  companyBenefit: any,
  employeeBenefit: any,
  grossEarnings: number,
): { employeeBenefits: number; companyBenefits: number } {
  const totalBenefitCost = grossEarnings * (companyBenefit.companyContributionPercent + companyBenefit.employeeContributionPercent) / 100;

  const employeeBenefits = totalBenefitCost * (employeeBenefit.employeeContributionPercent / 100);
  const companyBenefits = totalBenefitCost * (employeeBenefit.companyContributionPercent / 100);

  return { employeeBenefits, companyBenefits };
}
