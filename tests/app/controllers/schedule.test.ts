import { afterEach, describe, expect, it } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import TimeOffPolicy, { TimeOffPolicyType } from "@/models/timeoffpolicy";
import { Project } from "@/models";
import User from "@/models/user";

describe("ScheduleController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("POST /schedule-events", () => {
    it("should throw error when user has approved time off during schedule period", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-03-15"),
        endDate: new Date("2024-03-17"),
        reviewedBy: testUser.id,
        totalHours: 40,
        status: TimeOffRequestStatus.APPROVED
      });
      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-03-18"),
        endDate: new Date("2024-03-20"),
        reviewedBy: testUser.id,
        totalHours: 40,
        status: TimeOffRequestStatus.PAID
      });

      let response = await makeRequest(app, {
        method: "POST",
        url: "/schedule-events",
        payload: {
          startTime: new Date("2024-03-16").getTime().toString(),
          endTime: new Date("2024-03-17").getTime().toString(),
          userIds: `${testUser.id}`,
          projectId: project.id,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toMatch(/Cannot schedule event/);

      response = await makeRequest(app, {
        method: "POST",
        url: "/schedule-events",
        payload: {
          startTime: new Date("2024-03-18").getTime().toString(),
          endTime: new Date("2024-03-19").getTime().toString(),
          userIds: `${testUser.id}`,
          projectId: project.id,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toMatch(/Cannot schedule event/);
    });

    it("should create schedule when no time off conflicts exist", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      // Create an approved time off request for a different period
      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-03-01"),
        endDate: new Date("2024-03-05"),
        status: TimeOffRequestStatus.APPROVED,
        totalHours: 40,
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: "/schedule-events",
        payload: {
          startTime: new Date("2024-03-16").getTime().toString(),
          endTime: new Date("2024-03-17").getTime().toString(),
          userIds: `${testUser.id}`,
          projectId: project.id,
        },
      });

      expect(response.statusCode).toBe(200);
      expect(response.json().data).toBeDefined();
    });
  });

  describe("PATCH /schedule-events/:id", () => {
    it("should throw error when updating schedule time", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });
      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      // Create an approved time off request
      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-03-15"),
        endDate: new Date("2024-03-20"),
        reviewedBy: testUser.id,
        totalHours: 40,
      });

      // First create a schedule event
      const createResponse = await makeRequest(app, {
        method: "POST",
        url: "/schedule-events",
        payload: {
          startTime: new Date("2024-03-01").getTime().toString(),
          endTime: new Date("2024-03-02").getTime().toString(),
          userIds: `${testUser.id}`,
          projectId: project.id,
        },
      });

      const scheduleEvent = createResponse.json().data.scheduleEvent;

      // Try to update it to conflict with time off
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/schedule-events/${scheduleEvent.id}`,
        payload: {
          startTime: new Date("2024-03-16").getTime().toString(),
          endTime: new Date("2024-03-17").getTime().toString(),
          userIds: `${testUser.id}`,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toMatch(/Cannot schedule event/);
    });

    it("should throw error when updating schedule userIds", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });
      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      // Create an approved time off request
      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-03-15"),
        endDate: new Date("2024-03-20"),
        reviewedBy: testUser.id,
        totalHours: 40,
      });

      // Create two test users
      const secondUser = await User.create({
        email: "<EMAIL>",
        firstName: "Test2",
        lastName: "User2",
        checkEmployeeId: "12345abcde",
      });
      // First create a schedule event
      const createResponse = await makeRequest(app, {
        method: "POST",
        url: "/schedule-events",
        payload: {
          startTime: new Date("2024-03-16").getTime().toString(),
          endTime: new Date("2024-03-17").getTime().toString(),
          userIds: `${secondUser.id}`,
          projectId: project.id,
        },
      });

      const scheduleEvent = createResponse.json().data.scheduleEvent;

      // Try to update it to conflict with time off
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/schedule-events/${scheduleEvent.id}`,
        payload: {
          userIds: `${testUser.id}`,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toMatch(/Cannot schedule event/);
    });

    it("should update schedule when no time off conflicts exist", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      // Create an approved time off request for a different period
      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-03-01"),
        endDate: new Date("2024-03-05"),
        status: TimeOffRequestStatus.APPROVED,
        totalHours: 40,
      });

      // First create a schedule event
      const createResponse = await makeRequest(app, {
        method: "POST",
        url: "/schedule-events",
        payload: {
          startTime: new Date("2024-03-16").getTime().toString(),
          endTime: new Date("2024-03-17").getTime().toString(),
          userIds: `${testUser.id}`,
          projectId: project.id,
        },
      });

      const scheduleEvent = createResponse.json().data.scheduleEvent;

      // Update it to a period without conflicts
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/schedule-events/${scheduleEvent.id}`,
        payload: {
          startTime: new Date("2024-03-16").getTime().toString(),
          endTime: new Date("2024-03-17").getTime().toString(),
          userIds: `${testUser.id}`,
        },
      });

      expect(response.statusCode).toBe(200);
      expect(response.json().data).toBeDefined();
    });
  });
});
