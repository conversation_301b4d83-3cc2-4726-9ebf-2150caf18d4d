import { beforeEach, describe, expect, it, vi } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import PaySchedule from "@/models/payschedule";
import { CheckService } from "@/services/check";
import { InferCreationAttributes } from "sequelize";
import dayjs from "dayjs";
import { FeatureFlags } from "@/models";

describe("OrganizationPayScheduleController", () => {
  describe("GET /pay-schedule", () => {
    it("should return pay schedule for organization", async () => {
      const { app, testOrganization } = await setupTestEnvironment({
        organizationProps: {
          checkCompanyId: "TEST123",
          isRegisteredOnCheck: true,
        },
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: "pay-schedule",
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload).data;
      expect(responseBody.id).toBe(testOrganization?.paySchedule?.id);
      expect(responseBody.organizationId).toBe(testOrganization.id);
    });

    it("should return 404 if no pay schedule exists", async () => {
      const { app, testOrganization } = await setupTestEnvironment({
        organizationProps: {
          checkCompanyId: "TEST123",
          isRegisteredOnCheck: true,
        },
      });

      await PaySchedule.destroy({ where: { organizationId: testOrganization.id } });

      const response = await makeRequest(app, {
        method: "GET",
        url: "pay-schedule",
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("POST /pay-schedule", () => {
    let testEnv: Awaited<ReturnType<typeof setupTestEnvironment>>;

    beforeEach(async () => {
      testEnv = await setupTestEnvironment({
        organizationProps: {
          checkCompanyId: "TEST123",
          isRegisteredOnCheck: true,
        },
      });

      vi.spyOn(CheckService.prototype, "post").mockResolvedValue({ id: "123abc" });
      vi.spyOn(CheckService.prototype, "patch").mockResolvedValue({});
    });

    it("should create pay schedule with valid weekly data", async () => {
      const checkServiceSpyPost = vi.spyOn(CheckService.prototype, "post");
      const payload: Partial<InferCreationAttributes<PaySchedule>> = {
        payFrequency: "weekly",
        firstPayday: "2024-03-15",
        firstPeriodEnd: "2024-03-08",
      };

      const response = await makeRequest(testEnv.app, {
        method: "POST",
        url: "pay-schedule",
        payload,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload).data;
      expect(responseBody.payFrequency).toBe(payload.payFrequency);

      expect(checkServiceSpyPost).toHaveBeenCalledWith(
        `/pay_schedules`,
        {
          pay_frequency: payload.payFrequency,
          first_payday: dayjs(payload.firstPayday).format("YYYY-MM-DD"),
          first_period_end: dayjs(payload.firstPeriodEnd).format("YYYY-MM-DD"),
          second_payday: undefined,
          company: testEnv.testOrganization.checkCompanyId,
        },
      );
    });

    it("should create pay schedule with valid semimonthly data", async () => {
      const payload: Partial<InferCreationAttributes<PaySchedule>> = {
        payFrequency: "semimonthly",
        firstPayday: "2024-03-15",
        secondPayday: "2024-03-30",
        firstPeriodEnd: "2024-03-08",
      };

      const response = await makeRequest(testEnv.app, {
        method: "POST",
        url: "pay-schedule",
        payload,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload).data;
      expect(responseBody.payFrequency).toBe(payload.payFrequency);
      expect(responseBody.secondPayday).toBe(payload.secondPayday);
    });

    it("should validate required fields", async () => {
      const payload = {
        payFrequency: "weekly",
        // Missing required firstPayday and firstPeriodEnd
      };

      const response = await makeRequest(testEnv.app, {
        method: "POST",
        url: "pay-schedule",
        payload,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
    });

    it("should validate secondPayday requirement for semimonthly frequency", async () => {
      const payload = {
        payFrequency: "semimonthly",
        firstPayday: "2024-03-15",
        firstPeriodEnd: "2024-03-08",
        // Missing required secondPayday for semimonthly
      };

      const response = await makeRequest(testEnv.app, {
        method: "POST",
        url: "pay-schedule",
        payload,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
    });

    it("should update pay schedule without Check API for non-payroll company", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      await FeatureFlags.update(
        { isPayrollEnabled: false },
        {
          where: {
            organizationId: testOrganization.id,
          },
        }
      );

      const checkServiceSpyPost = vi.spyOn(CheckService.prototype, "post");
      const checkServiceSpyPatch = vi.spyOn(CheckService.prototype, "patch");

      const payload = {
        payFrequency: "weekly",
        firstPayday: "2024-03-15",
        firstPeriodEnd: "2024-03-08"
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: "pay-schedule",
        payload
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload).data;
      expect(responseBody.payFrequency).toBe(payload.payFrequency);
      expect(responseBody.firstPayday).toBe(payload.firstPayday);
      expect(responseBody.firstPeriodEnd).toBe(payload.firstPeriodEnd);

      // Verify that Check API was not called
      expect(checkServiceSpyPost).not.toHaveBeenCalled();
      expect(checkServiceSpyPatch).not.toHaveBeenCalled();
    });
  });
});
