import { afterEach, describe, expect, it, vi } from "vitest";
import TimeOffPolicy, { Acc<PERSON><PERSON><PERSON><PERSON><PERSON>, TimeOffPolicyType } from "@/models/timeoffpolicy";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import TimeOffPolicyEnrollment from "@/models/timeoffpolicyenrollment";
import User from "@/models/user";
import { CheckService } from "@/services/check";
import dayjs from "dayjs";
import TimeOffRequest, { TimeOffRequestStatus } from "../../../app/models/timeoffrequest";
import PaySchedule from "@/models/payschedule";
import Organization from "@/models/organization";

interface PolicyBalance {
  id: number;
  name: string;
  type: TimeOffPolicyType;
  accruedHours: number;
  usedHours: number;
  availableHours: number;
}

type UserBalance = User & { policies: PolicyBalance[] };

describe("TimeOffPolicyController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  // Just some dates we can use to base our calculations on.
  // We don't want to use the current date because we have hardcoded values in the "expect" assertions
  // 2024-12-03 to give us EXACTLY 4 weeks by the end of the year
  vi.setSystemTime(dayjs("2023-07-01").add(17, "months").add(2, "day").toDate());

  describe("POST /time-off-policies", () => {
    it("should create a time-off policy successfully", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const policyData = {
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        accrualLimit: 160,
        addNewEmployeesAutomatically: true,
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-policies",
        payload: policyData,
      });

      const data = response.json<{ data: TimeOffPolicy }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.name).toBe("PTO Policy");
      expect(data.type).toBe(TimeOffPolicyType.PTO);
      expect(data.isLimited).toBe(true);
      expect(data.accrualMethod).toBe(AccrualMethod.ACCRUED);
      expect(data.accrualHoursRate).toBe(120);
      expect(data.accrualLimit).toBe(160);
      expect(data.organizationId).toBe(testUser.organizationId);
    });
  });

  describe("PATCH /time-off-policies/:id", () => {
    it("should partially update multiple fields", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a policy first
      const policyData = {
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        accrualLimit: 160,
        addNewEmployeesAutomatically: true,
      };

      const createResponse = await makeRequest(app, {
        method: "POST",
        url: "/time-off-policies",
        payload: policyData,
      });

      const policy = createResponse.json<{ data: TimeOffPolicy }>().data;

      // Update multiple fields
      const updateData = {
        name: "Updated PTO Policy",
        carryoverLimit: 10,
        accrualLimit: 180,
      };

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/time-off-policies/${policy.id}`,
        payload: updateData,
      });

      expect(response.statusCode).toBe(200);
      const updatedPolicy = response.json<{ data: TimeOffPolicy }>().data;
      expect(updatedPolicy.name).toBe(updateData.name);
      expect(updatedPolicy.carryoverLimit).toBe(updateData.carryoverLimit);
      expect(updatedPolicy.accrualLimit).toBe(updateData.accrualLimit);
      // Other fields should remain unchanged
      expect(updatedPolicy.type).toBe(policyData.type);
      expect(updatedPolicy.accrualMethod).toBe(policyData.accrualMethod);
      expect(updatedPolicy.accrualHoursRate).toBe(policyData.accrualHoursRate);
    });

    it("should validate isLimited dependencies when updating", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a policy first
      const policyData = {
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
      };

      const createResponse = await makeRequest(app, {
        method: "POST",
        url: "/time-off-policies",
        payload: policyData,
      });

      const policy = createResponse.json<{ data: TimeOffPolicy }>().data;

      // Try to update to isLimited without required fields
      const response = await makeRequest(app, {
        method: "PATCH",
        url: `/time-off-policies/${policy.id}`,
        payload: { isLimited: true },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      const error = response.json();
      expect(error.error).toEqual("isLimited is not allowed");
    });
  });

  describe("GET /time-off-policies", () => {
    it("should list active time-off policies by default", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const activePolicy = await TimeOffPolicy.create({
        name: "Active Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        endDate: null,
        organizationId: testOrganization.id,
      });

      const inactivePolicy = await TimeOffPolicy.create({
        name: "Inactive Policy",
        type: TimeOffPolicyType.SICK,
        isLimited: true,
        endDate: today,
        organizationId: testOrganization.id,
      });

      const anotherOrganization = await Organization.create({
        name: "another org",
      });

      await TimeOffPolicy.create({
        name: "another org Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
        organizationId: anotherOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: "/time-off-policies",
      });

      const data = response.json<{ data: { timeOffPolicies: TimeOffPolicy[] }; total: number }>().data.timeOffPolicies;
      expect(response.statusCode).toBe(200);
      expect(data.length).toBe(1);
      expect(data.find((item) => item.id === activePolicy.id)).toBeTruthy();
      expect(data.find((item) => item.id === inactivePolicy.id)).toBeFalsy();
    });

    it("should list all time-off policies when includeArchived=true", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      await TimeOffPolicy.create({
        name: "Active Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        endDate: null,
        organizationId: testOrganization.id,
      });

      const policy = await TimeOffPolicy.create({
        name: "Inactive Policy",
        type: TimeOffPolicyType.SICK,
        isLimited: true,
        endDate: new Date(), // today
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: "/time-off-policies?includeArchived=true",
      });

      const data = response.json<{ data: { timeOffPolicies: TimeOffPolicy[] } }>().data.timeOffPolicies;
      expect(response.statusCode).toBe(200);
      expect(data).toHaveLength(2);
      expect(data.find((item) => item.id === policy.id).name).toBe("Inactive Policy");
    });

  });

  describe("GET /time-off-policies/:id", () => {
    it("should get a time-off policy by id", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const policy = await TimeOffPolicy.create({
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        organizationId: testOrganization.id,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `/time-off-policies/${policy.id}`,
      });

      const data = response.json<{ data: { timeOffPolicy: TimeOffPolicy } }>().data.timeOffPolicy;
      expect(response.statusCode).toBe(200);
      expect(data.id).toBe(policy.id);
      expect(data.name).toBe("PTO Policy");
      expect(data.type).toBe(TimeOffPolicyType.PTO);
    });

    it("should return 404 for non-existent policy", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "GET",
        url: "/time-off-policies/999999",
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("POST /time-off-policies/:policyId/users", () => {
    it("should enroll multiple users in a time-off policy", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({ start_date: dayjs().format("YYYY-MM-DD") });

      // Create two test users
      const secondUser = await User.create({
        email: "<EMAIL>",
        firstName: "Test2",
        lastName: "User2",
        checkEmployeeId: "12345abcde",
      });

      const policy = await TimeOffPolicy.create({
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        organizationId: testOrganization.id,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      // First enrollment with two users
      const firstEnrollmentData = {
        userIds: [testUser.id],
      };

      await makeRequest(app, {
        method: "POST",
        url: `/time-off-policies/${policy.id}/users`,
        payload: firstEnrollmentData,
      });

      // Verify first enrollment
      let policyResponse = await makeRequest(app, {
        method: "GET",
        url: `/time-off-policies/${policy.id}`,
      });

      expect(policyResponse.statusCode).toBe(200);
      let updatedPolicy = policyResponse.json<{
        data: { timeOffPolicy: TimeOffPolicy & { users: User[] } };
      }>().data.timeOffPolicy;
      expect(updatedPolicy.users).toHaveLength(1);
      expect(updatedPolicy.users.map((u) => u.id).sort()).toEqual([testUser.id].sort());

      // Second enrollment replacing previous users
      const secondEnrollmentData = {
        userIds: [secondUser.id],
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: `/time-off-policies/${policy.id}/users`,
        payload: secondEnrollmentData,
      });

      const data = response.json<{ data: TimeOffPolicyEnrollment[] }>().data;
      expect(response.statusCode).toBe(200);
      expect(data).toHaveLength(1);

      // Verify final state
      policyResponse = await makeRequest(app, {
        method: "GET",
        url: `/time-off-policies/${policy.id}`,
      });

      updatedPolicy = policyResponse.json<{
        data: { timeOffPolicy: TimeOffPolicy & { users: User[] } };
      }>().data.timeOffPolicy;
      expect(updatedPolicy.users).toHaveLength(2);
      expect(updatedPolicy.users.map((u) => u.id).sort()).toEqual([testUser.id, secondUser.id].sort());
    });

    it("should show an error when sending unexisting users", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const policy = await TimeOffPolicy.create({
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        organizationId: testOrganization.id,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: `/time-off-policies/${policy.id}/users`,
        payload: { userIds: [9999, 8888] }, // random unexisting suers
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);

      const error = response.json();
      expect(error.error).toEqual("Some of the assigned users don't exist");
    });
  });

  describe("DELETE /time-off-policies/:id/users/:userId", () => {
    it("should remove an employee from a time-off policy", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a policy and enroll the test user
      const policy = await TimeOffPolicy.create({
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        organizationId: testOrganization.id,
        addNewEmployeesAutomatically: true,
      });

      // First enroll the employee
      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(2, "years").toDate(),
        timeOffPolicyId: policy.id,
        userId: testUser.id,
      });

      // Then remove them
      const response = await makeRequest(app, {
        method: "DELETE",
        url: `/time-off-policies/${policy.id}/users/${testUser.id}`,
      });

      expect(response.statusCode).toBe(200);

      // Verify the enrollment was removed
      const enrollment = await TimeOffPolicyEnrollment.findOne({
        where: {
          timeOffPolicyId: policy.id,
          userId: testUser.id,
        },
      });

      expect(enrollment).toBeNull();
    });

    it("should return 404 if the policy or enrollment doesn't exist", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "DELETE",
        url: `/time-off-policies/999999/users/${testUser.id}`,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("POST /time-off-policies/:id/archive", () => {
    it("should archive a time-off policy by setting endDate", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const policy = await TimeOffPolicy.create({
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: `/time-off-policies/${policy.id}/archive`,
      });

      const data = response.json<{ data: TimeOffPolicy }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.endDate).toBeTruthy();

      // Verify policy appears in inactive list
      const listResponse = await makeRequest(app, {
        method: "GET",
        url: "/time-off-policies?includeArchived=true",
      });

      const listData = listResponse.json<{ data: { timeOffPolicies: TimeOffPolicy[] } }>().data.timeOffPolicies;
      expect(listData.find((item) => item.id === policy.id)).toBeTruthy();
    });

    it("should unarchive a time-off policy by setting endDate to null", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const policy = await TimeOffPolicy.create({
        name: "PTO Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
        endDate: new Date(),
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: `/time-off-policies/${policy.id}/unarchive`,
      });

      const data = response.json<{ data: TimeOffPolicy }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.endDate).toBeNull();

      // Verify policy appears in active list
      const listResponse = await makeRequest(app, {
        method: "GET",
        url: "/time-off-policies",
      });

      const listData = listResponse.json<{ data: { timeOffPolicies: TimeOffPolicy[] } }>().data.timeOffPolicies;
      expect(listData.find((item) => item.id === policy.id)).toBeTruthy();
    });

    it("should return 404 if policy doesn't exist", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-policies/999999/archive",
        payload: { archived: true },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("GET /users/:userId/time-off-policies", () => {
    it("should return policies for a given user", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      await PaySchedule.create({
        organizationId: testOrganization.id,
        payFrequency: "biweekly",
        firstPayday: dayjs().format("YYYY-MM-DD"),
        isActive: true,
        checkPayScheduleId: "test-schedule",
      });

      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({ start_date: dayjs().format("YYYY-MM-DD") });

      // Create test policies
      const policy1 = await TimeOffPolicy.create({
        name: "Policy 1",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        organizationId: testOrganization.id,
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      await TimeOffPolicy.create({
        name: "Policy 2",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        organizationId: testOrganization.id,
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      // Enroll testUser in policy1 only
      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(2, "years").toDate(),
        userId: testUser.id,
        timeOffPolicyId: policy1.id,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `/users/${testUser.id}/time-off-policies`,
      });

      const data = response.json<{ data: { timeOffPolicies: TimeOffPolicy[] } }>().data.timeOffPolicies;
      expect(response.statusCode).toBe(200);
      expect(data).toHaveLength(1);
      expect(data[0].id).toBe(policy1.id);
      expect(data[0].name).toBe("Policy 1");
    });

    it("should return empty array when user has no policies", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "GET",
        url: `/users/9999/time-off-policies`,
      });

      const data = response.json<{ data: { timeOffPolicies: TimeOffPolicy[] } }>().data.timeOffPolicies;
      expect(response.statusCode).toBe(200);
      expect(data).toHaveLength(0);
    });
  });

  describe("GET /my/time-off-policies", () => {
    it("should return the logged in user timeOff policies", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(2, "years").format("YYYY-MM-DD"),
      });

      await PaySchedule.create({
        organizationId: testOrganization.id,
        payFrequency: "biweekly",
        firstPayday: dayjs().format("YYYY-MM-DD"),
        isActive: true,
        checkPayScheduleId: "test-schedule",
      });

      // Create test policies
      const policy1 = await TimeOffPolicy.create({
        name: "Policy 1",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 120,
        organizationId: testOrganization.id,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      await TimeOffPolicy.create({
        name: "Policy 2",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        organizationId: testOrganization.id,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 40,
        addNewEmployeesAutomatically: true,
      });

      // Enroll testUser in policy1 only
      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(2, "years").toDate(),
        userId: testUser.id,
        timeOffPolicyId: policy1.id,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `/my/time-off-policies`,
      });

      const data = response.json<{
        data: { timeOffPolicies: (TimeOffPolicy & { availableHours: number })[] };
      }>().data.timeOffPolicies;
      expect(response.statusCode).toBe(200);
      expect(data).toHaveLength(1);
      expect(data[0].id).toBe(policy1.id);
      expect(data[0].name).toBe("Policy 1");
      expect(data[0].availableHours).toBeCloseTo(151, 0);
    });
  });

  describe("GET /time-off-policies/balances/:year", () => {
    it("should return time off balances for all users", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;
      await PaySchedule.create({
        organizationId: testOrganization.id,
        payFrequency: "biweekly",
        firstPayday: dayjs().format("YYYY-MM-DD"),
        isActive: true,
        checkPayScheduleId: "test-schedule",
      });

      // Mock Check API response
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().startOf("year").subtract(1, "year").format("YYYY-MM-DD"),
      });

      // Create another test user
      const secondUser = await User.create({
        email: "<EMAIL>",
        firstName: "Test2",
        lastName: "User2",
        organizationId: testOrganization.id,
        checkEmployeeId: "456efg",
      });

      // Create test policies
      const vacationPolicy = await TimeOffPolicy.create({
        name: "Vacation Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        organizationId: testOrganization.id,
        accrualHoursRate: 80,
        carryoverLimit: 40,
      });

      const accruedPolicy = await TimeOffPolicy.create({
        name: "Accrued Leave",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        organizationId: testOrganization.id,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 96,
        carryoverLimit: 0,
      });

      const accruedPolicyForSecondUser = await TimeOffPolicy.create({
        name: "Accrued Leave second user",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        organizationId: testOrganization.id,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 10,
        carryoverLimit: 4,
      });

      // Enroll users in policies
      await TimeOffPolicyEnrollment.bulkCreate([
        { userId: testUser.id, timeOffPolicyId: vacationPolicy.id, startDate: dayjs().subtract(2, "years").toDate() },
        { userId: testUser.id, timeOffPolicyId: accruedPolicy.id, startDate: dayjs().subtract(2, "years").toDate() },
        {
          userId: secondUser.id,
          timeOffPolicyId: accruedPolicyForSecondUser.id,
          startDate: dayjs().subtract(2, "years").toDate(),
        },
      ]);

      // Create some time off requests
      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: accruedPolicy.id,
        startDate: dayjs().startOf("year").toDate(),
        endDate: dayjs().startOf("year").add(2, "days").toDate(),
        totalHours: 16,
        reviewedBy: testUser.id,
      });

      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: accruedPolicy.id,
        startDate: dayjs().startOf("year").toDate(),
        endDate: dayjs().startOf("year").add(2, "days").toDate(),
        totalHours: 8,
        status: TimeOffRequestStatus.PAID,
        reviewedBy: testUser.id,
      });
      await TimeOffRequest.create({
        userId: secondUser.id,
        timeOffPolicyId: accruedPolicyForSecondUser.id,
        startDate: dayjs().startOf("year").toDate(),
        endDate: dayjs().startOf("year").add(2, "days").toDate(),
        totalHours: 2,
        reviewedBy: testUser.id,
      });

      const currentYear = dayjs().year();

      const response = await makeRequest(app, {
        method: "GET",
        url: `/time-off-policies/balances/${currentYear}`,
      });

      const data = response.json<{ data: UserBalance[] }>().data;
      expect(response.statusCode).toBe(200);

      const biWeeksPassed = Math.floor(dayjs().diff(dayjs().startOf("year"), "weeks") / 2);

      const accrualRateFirstPolicy = accruedPolicy.accrualHoursRate / 26;
      const accruedHoursFirstPolicy = accrualRateFirstPolicy * biWeeksPassed;

      // Check first user's balances
      const firstUserBalance = data.find((b: UserBalance) => b.id === testUser.id);
      expect(firstUserBalance.policies).toHaveLength(2);
      const policyToCheck = firstUserBalance.policies.find((p: PolicyBalance) => p.id === accruedPolicy.id);
      expect(policyToCheck.accruedHours).toBeCloseTo(accruedHoursFirstPolicy, 0);
      expect(policyToCheck.usedHours).toBe(24);
      expect(policyToCheck.availableHours).toBeCloseTo(accruedHoursFirstPolicy - 24, 0);

      // Check second user's balances
      const accrualRateSecondPolicy = accruedPolicyForSecondUser.accrualHoursRate / 26;
      const accruedHoursSecondPolicy = accrualRateSecondPolicy * biWeeksPassed;

      const secondUserBalance = data.find((b: UserBalance) => b.id === secondUser.id);
      expect(secondUserBalance.policies).toHaveLength(1);
      expect(secondUserBalance.policies[0]).toMatchObject({
        name: "Accrued Leave second user",
        accruedHours: accruedHoursSecondPolicy + accruedPolicyForSecondUser.carryoverLimit,
        usedHours: 2,
        availableHours: accruedHoursSecondPolicy + accruedPolicyForSecondUser.carryoverLimit - 2,
      });
    });

    it("should handle users with no time off requests", async () => {
      const {
        app,
        testUser,
        testOrganization,
        cleanup: testCleanup,
      } = await setupTestEnvironment({ userProps: { checkEmployeeId: "random-id" } });
      cleanup = testCleanup;

      await PaySchedule.create({
        organizationId: testOrganization.id,
        payFrequency: "biweekly",
        firstPayday: dayjs().format("YYYY-MM-DD"),
        isActive: true,
        checkPayScheduleId: "test-schedule",
      });

      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(2, "years").format("YYYY-MM-DD"),
      });

      const policy = await TimeOffPolicy.create({
        name: "Vacation Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualResetDate: "01-01",
        accrualMethod: AccrualMethod.FIXED,
        organizationId: testOrganization.id,
        accrualHoursRate: 80,
        carryoverLimit: 40,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().startOf('year').add(6, "months").toDate(),
        userId: testUser.id,
        timeOffPolicyId: policy.id,
      });

      const currentYear = dayjs().year();
      const response = await makeRequest(app, {
        method: "GET",
        url: `/time-off-policies/balances/${currentYear}`,
      });

      const data = response.json<{ data: UserBalance[] }>().data;
      expect(response.statusCode).toBe(200);

      const userBalance = data.find((item) => item.id === testUser.id);
      expect(userBalance.policies[0].name).toBe("Vacation Policy");
      expect(userBalance.policies[0].accruedHours).toBeCloseTo(0, 0);
      expect(userBalance.policies[0].usedHours).toBe(0);
      expect(userBalance.policies[0].availableHours).toBeCloseTo(0, 0);
    });
  });
});
