import { afterAll, beforeAll, describe, expect, it } from "vitest";
import { Express } from "express";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import Organization from "@/models/organization";
import User from "@/models/user";

describe("Authentication Routes", () => {
  let cleanup: () => void;
  let app: Express;
  let testUser: User;
  let testOrganization: Organization;

  beforeAll(async () => {
    const testSetup = await setupTestEnvironment({
      userProps: {
        phone: (Math.random() * 10).toString() + (Math.random() * 10).toString(),
      },
    });

    app = testSetup.app;
    cleanup = testSetup.cleanup;
    testUser = testSetup.testUser;
    testOrganization = testSetup.testOrganization;
  });

  afterAll(async () => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("POST /login", () => {
    it("should return 400 if phone number is missing", async () => {
      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: {},
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe("Missing or invalid phone number");
    });

    it("should return 400 if phone number is invalid", async () => {
      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: "123" },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe("Missing or invalid phone number");
    });

    it("should return 404 if user not found", async () => {
      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: "+15555555555" },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe("User not found.");
    });

    it("should return 403 if organization is churned", async () => {
      await testOrganization.update({ isChurned: true });

      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(403);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe("Organization is not active.");

      // Reset organization state
      await testOrganization.update({ isChurned: false });
    });

    it("should return 403 if user is archived", async () => {
      await testUser.update({ isArchived: true });

      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(403);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe("User is not active.");

      // Reset user state
      await testUser.update({ isArchived: false });
    });

    it("should return 400 if SMS cooldown not expired", async () => {
      process.env.SMS_COOLDOWN_SECONDS="1"
      // First request to set OTP
      await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      // Immediate second request
      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toContain("You have to wait");
      process.env.SMS_COOLDOWN_SECONDS=undefined
    });

    it("should successfully send OTP for valid request", async () => {
      const response = await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(200);

      // Verify user was updated with OTP
      const updatedUser = await User.findByPk(testUser.id);
      expect(updatedUser?.otpSmsCode).not.toBeNull();
      expect(updatedUser?.otpCodeSentAt).not.toBeNull();
    });
  });

  describe("POST /verify-otp", () => {
    it("should return 401 if session user is missing", async () => {
      const { app } = await setupTestEnvironment({
        authed: false,
      });
      const response = await makeRequest(app, {
        method: "POST",
        url: "verify-otp",
        payload: { code: "123456" },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(401);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toBe("Missing Auth cookie");
    });

    it("should return error for wrong OTP code", async () => {
      // First login to set session
      await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: "verify-otp",
        payload: { code: "000000" },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(403);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.error).toContain("Wrong OTP code");
    });

    it("should successfully verify correct OTP", async () => {
      // First login to set session and OTP
      await makeRequest(app, {
        method: "POST",
        url: "login",
        payload: { phone: testUser.phone },
        throwOnError: false,
      });

      const user = await User.findByPk(testUser.id);
      const otpCode = user?.otpSmsCode;

      const response = await makeRequest(app, {
        method: "POST",
        url: "verify-otp",
        payload: { code: otpCode },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);
      expect(responseBody.data).toBeDefined();
      expect(responseBody.data.id).toBe(testUser.id);
      expect(responseBody.data.phone).toBe(testUser.phone);

      // Verify OTP was cleared
      const updatedUser = await User.findByPk(testUser.id);
      expect(updatedUser?.otpSmsCode).toBeNull();
      expect(updatedUser?.otpCodeSentAt).toBeNull();
      expect(updatedUser?.status).toBe("ONBOARDED");
    });
  });
});
