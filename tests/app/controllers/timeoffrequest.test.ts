import { afterEach, describe, expect, it, vi } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import TimeOffPolicy, { AccrualMethod, TimeOffPolicyType } from "@/models/timeoffpolicy";
import User from "@/models/user";
import * as sendSMSModule from "@/util/sendSMS";
import dayjs from "dayjs";
import { CheckService } from "@/services/check";
import TimeOffPolicyEnrollment from "@/models/timeoffpolicyenrollment";

type TimeOffRequestExtended = TimeOffRequest & {
  user: User;
  reviewer: User;
  timeOffPolicy: TimeOffPolicy;
};

const mockSendEmail = vi.fn().mockReturnValue({
  promise: () => Promise.resolve(),
});

vi.mock("aws-sdk", () => {
  return {
    default: {
      SES: vi.fn().mockImplementation(() => ({
        sendEmail: mockSendEmail,
      })),
    },
  };
});

describe("TimeOffRequestController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("POST /time-off-requests", () => {
    it("as an ADMIN user, should create a time-off request successfully and send email notification", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy first
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
      });

      const startDate = dayjs("2024-01-01").startOf("day");
      const endDate = dayjs("2024-01-02").endOf("day");

      const requestData = {
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: startDate.valueOf(),
        endDate: endDate.valueOf(),
        totalHours: 16,
        requestNotes: "Vacation",
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: requestData,
      });

      const data = response.json<{ data: TimeOffRequestExtended }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.user.id).toBe(testUser.id);
      expect(data.timeOffPolicy.id).toBe(policy.id);
      expect(data.totalHours).toBe(16);
      expect(data.requestNotes).toBe("Vacation");
      expect(data.startDate).toBe(startDate.toISOString());
      expect(data.endDate).toBe(endDate.toISOString());
      expect(data.status).toBe(TimeOffRequestStatus.APPROVED);

      // Verify emails were not sent
      expect(mockSendEmail).not.toHaveBeenCalled();
    });

    it("as a WORKER user, should create a time-off request successfully and send email notification", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment({ userProps: { role: "WORKER" } });
      cleanup = testCleanup;

      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(2, "months").format("YYYY-MM-DD"),
      });

      const adminUser2 = await User.create({
        email: "<EMAIL>",
        firstName: "Admin",
        lastName: "Two",
        role: "ADMIN",
        organizationId: testUser.organizationId,
      });

      // Create a time off policy first
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 40, // 5 days * 8 hours
        carryoverLimit: 16, // 2 days * 8 hours
        organizationId: testUser.organizationId,
        addNewEmployeesAutomatically: true,
      });

      await TimeOffPolicyEnrollment.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date(),
      });

      const requestData = {
        timeOffPolicyId: policy.id,
        startDate: dayjs("2024-01-01").startOf("day").valueOf(),
        endDate: dayjs("2024-01-02").endOf("day").valueOf(),
        totalHours: 16,
        requestNotes: "Vacation",
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: requestData,
      });

      const data = response.json<{ data: TimeOffRequestExtended }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.user.id).toBe(testUser.id);
      expect(data.timeOffPolicy.id).toBe(policy.id);
      expect(data.totalHours).toBe(16);
      expect(data.requestNotes).toBe("Vacation");
      expect(data.status).toBe(TimeOffRequestStatus.PENDING);

      // Verify emails were sent to both admins
      expect(mockSendEmail).toHaveBeenCalledTimes(1);

      // Verify second admin email
      expect(mockSendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          Destination: {
            ToAddresses: [adminUser2.email],
          },
          Message: expect.objectContaining({
            Subject: {
              Charset: "UTF-8",
              Data: `Time-Off Request from ${testUser.firstName} ${testUser.lastName}`,
            },
          }),
        })
      );
    });

    it("as a WORKER user, should NOT be able to  create a time-off request for other users", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment({ userProps: { role: "WORKER" } });
      cleanup = testCleanup;

      const adminUser2 = await User.create({
        email: "<EMAIL>",
        firstName: "Admin",
        lastName: "Two",
        role: "ADMIN",
        organizationId: testUser.organizationId,
      });

      // Create a time off policy first
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
      });

      const requestData = {
        userId: adminUser2.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs("2024-01-01").startOf("day").valueOf(),
        endDate: dayjs("2024-01-02").endOf("day").valueOf(),
        totalHours: 16,
        requestNotes: "Vacation",
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: requestData,
        throwOnError: false,
      });

      const data = response.json<{ error: string }>();
      expect(response.statusCode).toBe(400);
      expect(data.error).toBe("You cannot create time-off requests for other users");
    });

    it("should return an error when requesting more hours than available in the policy", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(2, "months").format("YYYY-MM-DD"),
      });
      // Create a time off policy with limited hours
      const policy = await TimeOffPolicy.create({
        name: "Limited PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 40, // 5 days * 8 hours
        carryoverLimit: 16, // 2 days * 8 hours
        organizationId: testUser.organizationId,
      });

      await TimeOffPolicyEnrollment.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date(),
      });

      const requestData = {
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs("2024-01-01").startOf("day").valueOf(),
        endDate: dayjs("2024-01-07").endOf("day").valueOf(),
        totalHours: 999, // Requesting more hours than available
        requestNotes: "Vacation",
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: requestData,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toBe("Not enough hours available in the selected time off policy");
    });

    it("should validate required fields", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const policy = await TimeOffPolicy.create({
        name: "Limited PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 40,
        carryoverLimit: 16,
        organizationId: testUser.organizationId,
      });

      let response;

      response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: {},
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toBe("timeOffPolicyId is required");

      response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: {
          timeOffPolicyId: policy.id,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toBe("startDate is required");

      response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests",
        payload: {
          timeOffPolicyId: policy.id,
          startDate: dayjs("2024-01-01").valueOf(),
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toBe("endDate is required");
    });
  });

  describe("PUT /time-off-requests/:id", () => {
    it("should update a time-off request successfully", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        organizationId: testOrganization.id,
        isLimited: false,
        addNewEmployeesAutomatically: true,
      });

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const updateData = {
        totalHours: 24,
        requestNotes: "Extended vacation",
      };

      const response = await makeRequest(app, {
        method: "PUT",
        url: `/time-off-requests/${timeOffRequest.id}`,
        payload: updateData,
      });

      const data = response.json<{ data: TimeOffRequestExtended }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.totalHours).toBe(24);
      expect(data.requestNotes).toBe("Extended vacation");
      expect(data.status).toBe(TimeOffRequestStatus.PENDING);
    });
  });

  describe("GET /time-off-requests/:id", () => {
    it("should get a time-off request by id", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        organizationId: testOrganization.id,
        addNewEmployeesAutomatically: true,
      });

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `/time-off-requests/${timeOffRequest.id}`,
      });

      const data = response.json<{ data: TimeOffRequestExtended }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.id).toBe(timeOffRequest.id);
      expect(data.user.id).toBe(testUser.id);
      expect(data.timeOffPolicy.id).toBe(policy.id);
    });

    it("should return 404 for non-existent request", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "GET",
        url: "/time-off-requests/999999",
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("GET /time-off-requests", () => {
    it("should list time-off requests", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testUser.organizationId,
      });

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      // passing these search params just for code coverage
      const searchParams = new URLSearchParams({
        startDate: dayjs.utc(timeOffRequest.startDate).valueOf().toString(),
        endDate: dayjs.utc(timeOffRequest.endDate).valueOf().toString(),
      });

      searchParams.append("status", TimeOffRequestStatus.PENDING);
      searchParams.append("status", TimeOffRequestStatus.DECLINED);

      const response = await makeRequest(app, {
        method: "GET",
        url: `/time-off-requests?${searchParams}`,
      });

      const data = response.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>().data;
      expect(response.statusCode).toBe(200);
      const savedData = data.timeOffRequests.find((item) => item.id === timeOffRequest.id);
      expect(savedData.id).toBe(timeOffRequest.id);
      expect(savedData.user.id).toBe(testUser.id);
      expect(savedData.timeOffPolicy.id).toBe(policy.id);
    });

    it("should filter time-off requests by userId", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testUser.organizationId,
      });

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `/time-off-requests?${new URLSearchParams({
          userId: testUser.id.toString(),
        })}`,
      });

      const data = response.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.total).toBe(1);
      expect(data.timeOffRequests[0].id).toBe(timeOffRequest.id);
      expect(data.timeOffRequests[0].user.id).toBe(testUser.id);
      expect(data.timeOffRequests[0].timeOffPolicy.id).toBe(policy.id);
    });

    it("should filter time-off requests by start and/or end dates", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testUser.organizationId,
      });

      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-30"),
        endDate: new Date("2024-02-02"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-02-25"),
        endDate: new Date("2024-03-03"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const responseWithStartDate = await makeRequest(app, {
        method: "GET",
        url: `/time-off-requests?${new URLSearchParams({
          userId: testUser.id.toString(),
          startDate: new Date("2024-01-29").getTime().toString(),
        })}`,
      });

      const responseWithStartDateData = responseWithStartDate.json<{
        data: { data: TimeOffRequestExtended[]; total: number };
      }>().data;
      expect(responseWithStartDate.statusCode).toBe(200);
      expect(responseWithStartDateData.total).toBe(2);

      const responseWithEndDate = await makeRequest(app, {
        method: "GET",
        url: `/time-off-requests?${new URLSearchParams({
          userId: testUser.id.toString(),
          endDate: new Date("2024-03-29").getTime().toString(),
        })}`,
      });

      const responseWithEndDateData = responseWithEndDate.json<{
        data: { data: TimeOffRequestExtended[]; total: number };
      }>().data;
      expect(responseWithEndDate.statusCode).toBe(200);
      expect(responseWithEndDateData.total).toBe(2);

      const responseWithBothDates = await makeRequest(app, {
        method: "GET",
        url: `/time-off-requests?${new URLSearchParams({
          userId: testUser.id.toString(),
          startDate: new Date("2024-01-29").getTime().toString(),
          endDate: new Date("2024-02-10").getTime().toString(),
        })}`,
      });

      const responseWithBothDatesData = responseWithBothDates.json<{
        data: { data: TimeOffRequestExtended[]; total: number };
      }>().data;
      expect(responseWithBothDates.statusCode).toBe(200);
      expect(responseWithBothDatesData.total).toBe(1);
    });
  });

  describe("DELETE /time-off-requests/:id", () => {
    it("should delete a time-off request", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        organizationId: testOrganization.id,
        addNewEmployeesAutomatically: true,
      });

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const response = await makeRequest(app, {
        method: "DELETE",
        url: `/time-off-requests/${timeOffRequest.id}`,
      });

      expect(response.statusCode).toBe(200);

      // Verify the request was deleted
      const deletedRequest = await TimeOffRequest.findByPk(timeOffRequest.id);
      expect(deletedRequest).toBeNull();
    });

    it("should not allow deleting a PAID time off request", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        organizationId: testOrganization.id,
      });

      // Create a PAID time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Vacation",
        status: TimeOffRequestStatus.PAID,
      });

      const response = await makeRequest(app, {
        method: "DELETE",
        url: `/time-off-requests/${timeOffRequest.id}`,
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toBe("Cannot delete a paid Time-Off Request");

      const existingRequest = await TimeOffRequest.findByPk(timeOffRequest.id);
      expect(existingRequest).not.toBeNull();
      expect(existingRequest.id).toBe(timeOffRequest.id);
    });
  });

  describe("POST /time-off-requests/:id/approve", () => {
    it("should approve a time-off request successfully and send SMS", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Mock sendSMS function
      const sendSMSSpy = vi.spyOn(sendSMSModule, "sendSMS");

      // Update test user to have a phone number
      await testUser.update({ phone: "+1234567890" });

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      const startDate = new Date("2024-01-01");
      const endDate = new Date("2024-01-02");

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate,
        endDate,
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: `/time-off-requests/${timeOffRequest.id}/approve`,
      });

      const data = response.json<{ data: TimeOffRequestExtended }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.id).toBe(timeOffRequest.id);
      expect(data.status).toBe(TimeOffRequestStatus.APPROVED);
      expect(data.reviewedBy).toBe(testUser.id);

      // Verify SMS was sent
      expect(sendSMSSpy).toHaveBeenCalledWith(
        testUser.phone,
        `Your time off request from ${dayjs(startDate).format("MMM DD, YYYY")} to ${dayjs(endDate).format(
          "MMM DD, YYYY"
        )} has been approved.`
      );
    });

    it("should return 404 for non-existent request", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests/999999/approve",
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("POST /time-off-requests/:id/decline", () => {
    it("should decline a time-off request successfully and send SMS", async () => {
      const { app, testUser, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Mock sendSMS function
      const sendSMSSpy = vi.spyOn(sendSMSModule, "sendSMS");

      // Update test user to have a phone number
      await testUser.update({ phone: "+1234567890" });

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testOrganization.id,
      });

      const startDate = new Date("2024-01-01");
      const endDate = new Date("2024-01-02");

      // Create a time off request
      const timeOffRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate,
        endDate,
        totalHours: 16,
        requestNotes: "Vacation",
      });

      const response = await makeRequest(app, {
        method: "POST",
        url: `/time-off-requests/${timeOffRequest.id}/decline`,
        payload: {
          declineNotes: "Declined due to team coverage",
        },
      });

      const data = response.json<{ data: TimeOffRequestExtended }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.id).toBe(timeOffRequest.id);
      expect(data.status).toBe(TimeOffRequestStatus.DECLINED);
      expect(data.declineNotes).toBe("Declined due to team coverage");
      expect(data.reviewedBy).toBe(testUser.id);

      // Verify SMS was sent
      expect(sendSMSSpy).toHaveBeenCalledWith(
        testUser.phone,
        `Your time off request from ${dayjs(startDate).format("MMM DD, YYYY")} to ${dayjs(endDate).format(
          "MMM DD, YYYY"
        )} has been declined.`
      );
    });

    it("should return 404 for non-existent request", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "POST",
        url: "/time-off-requests/999999/decline",
        payload: {
          reviewNotes: "Declined due to team coverage",
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe("GET /users/:userId/time-off-requests", () => {
    it("should get user time-off requests successfully", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testUser.organizationId,
      });

      // Create multiple time off requests with different statuses
      const pendingRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Pending Vacation",
      });

      const approvedRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-02-01"),
        endDate: new Date("2024-02-02"),
        totalHours: 16,
        requestNotes: "Approved Vacation",
        reviewedBy: testUser.id,
      });

      // Test getting all requests
      const response = await makeRequest(app, {
        method: "GET",
        url: `/users/${testUser.id}/time-off-requests`,
      });

      const data = response.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.total).toBe(2);
      expect(data.timeOffRequests.map((r) => r.id).sort()).toEqual([pendingRequest.id, approvedRequest.id].sort());

      // Test filtering by status
      const pendingResponse = await makeRequest(app, {
        method: "GET",
        url: `/users/${testUser.id}/time-off-requests?${new URLSearchParams({
          "status[]": "PENDING",
        })}`,
      });

      const pendingData = pendingResponse.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>()
        .data;
      expect(pendingResponse.statusCode).toBe(200);
      expect(pendingData.total).toBe(1);
      expect(pendingData.timeOffRequests[0].id).toBe(pendingRequest.id);
      expect(pendingData.timeOffRequests[0].reviewedBy).toBe(null);
      expect(pendingData.timeOffRequests[0].status).toBe(TimeOffRequestStatus.PENDING);

      // Test filtering by approved status
      const approvedResponse = await makeRequest(app, {
        method: "GET",
        url: `/users/${testUser.id}/time-off-requests?${new URLSearchParams({
          "status[]": "APPROVED",
        })}`,
      });

      const approvedData = approvedResponse.json<{
        data: { timeOffRequests: TimeOffRequestExtended[]; total: number };
      }>().data;
      expect(approvedResponse.statusCode).toBe(200);
      expect(approvedData.total).toBe(1);
      expect(approvedData.timeOffRequests[0].id).toBe(approvedRequest.id);
      expect(approvedData.timeOffRequests[0].reviewedBy).toBe(testUser.id);
      expect(approvedData.timeOffRequests[0].status).toBe(TimeOffRequestStatus.APPROVED);
    });

    it("should return empty list for user with no requests", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "GET",
        url: `/users/${testUser.id}/time-off-requests`,
      });

      const data = response.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.total).toBe(0);
      expect(data.timeOffRequests).toHaveLength(0);
    });
  });

  describe("GET /my/time-off-requests", () => {
    it("should get current user's time-off requests successfully", async () => {
      const { app, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create a time off policy
      const policy = await TimeOffPolicy.create({
        name: "Test Policy",
        type: TimeOffPolicyType.PTO,
        isLimited: false,
        addNewEmployeesAutomatically: true,
        organizationId: testUser.organizationId,
      });

      // Create requests with different statuses
      const pendingRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-01-02"),
        totalHours: 16,
        requestNotes: "Pending Vacation",
      });

      const approvedRequest = await TimeOffRequest.create({
        userId: testUser.id,
        timeOffPolicyId: policy.id,
        startDate: new Date("2024-02-01"),
        endDate: new Date("2024-02-02"),
        totalHours: 16,
        requestNotes: "Approved Vacation",
        reviewedBy: testUser.id,
      });

      // Test getting all requests
      const response = await makeRequest(app, {
        method: "GET",
        url: "/my/time-off-requests",
      });

      const data = response.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>().data;
      expect(response.statusCode).toBe(200);
      expect(data.total).toBe(2);
      expect(data.timeOffRequests.map((r) => r.id).sort()).toEqual([pendingRequest.id, approvedRequest.id].sort());

      const queryParams = new URLSearchParams();
      queryParams.append("status", "PENDING");
      queryParams.append("status", "DECLINED");

      // Test filtering by status
      const pendingResponse = await makeRequest(app, {
        method: "GET",
        url: `/my/time-off-requests?${queryParams}`,
      });

      const pendingData = pendingResponse.json<{ data: { timeOffRequests: TimeOffRequestExtended[]; total: number } }>()
        .data;
      expect(pendingResponse.statusCode).toBe(200);
      expect(pendingData.total).toBe(1);
      expect(pendingData.timeOffRequests[0].id).toBe(pendingRequest.id);
      expect(pendingData.timeOffRequests[0].reviewedBy).toBe(null);
      expect(pendingData.timeOffRequests[0].status).toBe(TimeOffRequestStatus.PENDING);
    });
  });
});
