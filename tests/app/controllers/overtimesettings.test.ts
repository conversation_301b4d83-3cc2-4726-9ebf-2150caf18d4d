import { afterEach, describe, expect, it } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import OvertimeSettings from "@/models/overtimesettings";

describe("OvertimeSettingsController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("PATCH /overtime-settings", () => {
    it("should update overtime settings with valid data", async () => {
      const { app, testOrganization, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;


      const response = await makeRequest(app, {
        method: "PATCH",
        url: `overtime-settings/${testOrganization.overtimeSettings[0].id}`,
        payload: {
          weeklyOvertimeEnabled: true,
          dailyOvertimeEnabled: true,
          dailyOvertimeThreshold: 600,
          weeklyOvertimeThreshold: 3000,
          overtimeMultiplier: 2.0,
          weekStartDay: "SUNDAY",
        },
      });

      expect(response.statusCode).toBe(200);

      const updatedSettings = await OvertimeSettings.findOne({
        where: { organizationId: testOrganization.id },
      });

      expect(updatedSettings?.weeklyOvertimeEnabled).toBe(true);
      expect(updatedSettings?.dailyOvertimeEnabled).toBe(true);
      expect(updatedSettings?.dailyOvertimeThreshold).toBe(600);
      expect(updatedSettings?.weeklyOvertimeThreshold).toBe(3000);
      expect(updatedSettings?.overtimeMultiplier).toBe(2.0);
      expect(updatedSettings?.weekStartDay).toBe("SUNDAY");
    });

    it("should return 400 for invalid threshold values", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `overtime-settings/${testOrganization.overtimeSettings[0].id}`,
        payload: {
          dailyOvertimeThreshold: -1,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "dailyOvertimeThreshold must be greater than or equal to 0",
      });
    });

    it("should return 400 for invalid multiplier value", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `overtime-settings/${testOrganization.overtimeSettings[0].id}`,
        payload: {
          overtimeMultiplier: 0.5,
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "overtimeMultiplier must be greater than or equal to 1",
      });
    });

    it("should return 400 for invalid weekStartDay value", async () => {
      const { app, testOrganization } = await setupTestEnvironment();

      const response = await makeRequest(app, {
        method: "PATCH",
        url: `overtime-settings/${testOrganization.overtimeSettings[0].id}`,
        payload: {
          weekStartDay: "INVALID_DAY",
        },
        throwOnError: false,
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.payload)).toEqual({
        error: "weekStartDay must be one of [SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY]",
      });
    });
  });
});
