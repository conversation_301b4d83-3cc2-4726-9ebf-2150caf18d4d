import { afterEach, describe, expect, it, vi } from "vitest";
import { setupTestEnvironment } from "../../../helpers/testSetup";
import { makeRequest } from "../../../helpers/request";
import IntegrationObjectSetting, { OBJECT_TYPES, SYNC_TYPES } from "@/models/integrationobjectsetting";
import IntegrationUserToken from "@/models/integrationusertoken";

describe("IntegrationSettingsController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
    vi.clearAllMocks();
  });

  describe("GET /integrations/:id/settings", () => {
    it("should return integration settings", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create an integration token
      const integrationToken = await IntegrationUserToken.create({
        platform: "QUICKBOOKS",
        provider: "RUTTER",
        isEnabled: true,
        createdBy: testUser.id,
        organizationId: testOrganization.id,
      });

      // Create some settings for the integration
      await IntegrationObjectSetting.create({
        integrationUserTokenId: integrationToken.id,
        objectType: OBJECT_TYPES.PROJECTS,
        isEnabled: true,
        syncType: SYNC_TYPES.PUSH,
      });

      await IntegrationObjectSetting.create({
        integrationUserTokenId: integrationToken.id,
        objectType: OBJECT_TYPES.EMPLOYEES,
        isEnabled: false,
        syncType: SYNC_TYPES.PULL,
      });

      // Make the request
      const response = await makeRequest(app, {
        method: "GET",
        url: `integrations/${integrationToken.platform}/settings`,
      });

      expect(response.statusCode).toBe(200);
      const { data } = JSON.parse(response.payload);

      expect(data.settings).toHaveLength(2);

      // Verify the settings data
      const projectsSetting = data.settings.find((s: any) => s.objectType === OBJECT_TYPES.PROJECTS);
      const employeesSetting = data.settings.find((s: any) => s.objectType === OBJECT_TYPES.EMPLOYEES);

      expect(projectsSetting).toBeDefined();
      expect(projectsSetting.isEnabled).toBe(true);
      expect(projectsSetting.syncType).toBe(SYNC_TYPES.PUSH);

      expect(employeesSetting).toBeDefined();
      expect(employeesSetting.isEnabled).toBe(false);
      expect(employeesSetting.syncType).toBe(SYNC_TYPES.PULL);
    });


    it("should return empty array when no settings exist", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create an integration token without settings
      const integrationToken = await IntegrationUserToken.create({
        platform: "QUICKBOOKS",
        provider: "RUTTER",
        isEnabled: true,
        createdBy: testUser.id,
        organizationId: testOrganization.id,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: `integrations/${integrationToken.platform}/settings`,
      });

      expect(response.statusCode).toBe(200);
      const { data } = JSON.parse(response.payload);

      expect(data.settings).toEqual([]);
    });
  });

  describe("POST /integrations/:id/settings", () => {
    it("should create a new integration setting", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create an integration token
      const integrationToken = await IntegrationUserToken.create({
        platform: "QUICKBOOKS",
        provider: "RUTTER",
        isEnabled: true,
        createdBy: testUser.id,
        organizationId: testOrganization.id,
      });

      const settingData = {
        objectType: OBJECT_TYPES.PROJECTS,
        isEnabled: true,
        syncType: SYNC_TYPES.PUSH,
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: `integrations/${integrationToken.platform}/settings`,
        payload: settingData,
      });

      expect(response.statusCode).toBe(200);
      const { data } = JSON.parse(response.payload);

      expect(data.setting).toBeDefined();
      expect(data.setting.objectType).toBe(OBJECT_TYPES.PROJECTS);
      expect(data.setting.isEnabled).toBe(true);
      expect(data.setting.syncType).toBe(SYNC_TYPES.PUSH);
      expect(data.setting.integrationUserTokenId).toBe(integrationToken.id);
    });

    it("should update an existing integration setting", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create an integration token
      const integrationToken = await IntegrationUserToken.create({
        platform: "QUICKBOOKS",
        provider: "RUTTER",
        isEnabled: true,
        createdBy: testUser.id,
        organizationId: testOrganization.id,
      });

      // Create an initial setting
      await IntegrationObjectSetting.create({
        integrationUserTokenId: integrationToken.id,
        objectType: OBJECT_TYPES.PROJECTS,
        isEnabled: false,
        syncType: SYNC_TYPES.PULL,
      });

      // Update the setting
      const updatedSettingData = {
        objectType: OBJECT_TYPES.PROJECTS,
        isEnabled: true,
        syncType: SYNC_TYPES.PUSH,
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: `integrations/${integrationToken.platform}/settings`,
        payload: updatedSettingData,
      });

      expect(response.statusCode).toBe(200);
      const { data } = JSON.parse(response.payload);

      expect(data.setting).toBeDefined();
      expect(data.setting.objectType).toBe(OBJECT_TYPES.PROJECTS);
      expect(data.setting.isEnabled).toBe(true);
      expect(data.setting.syncType).toBe(SYNC_TYPES.PUSH);

      // Verify in database
      const settings = await IntegrationObjectSetting.findAll({
        where: {
          integrationUserTokenId: integrationToken.id,
          objectType: OBJECT_TYPES.PROJECTS
        }
      });

      expect(settings.length).toBe(1);
      expect(settings[0].isEnabled).toBe(true);
      expect(settings[0].syncType).toBe(SYNC_TYPES.PUSH);
    });

    it("should return 404 for non-existent integration", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const settingData = {
        objectType: OBJECT_TYPES.PROJECTS,
        isEnabled: true,
        syncType: SYNC_TYPES.PUSH,
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: `integrations/999999/settings`,
        payload: settingData,
        throwOnError: false
      });

      expect(response.statusCode).toBe(404);
    });

    it("should return 400 for invalid object type", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create an integration token
      const integrationToken = await IntegrationUserToken.create({
        platform: "QUICKBOOKS",
        provider: "RUTTER",
        isEnabled: true,
        createdBy: testUser.id,
        organizationId: testOrganization.id,
      });

      const invalidSettingData = {
        objectType: "INVALID_TYPE",
        isEnabled: true,
        syncType: SYNC_TYPES.PUSH,
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: `integrations/${integrationToken.platform}/settings`,
        payload: invalidSettingData,
        throwOnError: false
      });

      expect(response.statusCode).toBe(400);
    });

    it("should return 400 for invalid sync type", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create an integration token
      const integrationToken = await IntegrationUserToken.create({
        platform: "QUICKBOOKS",
        provider: "RUTTER",
        isEnabled: true,
        createdBy: testUser.id,
        organizationId: testOrganization.id,
      });

      const invalidSettingData = {
        objectType: OBJECT_TYPES.PROJECTS,
        isEnabled: true,
        syncType: "INVALID_SYNC_TYPE",
      };

      const response = await makeRequest(app, {
        method: "POST",
        url: `integrations/${integrationToken.platform}/settings`,
        payload: invalidSettingData,
        throwOnError: false
      });

      expect(response.statusCode).toBe(400);
    });
  });
});
