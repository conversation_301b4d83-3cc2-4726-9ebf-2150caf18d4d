import { afterEach, describe, expect, it } from "vitest";
import { setupTestEnvironment } from "../../helpers/testSetup";
import { makeRequest } from "../../helpers/request";
import { ProjectPhoto, ProjectPhotosCollection, Project } from "@/models";

describe("ProjectPhotosCollectionController", () => {
  let cleanup: () => void;

  afterEach(() => {
    if (cleanup) {
      cleanup();
    }
  });

  describe("GET /project-photos-collections", () => {
    it("should return only non-deleted photos", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create test project
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create test collection
      const collection = await ProjectPhotosCollection.create({
        note: "Test collection",
        organizationId: testOrganization.id,
        projectId: project.id,
        createdBy: testUser.id,
      });

      // Create test photos - one deleted, one not deleted
      await ProjectPhoto.create({
        objectId: "test-object-1",
        collectionId: collection.id,
        createdBy: testUser.id,
        isDeleted: false,
      });

      await ProjectPhoto.create({
        objectId: "test-object-2",
        collectionId: collection.id,
        createdBy: testUser.id,
        isDeleted: true,
      });

      const response = await makeRequest(app, {
        method: "GET",
        url: "project-photos-collections",
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.payload);

      expect(responseBody.data.projectPhotosCollections.length).toBe(1);
      expect(responseBody.data.projectPhotosCollections[0].projectPhotos.length).toBe(1);
      expect(responseBody.data.projectPhotosCollections[0].projectPhotos[0].objectId).toBe("test-object-1");
    });
  });

  describe("DELETE /project-photos/:id", () => {
    it("should soft delete a project photo", async () => {
      const { app, testOrganization, testUser, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      // Create test project
      const project = await Project.create({
        name: "Test Project",
        organizationId: testOrganization.id,
        status: "ACTIVE",
        isPrevailingWage: false,
      });

      // Create test collection
      const collection = await ProjectPhotosCollection.create({
        note: "Test collection",
        organizationId: testOrganization.id,
        projectId: project.id,
        createdBy: testUser.id,
      });

      // Create test photo
      const photo = await ProjectPhoto.create({
        objectId: "test-object-1",
        collectionId: collection.id,
        createdBy: testUser.id,
        isDeleted: false,
      });

      // Delete the photo
      const deleteResponse = await makeRequest(app, {
        method: "DELETE",
        url: `project-photos/${photo.id}`,
      });

      expect(deleteResponse.statusCode).toBe(200);
      expect(JSON.parse(deleteResponse.payload)).toEqual({});

      // Verify the photo is soft deleted
      const updatedPhoto = await ProjectPhoto.findByPk(photo.id);
      expect(updatedPhoto?.isDeleted).toBe(true);

      // Verify the photo doesn't appear in collections
      const getResponse = await makeRequest(app, {
        method: "GET",
        url: "project-photos-collections",
      });

      expect(getResponse.statusCode).toBe(200);
      const responseBody = JSON.parse(getResponse.payload);

      expect(responseBody.data.projectPhotosCollections.length).toBe(0);
    });

    it("should return 404 for non-existent photo", async () => {
      const { app, cleanup: testCleanup } = await setupTestEnvironment();
      cleanup = testCleanup;

      const response = await makeRequest(app, {
        method: "DELETE",
        url: "project-photos/99999",
        throwOnError: false,
      });

      expect(response.statusCode).toBe(404);
      expect(JSON.parse(response.payload)).toEqual({
        message: "Project photo not found",
      });
    });
  });
});
