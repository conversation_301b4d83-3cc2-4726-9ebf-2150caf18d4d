import { beforeEach, describe, expect, it } from "vitest";
import { TimesheetsService } from "@/services/timesheets";
import {
  <PERSON>r<PERSON><PERSON><PERSON>,
  ProjectBuilder,
  TimesheetBuilder,
  EarningRateBuilder,
  createOvertimeOrganization,
} from "../../factories";
import { Organization, User, Project, TimeSheet } from "@/models";

describe("TimesheetsService", () => {
  let timesheetsService: TimesheetsService;
  let organization: Organization;
  let user: User;
  let project: Project;
  let regularRate: number;
  let overtimeRate: number;

  beforeEach(async () => {
    // Initialize service
    timesheetsService = new TimesheetsService();

    // Create base test data
    organization = await createOvertimeOrganization();

    project = await new ProjectBuilder().withOrganization(organization.id).buildAndCreate();

    // Create earning rates for the user
    user = await new UserBuilder().withOrganization(organization.id).buildAndCreate();

    // Create earning rates for the user
    const rates = await Promise.all([
      new EarningRateBuilder().forOrganization(organization.id).forUser(user.id).asRegularRate(25.0).buildAndCreate(),
      new EarningRateBuilder().forOrganization(organization.id).forUser(user.id).asOvertimeRate(25.0).buildAndCreate(),
    ]);

    [regularRate, overtimeRate] = rates.map((rate) => rate.id);
  });

  describe("findOne", () => {
    it("should find a single timesheet by id", async () => {
      // Create a test timesheet
      const timesheet = await new TimesheetBuilder()
        .forWorker(user.id)
        .withOrganization(organization.id)
        .forProject(project.id)
        .withEarningRates(regularRate, overtimeRate)
        .withClockInAndOut(new Date("2024-03-01T09:00:00Z"), new Date("2024-03-01T17:00:00Z"))
        .buildAndCreate();

      // Find the timesheet
      const foundTimesheet = await timesheetsService.findOne({
        where: { id: timesheet.id },
      });

      // Assertions
      expect(foundTimesheet).toBeDefined();
      expect(foundTimesheet.id).toBe(timesheet.id);
      expect(foundTimesheet.workerId).toBe(user.id);
      expect(foundTimesheet.organizationId).toBe(organization.id);
      expect(foundTimesheet.projectId).toBe(project.id);
      expect(foundTimesheet.status).toBe("CLOCKED_IN");
    });

    it("should return null for non-existent timesheet", async () => {
      const result = await timesheetsService.findOne({
        where: { id: 99999 },
      });

      expect(result).toBeNull();
    });
  });

  describe("list", () => {
    const timesheets: TimeSheet[] = [];
    beforeEach(async () => {
      // Create multiple timesheets for testing list functionality
      const clockInTimes = [
        new Date("2024-03-04T09:00:00Z"), // Monday
        new Date("2024-03-05T09:00:00Z"), // Tuesday
        new Date("2024-03-06T09:00:00Z"), // Wednesday
      ];

      for (const clockIn of clockInTimes) {
        const clockOut = new Date(clockIn);
        clockOut.setHours(clockOut.getHours() + 8); // 8-hour shift

        const timesheet = await new TimesheetBuilder()
          .forWorker(user.id)
          .withOrganization(organization.id)
          .forProject(project.id)
          .withEarningRates(regularRate, overtimeRate)
          .withClockInAndOut(clockIn, clockOut)
          .buildAndCreate();

        timesheets.push(timesheet);
      }
    });

    it("should list timesheets within a date range", async () => {
      const result = await timesheetsService.list(organization, {
        from: new Date("2024-03-04T00:00:00Z").getTime(),
        to: new Date("2024-03-06T23:59:59Z").getTime(),
      });

      expect(result.timesheets).toHaveLength(3);
      expect(result.totalMinutes).toBe(3 * 8 * 60); // 3 days * 8 hours * 60 minutes
    });

    it("should list timesheets for a specific user", async () => {
      const result = await timesheetsService.list(organization, {
        from: new Date("2024-03-04T00:00:00Z").getTime(),
        to: new Date("2024-03-06T23:59:59Z").getTime(),
        userId: user.id.toString(),
      });

      expect(result.timesheets).toHaveLength(3);
      expect(result.timesheets.every((t) => t.workerId === user.id)).toBe(true);
    });

    it("should have correct calculations for timesheets", async () => {
      const result = await timesheetsService.list(organization, {
        from: new Date("2024-03-04T00:00:00Z").getTime(),
        to: new Date("2024-03-06T23:59:59Z").getTime(),
        userId: user.id.toString(),
      });

      expect(result.timesheets).toHaveLength(3);
      expect(result.timesheets.every((t) => t.workerId === user.id)).toBe(true);
      expect(result.totalMinutes).toBe(3 * 8 * 60); // 3 days * 8 hours * 60 minutes
      expect(result.totalWages).toBe(3 * 8 * 25); // 3 days * 8 hours * $25/hour

      // look into individual timesheets
      expect(result.timesheets[0].regularMinutes).toBe(8 * 60); // 8 hours * 60 minutes
      expect(result.timesheets[0].overtimeMinutes).toBe(0);
      expect(result.timesheets[0].regularWages).toBe(8 * 25); // 8 hours * $25/hour
      expect(result.timesheets[0].overtimeWages).toBe(0);
      expect(result.timesheets[0].totalWages).toBe(8 * 25); // 8 hours * $25/hour

      expect(result.timesheets[1].regularMinutes).toBe(8 * 60); // 8 hours * 60 minutes
      expect(result.timesheets[1].overtimeMinutes).toBe(0);
      expect(result.timesheets[1].regularWages).toBe(8 * 25); // 8 hours * $25/hour
      expect(result.timesheets[1].overtimeWages).toBe(0);
      expect(result.timesheets[1].totalWages).toBe(8 * 25); // 8 hours * $25/hour

      expect(result.timesheets[2].regularMinutes).toBe(8 * 60); // 8 hours * 60 minutes
      expect(result.timesheets[2].overtimeMinutes).toBe(0);
      expect(result.timesheets[2].regularWages).toBe(8 * 25); // 8 hours * $25/hour
      expect(result.timesheets[2].overtimeWages).toBe(0);
      expect(result.timesheets[2].totalWages).toBe(8 * 25); // 8 hours * $25/hour
    });
  });
});
