import { beforeEach, describe, expect, it, vi } from "vitest";
import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import TimeOffPolicy, { AccrualMethod, TimeOffPolicyType } from "@/models/timeoffpolicy";
import TimeOffPolicyEnrollment from "@/models/timeoffpolicyenrollment";
import User from "@/models/user";
import dayjs from "dayjs";
import { CheckService } from "@/services/check";
import TimeOffPolicyService from "@/services/timeoffpolicy";
import Organization from "@/models/organization";
import PaySchedule from "@/models/payschedule";
import { CostCode, Project, TimeSheet } from "@/models";
import EarningRate from "@/models/earningrate";

const totalWeeksInYear = 52;

describe("TimeOffPolicyService", () => {
  let service: TimeOffPolicyService;
  let user: User;
  let organization: Organization;
  let fixedPolicy: TimeOffPolicy;
  let accruedPolicy: TimeOffPolicy;
  let unlimitedPolicy: TimeOffPolicy;

  // Just some dates we can use to base our calculations on.
  // We don't want to use the current date because we have hardcoded values in the "expect" assertions
  const policyStartDate = dayjs("2021-07-01");
  const currentDate = policyStartDate.add(41, "months").add(2, "days"); // 2024-12-03 to give us EXACTLY 4 weeks by the end of the year
  vi.setSystemTime(currentDate.toDate());

  function resetCheckStartDate() {
    vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
      start_date: dayjs().subtract(4, "years"),
    });
  }

  resetCheckStartDate();

  beforeEach(async () => {
    service = new TimeOffPolicyService();

    // Create test organization
    organization = await Organization.create({
      name: "Test Org",
      email: "<EMAIL>",
    });

    // Create pay schedule
    await PaySchedule.create({
      organizationId: organization.id,
      payFrequency: "biweekly",
      firstPayday: policyStartDate.format("YYYY-MM-DD"),
      isActive: true,
      checkPayScheduleId: "test-schedule",
    });

    // Create test user
    user = await User.create({
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      role: "WORKER",
      checkEmployeeId: "123abc",
      workerClassification: "EMPLOYEE",
      organizationId: organization.id,
    });
    // Create test policies
    fixedPolicy = await TimeOffPolicy.create({
      name: "Fixed PTO",
      type: TimeOffPolicyType.PTO,
      isLimited: true,
      accrualMethod: AccrualMethod.FIXED,
      accrualHoursRate: 80,
      accrualResetDate: "01-01",
      carryoverLimit: 16,
      organizationId: organization.id,
    });

    accruedPolicy = await TimeOffPolicy.create({
      name: "Accrued PTO",
      type: TimeOffPolicyType.PTO,
      isLimited: true,
      accrualMethod: AccrualMethod.ACCRUED,
      accrualHoursRate: 96,
      accrualResetDate: "01-01",
      carryoverLimit: 24,
      organizationId: organization.id,
    });

    unlimitedPolicy = await TimeOffPolicy.create({
      name: "Unlimited PTO",
      type: TimeOffPolicyType.PTO,
      isLimited: false,
      organizationId: organization.id,
    });
  });

  describe("calculateAccruedHours", () => {
    describe("FIXED Accrual", () => {
      describe("policy with a specific accrual reset date", () => {
        it("should prorate fixed policy for new employees starting mid-year", async () => {
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: dayjs().startOf("year").add(6, "months").toDate(),
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().startOf("year").add(6, "months").toDate(),
            userId: user.id,
            timeOffPolicyId: fixedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(fixedPolicy.id, user.id);
          expect(timeOffSummary.available).toBeCloseTo(40, 0); // ~5 days (half year) worth of hours

          resetCheckStartDate();
        });

        it("should set fixed hours for old employees plus the carryover", async () => {
          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: fixedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(fixedPolicy.id, user.id);
          expect(timeOffSummary.available).toBeCloseTo(80 + 16, 0); // ~10 days from current year and 2 days carryover
        });
      });
      describe("policy with employee start date as accrual reset date", () => {
        let policy: TimeOffPolicy;
        beforeEach(async () => {
          policy = await TimeOffPolicy.create({
            name: "Fixed PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.FIXED,
            accrualHoursRate: 80,
            carryoverLimit: 12,
            organizationId: organization.id,
          });
        });
        it("should return the full fixed amount of hours for new employees ", async () => {
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: dayjs().subtract(2, 'week').toDate(),
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, 'weeks').toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          expect(timeOffSummary.available).toBeCloseTo(80, 0);
        });

        it("should return all the fixed hours plus the carryover for old employees ", async () => {
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: dayjs().subtract(4, "years"),
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          expect(timeOffSummary.available).toBeCloseTo(80 + 12, 0); // ~10 days from current year and 2 days carryover
        });
      });
    });

    describe("PAY PERIOD Accrued", () => {
      describe("WEEKLY", () => {
        it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
          await PaySchedule.update({ payFrequency: "weekly" }, { where: { organizationId: organization.id } });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: accruedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(accruedPolicy.id, user.id);
          const periodWorked = 48;
          const accrualRate = accruedPolicy.accrualHoursRate / totalWeeksInYear;
          expect(timeOffSummary.available).toBeCloseTo(24 + periodWorked * accrualRate); // 3 from last year
        });

        it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
          await PaySchedule.update({ payFrequency: "weekly" }, { where: { organizationId: organization.id } });

          const policy = await TimeOffPolicy.create({
            name: "Accrued PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.ACCRUED,
            accrualHoursRate: 96,
            carryoverLimit: 24,
            organizationId: organization.id,
          });

          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: dayjs().subtract(1, "year").add(2, "weeks"),
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(1, "month").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          const periodWorked = dayjs().diff(dayjs().subtract(1, "month"), "weeks");
          const accrualRate = policy.accrualHoursRate / totalWeeksInYear;
          expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate);
          expect(timeOffSummary.carryover).toBe(0);
          resetCheckStartDate();
        });
      });

      describe("BIWEEKLY", () => {
        const totalBiWeeksInYear = 26;
        it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
          await PaySchedule.update({ payFrequency: "biweekly" }, { where: { organizationId: organization.id } });

          const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: accruedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(accruedPolicy.id, user.id);
          const periodWorked = dayjs().diff(dayjs().startOf("year"), "weeks") / 2;
          const accrualRate = accruedPolicy.accrualHoursRate / totalBiWeeksInYear;
          expect(timeOffSummary.available).toBeCloseTo(24 + periodWorked * accrualRate); // 3 from last year
        });

        it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
          await PaySchedule.update({ payFrequency: "biweekly" }, { where: { organizationId: organization.id } });

          const policy = await TimeOffPolicy.create({
            name: "Accrued PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.ACCRUED,
            accrualHoursRate: 96,
            carryoverLimit: 24,
            organizationId: organization.id,
          });

          const userStartDate = dayjs().startOf("year").add(6, "months");
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate, // 1st of July 2024
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          const periodWorked = dayjs().diff(userStartDate, "weeks") / 2; // dividing by 2 to because we need to calculate for "biweekly" pay schedule
          const accrualRate = policy.accrualHoursRate / totalBiWeeksInYear;
          expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate + policy.carryoverLimit);
          resetCheckStartDate();
        });
      });

      describe("SEMIMONTHLY", () => {
        const totalSemiMonthsInYear = 24;
        it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
          await PaySchedule.update({ payFrequency: "semimonthly" }, { where: { organizationId: organization.id } });

          const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: accruedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(accruedPolicy.id, user.id);
          const periodWorked = dayjs().diff(dayjs().startOf("year"), "months") * 2;
          const accrualRate = accruedPolicy.accrualHoursRate / totalSemiMonthsInYear;
          expect(timeOffSummary.available).toBeCloseTo(24 + periodWorked * accrualRate); // 3 from last year
        });

        it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
          await PaySchedule.update({ payFrequency: "semimonthly" }, { where: { organizationId: organization.id } });

          const policy = await TimeOffPolicy.create({
            name: "Accrued PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.ACCRUED,
            accrualHoursRate: 96,
            carryoverLimit: 24,
            organizationId: organization.id,
          });

          const userStartDate = dayjs().startOf("year").add(6, "months"); // 1st of July 2024
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          const periodWorked = dayjs().diff(userStartDate, "months") * 2; // multiply by 2 because we need to calculate for the "semimonthly" pay schedule
          const accrualRate = policy.accrualHoursRate / totalSemiMonthsInYear;
          expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate + policy.carryoverLimit);
          resetCheckStartDate();
        });
      });

      describe("MONTHLY", () => {
        const totalMonthsInYear = 12;
        it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
          await PaySchedule.update({ payFrequency: "monthly" }, { where: { organizationId: organization.id } });

          const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: accruedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(accruedPolicy.id, user.id);
          const periodWorked = dayjs().diff(dayjs().startOf("year"), "months");
          const accrualRate = accruedPolicy.accrualHoursRate / totalMonthsInYear;
          expect(timeOffSummary.available).toBeCloseTo(24 + periodWorked * accrualRate); // 3 from last year
        });

        it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
          await PaySchedule.update({ payFrequency: "monthly" }, { where: { organizationId: organization.id } });

          const policy = await TimeOffPolicy.create({
            name: "Accrued PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.ACCRUED,
            accrualHoursRate: 96,
            carryoverLimit: 24,
            organizationId: organization.id,
          });

          const userStartDate = dayjs().startOf("year").add(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          const periodWorked = dayjs().diff(userStartDate.set("year", dayjs().get("year")), "months");
          const accrualRate = policy.accrualHoursRate / totalMonthsInYear;
          expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate + policy.carryoverLimit);
          resetCheckStartDate();
        });
      });

      describe("QUARTERLY", () => {
        const totalQuartersInYear = 4;
        it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
          await PaySchedule.update({ payFrequency: "quarterly" }, { where: { organizationId: organization.id } });

          const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: accruedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(accruedPolicy.id, user.id);
          const periodWorked = dayjs().diff(dayjs().startOf("year"), "quarters");
          const accrualRate = accruedPolicy.accrualHoursRate / totalQuartersInYear;
          expect(timeOffSummary.available).toBeCloseTo(24 + periodWorked * accrualRate); // 3 from last year
        });

        it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
          await PaySchedule.update({ payFrequency: "quarterly" }, { where: { organizationId: organization.id } });

          const policy = await TimeOffPolicy.create({
            name: "Accrued PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.ACCRUED,
            accrualHoursRate: 96,
            carryoverLimit: 24,
            organizationId: organization.id,
          });

          const userStartDate = dayjs().startOf("year").add(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          const periodWorked = dayjs().diff(userStartDate.set("year", dayjs().get("year")), "quarters");
          const accrualRate = policy.accrualHoursRate / totalQuartersInYear;
          expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate + policy.carryoverLimit);
        });
      });
      describe("ANNUALLY", () => {
        it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
          await PaySchedule.update({ payFrequency: "annually" }, { where: { organizationId: organization.id } });

          const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: accruedPolicy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(accruedPolicy.id, user.id);
          const periodWorked = dayjs().diff(dayjs().startOf("year"), "years");
          const accrualRate = accruedPolicy.accrualHoursRate;
          expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate + accruedPolicy.carryoverLimit); // no full year has passed
        });

        it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
          await PaySchedule.update({ payFrequency: "annually" }, { where: { organizationId: organization.id } });

          const policy = await TimeOffPolicy.create({
            name: "Accrued PTO",
            type: TimeOffPolicyType.PTO,
            isLimited: true,
            accrualMethod: AccrualMethod.ACCRUED,
            accrualHoursRate: 96,
            carryoverLimit: 24,
            organizationId: organization.id,
          });

          const userStartDate = dayjs().subtract(14, "months");
          vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
            start_date: userStartDate,
          });

          await TimeOffPolicyEnrollment.create({
            startDate: dayjs().subtract(2, "years").toDate(),
            userId: user.id,
            timeOffPolicyId: policy.id,
          });

          const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
          expect(timeOffSummary.available).toBeCloseTo(policy.carryoverLimit); // only the last period reset was a full year and that's why we can get the carryover from there.
        });
      });
    });

    describe("HOURS WORKED Accrued", () => {
      const totalHoursInYear = 52 * 40; // 52 weeks * 40 hours
      let project: Project;
      let costCode: CostCode;

      beforeEach(async () => {
        // Create test project
        project = await Project.create({
          name: "Test Project",
          organizationId: organization.id,
          isPrevailingWage: false,
        });
        // Create test cost codes
        costCode = await CostCode.create({
          name: "Test Cost Code",
          organizationId: organization.id,
        });
      });
      it("returns accrued hours from current + last year's carryover when reset date is Jan 1", async () => {
        await EarningRate.create({
          name: "Test",
          amount: 20,
          period: "HOURLY",
          active: true,
          type: "REG",
          startDate: new Date().valueOf(),
          userId: user.id,
        });
        const hoursWorkedPolicy = await TimeOffPolicy.create({
          name: "Hours Worked PTO",
          type: TimeOffPolicyType.PTO,
          isLimited: true,
          accrualMethod: AccrualMethod.HOURS_WORKED,
          accrualHoursRate: 1,
          accrualResetDate: "01-01",
          carryoverLimit: 10,
          organizationId: organization.id,
          accrualHoursInterval: 20,
        });

        const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
        vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
          start_date: userStartDate,
        });

        const workedDays = [
          userStartDate,
          userStartDate.add(1, "day"),
          userStartDate.add(2, "days"),

          dayjs().startOf("year").add(1, "day"),
          dayjs().startOf("year").add(2, "days"),
        ];

        await Promise.all(
          workedDays.map((clockIn, index) =>
            TimeSheet.create({
              workerId: user.id,
              projectId: project.id,
              costCodeId: costCode.id,
              organizationId: organization.id,
              clockIn: clockIn.toDate(),
              clockOut: clockIn.add(8, "hours").toDate(),
              description: "Test timesheet",
              breakDuration: 0,
              status: ["APPROVED", "PAID"][index % 2], // alternate statuses for both timesheet types
              isManual: true,
              createdBy: user.id,
            })
          )
        );

        await TimeOffPolicyEnrollment.create({
          startDate: dayjs().subtract(2, "years").toDate(),
          userId: user.id,
          timeOffPolicyId: hoursWorkedPolicy.id,
        });

        const timeOffSummary = await service.getTimeOffSummary(hoursWorkedPolicy.id, user.id);
        const periodWorked = workedDays.length * 8;
        const accrualRate = hoursWorkedPolicy.accrualHoursRate / hoursWorkedPolicy.accrualHoursInterval;
        expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate);
      });

      it("returns accrued hours from current + last year's carryover when reset date is employee start date", async () => {
        await EarningRate.create({
          name: "Test",
          amount: 20,
          period: "HOURLY",
          active: true,
          type: "REG",
          startDate: new Date().valueOf(),
          userId: user.id,
        });
        const hoursWorkedPolicy = await TimeOffPolicy.create({
          name: "Hours Worked PTO",
          type: TimeOffPolicyType.PTO,
          isLimited: true,
          accrualMethod: AccrualMethod.HOURS_WORKED,
          accrualHoursRate: 24 * 8,
          carryoverLimit: 16,
          organizationId: organization.id,
          accrualHoursInterval: totalHoursInYear,
        });

        const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
        vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
          start_date: userStartDate,
        });

        const workedDaysBeforeReset = Array.from({ length: 45 }).map((_, index) =>
          dayjs()
            .startOf("year")
            .add(index + 1, "day")
        );

        const workedDaysAfterReset = [dayjs().subtract(3, "days"), dayjs().subtract(2, "days")];

        await Promise.all(
          [...workedDaysBeforeReset, ...workedDaysAfterReset].map((clockIn, index) =>
            TimeSheet.create({
              workerId: user.id,
              projectId: project.id,
              costCodeId: costCode.id,
              organizationId: organization.id,
              clockIn: clockIn.toDate(),
              clockOut: clockIn.add(8, "hours").toDate(),
              description: "Test timesheet",
              breakDuration: 0,
              status: ["APPROVED", "PAID"][index % 2], // alternate between the statuses so we have both types of timesheets
              isManual: true,
              createdBy: user.id,
            })
          )
        );

        await TimeOffPolicyEnrollment.create({
          startDate: dayjs().subtract(2, "years").toDate(),
          userId: user.id,
          timeOffPolicyId: hoursWorkedPolicy.id,
        });

        const timeOffSummary = await service.getTimeOffSummary(hoursWorkedPolicy.id, user.id);
        const periodWorked = workedDaysAfterReset.length * 8;
        const accrualRate = hoursWorkedPolicy.accrualHoursRate / totalHoursInYear;
        expect(timeOffSummary.available).toBeCloseTo(hoursWorkedPolicy.carryoverLimit + periodWorked * accrualRate); // 3 from last year
      });

      it("returns accrued hours from current + last year's carryover when reset date is Jan 1 for a user with ANNUALLY salary", async () => {
        await EarningRate.create({
          name: "Test",
          amount: 20000,
          period: "ANNUALLY",
          active: true,
          type: "REG",
          startDate: new Date().valueOf(),
          userId: user.id,
        });
        const hoursWorkedPolicy = await TimeOffPolicy.create({
          name: "Hours Worked PTO",
          type: TimeOffPolicyType.PTO,
          isLimited: true,
          accrualMethod: AccrualMethod.HOURS_WORKED,
          accrualHoursRate: 96,
          accrualResetDate: "01-01",
          carryoverLimit: 16,
          organizationId: organization.id,
          accrualHoursInterval: totalHoursInYear,
        });

        const userStartDate = dayjs().startOf("year").subtract(6, "months"); // 1st of July last year
        vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
          start_date: userStartDate,
        });

        await TimeOffPolicyEnrollment.create({
          startDate: dayjs().subtract(2, "years").toDate(),
          userId: user.id,
          timeOffPolicyId: hoursWorkedPolicy.id,
        });

        const timeOffSummary = await service.getTimeOffSummary(hoursWorkedPolicy.id, user.id);
        const periodWorked = dayjs().diff(dayjs().startOf("year"), "weeks") * 40;
        const accrualRate = hoursWorkedPolicy.accrualHoursRate / totalHoursInYear;
        expect(timeOffSummary.available).toBeCloseTo(hoursWorkedPolicy.carryoverLimit + periodWorked * accrualRate, 0); // 3 from last year
      });
    });

    it("should not include unlimited policies in calculation", async () => {
      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(2, "years").toDate(),
        userId: user.id,
        timeOffPolicyId: unlimitedPolicy.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(unlimitedPolicy.id, user.id);
      expect(timeOffSummary.available).toBe(0);
    });

    it("should subtract used hours from accrued total when employee is enrolled before policy reset (it means the employee has carryover)", async () => {
      const policy = await TimeOffPolicy.create({
        name: "Fixed PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 8,
        accrualResetDate: "12-01",
        carryoverLimit: 8,
        organizationId: organization.id,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(1, "year").toDate(),
        userId: user.id,
        timeOffPolicyId: policy.id,
      });

      const takenHours = 4;

      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs().toDate(),
        endDate: dayjs().toDate(),
        totalHours: takenHours,

        reviewedBy: user.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
      expect(timeOffSummary.available).toBeCloseTo(policy.accrualHoursRate + policy.carryoverLimit - takenHours, 1);
      expect(timeOffSummary.carryover).toBeCloseTo(policy.carryoverLimit, 1);
    });

    it("should subtract used hours from accrued total when employee is enrolled after policy reset (it means the employee doesn't have carryover)", async () => {
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(1, "day"),
      });
      const policy = await TimeOffPolicy.create({
        name: "Fixed PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 8,
        accrualResetDate: "12-01",
        carryoverLimit: 8,
        organizationId: organization.id,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().toDate(),
        userId: user.id,
        timeOffPolicyId: policy.id,
      });

      const takenHours = 4;

      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs().toDate(),
        endDate: dayjs().subtract(1, "months").toDate(),
        totalHours: takenHours,

        reviewedBy: user.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
      expect(timeOffSummary.available).toBeCloseTo(policy.accrualHoursRate - takenHours, 1);
      expect(timeOffSummary.carryover).toBe(0);
    });

    it("should apply carryover limits correctly", async () => {
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(4, "years"),
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(4, "years").toDate(),
        userId: user.id,
        timeOffPolicyId: fixedPolicy.id,
      });

      // Create last year's request, but it's not PAID yet. It will just be approved
      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: fixedPolicy.id,
        startDate: dayjs().subtract(1, "year").toDate(),
        endDate: dayjs().subtract(1, "year").add(8, "hours").toDate(),
        totalHours: fixedPolicy.accrualHoursRate - 4, // taking most of the PTO from last year except 4 hours
        reviewedBy: user.id,
      });

      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: fixedPolicy.id,
        startDate: dayjs().subtract(1, "year").toDate(),
        endDate: dayjs().subtract(1, "year").add(8, "hours").toDate(),
        totalHours: 2, // Some more PTO. In this case, the PTO is PAID
        status: TimeOffRequestStatus.PAID,
        reviewedBy: user.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(fixedPolicy.id, user.id);
      expect(timeOffSummary.available).toBeCloseTo(fixedPolicy.accrualHoursRate + fixedPolicy.carryoverLimit, 0); // 80 current + 2 carryover (from the previous year since we only took 78 hours last year)
    });

    it("should return carryover recursively if the employee was enrolled several years ago", async () => {
      // const currentDate = dayjs("2025-07-01");
      // vi.setSystemTime(currentDate.toDate());

      const fixedPolicy = await TimeOffPolicy.create({
        name: "Fixed PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 80,
        accrualResetDate: "01-01",
        carryoverLimit: 10,
        organizationId: organization.id,
      });

      const accruedPolicy = await TimeOffPolicy.create({
        name: "Accrued PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 96,
        accrualResetDate: "01-01",
        carryoverLimit: 10,
        organizationId: organization.id,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(25, "months").toDate(),
        userId: user.id,
        timeOffPolicyId: fixedPolicy.id,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(25, "months").toDate(),
        userId: user.id,
        timeOffPolicyId: accruedPolicy.id,
      });

      // Create last year's request
      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: fixedPolicy.id,
        startDate: dayjs().subtract(1, "year").toDate(),
        endDate: dayjs().subtract(1, "year").add(8, "day").toDate(),
        totalHours: fixedPolicy.accrualHoursRate , // all the time off
        reviewedBy: user.id,
      });

      // Create last year's request
      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: accruedPolicy.id,
        startDate: dayjs().subtract(1, "year").toDate(),
        endDate: dayjs().subtract(1, "year").add(8, "day").toDate(),
        totalHours: accruedPolicy.accrualHoursRate,
        status: TimeOffRequestStatus.PAID,
        reviewedBy: user.id,
      });

      const timeOffSummary1 = await service.getTimeOffSummary(fixedPolicy.id, user.id);
      // from first policy: 80 current + 10 carryover
      expect(timeOffSummary1.available).toBeCloseTo(fixedPolicy.accrualHoursRate + fixedPolicy.carryoverLimit, 0);
      expect(timeOffSummary1.accrued).toBe(fixedPolicy.accrualHoursRate);
      expect(timeOffSummary1.carryover).toBe(fixedPolicy.carryoverLimit);

      const timeOffSummary2 = await service.getTimeOffSummary(accruedPolicy.id, user.id);
      const periodPassed = Math.floor(dayjs().diff(dayjs().startOf("year"), "weeks")/2)
      const accruedThisYear = accruedPolicy.accrualHoursRate / (totalWeeksInYear/2) * periodPassed;

      expect(timeOffSummary2.available).toBeCloseTo(accruedThisYear + accruedPolicy.carryoverLimit , 0);
      expect(timeOffSummary2.accrued).toBeCloseTo(accruedThisYear, 0);
      expect(timeOffSummary2.carryover).toBe(accruedPolicy.carryoverLimit);
    });

    it("should not exceed accrual limit when calculating total hours", async () => {
      const policyWithLimit = await TimeOffPolicy.create({
        name: "Limited PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 120,
        accrualLimit: 100, // Setting a limit lower than yearly accrual
        organizationId: organization.id,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(2, "years").toDate(),
        userId: user.id,
        timeOffPolicyId: policyWithLimit.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(policyWithLimit.id, user.id);
      expect(timeOffSummary.available).toBe(100); // Should be capped at accrual limit
    });

    it("checks if the accrued hours are correctly calculated if the accrualResetDate is in the future", async () => {
      vi.setSystemTime(dayjs("2023-02-01").toDate());

      await PaySchedule.create({
        organizationId: organization.id,
        payFrequency: "weekly",
        firstPayday: policyStartDate.format("YYYY-MM-DD"),
        isActive: true,
        checkPayScheduleId: "test-schedule",
      });

      const policy = await TimeOffPolicy.create({
        name: "Accrued PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.ACCRUED,
        accrualHoursRate: 96,
        accrualResetDate: "03-01",
        carryoverLimit: 24,
        organizationId: organization.id,
      });

      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(4, "weeks").toDate(),
        userId: user.id,
        timeOffPolicyId: policy.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);
      const periodWorked = 4; // remember, the user was added exactly 4 weeks before the current date
      const accrualRate = accruedPolicy.accrualHoursRate / totalWeeksInYear;
      expect(timeOffSummary.available).toBeCloseTo(periodWorked * accrualRate);
    });
  });

  describe("Starting balance", () => {
    it("should add starting balance to accrued hours if user was enrolled in current year", async () => {
      const policy = await TimeOffPolicy.create({
        name: "Fixed PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 10,
        organizationId: organization.id,
      });

      // Set current system time so we can base our calculation on it
      const currentDate = dayjs().startOf("year").add(3, "months");
      vi.setSystemTime(currentDate.toDate());

      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(2, "years"),
      });

      // Create enrollment with starting balance in current year
      const startingBalance = 5;
      const enrollment = await TimeOffPolicyEnrollment.create({
        startDate: currentDate.subtract(1, "months").toDate(),
        userId: user.id,
        timeOffPolicyId: policy.id,
        startingBalance,
      });

      const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);

      // The time off summary should include the starting balance in the accrued hours
      // Fixed accrual should be prorated for the rest of the year plus starting balance
      const totalHoursInYear = totalWeeksInYear * 7 * 24;
      const periodPassed = totalHoursInYear - dayjs(enrollment.startDate).diff(dayjs().startOf("year"), "hours");
      const accrualRate = policy.accrualHoursRate / totalHoursInYear;
      const expectedAccrued = accrualRate * periodPassed + startingBalance;

      expect(timeOffSummary.accrued).toBeCloseTo(expectedAccrued, 0);
      expect(timeOffSummary.available).toBeCloseTo(expectedAccrued, 0);
      expect(timeOffSummary.carryover).toBe(0);
    });

    it("should add starting balance to last year's accrued hours if user was enrolled last year", async () => {
      const policy = await TimeOffPolicy.create({
        name: "Fixed PTO",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 120,
        accrualResetDate: "01-01",
        carryoverLimit: 30, // Set higher to ensure we can observe the starting balance effect
        organizationId: organization.id,
      });

      // Set current system time to early in the year
      const earlyCurrentYear = dayjs().startOf("year").add(1, "month");
      vi.setSystemTime(earlyCurrentYear.toDate());

      // Mock employee start date
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(3, "years"),
      });

      const startingBalance = 25;
      // Enrolled 2 months ago. This should give us a bit of carryover hours from accrued hours in the previous year.
      const enrollment = await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(2, "months").toDate(),
        userId: user.id,
        timeOffPolicyId: policy.id,
        startingBalance,
      });

      // Simulate a time off request in the previous year to use some hours
      const usedHours = 15;
      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs().subtract(1, "months").toDate(),
        endDate: dayjs().subtract(1, "months").add(1, "day").toDate(),
        totalHours: usedHours,
        status: TimeOffRequestStatus.APPROVED,
        reviewedBy: user.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);

      expect(timeOffSummary.accrued).toBe(policy.accrualHoursRate); // new year -> full accrued hours
      expect(timeOffSummary.carryover).toBeCloseTo(20, 0);
    });

    it("should not add starting balance anywhere if user was enrolled more than 2 years ago", async () => {
      // Create a fixed policy
      const policy = await TimeOffPolicy.create({
        name: "Fixed PTO Old Enrollment",
        type: TimeOffPolicyType.PTO,
        isLimited: true,
        accrualMethod: AccrualMethod.FIXED,
        accrualHoursRate: 80,
        accrualResetDate: "01-01",
        carryoverLimit: 20,
        organizationId: organization.id,
      });

      // Set current system time
      const currentDate = dayjs().startOf("year").add(2, "months");
      vi.setSystemTime(currentDate.toDate());

      // Mock employee start date to before policy start date
      vi.spyOn(CheckService.prototype, "get").mockResolvedValue({
        start_date: dayjs().subtract(5, "years"),
      });

      // Create enrollment with starting balance more than 2 years ago
      const startingBalance = 30;
      await TimeOffPolicyEnrollment.create({
        startDate: dayjs().subtract(3, "years").toDate(),
        userId: user.id,
        timeOffPolicyId: policy.id,
        startingBalance,
      });

      // Create time off requests from both previous years to ensure they took all hours
      // This makes sure carryover is only limited by carryoverLimit, not remaining hours
      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs().subtract(2, "years").add(6, "months").toDate(),
        endDate: dayjs().subtract(2, "years").add(6, "months").add(1, "day").toDate(),
        totalHours: policy.accrualHoursRate, // Take all hours from 2 years ago
        status: TimeOffRequestStatus.APPROVED,
        reviewedBy: user.id,
      });

      await TimeOffRequest.create({
        userId: user.id,
        timeOffPolicyId: policy.id,
        startDate: dayjs().subtract(1, "years").add(6, "months").toDate(),
        endDate: dayjs().subtract(1, "years").add(6, "months").add(1, "day").toDate(),
        totalHours: 60, // Take most hours from last year
        status: TimeOffRequestStatus.APPROVED,
        reviewedBy: user.id,
      });

      const timeOffSummary = await service.getTimeOffSummary(policy.id, user.id);

      // Starting balance should not affect carryover since enrollment was more than 2 years ago
      // For last year, we had 80 hours and used 60, leaving 20 exactly at carryover limit
      expect(timeOffSummary.carryover).toBeCloseTo(20, 0);

      expect(timeOffSummary.available).toBeCloseTo(policy.accrualHoursRate + 20, 1);
    });
  });
});
