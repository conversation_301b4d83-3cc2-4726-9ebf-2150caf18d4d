import { beforeEach, describe, expect, it, vi } from "vitest";
import TimeOffRequestService from "@/services/timeoffrequest";
import TimeOffRequest, { TimeOffRequestStatus } from "@/models/timeoffrequest";
import TimeOffPolicy, { AccrualMethod, TimeOffPolicyType } from "@/models/timeoffpolicy";
import User from "@/models/user";
import dayjs from "dayjs";
import { CheckService } from "@/services/check";
import TimeOffPolicyEnrollment from "@/models/timeoffpolicyenrollment";


describe("TimeOffRequestService", () => {
  let service: TimeOffRequestService;
  let user: User;
  let fixedPolicy: TimeOffPolicy;

  // Just some dates we can use to base our calculations on.
  // We don't want to use the current date because we have hardcoded values in the "expect" assertions
  const startDate = dayjs("2023-07-01");
  const currentDate = startDate.add(17, "months");
  vi.setSystemTime(currentDate.toDate());

  beforeEach(async () => {
    service = new TimeOffRequestService();
    vi.spyOn(CheckService.prototype, "get").mockResolvedValue({ start_date: dayjs().subtract(1, "year").format("YYYY-MM-DD") });

    // Create test user
    user = await User.create({
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      role: "WORKER",
      checkEmployeeId: "123abc",
      workerClassification: "EMPLOYEE",
    });

    // Create test policies
    fixedPolicy = await TimeOffPolicy.create({
      name: "Fixed PTO",
      type: TimeOffPolicyType.PTO,
      isLimited: true,
      accrualMethod: AccrualMethod.FIXED,
      accrualHoursRate: 80,
      accrualResetDate: "01-01",
      carryoverLimit: 2,
      organizationId: user.organizationId,
    });

    await TimeOffPolicyEnrollment.create({
      userId: user.id,
      timeOffPolicyId: fixedPolicy.id,
      startDate: dayjs().subtract(1, 'year').toDate(),
    });
  });

  describe("CRUD operations", () => {
    it("should create a time off request", async () => {
      const request = await service.create(user,{
        timeOffPolicyId: fixedPolicy.id,
        endDate: dayjs().add(1, "day").toDate(),
        totalHours: 16,
      } as TimeOffRequest);

      expect(request.userId).toBe(user.id);
      expect(request.totalHours).toBe(16);
      expect(request.status).toBe(TimeOffRequestStatus.PENDING);
    });

    it("should update a time off request", async () => {
      const request = await service.create(user,{
        timeOffPolicyId: fixedPolicy.id,
        endDate: dayjs().add(1, "day").toDate(),
        totalHours: 16,
      } as TimeOffRequest);

      const updated = await service.update(request.id, {
        totalHours: 24,
      });

      expect(updated.totalHours).toBe(24);
      expect(updated.status).toBe(TimeOffRequestStatus.PENDING);
    });

    it("should delete a time off request", async () => {
      const request = await service.create(user,{
        timeOffPolicyId: fixedPolicy.id,
        endDate: dayjs().add(1, "day").toDate(),
        totalHours: 16,
      } as TimeOffRequest);

      await service.delete(request.id);
      const deleted = await TimeOffRequest.findByPk(request.id);
      expect(deleted).toBeNull();
    });
  });
});
