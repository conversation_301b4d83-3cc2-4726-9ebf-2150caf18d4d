import sequelize from "@/lib/sequelize";
import CompanyBenefit from "@/models/companybenefit";
import { CATEGORY_TYPES, CONTRIBUTION_TYPES } from "@/models/companybenefit";

export class CompanyBenefitBuilder {
  private companyBenefit: any;

  constructor() {
    this.companyBenefit = {
      organizationId: null, // required
      name: "Test Benefit",
      category: CATEGORY_TYPES[0], // MEDICAL
      contributionType: CONTRIBUTION_TYPES[1], // AMOUNT
      benefitProviderName: "Test Provider",
      benefitProviderAddress: "123 Test Street",
      benefitProviderPhone: "************",
      active: true,
      isRequired: false,
    };
  }

  forOrganization(organizationId: number): CompanyBenefitBuilder {
    this.companyBenefit.organizationId = organizationId;

    return this;
  }

  withName(name: string): CompanyBenefitBuilder {
    this.companyBenefit.name = name;

    return this;
  }

  withCategory(category: typeof CATEGORY_TYPES[number]): CompanyBenefitBuilder {
    this.companyBenefit.category = category;

    return this;
  }

  withContributionType(contributionType: typeof CONTRIBUTION_TYPES[number]): CompanyBenefitBuilder {
    this.companyBenefit.contributionType = contributionType;

    return this;
  }

  withProvider(name: string, address: string, phone: string): CompanyBenefitBuilder {
    this.companyBenefit.benefitProviderName = name;
    this.companyBenefit.benefitProviderAddress = address;
    this.companyBenefit.benefitProviderPhone = phone;

    return this;
  }

  withActive(active: boolean): CompanyBenefitBuilder {
    this.companyBenefit.active = active;

    return this;
  }

  withRequired(isRequired: boolean): CompanyBenefitBuilder {
    this.companyBenefit.isRequired = isRequired;

    return this;
  }

  async buildAndCreate(): Promise<CompanyBenefit> {
    if (!this.companyBenefit.organizationId) {
      throw new Error("Organization ID is required to create a company benefit");
    }

    const result = await sequelize.transaction(async (transaction) => {
      const { id: _id, ...createPayload } = this.companyBenefit;
      const benefit = await CompanyBenefit.create(createPayload, { transaction });

      return benefit;
    });

    return result;
  }
}

// Factory functions for common company benefit scenarios
export async function createHealthBenefit(organizationId: number, name = "Health Insurance"): Promise<CompanyBenefit> {
  return new CompanyBenefitBuilder()
    .forOrganization(organizationId)
    .withName(name)
    .withCategory(CATEGORY_TYPES[0]) // MEDICAL
    .withContributionType(CONTRIBUTION_TYPES[1]) // amount
    .withProvider("Health Provider Inc.", "123 Health St.", "555-HEALTH")
    .buildAndCreate();
}

export async function createRetirementBenefit(organizationId: number, name = "401K Plan"): Promise<CompanyBenefit> {
  return new CompanyBenefitBuilder()
    .forOrganization(organizationId)
    .withName(name)
    .withCategory(CATEGORY_TYPES[5]) // 401K
    .withContributionType(CONTRIBUTION_TYPES[0]) // percent
    .withProvider("Retirement Services LLC", "456 Future Ave.", "555-RETIRE")
    .buildAndCreate();
}

export async function createDynamicFringeBenefit(
  organizationId: number,
  name = "Dynamic Fringe Benefit"
): Promise<CompanyBenefit> {
  return new CompanyBenefitBuilder()
    .forOrganization(organizationId)
    .withName(name)
    .withCategory(CATEGORY_TYPES[6]) // ROTH 401K
    .withContributionType(CONTRIBUTION_TYPES[2]) // dynamic
    .withProvider("Fringe Benefits Corp", "789 Fringe Blvd.", "555-FRINGE")
    .buildAndCreate();
}
