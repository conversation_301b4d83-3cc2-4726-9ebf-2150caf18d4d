import UserClassification from "@/models/userclassification";

export class UserClassificationFactory {
  private userClassification: any;

  constructor() {
    this.userClassification = {
      basePay: "50.00",
      fringePay: "10.00",
      startDate: new Date(),
      endDate: null,
      userId: null, // required
      classificationId: null, // required
      wageTableId: null, // required
      organizationId: null, // required
    };
  }

  withOrganization(organizationId: number): UserClassificationFactory {
    this.userClassification.organizationId = organizationId;

    return this;
  }

  withClassification(classificationId: number): UserClassificationFactory {
    this.userClassification.classificationId = classificationId;

    return this;
  }

  withWageTable(wageTableId: number): UserClassificationFactory {
    this.userClassification.wageTableId = wageTableId;

    return this;
  }

  withUser(userId: number): UserClassificationFactory {
    this.userClassification.userId = userId;

    return this;
  }

  withAllocatedPay(basePay: string, fringePay: string): UserClassificationFactory {
    this.userClassification.basePay = basePay;
    this.userClassification.fringePay = fringePay;

    return this;
  }

  async buildAndCreate(): Promise<UserClassification> {
    if (!this.userClassification.organizationId) {
      throw new Error("Organization ID is required to create a user classification");
    }

    if (!this.userClassification.classificationId) {
      throw new Error("Classification ID is required to create a user classification");
    }

    if (!this.userClassification.userId) {
      throw new Error("User ID is required to create a user classification");
    }

    if (!this.userClassification.wageTableId) {
      throw new Error("Wage table ID is required to create a user classification");
    }

    return UserClassification.create(this.userClassification);
  }
}

// Factory functions for common scenarios
export async function createUserClassification(
  organizationId: number,
  classificationId: number,
  userId: number,
  wageTableId: number
): Promise<UserClassification> {
  return new UserClassificationFactory()
    .withOrganization(organizationId)
    .withClassification(classificationId)
    .withUser(userId)
    .withWageTable(wageTableId)
    .buildAndCreate();
}

export async function createUserClassificationWithPay(
  organizationId: number,
  classificationId: number,
  userId: number,
  wageTableId: number,
  basePay: string,
  fringePay: string
): Promise<UserClassification> {
  return new UserClassificationFactory()
    .withOrganization(organizationId)
    .withClassification(classificationId)
    .withUser(userId)
    .withWageTable(wageTableId)
    .withAllocatedPay(basePay, fringePay)
    .buildAndCreate();
}
