import Project from "@/models/project";
import sequelize from "@/lib/sequelize";
import { Point } from "geojson";

export interface GeofenceSettings {
  coordinates: [number, number]; // [longitude, latitude]
  radius: number; // in meters
}

const DEFAULT_GEOFENCE: GeofenceSettings = {
  coordinates: [-118.2437, 34.0522], // Los Angeles coordinates
  radius: 1000, // 1km radius
};

export class ProjectBuilder {
  private project: any;

  constructor() {
    this.project = {
      name: "Test Project",
      address: `123 Main St., Los Angeles, CA 90012`,
      projectNumber: "123456",
      startDate: null,
      foremanId: null,
      location: null,
      isGeofenced: false,
      geofenceRadius: 0,
      hoursBudget: 0,
      costBudget: 0,
      notes: "Test project for unit tests",
      isPrevailingWage: false,
      prevailingWageCategory: null,
      prevailingWageState: null,
      prevailingWageDirProjectId: null,
      prevailingWageAwardingBody: null,
      prevailingWagePrimeContractor: null,
      prevailingWageBidAwardDate: null,
      prevailingWageIsSubcontractor: false,
      wageTableId: null, // optional
      overtimeSettingsId: null, // optional
      organizationId: null, // required
    };
  }

  withOrganization(organizationId: number): ProjectBuilder {
    this.project.organizationId = organizationId;

    return this;
  }

  withWageTable(wageTableId: number): ProjectBuilder {
    this.project.wageTableId = wageTableId;

    return this;
  }

  withOvertimeSettings(overtimeSettingsId: number): ProjectBuilder {
    this.project.overtimeSettingsId = overtimeSettingsId;

    return this;
  }

  withName(name: string): ProjectBuilder {
    this.project.name = name;

    return this;
  }

  withAddress(address: string): ProjectBuilder {
    this.project.address = address;

    return this;
  }

  withNotes(notes: string): ProjectBuilder {
    this.project.notes = notes;

    return this;
  }

  withLocation(location: Point): ProjectBuilder {
    this.project.location = location;

    return this;
  }

  withIsArchived(isArchived: boolean): ProjectBuilder {
    this.project.isArchived = isArchived;

    return this;
  }

  withBudget({ hours, cost }: { hours: number; cost: number }): ProjectBuilder {
    this.project.hoursBudget = hours ?? null;
    this.project.costBudget = cost ?? null;

    return this;
  }

  withGeofence({ location, radius }: { location: Point; radius: number }): ProjectBuilder {
    this.project.location = location;
    this.project.geofenceRadius = radius;
    this.project.isGeofenced = true;

    return this;
  }

  withPrevailingWage({
    category,
    state,
    dirProjectId,
    awardingBody,
    primeContractor,
    bidAwardDate,
    isSubcontractor,
  }: {
    category: string;
    state: string;
    dirProjectId: string;
    awardingBody: string;
    primeContractor: string;
    bidAwardDate: Date;
    isSubcontractor: boolean;
  }): ProjectBuilder {
    this.project.isPrevailingWage = true;
    this.project.prevailingWageCategory = category;
    this.project.prevailingWageState = state;
    this.project.prevailingWageDirProjectId = dirProjectId;
    this.project.prevailingWageAwardingBody = awardingBody;
    this.project.prevailingWagePrimeContractor = primeContractor;
    this.project.prevailingWageBidAwardDate = bidAwardDate;
    this.project.prevailingWageIsSubcontractor = isSubcontractor;

    return this;
  }

  async buildAndCreate(): Promise<Project> {
    if (!this.project.organizationId) {
      throw new Error("Organization ID is required");
    }

    const result = await sequelize.transaction(async (transaction) => {
      return Project.create(this.project, { transaction });
    });

    return result;
  }
}

// Factory functions for common scenarios
export async function createBasicProject(organizationId: number): Promise<Project> {
  return new ProjectBuilder().withOrganization(organizationId).buildAndCreate();
}

export async function createGeofencedProject(
  organizationId: number,
  geofenceSettings: Partial<GeofenceSettings> = {}
): Promise<Project> {
  // Merge default settings with provided settings
  const settings: GeofenceSettings = {
    ...DEFAULT_GEOFENCE,
    ...geofenceSettings,
  };

  return new ProjectBuilder()
    .withOrganization(organizationId)
    .withGeofence({
      location: {
        type: "Point",
        coordinates: settings.coordinates,
      },
      radius: settings.radius,
    })
    .buildAndCreate();
}

export async function createPrevailingWageProject(organizationId: number, wageTableId: number): Promise<Project> {
  return new ProjectBuilder()
    .withOrganization(organizationId)
    .withWageTable(wageTableId)
    .withPrevailingWage({
      category: "FEDERAL",
      state: "CA",
      dirProjectId: "123456",
      awardingBody: "Wage Board",
      primeContractor: "Prime Contractor",
      bidAwardDate: new Date(),
      isSubcontractor: false,
    })
    .buildAndCreate();
}
