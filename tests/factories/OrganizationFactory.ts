import sequelize from "@/lib/sequelize";
import Organization from "@/models/organization";
import PaySchedule from "@/models/payschedule";
import FeatureFlags from "@/models/featureflags";
import TimeTrackingSettings from "@/models/timetrackingsettings";
import OvertimeSettings, { WeekDay } from "@/models/overtimesettings";
import PrevailingWageSettings from "@/models/prevailingwagesettings";

// Import types from your models
import { PayFrequency } from "@/types/checkDto";
import { Model } from "sequelize";

// Create a type that represents a plain object version of your models
export type PlainObject<T> = T extends Model ? any : never;

// Factory interface that matches your Sequelize model relationships
export interface OrganizationData {
  id: number;
  name: string;
  checkCompanyId?: string;
  isRegisteredOnCheck: boolean;
  slug?: string;
  isChurned: boolean;
  timezone: string;
  stripeCustomerId?: string;
  createdAt: Date;
  updatedAt: Date;

  // Virtual properties from FeatureFlags
  isSchedulingEnabled: boolean;
  isPayrollEnabled: boolean;
  isMessagingEnabled: boolean;

  // Related models as plain objects
  featureFlag: {
    id: number;
    organizationId: number;
    isSchedulingEnabled: boolean;
    isPayrollEnabled: boolean;
    isMessagingEnabled: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
  timeTrackingSettings: {
    id: number;
    organizationId: number;
    clockInReminderAt?: Date;
    clockOutReminderAt?: Date;
    allowWorkersToAddEditTime: boolean;
    areBreaksPaid: boolean;
    areRealtimeBreaksEnabled: boolean;
    areRealtimeBreakRemindersEnabled: boolean;
    realtimeBreakStartReminderAt?: Date;
    realtimeBreakEndReminderAfter?: number;
    useDecimalHours: boolean;
    breakOptions: number[];
    locationBreadcrumbingEnabled: boolean;
    isClockinClockoutPhotosEnabled: boolean;
    showWagesToWorkers: boolean;
    timesheetRoundingEnabled: boolean;
    timesheetRoundingType?: string;
    timesheetRoundingInterval?: string;
    isCostCodeRequired: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
  overtimeSettings: {
    id: number;
    organizationId: number;
    name: string;
    weeklyOvertimeEnabled: boolean;
    dailyOvertimeEnabled: boolean;
    dailyOvertimeThreshold: number;
    weeklyOvertimeThreshold: number;
    overtimeMultiplier: number;
    weekStartDay: WeekDay;
    aggregatePwForDailyOvertime: boolean;
    dailyDoubleOvertimeEnabled: boolean;
    dailyDoubleOvertimeThreshold: number;
    overtimeDistribution: string;
    overtimeDaysOfWeekNonPw: WeekDay[];
    overtimeDaysOfWeekPw: WeekDay[];
    doubleOvertimeDaysOfWeekNonPw: WeekDay[];
    doubleOvertimeDaysOfWeekPw: WeekDay[];
    createdAt: Date;
    updatedAt: Date;
  };
  prevailingWageSettings: {
    id: number;
    organizationId: number;
    allowCustomPrevailingWagePerEmployee: boolean;
    overridePwIfBelowRegularRate: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
  paySchedule?: {
    id: number;
    organizationId: number;
    payFrequency: PayFrequency;
    firstPayday: string;
    secondPayday?: string;
    firstPeriodEnd: string;
    isActive: boolean;
    checkPayScheduleId?: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

// Types for all related settings
interface OrganizationSettings {
  featureFlags?: Partial<FeatureFlags>;
  timeTrackingSettings?: Partial<TimeTrackingSettings>;
  overtimeSettings?: Partial<OvertimeSettings>;
  prevailingWageSettings?: Partial<PrevailingWageSettings>;
  paySchedule?: Partial<PaySchedule>;
}

export class OrganizationBuilder {
  private organization: any;
  private settings: OrganizationSettings;

  constructor() {
    const now = new Date();
    const id = Math.floor(Math.random() * 10000);

    // Base organization properties
    this.organization = {
      name: `Test Organization ${id}`,
      isRegisteredOnCheck: false,
      slug: `test-org-${id}`,
      checkCompanyId: `com_test-org-${id}`,
      isChurned: false,
      timezone: "America/Los_Angeles",
      createdAt: now,
      updatedAt: now,
    };

    // Default settings that match most common testing scenarios
    this.settings = {
      featureFlags: {
        isSchedulingEnabled: true,
        isPayrollEnabled: true,
        isMessagingEnabled: true,
      },
      timeTrackingSettings: {
        allowWorkersToAddEditTime: true,
        areBreaksPaid: false,
        areRealtimeBreaksEnabled: false,
        useDecimalHours: false,
        breakOptions: [0, 15, 30, 60],
        showWagesToWorkers: true,
        isCostCodeRequired: false,
      },
      overtimeSettings: {
        name: "default", // default name
        weeklyOvertimeEnabled: true, // weekly overtime enabled by default
        dailyOvertimeEnabled: false,
        dailyOvertimeThreshold: 480, // 8 hours in minutes
        weeklyOvertimeThreshold: 2400, // 40 hours in minutes
        overtimeMultiplier: 1.5,
        weekStartDay: "MONDAY",
        overtimeDistribution: "SEQUENTIAL",
      },
      prevailingWageSettings: {
        allowCustomPrevailingWagePerEmployee: false,
        overridePwIfBelowRegularRate: true,
      },
      paySchedule: {
        payFrequency: "weekly",
        firstPayday: "2024-01-01",
        secondPayday: "2024-01-15",
        firstPeriodEnd: "2024-01-31",
        isActive: true,
      },
    };
  }

  // Base organization methods
  withName(name: string): OrganizationBuilder {
    this.organization.name = name;

    return this;
  }

  withCheckCompanyId(id: string): OrganizationBuilder {
    this.organization.checkCompanyId = id;

    return this;
  }

  // Feature flag methods
  withFeatures(features: Partial<FeatureFlags>): OrganizationBuilder {
    this.settings.featureFlags = {
      ...this.settings.featureFlags,
      ...features,
    };

    return this;
  }

  // Time tracking methods
  withTimeTrackingSettings(settings: Partial<TimeTrackingSettings>): OrganizationBuilder {
    this.settings.timeTrackingSettings = {
      ...this.settings.timeTrackingSettings,
      ...settings,
    };

    return this;
  }

  // Overtime methods
  withOvertimeSettings(settings: Partial<OvertimeSettings>): OrganizationBuilder {
    this.settings.overtimeSettings = {
      ...this.settings.overtimeSettings,
      ...settings,
    };

    return this;
  }

  // Prevailing wage methods
  withPrevailingWageSettings(settings: Partial<PrevailingWageSettings>): OrganizationBuilder {
    this.settings.prevailingWageSettings = {
      ...this.settings.prevailingWageSettings,
      ...settings,
    };

    return this;
  }

  // Common scenario methods
  asPayrollCustomer(): OrganizationBuilder {
    this.settings.featureFlags.isPayrollEnabled = true;
    this.settings.overtimeSettings.weeklyOvertimeEnabled = true;
    this.settings.timeTrackingSettings.showWagesToWorkers = true;

    return this;
  }

  withOvertimeEnabled(): OrganizationBuilder {
    this.settings.overtimeSettings = {
      ...this.settings.overtimeSettings,
      weeklyOvertimeEnabled: true,
      dailyOvertimeEnabled: true,
    };

    return this;
  }

  private async createRelatedModels(organization: Organization, transaction: any) {
    const { id } = organization;

    // Create all related models with their respective settings
    const results = await Promise.all([
      FeatureFlags.create(
        {
          ...this.settings.featureFlags,
          organizationId: id,
        },
        { transaction }
      ),
      TimeTrackingSettings.create(
        {
          ...this.settings.timeTrackingSettings,
          organizationId: id,
        },
        { transaction }
      ),
      OvertimeSettings.create(
        {
          ...this.settings.overtimeSettings,
          name: "default", // Ensure the name is set to "default"
          organizationId: id,
        },
        { transaction }
      ),
      PrevailingWageSettings.create(
        {
          ...this.settings.prevailingWageSettings,
          organizationId: id,
        },
        { transaction }
      ),
      PaySchedule.create(
        {
          ...this.settings.paySchedule,
          organizationId: id,
        },
        { transaction }
      ),
    ]);

    const [featureFlags, timeTrackingSettings, overtimeSettings, prevailingWageSettings, paySchedule] = results;

    // Return the models with the correct association names
    return {
      featureFlags,
      timeTrackingSettings,
      overtimeSettings,
      prevailingWageSettings,
      paySchedule,
    };
  }

  async buildAndCreate(): Promise<Organization> {
    const result = await sequelize.transaction(async (transaction) => {
      // Create the organization first
      const org = await Organization.create(this.organization, {
        transaction,
        hooks: false, // Disable afterCreate hooks to prevent double creation
      });

      // Create all related models
      await this.createRelatedModels(org, transaction);

      // Reload to get all associations
      const reloadedOrg = await org.reload({
        transaction,
        include: { all: true },
      });

      // Transform the organization to ensure overtimeSettings is a single object
      // This fixes both dataValues and the instance property
      if (
        reloadedOrg.dataValues.overtimeSettings &&
        Array.isArray(reloadedOrg.dataValues.overtimeSettings) &&
        reloadedOrg.dataValues.overtimeSettings.length > 0
      ) {
        const settings = reloadedOrg.dataValues.overtimeSettings[0];
        reloadedOrg.dataValues.overtimeSettings = settings;
        reloadedOrg.overtimeSettings = settings;
      }

      return reloadedOrg;
    });

    return result;
  }
}

// Factory functions for common scenarios
export async function createBasicOrganization(): Promise<Organization> {
  return new OrganizationBuilder().buildAndCreate();
}

export async function createPayrollOrganization(): Promise<Organization> {
  return new OrganizationBuilder().asPayrollCustomer().buildAndCreate();
}

export async function createOvertimeOrganization(): Promise<Organization> {
  return new OrganizationBuilder().withOvertimeEnabled().buildAndCreate();
}
