// tests/factories/EarningRateFactory.ts

import { EARNING_RATE_PERIODS, EARNING_RATE_TYPES } from "@/models/earningrate";
import EarningRate from "@/models/earningrate";

export interface EarningRateData {
  amount: string;
  period: EARNING_RATE_PERIODS;
  userId?: number;
  organizationId: number;
  name: string;
  active: boolean;
  type: EARNING_RATE_TYPES;
  weeklyHours?: number;
  checkEarningRateId?: string;
  startDate: Date;
  endDate?: Date;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

export class EarningRateBuilder {
  private earningRate: EarningRateData;

  constructor() {
    const now = new Date();

    // Sensible defaults based on the most common scenario (hourly regular rate)
    this.earningRate = {
      amount: "0.00",
      period: EARNING_RATE_PERIODS.HOURLY,
      organizationId: null, // Required - must be set
      name: "Regular",
      active: true,
      type: EARNING_RATE_TYPES.REG,
      startDate: now,
      createdAt: now,
      updatedAt: now,
    };
  }

  // Required fields
  forOrganization(organizationId: number): EarningRateBuilder {
    this.earningRate.organizationId = organizationId;

    return this;
  }

  // Optional fields
  forUser(userId: number): EarningRateBuilder {
    this.earningRate.userId = userId;

    return this;
  }

  withAmount(amount: number | string): EarningRateBuilder {
    this.earningRate.amount = typeof amount === "number" ? amount.toFixed(2) : amount;

    return this;
  }

  withPeriod(period: EARNING_RATE_PERIODS): EarningRateBuilder {
    this.earningRate.period = period;

    return this;
  }

  withDateRange(startDate: Date, endDate?: Date): EarningRateBuilder {
    this.earningRate.startDate = startDate;
    this.earningRate.endDate = endDate;

    return this;
  }

  setActive(active = true): EarningRateBuilder {
    this.earningRate.active = active;

    return this;
  }

  withWeeklyHours(hours: number): EarningRateBuilder {
    this.earningRate.weeklyHours = hours;

    return this;
  }

  // Common scenarios
  asRegularRate(amount: number): EarningRateBuilder {
    this.earningRate.type = EARNING_RATE_TYPES.REG;
    this.earningRate.name = "Regular";

    if (amount) {
      this.earningRate.amount = amount.toFixed(2);
    }

    return this;
  }

  asOvertimeRate(baseAmount?: number): EarningRateBuilder {
    this.earningRate.type = EARNING_RATE_TYPES.OT;
    this.earningRate.name = "Overtime";

    if (baseAmount) {
      const otAmount = baseAmount * 1.5;
      this.earningRate.amount = otAmount.toFixed(2);
    }

    return this;
  }

  // Specific rate types
  asHourlyRate(hourlyRate: number): EarningRateBuilder {
    this.earningRate.period = EARNING_RATE_PERIODS.HOURLY;
    this.earningRate.amount = hourlyRate.toFixed(2);

    return this;
  }

  asSalaryRate(annualSalary: number): EarningRateBuilder {
    this.earningRate.period = EARNING_RATE_PERIODS.ANNUALLY;
    this.earningRate.amount = annualSalary.toFixed(2);

    return this;
  }

  asPieceRate(pieceRate: number): EarningRateBuilder {
    this.earningRate.period = EARNING_RATE_PERIODS.PIECE;
    this.earningRate.amount = pieceRate.toFixed(2);

    return this;
  }

  private toCreatePayload() {
    const { createdAt: _createdAt, updatedAt: _updatedAt, ...createPayload } = this.earningRate;

    return createPayload;
  }

  build(): EarningRateData {
    return { ...this.earningRate };
  }

  async buildAndCreate(transaction?: any): Promise<EarningRate> {
    if (!this.earningRate.organizationId) {
      throw new Error("Organization ID is required to create an earning rate");
    }

    try {
      return await EarningRate.create(this.toCreatePayload(), { transaction });
    } catch (error) {
      console.error("Error creating earning rate:", error);
      throw error;
    }
  }
}

// Factory functions for common scenarios
export async function createHourlyRates(
  organizationId: number,
  userId: number,
  hourlyRate = 25.0,
  transaction?: any
): Promise<EarningRate[]> {
  // Create regular rate
  const regularRate = await new EarningRateBuilder()
    .forOrganization(organizationId)
    .forUser(userId)
    .asHourlyRate(hourlyRate)
    .asRegularRate(hourlyRate)
    .buildAndCreate(transaction);

  // Create overtime rate
  const overtimeRate = await new EarningRateBuilder()
    .forOrganization(organizationId)
    .forUser(userId)
    .asHourlyRate(hourlyRate * 1.5)
    .asOvertimeRate(hourlyRate)
    .buildAndCreate(transaction);

  return [regularRate, overtimeRate];
}

export async function createSalaryRate(
  organizationId: number,
  userId: number,
  annualSalary = 80000,
  transaction?: any
): Promise<EarningRate> {
  return new EarningRateBuilder()
    .forOrganization(organizationId)
    .forUser(userId)
    .asSalaryRate(annualSalary)
    .asRegularRate(annualSalary)
    .buildAndCreate(transaction);
}

// Historical rate creation
export async function createHistoricalRate(
  organizationId: number,
  userId: number,
  { amount, startDate, endDate }: { amount: number; startDate: Date; endDate: Date }
): Promise<EarningRate> {
  return new EarningRateBuilder()
    .forOrganization(organizationId)
    .forUser(userId)
    .asHourlyRate(amount)
    .asRegularRate(amount)
    .withDateRange(startDate, endDate)
    .setActive(false)
    .buildAndCreate();
}

export const ROLE = ["ADMIN", "FOREMAN", "WORKER"] as const;
