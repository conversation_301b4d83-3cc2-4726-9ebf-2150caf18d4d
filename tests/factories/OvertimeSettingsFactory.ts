import OvertimeSettings from "@/models/overtimesettings";
import { WEEK_DAYS } from "@/models/overtimesettings";

export class OvertimeSettingsBuilder {
  private overtimeSettings: any;

  constructor() {
    this.overtimeSettings = {
      name: `Overtime Settings for ${Math.random().toString(36).substring(2, 15)}`, // required - should be unique per organization
      weeklyOvertimeEnabled: false, // lets default everything to false as base case
      dailyOvertimeEnabled: false,
      dailyOvertimeThreshold: 480, // 8 hours
      weeklyOvertimeThreshold: 2400, // 40 hours
      overtimeMultiplier: 1.5, // 50% more pay
      overtimeDaysOfWeek: [], // default to empty array
      weekStartDay: WEEK_DAYS[1], // default to monday
      dailyDoubleOvertimeEnabled: false,
      dailyDoubleOvertimeThreshold: 720, // 12 hours
      doubleOvertimeDaysOfWeek: [], // default to empty array

      // global settings
      overtimeDistribution: "SEQUENTIAL", // default to sequential

      // required fk
      organizationId: null, // required
    };
  }

  withName(name: string): OvertimeSettingsBuilder {
    this.overtimeSettings.name = name;

    return this;
  }

  withOrganizationId(organizationId: number): OvertimeSettingsBuilder {
    this.overtimeSettings.organizationId = organizationId;

    return this;
  }

  withOvertimeSettings(overtimeSettings: Partial<OvertimeSettings>): OvertimeSettingsBuilder {
    this.overtimeSettings = { ...this.overtimeSettings, ...overtimeSettings };

    return this;
  }

  withDoubleOvertimeSettings(doubleOvertimeSettings: Partial<OvertimeSettings>): OvertimeSettingsBuilder {
    this.overtimeSettings = { ...this.overtimeSettings, ...doubleOvertimeSettings };

    return this;
  }

  withGlobalSettings(globalSettings: Partial<OvertimeSettings>): OvertimeSettingsBuilder {
    this.overtimeSettings = { ...this.overtimeSettings, ...globalSettings };

    return this;
  }

  async buildAndCreate(): Promise<OvertimeSettings> {
    if (!this.overtimeSettings.organizationId) {
      throw new Error("Organization ID is required");
    }

    const overtimeSettings = await OvertimeSettings.create(this.overtimeSettings);

    return overtimeSettings;
  }
}

// Factory function for common scenarios
export async function createFullFeaturedOvertimeSequentialSettings(organizationId: number): Promise<OvertimeSettings> {
  return new OvertimeSettingsBuilder()
    .withOvertimeSettings({
      weeklyOvertimeEnabled: true,
      dailyOvertimeEnabled: true,
      overtimeDays: [WEEK_DAYS[0], WEEK_DAYS[1], WEEK_DAYS[2], WEEK_DAYS[3], WEEK_DAYS[4], WEEK_DAYS[5], WEEK_DAYS[6]], // all days
    })
    .withDoubleOvertimeSettings({
      dailyDoubleOvertimeEnabled: true,
      dailyDoubleOvertimeThreshold: 720, // 12 hours
      doubleOvertimeDays: [
        WEEK_DAYS[0],
        WEEK_DAYS[1],
        WEEK_DAYS[2],
        WEEK_DAYS[3],
        WEEK_DAYS[4],
        WEEK_DAYS[5],
        WEEK_DAYS[6],
      ], // all days
    })
    .withGlobalSettings({
      overtimeDistribution: "SEQUENTIAL",
    })
    .withName("Sequential Overtime Settings")
    .withOrganizationId(organizationId)
    .buildAndCreate();
}

export async function createFullFeaturedOvertimeWeightedSettings(organizationId: number): Promise<OvertimeSettings> {
  return new OvertimeSettingsBuilder()
    .withOvertimeSettings({
      weeklyOvertimeEnabled: true,
      dailyOvertimeEnabled: true,
      overtimeDays: [WEEK_DAYS[0], WEEK_DAYS[1], WEEK_DAYS[2], WEEK_DAYS[3], WEEK_DAYS[4], WEEK_DAYS[5], WEEK_DAYS[6]], // all days
    })
    .withDoubleOvertimeSettings({
      dailyDoubleOvertimeEnabled: true,
      dailyDoubleOvertimeThreshold: 720, // 12 hours
      doubleOvertimeDays: [
        WEEK_DAYS[0],
        WEEK_DAYS[1],
        WEEK_DAYS[2],
        WEEK_DAYS[3],
        WEEK_DAYS[4],
        WEEK_DAYS[5],
        WEEK_DAYS[6],
      ], // all days
    })
    .withGlobalSettings({
      overtimeDistribution: "WEIGHTED",
    })
    .withName("Weighted Overtime Settings")
    .withOrganizationId(organizationId)
    .buildAndCreate();
}
