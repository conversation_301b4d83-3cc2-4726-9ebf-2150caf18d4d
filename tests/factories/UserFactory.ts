// tests/factories/UserFactory.ts

import User from "@/models/user";
import EarningRate from "@/models/earningrate";
import { EarningRateBuilder } from "./EarningRateFactory";
import { EARNING_RATE_PERIODS, EARNING_RATE_TYPES } from "@/models/earningrate";
import sequelize from "@/lib/sequelize";

export class UserBuilder {
  private user: any;
  private _earningRates: Array<any> = [];

  constructor() {
    const now = new Date();
    const id = Math.floor(Math.random() * 10000);

    // Base user properties with sensible defaults based on model
    this.user = {
      organizationId: null, // Required - must be set
      role: "WORKER", // Default from ROLE enum
      firstName: "Test",
      lastName: "User",
      email: `test.user.${id}@example.com`,
      phone: "+1234567890",
      status: "ONBOARDED", // From USER_STATUS
      position: "General Worker",
      workerClassification: "EMPLOYEE", // From WORKER_CLASSIFICATION
      isArchived: false,
      isClockInReminderEnabled: true,
      isClockOutReminderEnabled: true,
      createdAt: now,
      updatedAt: now,
    };
  }

  withOrganization(organizationId: number): UserBuilder {
    this.user.organizationId = organizationId;

    return this;
  }

  // Hourly employee setup (most common case)
  asHourlyEmployee(hourlyRate = 25.0): UserBuilder {
    // Clear any existing rates
    this._earningRates = [];

    // Store rate configurations instead of built objects
    this._earningRates.push({
      amount: hourlyRate,
      period: EARNING_RATE_PERIODS.HOURLY,
      type: EARNING_RATE_TYPES.REG,
      name: "Regular",
      active: true,
    });

    this._earningRates.push({
      amount: hourlyRate * 1.5,
      period: EARNING_RATE_PERIODS.HOURLY,
      type: EARNING_RATE_TYPES.OT,
      name: "Overtime",
      active: true,
    });

    return this;
  }

  // Salaried employee setup
  asSalariedEmployee(annualSalary = 80000): UserBuilder {
    // Clear any existing rates
    this._earningRates = [];

    // Create single salary rate
    this._earningRates.push(
      new EarningRateBuilder()
        .forOrganization(this.user.organizationId)
        .asSalaryRate(annualSalary)
        .asRegularRate(annualSalary / 2080)
        .build()
    );

    return this;
  }

  // Role-specific setups
  asWorker(): UserBuilder {
    this.user.role = "WORKER";

    return this;
  }

  asForeman(): UserBuilder {
    this.user.role = "FOREMAN";

    return this;
  }

  asAdmin(): UserBuilder {
    this.user.role = "ADMIN";

    return this;
  }

  // ... rest of your existing methods ...

  async buildAndCreate(): Promise<User> {
    if (!this.user.organizationId) {
      throw new Error("Organization ID is required to create a user");
    }

    const result = await sequelize.transaction(async (transaction) => {
      // Remove auto-generated fields
      const { id: _id, createdAt: _createdAt, updatedAt: _updatedAt, ...createPayload } = this.user;

      // Create the user
      const user = await User.create(createPayload, { transaction });

      // Create earning rates if any exist
      if (this._earningRates.length > 0) {
        await Promise.all(
          this._earningRates.map((rate) => {
            const builder = new EarningRateBuilder()
              .forOrganization(this.user.organizationId)
              .forUser(user.id)
              .withAmount(rate.amount)
              .withPeriod(rate.period)
              .setActive(rate.active);

            // Set the type based on the rate configuration
            if (rate.type === EARNING_RATE_TYPES.REG) {
              builder.asRegularRate(rate.amount);
            } else if (rate.type === EARNING_RATE_TYPES.OT) {
              builder.asOvertimeRate(rate.amount / 1.5); // Convert back to base rate
            }

            return builder.buildAndCreate(transaction);
          })
        );
      }

      // Reload with associations
      return user.reload({
        transaction,
        include: [
          {
            model: EarningRate,
            as: "earningRates",
          },
        ],
      });
    });

    return result;
  }
}

// Factory functions for common scenarios
export async function createHourlyWorker(organizationId: number, hourlyRate = 25.0): Promise<User> {
  return new UserBuilder().withOrganization(organizationId).asWorker().asHourlyEmployee(hourlyRate).buildAndCreate();
}

export async function createSalariedAdmin(organizationId: number, annualSalary = 80000): Promise<User> {
  return new UserBuilder().withOrganization(organizationId).asAdmin().asSalariedEmployee(annualSalary).buildAndCreate();
}
