import { describe, it, expect, beforeEach } from "vitest";
import { ClassificationBuilder } from "../ClassificationFactory";
import { OrganizationBuilder } from "../OrganizationFactory";
import { WageTableBuilder } from "../WageTableFactory";

describe("ClassificationFactory", () => {
  let organizationId: number;
  let wageTableId: number;

  beforeEach(async () => {
    // Create organization
    const org = await new OrganizationBuilder().buildAndCreate();
    organizationId = org.id;

    // Create wage table
    const wageTable = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();
    wageTableId = wageTable.id;
  });

  describe("basic creation", () => {
    it("creates a classification with default settings", async () => {
      const classification = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      expect(classification.name).toBe("Test Classification");
      expect(classification.basePay).toBe("50.00");
      expect(classification.fringePay).toBe("10.00");
      expect(classification.organizationId).toBe(organizationId);
      expect(classification.wageTableId).toBe(wageTableId);
      expect(classification.checkRegEarningCodeId).toMatch(/^[A-Za-z0-9]{12}$/);
      expect(classification.checkOtEarningCodeId).toMatch(/^[A-Za-z0-9]{12}$/);
      expect(classification.startDate).toBeDefined();
      expect(classification.endDate).toBeNull();
    });
  });

  describe("ClassificationBuilder", () => {
    it("requires an organization ID", async () => {
      const builder = new ClassificationBuilder();
      await expect(async () => {
        await builder.buildAndCreate();
      }).rejects.toThrow("Organization ID is required to create a classification");
    });

    it("allows setting custom pay rates", async () => {
      const classification = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .withAllocatedPay("75.00", "15.00")
        .buildAndCreate();

      expect(classification.basePay).toBe("75.00");
      expect(classification.fringePay).toBe("15.00");
    });

    it("allows setting custom earning codes", async () => {
      const regCode = "REG123456789";
      const otCode = "OT123456789";

      const classification = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .withEarningCodes(regCode, otCode)
        .buildAndCreate();

      expect(classification.checkRegEarningCodeId).toBe(regCode);
      expect(classification.checkOtEarningCodeId).toBe(otCode);
    });

    it("generates unique earning code IDs by default", async () => {
      const classification1 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      const classification2 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      expect(classification1.checkRegEarningCodeId).not.toBe(classification2.checkRegEarningCodeId);
      expect(classification1.checkOtEarningCodeId).not.toBe(classification2.checkOtEarningCodeId);
    });
  });

  describe("validation and error cases", () => {
    it("creates multiple classifications for the same organization", async () => {
      const classification1 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      const classification2 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      expect(classification1.organizationId).toBe(organizationId);
      expect(classification2.organizationId).toBe(organizationId);
      expect(classification1.id).not.toBe(classification2.id);
    });

    it("creates classifications with different wage tables", async () => {
      // Create another wage table
      const wageTable2 = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      const classification1 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      const classification2 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTable2.id)
        .buildAndCreate();

      expect(classification1.wageTableId).toBe(wageTableId);
      expect(classification2.wageTableId).toBe(wageTable2.id);
    });

    it("creates a classification with all properties set", async () => {
      const classification = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .withAllocatedPay("100.00", "20.00")
        .withEarningCodes("CUSTOMREG123", "CUSTOMOT123")
        .buildAndCreate();

      expect(classification.name).toBe("Test Classification");
      expect(classification.basePay).toBe("100.00");
      expect(classification.fringePay).toBe("20.00");
      expect(classification.organizationId).toBe(organizationId);
      expect(classification.wageTableId).toBe(wageTableId);
      expect(classification.checkRegEarningCodeId).toBe("CUSTOMREG123");
      expect(classification.checkOtEarningCodeId).toBe("CUSTOMOT123");
      expect(classification.startDate).toBeDefined();
      expect(classification.endDate).toBeNull();
    });
  });
});
