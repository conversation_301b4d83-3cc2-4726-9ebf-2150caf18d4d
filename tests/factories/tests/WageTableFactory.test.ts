import { describe, it, expect, beforeEach } from "vitest";
import { WageTableBuilder } from "../WageTableFactory";
import { OrganizationBuilder } from "../OrganizationFactory";

describe("WageTableFactory", () => {
  let organizationId: number;

  beforeEach(async () => {
    const org = await new OrganizationBuilder().buildAndCreate();
    organizationId = org.id;
  });

  describe("basic creation", () => {
    it("creates a wage table with default settings", async () => {
      const wageTable = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      expect(wageTable.name).toBe("Test Wage Table");
      expect(wageTable.description).toBe("Test Description");
      expect(wageTable.organizationId).toBe(organizationId);
    });
  });

  describe("WageTableBuilder", () => {
    it("allows setting custom wage table details", async () => {
      const wageTable = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      expect(wageTable.name).toBe("Test Wage Table");
      expect(wageTable.description).toBe("Test Description");
      expect(wageTable.organizationId).toBe(organizationId);
    });

    it("requires an organization ID", async () => {
      const builder = new WageTableBuilder();
      await expect(async () => {
        await builder.buildAndCreate();
      }).rejects.toThrow("Organization ID is required to create a wage table");
    });

    it("creates a wage table with a custom name", async () => {
      const wageTable = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      expect(wageTable.name).toBeDefined();
      expect(wageTable.organizationId).toBe(organizationId);
    });
  });

  describe("validation and error cases", () => {
    it("handles missing optional properties", async () => {
      const wageTable = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      expect(wageTable).toBeDefined();
      expect(wageTable.organizationId).toBe(organizationId);
      expect(wageTable.name).toBeDefined();
      expect(wageTable.description).toBeDefined();
    });

    it("creates multiple wage tables for the same organization", async () => {
      const wageTable1 = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      const wageTable2 = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      expect(wageTable1.organizationId).toBe(organizationId);
      expect(wageTable2.organizationId).toBe(organizationId);
      expect(wageTable1.id).not.toBe(wageTable2.id);
    });
  });
});
