import { describe, it, expect, beforeEach } from "vitest";
import { ProjectBuilder, createBasicProject, createGeofencedProject, GeofenceSettings } from "../ProjectFactory";
import { OrganizationBuilder } from "../OrganizationFactory";
import { OvertimeSettingsBuilder, createFullFeaturedOvertimeSequentialSettings } from "../OvertimeSettingsFactory";
import sequelize from "@/lib/sequelize";

describe("ProjectFactory", () => {
  let organizationId: number;

  beforeEach(async () => {
    await sequelize.transaction(async (_transaction) => {
      const org = await new OrganizationBuilder().buildAndCreate();
      organizationId = org.id;
    });
  });

  describe("createBasicProject", () => {
    it("creates a project with default settings", async () => {
      const project = await createBasicProject(organizationId);

      expect(project.organizationId).toBe(organizationId);
      expect(project.name).toBeDefined();
      expect(project.projectNumber).toBeDefined();
      expect(project.isGeofenced).toBe(false);
      expect(project.isPrevailingWage).toBe(false);
    });
  });

  describe("createGeofencedProject", () => {
    it("creates a project with default geofence settings", async () => {
      const project = await createGeofencedProject(organizationId);

      expect(project.isGeofenced).toBe(true);
      expect(project.location.type).toBe("Point");
      expect(project.location.coordinates).toEqual([-118.2437, 34.0522]); // LA coordinates
      expect(project.geofenceRadius).toBe(1000);
    });

    it("creates a project with custom geofence settings", async () => {
      const customSettings: GeofenceSettings = {
        coordinates: [-122.4194, 37.7749], // SF coordinates
        radius: 2000,
      };

      const project = await createGeofencedProject(organizationId, customSettings);

      expect(project.isGeofenced).toBe(true);
      expect(project.location.coordinates).toEqual(customSettings.coordinates);
      expect(project.geofenceRadius).toBe(customSettings.radius);
    });

    it("allows partial override of geofence settings", async () => {
      const project = await createGeofencedProject(organizationId, { radius: 3000 });

      expect(project.isGeofenced).toBe(true);
      expect(project.location.coordinates).toEqual([-118.2437, 34.0522]); // Default LA coordinates
      expect(project.geofenceRadius).toBe(3000); // Custom radius
    });
  });

  // TODO: Add tests for prevailing wage projects - need wage table factory
  // describe("createPrevailingWageProject", () => {
  //   it("creates a project with prevailing wage settings", async () => {
  //     const wageTableId = 123;
  //     const project = await createPrevailingWageProject(organizationId, wageTableId);

  //     expect(project.isPrevailingWage).toBe(true);
  //     expect(project.wageTableId).toBe(wageTableId);
  //     expect(project.prevailingWageCategory).toBe("FEDERAL");
  //     expect(project.prevailingWageState).toBe("CA");
  //     expect(project.prevailingWageDirProjectId).toBeDefined();
  //     expect(project.prevailingWageIsSubcontractor).toBe(false);
  //   });
  // });

  describe("ProjectBuilder", () => {
    it("allows setting custom project details", async () => {
      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withName("Custom Project")
        .withAddress("456 Market St, San Francisco, CA")
        .withNotes("Special project notes")
        .buildAndCreate();

      expect(project.name).toBe("Custom Project");
      expect(project.address).toBe("456 Market St, San Francisco, CA");
      expect(project.notes).toBe("Special project notes");
    });

    it("allows setting budget information", async () => {
      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withBudget({ hours: 1000, cost: 50000 })
        .buildAndCreate();

      expect(project.hoursBudget).toBe(1000);
      expect(project.costBudget).toBe(50000);
    });

    it("allows setting location without geofence", async () => {
      const location = {
        type: "Point" as const,
        coordinates: [-122.4194, 37.7749],
      };

      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withLocation(location)
        .buildAndCreate();

      // Only test the parts we care about
      expect(project.location.type).toBe(location.type);
      expect(project.location.coordinates).toEqual(location.coordinates);
      expect(project.isGeofenced).toBe(false);
    });

    it("allows setting overtime settings", async () => {
      // Create an overtime settings instance
      const overtimeSettings = await new OvertimeSettingsBuilder()
        .withName("Project Overtime Settings")
        .withOrganizationId(organizationId)
        .withOvertimeSettings({
          weeklyOvertimeEnabled: true,
          dailyOvertimeEnabled: true,
          dailyOvertimeThreshold: 600, // 10 hours
        })
        .buildAndCreate();

      // Create a project with the overtime settings
      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withName("Project with Overtime")
        .withOvertimeSettings(overtimeSettings.id)
        .buildAndCreate();

      expect(project.overtimeSettingsId).toBe(overtimeSettings.id);
    });

    it("allows setting overtime settings with full featured settings", async () => {
      // Create a full featured overtime settings instance
      const overtimeSettings = await createFullFeaturedOvertimeSequentialSettings(organizationId);

      // Create a project with the overtime settings
      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withName("Project with Full Featured Overtime")
        .withOvertimeSettings(overtimeSettings.id)
        .buildAndCreate();

      expect(project.overtimeSettingsId).toBe(overtimeSettings.id);
    });
  });

  describe("validation and error cases", () => {
    it("requires an organization ID", async () => {
      await expect(new ProjectBuilder().buildAndCreate()).rejects.toThrow("Organization ID is required");
    });

    it("handles archived projects", async () => {
      const project = await new ProjectBuilder().withOrganization(organizationId).withIsArchived(true).buildAndCreate();

      expect(project.isArchived).toBe(true);
    });
  });

  describe("business rules", () => {
    it("ensures prevailing wage projects have required fields", async () => {
      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withPrevailingWage({
          category: "STATE",
          state: "NY",
          dirProjectId: "NY-2023-001",
          awardingBody: "NY State",
          primeContractor: "ABC Construction",
          bidAwardDate: new Date("2023-01-01"),
          isSubcontractor: true,
        })
        .buildAndCreate();

      expect(project.isPrevailingWage).toBe(true);
      expect(project.prevailingWageCategory).toBe("STATE");
      expect(project.prevailingWageState).toBe("NY");
      expect(project.prevailingWageIsSubcontractor).toBe(true);
      expect(project.prevailingWagePrimeContractor).toBe("ABC Construction");
    });

    it("allows a project to have both overtime settings and prevailing wage", async () => {
      // Create overtime settings
      const overtimeSettings = await new OvertimeSettingsBuilder()
        .withName("Project Overtime Settings")
        .withOrganizationId(organizationId)
        .withOvertimeSettings({
          weeklyOvertimeEnabled: true,
          dailyOvertimeEnabled: true,
        })
        .buildAndCreate();

      // Create a project with both overtime settings and prevailing wage
      const project = await new ProjectBuilder()
        .withOrganization(organizationId)
        .withName("Project with Overtime and PW")
        .withOvertimeSettings(overtimeSettings.id)
        .withPrevailingWage({
          category: "STATE",
          state: "CA",
          dirProjectId: "CA-2023-001",
          awardingBody: "CA State",
          primeContractor: "XYZ Construction",
          bidAwardDate: new Date("2023-01-01"),
          isSubcontractor: false,
        })
        .buildAndCreate();

      expect(project.overtimeSettingsId).toBe(overtimeSettings.id);
      expect(project.isPrevailingWage).toBe(true);
      expect(project.prevailingWageCategory).toBe("STATE");
    });
  });
});
