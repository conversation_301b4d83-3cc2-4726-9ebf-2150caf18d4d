import { describe, it, expect, beforeEach } from "vitest";
import { CostCodeBuilder, createBasicCostCode } from "../CostCodeFactory";
import { OrganizationBuilder } from "../OrganizationFactory";
import sequelize from "@/lib/sequelize";

describe("CostCodeFactory", () => {
  let organizationId: number;

  beforeEach(async () => {
    await sequelize.transaction(async (_transaction) => {
      // Create organization first
      const org = await new OrganizationBuilder().buildAndCreate();
      organizationId = org.id;
    });
  });

  describe("basic cost code creation", () => {
    it("creates a cost code with default settings", async () => {
      const costCode = await new CostCodeBuilder().withOrganization(organizationId).buildAndCreate();

      expect(costCode).toBeDefined();
      expect(costCode.organizationId).toBe(organizationId);
      expect(costCode.number).toBeDefined();
      expect(costCode.name).toEqual("Test Cost Code");
    });

    it("creates a cost code with custom name", async () => {
      const costCode = await new CostCodeBuilder()
        .withOrganization(organizationId)
        .withName("Custom Name")
        .buildAndCreate();

      expect(costCode.id).toBeDefined();
      expect(costCode.name).toEqual("Custom Name");
    });
  });

  describe("factory functions", () => {
    it("createBasicCostCode creates a default cost code", async () => {
      const costCode = await createBasicCostCode(organizationId);

      expect(costCode.organizationId).toBe(organizationId);
      expect(costCode.number).toBeDefined();
      expect(costCode.name).toEqual("Test Cost Code");
    });
  });

  describe("validation and error cases", () => {
    it("requires an organization ID", async () => {
      await expect(new CostCodeBuilder().buildAndCreate()).rejects.toThrow("Organization ID is required");
    });

    it("handles custom code and name", async () => {
      const customNumber = "CC-123";
      const customName = "Special Project Phase";

      const costCode = await new CostCodeBuilder()
        .withOrganization(organizationId)
        .withNumber(customNumber)
        .withName(customName)
        .buildAndCreate();

      expect(costCode.number).toBe(customNumber);
      expect(costCode.name).toBe(customName);
    });
  });
});
