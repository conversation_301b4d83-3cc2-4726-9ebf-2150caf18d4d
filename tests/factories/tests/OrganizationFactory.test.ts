// tests/factories/tests/OrganizationFactory.test.ts

import { describe, it, expect } from "vitest";
import { OrganizationBuilder, createBasicOrganization, createPayrollOrganization } from "../OrganizationFactory";

describe("OrganizationFactory", () => {
  describe("createBasicOrganization", () => {
    it("creates an organization with default settings", async () => {
      const org = await createBasicOrganization();

      expect(org.name).toBeDefined();
      expect(org.checkCompanyId).toBeDefined();

      // Verify default feature flags
      expect(org.featureFlag).toBeDefined();
      expect(org.featureFlag.isPayrollEnabled).toBe(true);
      expect(org.featureFlag.isSchedulingEnabled).toBe(true);

      // Verify default time tracking settings
      expect(org.timeTrackingSettings).toBeDefined();
      expect(org.timeTrackingSettings.allowWorkersToAddEditTime).toBe(true);
      expect(org.timeTrackingSettings.showWagesToWorkers).toBe(true);

      // Verify default overtime settings
      expect(org.overtimeSettings).toBeDefined();
      expect(org.overtimeSettings.weeklyOvertimeEnabled).toBe(true);
      expect(org.overtimeSettings.overtimeMultiplier).toBe(1.5);
    });
  });

  describe("createPayrollOrganization", () => {
    it("creates an organization with payroll-specific settings", async () => {
      const org = await createPayrollOrganization();

      expect(org.featureFlag.isPayrollEnabled).toBe(true);
      expect(org.timeTrackingSettings.showWagesToWorkers).toBe(true);
      expect(org.overtimeSettings.weeklyOvertimeEnabled).toBe(true);
    });
  });

  describe("OrganizationBuilder", () => {
    it("allows custom time tracking settings", async () => {
      const org = await new OrganizationBuilder()
        .withTimeTrackingSettings({
          areBreaksPaid: true,
          isCostCodeRequired: true,
          breakOptions: [30, 60],
        })
        .buildAndCreate();

      expect(org.timeTrackingSettings.areBreaksPaid).toBe(true);
      expect(org.timeTrackingSettings.isCostCodeRequired).toBe(true);
      expect(org.timeTrackingSettings.breakOptions).toEqual([30, 60]);
    });

    it("allows custom overtime settings", async () => {
      const org = await new OrganizationBuilder()
        .withOvertimeSettings({
          dailyOvertimeEnabled: true,
          dailyOvertimeThreshold: 600, // 10 hours
          overtimeMultiplier: 2.0,
        })
        .buildAndCreate();

      expect(org.overtimeSettings.dailyOvertimeEnabled).toBe(true);
      expect(org.overtimeSettings.dailyOvertimeThreshold).toBe(600);
      expect(org.overtimeSettings.overtimeMultiplier).toBe(2.0);
    });

    it("creates an organization with multiple custom settings", async () => {
      const org = await new OrganizationBuilder()
        .withName("Custom Organization")
        .withFeatures({
          isPayrollEnabled: true,
          isSchedulingEnabled: false,
        })
        .withTimeTrackingSettings({
          areBreaksPaid: true,
        })
        .withOvertimeSettings({
          weeklyOvertimeEnabled: true,
        })
        .withPrevailingWageSettings({
          allowCustomPrevailingWagePerEmployee: true,
        })
        .buildAndCreate();

      expect(org.name).toBe("Custom Organization");
      expect(org.featureFlag.isPayrollEnabled).toBe(true);
      expect(org.featureFlag.isSchedulingEnabled).toBe(false);
      expect(org.timeTrackingSettings.areBreaksPaid).toBe(true);
      expect(org.overtimeSettings.weeklyOvertimeEnabled).toBe(true);
      expect(org.prevailingWageSettings.allowCustomPrevailingWagePerEmployee).toBe(true);
    });

    describe("business rule validation", () => {
      it("enforces payroll feature flag when overtime is enabled", async () => {
        const org = await new OrganizationBuilder().withOvertimeEnabled().buildAndCreate();

        expect(org.featureFlag.isPayrollEnabled).toBe(true);
        expect(org.overtimeSettings.weeklyOvertimeEnabled).toBe(true);
      });

      // Add more business rule validation tests as they're discovered
    });

    describe("error cases", () => {
      it("handles missing optional properties", async () => {
        const org = await new OrganizationBuilder().withName("Basic Org").buildAndCreate();

        expect(org).toBeDefined();
        expect(org.name).toBe("Basic Org");
        // Verify default values are applied
      });
    });
  });
});
