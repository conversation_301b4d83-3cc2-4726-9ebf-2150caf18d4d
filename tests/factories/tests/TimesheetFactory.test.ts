import { describe, it, expect, beforeEach } from "vitest";
import { TimesheetBuilder, createClockedInTimesheet, createTimesheetSeries } from "../TimesheetFactory";
import { OrganizationBuilder } from "../OrganizationFactory";
import { UserBuilder } from "../UserFactory";
import { ProjectBuilder } from "../ProjectFactory";
import { EarningRateBuilder } from "../EarningRateFactory";
import sequelize from "@/lib/sequelize";

describe("TimesheetFactory", () => {
  let organizationId: number;
  let userId: number;
  let projectId: number;
  let regEarningRateId: number;
  let otEarningRateId: number;

  beforeEach(async () => {
    await sequelize.transaction(async (_transaction) => {
      // Create organization
      const org = await new OrganizationBuilder().buildAndCreate();
      organizationId = org.id;

      // Create user
      const user = await new UserBuilder().withOrganization(organizationId).buildAndCreate();
      userId = user.id;

      // Create project
      const project = await new ProjectBuilder().withOrganization(organizationId).buildAndCreate();
      projectId = project.id;

      // Create earning rates
      const rates = await Promise.all([
        new EarningRateBuilder().forOrganization(organizationId).forUser(userId).buildAndCreate(),
        new EarningRateBuilder().forOrganization(organizationId).forUser(userId).asOvertimeRate(25.0).buildAndCreate(),
      ]);

      [regEarningRateId, otEarningRateId] = rates.map((rate) => rate.id);
    });
  });

  describe("createClockedInTimesheet", () => {
    it("creates a timesheet with default settings", async () => {
      const timesheet = await createClockedInTimesheet(
        userId,
        organizationId,
        projectId,
        regEarningRateId,
        otEarningRateId
      );

      expect(timesheet.workerId).toBe(userId);
      expect(timesheet.organizationId).toBe(organizationId);
      expect(timesheet.projectId).toBe(projectId);
      expect(timesheet.regEarningRateId).toBe(regEarningRateId);
      expect(timesheet.otEarningRateId).toBe(otEarningRateId);
      expect(timesheet.status).toBe("CLOCKED_IN");
      expect(timesheet.clockIn).toBeDefined();
      expect(timesheet.clockOut).toBeNull();
    });
  });

  describe("createTimesheetSeries", () => {
    // Helper function to create a date at noon local time
    // Using noon ensures we don't run into timezone issues around midnight
    function createTestDate(dateString: string): Date {
      // Create date in local time
      const [year, month, day] = dateString.split("-").map(Number);

      return new Date(year, month - 1, day, 12, 0, 0, 0);
    }

    function getLocalDateString(date: Date): string {
      // Convert to YYYY-MM-DD format in local timezone
      return date.toISOString().split("T")[0];
    }

    it("creates a series of timesheets for weekdays", async () => {
      const startDate = createTestDate("2024-03-04"); // Monday

      const timesheets = await createTimesheetSeries(
        userId,
        organizationId,
        projectId,
        regEarningRateId,
        otEarningRateId,
        {
          startDate,
          numberOfDays: 5,
          skipWeekends: true,
          clockInTime: "08:00", // 8 AM local time
          clockOutTime: "16:00", // 4 PM local time
        }
      );

      expect(timesheets).toHaveLength(5);

      // Verify first timesheet
      expect(timesheets[0].clockIn.getHours()).toBe(8);
      expect(timesheets[0].clockOut.getHours()).toBe(16);

      // Verify all dates are weekdays
      timesheets.forEach((timesheet) => {
        const day = timesheet.clockIn.getDay();
        expect(day).toBeGreaterThan(0); // Not Sunday
        expect(day).toBeLessThan(6); // Not Saturday
      });
    });

    it("includes weekends when skipWeekends is false", async () => {
      const startDate = createTestDate("2024-03-08"); // Friday

      const timesheets = await createTimesheetSeries(
        userId,
        organizationId,
        projectId,
        regEarningRateId,
        otEarningRateId,
        {
          startDate,
          numberOfDays: 3,
          skipWeekends: false,
          clockInTime: "09:00",
          clockOutTime: "17:00",
        }
      );

      expect(timesheets).toHaveLength(3);

      const expectedDates = [
        "2024-03-08", // Friday
        "2024-03-09", // Saturday
        "2024-03-10", // Sunday
      ];

      timesheets.forEach((timesheet, index) => {
        const actualDate = getLocalDateString(timesheet.clockIn);
        expect(actualDate).toBe(expectedDates[index]);
      });
    });
  });

  describe("TimesheetBuilder", () => {
    it("allows setting custom timesheet details", async () => {
      const clockIn = new Date();
      const clockOut = new Date(clockIn.getTime() + 8 * 60 * 60 * 1000); // 8 hours later

      const timesheet = await new TimesheetBuilder()
        .forWorker(userId)
        .withOrganization(organizationId)
        .forProject(projectId)
        .withEarningRates(regEarningRateId, otEarningRateId)
        .withClockInAndOut(clockIn, clockOut)
        .withDescription("Custom timesheet description")
        .buildAndCreate();

      expect(timesheet.description).toBe("Custom timesheet description");
      expect(timesheet.clockIn).toEqual(clockIn);
      expect(timesheet.clockOut).toEqual(clockOut);
    });
  });

  describe("validation and error cases", () => {
    it("requires a worker ID", async () => {
      await expect(
        new TimesheetBuilder()
          .withOrganization(organizationId)
          .forProject(projectId)
          .withEarningRates(regEarningRateId, otEarningRateId)
          .buildAndCreate()
      ).rejects.toThrow("Worker ID is required");
    });

    // Add more validation tests...
  });
});
