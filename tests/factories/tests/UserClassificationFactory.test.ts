import { describe, it, expect, beforeEach } from "vitest";
import {
  UserClassificationFactory,
  createUserClassification,
  createUserClassificationWithPay,
} from "../UserClassificationFactory";
import { OrganizationBuilder } from "../OrganizationFactory";
import { UserBuilder } from "../UserFactory";
import { ClassificationBuilder } from "../ClassificationFactory";
import { WageTableBuilder } from "../WageTableFactory";

describe("UserClassificationFactory", () => {
  let organizationId: number;
  let userId: number;
  let classificationId: number;
  let wageTableId: number;

  beforeEach(async () => {
    // Create organization
    const org = await new OrganizationBuilder().buildAndCreate();
    organizationId = org.id;

    // Create user
    const user = await new UserBuilder().withOrganization(organizationId).buildAndCreate();
    userId = user.id;

    // Create wage table
    const wageTable = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();
    wageTableId = wageTable.id;

    // Create classification
    const classification = await new ClassificationBuilder()
      .withOrganization(organizationId)
      .withWageTable(wageTableId)
      .buildAndCreate();
    classificationId = classification.id;
  });

  describe("basic creation", () => {
    it("creates a user classification with default settings", async () => {
      const userClassification = await new UserClassificationFactory()
        .withOrganization(organizationId)
        .withUser(userId)
        .withClassification(classificationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      expect(userClassification.basePay).toBe("50.00");
      expect(userClassification.fringePay).toBe("10.00");
      expect(userClassification.organizationId).toBe(organizationId);
      expect(userClassification.userId).toBe(userId);
      expect(userClassification.classificationId).toBe(classificationId);
      expect(userClassification.wageTableId).toBe(wageTableId);
      expect(userClassification.startDate).toBeDefined();
      expect(userClassification.endDate).toBeNull();
    });
  });

  describe("UserClassificationFactory", () => {
    it("requires all required fields", async () => {
      // Test missing organization ID
      await expect(
        new UserClassificationFactory()
          .withUser(userId)
          .withClassification(classificationId)
          .withWageTable(wageTableId)
          .buildAndCreate()
      ).rejects.toThrow("Organization ID is required to create a user classification");

      // Test missing user ID
      await expect(
        new UserClassificationFactory()
          .withOrganization(organizationId)
          .withClassification(classificationId)
          .withWageTable(wageTableId)
          .buildAndCreate()
      ).rejects.toThrow("User ID is required to create a user classification");

      // Test missing classification ID
      await expect(
        new UserClassificationFactory()
          .withOrganization(organizationId)
          .withUser(userId)
          .withWageTable(wageTableId)
          .buildAndCreate()
      ).rejects.toThrow("Classification ID is required to create a user classification");

      // Test missing wage table ID
      await expect(
        new UserClassificationFactory()
          .withOrganization(organizationId)
          .withUser(userId)
          .withClassification(classificationId)
          .buildAndCreate()
      ).rejects.toThrow("Wage table ID is required to create a user classification");
    });

    it("allows setting custom pay rates", async () => {
      const userClassification = await new UserClassificationFactory()
        .withOrganization(organizationId)
        .withUser(userId)
        .withClassification(classificationId)
        .withWageTable(wageTableId)
        .withAllocatedPay("75.00", "15.00")
        .buildAndCreate();

      expect(userClassification.basePay).toBe("75.00");
      expect(userClassification.fringePay).toBe("15.00");
    });
  });

  describe("factory functions", () => {
    it("creates a user classification using createUserClassification", async () => {
      const userClassification = await createUserClassification(organizationId, classificationId, userId, wageTableId);

      expect(userClassification.basePay).toBe("50.00");
      expect(userClassification.fringePay).toBe("10.00");
      expect(userClassification.organizationId).toBe(organizationId);
      expect(userClassification.userId).toBe(userId);
      expect(userClassification.classificationId).toBe(classificationId);
      expect(userClassification.wageTableId).toBe(wageTableId);
    });

    it("creates a user classification with custom pay using createUserClassificationWithPay", async () => {
      const userClassification = await createUserClassificationWithPay(
        organizationId,
        classificationId,
        userId,
        wageTableId,
        "100.00",
        "20.00"
      );

      expect(userClassification.basePay).toBe("100.00");
      expect(userClassification.fringePay).toBe("20.00");
      expect(userClassification.organizationId).toBe(organizationId);
      expect(userClassification.userId).toBe(userId);
      expect(userClassification.classificationId).toBe(classificationId);
      expect(userClassification.wageTableId).toBe(wageTableId);
    });
  });

  describe("validation and error cases", () => {
    it("creates multiple user classifications for the same user", async () => {
      // Create another classification
      const classification2 = await new ClassificationBuilder()
        .withOrganization(organizationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      const userClassification1 = await new UserClassificationFactory()
        .withOrganization(organizationId)
        .withUser(userId)
        .withClassification(classificationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      const userClassification2 = await new UserClassificationFactory()
        .withOrganization(organizationId)
        .withUser(userId)
        .withClassification(classification2.id)
        .withWageTable(wageTableId)
        .buildAndCreate();

      expect(userClassification1.userId).toBe(userId);
      expect(userClassification2.userId).toBe(userId);
      expect(userClassification1.id).not.toBe(userClassification2.id);
      expect(userClassification1.classificationId).not.toBe(userClassification2.classificationId);
    });

    it("creates user classifications with different wage tables", async () => {
      // Create another wage table
      const wageTable2 = await new WageTableBuilder().withOrganization(organizationId).buildAndCreate();

      const userClassification1 = await new UserClassificationFactory()
        .withOrganization(organizationId)
        .withUser(userId)
        .withClassification(classificationId)
        .withWageTable(wageTableId)
        .buildAndCreate();

      const userClassification2 = await new UserClassificationFactory()
        .withOrganization(organizationId)
        .withUser(userId)
        .withClassification(classificationId)
        .withWageTable(wageTable2.id)
        .buildAndCreate();

      expect(userClassification1.wageTableId).toBe(wageTableId);
      expect(userClassification2.wageTableId).toBe(wageTable2.id);
    });
  });
});
