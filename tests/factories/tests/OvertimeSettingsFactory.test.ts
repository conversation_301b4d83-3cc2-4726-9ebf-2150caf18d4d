import { describe, it, expect, beforeEach } from "vitest";
import {
  OvertimeSettingsBuilder,
  createFullFeaturedOvertimeSequentialSettings,
  createFullFeaturedOvertimeWeightedSettings,
} from "../OvertimeSettingsFactory";
import { OrganizationBuilder } from "../OrganizationFactory";
import { WEEK_DAYS } from "@/models/overtimesettings";

describe("OvertimeSettingsFactory", () => {
  let organizationId: number;

  beforeEach(async () => {
    const org = await new OrganizationBuilder().buildAndCreate();
    organizationId = org.id;
  });

  describe("OvertimeSettingsBuilder", () => {
    it("creates overtime settings with default values", async () => {
      const settings = await new OvertimeSettingsBuilder().withOrganizationId(organizationId).buildAndCreate();

      expect(settings.name).toBeDefined();
      expect(settings.organizationId).toBe(organizationId);
      expect(settings.weeklyOvertimeEnabled).toBe(false);
      expect(settings.dailyOvertimeEnabled).toBe(false);
      expect(settings.dailyOvertimeThreshold).toBe(480);
      expect(settings.weeklyOvertimeThreshold).toBe(2400);
      expect(settings.overtimeMultiplier).toBe(1.5);
      expect(settings.overtimeDays).toEqual([]);
      expect(settings.weekStartDay).toBe(WEEK_DAYS[1]); // Monday
      expect(settings.dailyDoubleOvertimeEnabled).toBe(false);
      expect(settings.dailyDoubleOvertimeThreshold).toBe(720);
      expect(settings.doubleOvertimeDays).toEqual([]);
      expect(settings.overtimeDistribution).toBe("SEQUENTIAL");
    });

    it("allows customizing name", async () => {
      const customName = "Custom Overtime Settings";
      const settings = await new OvertimeSettingsBuilder()
        .withName(customName)
        .withOrganizationId(organizationId)
        .buildAndCreate();

      expect(settings.name).toBe(customName);
    });

    it("allows customizing overtime settings", async () => {
      const settings = await new OvertimeSettingsBuilder()
        .withOrganizationId(organizationId)
        .withOvertimeSettings({
          weeklyOvertimeEnabled: true,
          dailyOvertimeEnabled: true,
          dailyOvertimeThreshold: 600, // 10 hours
          weeklyOvertimeThreshold: 3000, // 50 hours
          overtimeMultiplier: 2.0,
          overtimeDays: [WEEK_DAYS[1], WEEK_DAYS[2], WEEK_DAYS[3]], // Mon, Tue, Wed
        })
        .buildAndCreate();

      expect(settings.weeklyOvertimeEnabled).toBe(true);
      expect(settings.dailyOvertimeEnabled).toBe(true);
      expect(settings.dailyOvertimeThreshold).toBe(600);
      expect(settings.weeklyOvertimeThreshold).toBe(3000);
      expect(settings.overtimeMultiplier).toBe(2.0);
      expect(settings.overtimeDays).toEqual([WEEK_DAYS[1], WEEK_DAYS[2], WEEK_DAYS[3]]);
    });

    it.skip("allows customizing double overtime settings", async () => {
      const settings = await new OvertimeSettingsBuilder()
        .withOrganizationId(organizationId)
        .withDoubleOvertimeSettings({
          dailyDoubleOvertimeEnabled: true,
          dailyDoubleOvertimeThreshold: 840, // 14 hours
          doubleOvertimeDays: [WEEK_DAYS[4], WEEK_DAYS[5]], // Thu, Fri
        })
        .buildAndCreate();

      expect(settings.dailyDoubleOvertimeEnabled).toBe(true);
      expect(settings.dailyDoubleOvertimeThreshold).toBe(840);
      expect(settings.doubleOvertimeDays).toEqual([WEEK_DAYS[4], WEEK_DAYS[5]]);
    });

    it("allows customizing global settings", async () => {
      const settings = await new OvertimeSettingsBuilder()
        .withOrganizationId(organizationId)
        .withGlobalSettings({
          overtimeDistribution: "WEIGHTED",
        })
        .buildAndCreate();

      expect(settings.overtimeDistribution).toBe("WEIGHTED");
    });

    it("enforces unique name per organization", async () => {
      const name = "Duplicate Name";

      // Create first settings with the name
      await new OvertimeSettingsBuilder().withName(name).withOrganizationId(organizationId).buildAndCreate();

      // Attempt to create second settings with same name in same organization
      await expect(
        new OvertimeSettingsBuilder().withName(name).withOrganizationId(organizationId).buildAndCreate()
      ).rejects.toThrow();
    });

    it("allows same name in different organizations", async () => {
      const name = "Same Name";
      const org2 = await new OrganizationBuilder().buildAndCreate();

      // Create settings in first organization
      const settings1 = await new OvertimeSettingsBuilder()
        .withName(name)
        .withOrganizationId(organizationId)
        .buildAndCreate();

      // Create settings with same name in second organization
      const settings2 = await new OvertimeSettingsBuilder().withName(name).withOrganizationId(org2.id).buildAndCreate();

      expect(settings1.name).toBe(name);
      expect(settings2.name).toBe(name);
      expect(settings1.organizationId).not.toBe(settings2.organizationId);
    });

    it("requires an organization ID", async () => {
      await expect(new OvertimeSettingsBuilder().withName("Test Settings").buildAndCreate()).rejects.toThrow(
        "Organization ID is required"
      );
    });
  });

  describe("createFullFeaturedOvertimeSequentialSettings", () => {
    it("creates settings with all features enabled and sequential distribution", async () => {
      const settings = await createFullFeaturedOvertimeSequentialSettings(organizationId);

      expect(settings.name).toBe("Sequential Overtime Settings");
      expect(settings.organizationId).toBe(organizationId);
      expect(settings.weeklyOvertimeEnabled).toBe(true);
      expect(settings.dailyOvertimeEnabled).toBe(true);
      expect(settings.overtimeDays).toEqual(WEEK_DAYS);
      expect(settings.dailyDoubleOvertimeEnabled).toBe(true);
      expect(settings.dailyDoubleOvertimeThreshold).toBe(720);
      expect(settings.doubleOvertimeDays).toEqual(WEEK_DAYS);

      expect(settings.overtimeDistribution).toBe("SEQUENTIAL");
    });
  });

  describe("createFullFeaturedOvertimeWeightedSettings", () => {
    it("creates settings with all features enabled and weighted distribution", async () => {
      const settings = await createFullFeaturedOvertimeWeightedSettings(organizationId);

      expect(settings.name).toBe("Weighted Overtime Settings");
      expect(settings.organizationId).toBe(organizationId);
      expect(settings.weeklyOvertimeEnabled).toBe(true);
      expect(settings.dailyOvertimeEnabled).toBe(true);
      expect(settings.overtimeDays).toEqual(WEEK_DAYS);
      expect(settings.dailyDoubleOvertimeEnabled).toBe(true);
      expect(settings.dailyDoubleOvertimeThreshold).toBe(720);
      expect(settings.doubleOvertimeDays).toEqual(WEEK_DAYS);
      expect(settings.overtimeDistribution).toBe("WEIGHTED");
    });
  });
});
