// tests/factories/tests/EarningRateFactory.test.ts

import { describe, it, expect, beforeEach } from "vitest";
import { EarningRateBuilder, createHourlyRates } from "../EarningRateFactory";
import { EARNING_RATE_PERIODS, EARNING_RATE_TYPES } from "@/models/earningrate";
import { OrganizationBuilder } from "../OrganizationFactory";
import { UserBuilder } from "../UserFactory";

describe("UserFactory", () => {
  let organizationId: number;
  let userId: number;

  beforeEach(async () => {
    const org = await new OrganizationBuilder().buildAndCreate();
    const user = await new UserBuilder().withOrganization(org.id).buildAndCreate();

    organizationId = org.id;
    userId = user.id;
  });

  describe("basic rate creation", () => {
    it("creates a regular hourly rate with defaults", async () => {
      const rate = await new EarningRateBuilder()
        .forOrganization(organizationId)
        .forUser(userId) // Add user ID
        .buildAndCreate();

      expect(rate.period).toBe(EARNING_RATE_PERIODS.HOURLY);
      expect(rate.type).toBe(EARNING_RATE_TYPES.REG);
      expect(rate.active).toBe(true);
    });

    it("creates an overtime rate", async () => {
      const rate = await new EarningRateBuilder()
        .forOrganization(organizationId)
        .forUser(userId) // Add user ID
        .asOvertimeRate(25.0)
        .buildAndCreate();

      expect(rate.type).toBe(EARNING_RATE_TYPES.OT);
      expect(rate.amount).toBe("37.50");
    });
  });

  describe("createHourlyRates", () => {
    it("creates both regular and overtime rates", async () => {
      const rates = await createHourlyRates(organizationId, userId, 30.0);

      expect(rates).toHaveLength(2);
      const [regular, overtime] = rates;

      expect(regular.amount).toBe("30.00");
      expect(regular.type).toBe(EARNING_RATE_TYPES.REG);

      expect(overtime.amount).toBe("45.00");
      expect(overtime.type).toBe(EARNING_RATE_TYPES.OT);
    });
  });
});
