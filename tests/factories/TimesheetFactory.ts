import TimeSheet from "@/models/timesheet";
import sequelize from "@/lib/sequelize";

interface TimesheetSchedule {
  clockInTime?: string; // Format: "HH:mm", e.g. "09:00"
  clockOutTime?: string; // Format: "HH:mm", e.g. "17:00"
  startDate: Date; // First day
  numberOfDays: number; // How many days to create timesheets for
  skipWeekends?: boolean; // Optional: skip Saturday/Sunday
}

export class TimesheetBuilder {
  private timesheet: any;

  constructor() {
    this.timesheet = {
      workerId: null, // required
      organizationId: null, // required
      projectId: null, // required
      regEarningRateId: null, // required
      otEarningRateId: null, // required
      updatedAt: new Date(),
      clockIn: new Date(),
      clockOut: null,
      description: "Just a timesheet description",
      isManual: false,
      costCodeId: null, // not required
      isDeleted: false,
      createdBy: null, // required - existing user or admin user
      editedBy: null, // required - existing user or admin user
      breakDuration: 0,
      status: "CLOCKED_IN",
      hideGeofenceWarning: false,
      clockedOutBySwitch: false,
      userClassificationId: null, // required if project is prevailing wage
      checkPayrollId: null,
      workersCompCodeId: null,
      userLocations: [],
    };
  }

  withOrganization(organizationId: number): TimesheetBuilder {
    this.timesheet.organizationId = organizationId;

    return this;
  }

  withCostCode(costCodeId: number): TimesheetBuilder {
    this.timesheet.costCodeId = costCodeId;

    return this;
  }

  withDescription(description: string): TimesheetBuilder {
    this.timesheet.description = description;

    return this;
  }

  withEarningRates(regEarningRateId: number, otEarningRateId: number): TimesheetBuilder {
    this.timesheet.regEarningRateId = regEarningRateId;
    this.timesheet.otEarningRateId = otEarningRateId;

    return this;
  }

  withUserClassification(userClassificationId: number): TimesheetBuilder {
    this.timesheet.userClassificationId = userClassificationId;

    return this;
  }

  withClockInAndOut(clockIn: Date | null = null, clockOut: Date | null = null): TimesheetBuilder {
    this.timesheet.clockIn = clockIn;
    this.timesheet.clockOut = clockOut;

    return this;
  }

  forWorker(userId: number): TimesheetBuilder {
    this.timesheet.workerId = userId;

    return this;
  }

  forProject(projectId: number): TimesheetBuilder {
    this.timesheet.projectId = projectId;

    return this;
  }

  async buildAndCreate(): Promise<TimeSheet> {
    if (!this.timesheet.workerId) {
      throw new Error("Worker ID is required to create a timesheet");
    }

    if (!this.timesheet.organizationId) {
      throw new Error("Organization ID is required to create a timesheet");
    }

    if (!this.timesheet.projectId) {
      throw new Error("Project ID is required to create a timesheet");
    }

    if (!this.timesheet.regEarningRateId) {
      throw new Error("Regular earning rate ID is required to create a timesheet");
    }

    if (!this.timesheet.otEarningRateId) {
      throw new Error("Overtime earning rate ID is required to create a timesheet");
    }

    const result = await sequelize.transaction(async (transaction) => {
      const { id: _id, createdAt: _createdAt, updatedAt: _updatedAt, ...createPayload } = this.timesheet;

      const timesheet = await TimeSheet.create(createPayload, { transaction });

      return timesheet;
    });

    return result;
  }
}

// Factory functions for common timesheet scenarios
export async function createClockedInTimesheet(
  userId: number,
  organizationId: number,
  projectId: number,
  regEarningRateId: number,
  otEarningRateId: number
): Promise<TimeSheet> {
  return new TimesheetBuilder()
    .forWorker(userId)
    .withOrganization(organizationId)
    .forProject(projectId)
    .withEarningRates(regEarningRateId, otEarningRateId)
    .buildAndCreate();
}

export async function createTimesheetSeries(
  userId: number,
  organizationId: number,
  projectId: number,
  regEarningRateId: number,
  otEarningRateId: number,
  schedule: TimesheetSchedule,
  userClassificationId?: number
): Promise<TimeSheet[]> {
  const {
    clockInTime = "08:00", // Default to 8 AM local time
    clockOutTime = "16:00", // Default to 4 PM local time
    startDate,
    numberOfDays,
    skipWeekends = true,
  } = schedule;

  const timesheets: TimeSheet[] = [];

  // Create a new date object in local time
  const currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 12, 0, 0, 0);

  for (let i = 0; i < numberOfDays; ) {
    // Skip weekends if specified
    if (skipWeekends && (currentDate.getDay() === 0 || currentDate.getDay() === 6)) {
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    // Create clockIn date
    const clockIn = new Date(currentDate.getTime());
    const [inHours, inMinutes] = clockInTime.split(":");
    clockIn.setHours(parseInt(inHours, 10), parseInt(inMinutes, 10), 0, 0);

    // Create clockOut date
    const clockOut = new Date(currentDate.getTime());
    const [outHours, outMinutes] = clockOutTime.split(":");
    clockOut.setHours(parseInt(outHours, 10), parseInt(outMinutes, 10), 0, 0);

    const timesheet = await new TimesheetBuilder()
      .forWorker(userId)
      .withOrganization(organizationId)
      .forProject(projectId)
      .withEarningRates(regEarningRateId, otEarningRateId)
      .withClockInAndOut(clockIn, clockOut)
      .buildAndCreate();

    if (userClassificationId) {
      timesheet.userClassificationId = userClassificationId;
    }

    timesheets.push(timesheet);

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
    i++;
  }

  return timesheets;
}
