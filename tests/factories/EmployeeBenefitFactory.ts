import EmployeeBenefit from "@/models/employeebenefit";
import sequelize from "@/lib/sequelize";

export class EmployeeBenefitBuilder {
  private employeeBenefit: any;

  constructor() {
    this.employeeBenefit = {
      name: "Test Employee Benefit",
      userId: null, // required
      companyBenefitId: null, // required
      employeeContributionPercent: 0,
      companyContributionPercent: 0,
      employeeContributionAmount: 0,
      companyContributionAmount: 0,
      contributionType: "AMOUNT", // "AMOUNT", "PERCENT", or "DYNAMIC"
      active: true,
      fringeHourlyContribution: 0, // calculated field
      employeeHourlyContribution: 0, // calculated field
    };
  }

  withName(name: string): EmployeeBenefitBuilder {
    this.employeeBenefit.name = name;

    return this;
  }

  forUser(userId: number): EmployeeBenefitBuilder {
    this.employeeBenefit.userId = userId;

    return this;
  }

  withCompanyBenefit(companyBenefitId: number): EmployeeBenefitBuilder {
    this.employeeBenefit.companyBenefitId = companyBenefitId;

    return this;
  }

  withContributionType(type: "AMOUNT" | "PERCENT" | "DYNAMIC"): EmployeeBenefitBuilder {
    this.employeeBenefit.contributionType = type;

    return this;
  }

  withEmployeeContribution(percent: number, amount: number): EmployeeBenefitBuilder {
    this.employeeBenefit.employeeContributionPercent = percent;
    this.employeeBenefit.employeeContributionAmount = amount;

    return this;
  }

  withCompanyContribution(percent: number, amount: number): EmployeeBenefitBuilder {
    this.employeeBenefit.companyContributionPercent = percent;
    this.employeeBenefit.companyContributionAmount = amount;

    return this;
  }

  withActive(active: boolean): EmployeeBenefitBuilder {
    this.employeeBenefit.active = active;

    return this;
  }

  async buildAndCreate(): Promise<EmployeeBenefit> {
    if (!this.employeeBenefit.userId) {
      throw new Error("User ID is required to create an employee benefit");
    }

    if (!this.employeeBenefit.companyBenefitId) {
      throw new Error("Company benefit ID is required to create an employee benefit");
    }

    const result = await sequelize.transaction(async (transaction) => {
      const { id: _id, ...createPayload } = this.employeeBenefit;
      const benefit = await EmployeeBenefit.create(createPayload, { transaction });

      return benefit;
    });

    return result;
  }
}

// Factory functions for common employee benefit scenarios
export async function createAmountBenefit(
  userId: number,
  companyBenefitId: number,
  employeeAmount: number,
  companyAmount: number
): Promise<EmployeeBenefit> {
  return new EmployeeBenefitBuilder()
    .forUser(userId)
    .withCompanyBenefit(companyBenefitId)
    .withName("Test Amount Benefit")
    .withContributionType("AMOUNT")
    .withEmployeeContribution(0, employeeAmount)
    .withCompanyContribution(0, companyAmount)
    .buildAndCreate();
}

export async function createPercentBenefit(
  userId: number,
  companyBenefitId: number,
  employeePercent: number,
  companyPercent: number
): Promise<EmployeeBenefit> {
  return new EmployeeBenefitBuilder()
    .forUser(userId)
    .withCompanyBenefit(companyBenefitId)
    .withName("Test Percent Benefit")
    .withContributionType("PERCENT")
    .withEmployeeContribution(employeePercent, 0)
    .withCompanyContribution(companyPercent, 0)
    .buildAndCreate();
}

export async function createDynamicBenefit(
  userId: number,
  companyBenefitId: number,
  employeePercent: number
): Promise<EmployeeBenefit> {
  return new EmployeeBenefitBuilder()
    .forUser(userId)
    .withCompanyBenefit(companyBenefitId)
    .withName("Test Dynamic Benefit")
    .withContributionType("DYNAMIC")
    .withEmployeeContribution(employeePercent, 0)
    .withCompanyContribution(0, 0)
    .buildAndCreate();
}
