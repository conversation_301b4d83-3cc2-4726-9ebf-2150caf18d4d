import CostCode from "@/models/costcode";
import sequelize from "@/lib/sequelize";
export class CostCodeBuilder {
  private costCode: any;

  constructor() {
    this.costCode = {
      number: `CC${Math.floor(Math.random() * 10000)}`,
      name: "Test Cost Code",
      organizationId: null, // required
      workersCompCodeId: null, // optional
    };
  }

  withOrganization(organizationId: number): CostCodeBuilder {
    this.costCode.organizationId = organizationId;

    return this;
  }

  withNumber(number: string): CostCodeBuilder {
    this.costCode.number = number;

    return this;
  }

  withName(name: string): CostCodeBuilder {
    this.costCode.name = name;

    return this;
  }

  build(): CostCode {
    return { ...this.costCode };
  }

  async buildAndCreate(): Promise<CostCode> {
    if (!this.costCode.organizationId) {
      throw new Error("Organization ID is required");
    }

    const result = await sequelize.transaction(async (transaction) => {
      // Create the costcode
      const costCode = await CostCode.create(this.costCode, {
        transaction,
        hooks: false, // Disable afterCreate hooks to prevent double creation
      });

      // Reload
      return costCode.reload({
        transaction,
        include: { all: true },
      });
    });

    return result;
  }
}

// Factory functions for common scenarios
export function createBasicCostCode(organizationId: number): CostCode {
  return new CostCodeBuilder().withOrganization(organizationId).build();
}
