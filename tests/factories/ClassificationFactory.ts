import { Classification } from "@/models";
import { generateRandomString } from "../util/utils";

export class ClassificationBuilder {
  private classification: any;

  constructor() {
    const now = new Date();

    this.classification = {
      name: "Test Classification",
      basePay: "50.00",
      fringePay: "10.00",
      startDate: now,
      endDate: null,
      checkRegEarningCodeId: generateRandomString(),
      checkOtEarningCodeId: generateRandomString(),
      wageTableId: null, // required
      organizationId: null, // required
    };
  }

  withOrganization(organizationId: number): ClassificationBuilder {
    this.classification.organizationId = organizationId;

    return this;
  }

  withWageTable(wageTableId: number): ClassificationBuilder {
    this.classification.wageTableId = wageTableId;

    return this;
  }

  withAllocatedPay(basePay: string, fringePay: string): ClassificationBuilder {
    this.classification.basePay = basePay;
    this.classification.fringePay = fringePay;

    return this;
  }

  withEarningCodes(checkRegEarningCodeId: string, checkOtEarningCodeId: string): ClassificationBuilder {
    this.classification.checkRegEarningCodeId = checkRegEarningCodeId;
    this.classification.checkOtEarningCodeId = checkOtEarningCodeId;

    return this;
  }

  async buildAndCreate(): Promise<Classification> {
    if (!this.classification.organizationId) {
      throw new Error("Organization ID is required to create a classification");
    }

    return Classification.create(this.classification);
  }
}

// Factory functions for common scenarios
export async function createClassification(organizationId: number, wageTableId: number): Promise<Classification> {
  return new ClassificationBuilder().withOrganization(organizationId).withWageTable(wageTableId).buildAndCreate();
}

export async function createClassificationWithEarningCodes(
  organizationId: number,
  wageTableId: number,
  checkRegEarningCodeId: string,
  checkOtEarningCodeId: string
): Promise<Classification> {
  return new ClassificationBuilder()
    .withOrganization(organizationId)
    .withWageTable(wageTableId)
    .withEarningCodes(checkRegEarningCodeId, checkOtEarningCodeId)
    .buildAndCreate();
}
