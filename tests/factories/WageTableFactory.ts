import { WageTable } from "@/models";

export class WageTableBuilder {
  private wageTable: any;

  constructor() {
    this.wageTable = {
      name: "Test Wage Table",
      description: "Test Description",
      organizationId: null, // required
    };
  }

  withName(name: string): WageTableBuilder {
    this.wageTable.name = name;

    return this;
  }

  withDescription(description: string): WageTableBuilder {
    this.wageTable.description = description;

    return this;
  }

  withOrganization(organizationId: number): WageTableBuilder {
    this.wageTable.organizationId = organizationId;

    return this;
  }

  async buildAndCreate(): Promise<WageTable> {
    if (!this.wageTable.organizationId) {
      throw new Error("Organization ID is required to create a wage table");
    }

    return WageTable.create(this.wageTable);
  }
}

// Factory functions for common scenarios
export async function createWageTable(organizationId: number): Promise<WageTable> {
  return new WageTableBuilder().withOrganization(organizationId).buildAndCreate();
}
