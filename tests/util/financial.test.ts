import { describe, it, expect, beforeEach } from "vitest";
import dayjs from "dayjs";
import { refinedAttributedWeeklyTimesheets } from "@/util/financial";
import { ExtendedTimesheet } from "@/models/timesheet";
import Organization from "@/models/organization";
import { WEEK_DAYS } from "@/models/overtimesettings";
import { createBasicOrganization } from "../factories/OrganizationFactory";
import { createHourlyWorker } from "../factories/UserFactory";
import { createBasicProject } from "../factories/ProjectFactory";
import EarningRate from "@/models/earningrate";
import { createTimesheetSeries } from "../factories/TimesheetFactory";
import { createFullFeaturedOvertimeSequentialSettings } from "../factories/OvertimeSettingsFactory";

describe("refinedAttributedWeeklyTimesheets", () => {
  let organization: Organization;
  let user: any;
  let project: any;
  let regEarningRate: any;
  let otEarningRate: any;

  beforeEach(async () => {
    // Create a basic organization
    organization = await createBasicOrganization();

    // Create overtime settings with ONLY weekly overtime enabled
    await createFullFeaturedOvertimeSequentialSettings(organization.id);

    // Create a user with hourly rates
    user = await createHourlyWorker(organization.id, 25.0);

    // Ensure user is saved to the database before proceeding
    await user.reload({
      include: [
        {
          model: EarningRate,
          as: "earningRates",
        },
      ],
    });

    // Create a project
    project = await createBasicProject(organization.id);

    // Get the user's earning rates that were already created by the UserFactory
    regEarningRate = user.earningRates.find((rate: any) => rate.type === "REG");
    otEarningRate = user.earningRates.find((rate: any) => rate.type === "OT");

    if (!regEarningRate || !otEarningRate) {
      throw new Error("Failed to find earning rates for user");
    }
  });

  describe("Basic overtime scenarios", () => {
    it("should correctly attribute regular hours for a standard 8-hour day", async () => {
      // Create a timesheet for a standard 8-hour day
      const startDate = new Date();
      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "16:00",
          startDate,
          numberOfDays: 1,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 480, // 8 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      expect(extendedTimesheets[0].regularMinutes).to.equal(480);
      expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].regularWages).to.equal(200); // 8 hours * $25
      expect(extendedTimesheets[0].overtimeWages).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeWages).to.equal(0);
    });

    it("should correctly attribute overtime hours for a 10-hour day (daily overtime disabled)", async () => {
      // Create a timesheet for a 10-hour day
      const startDate = new Date();
      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "18:00",
          startDate,
          numberOfDays: 1,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 600, // 10 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
            ...project,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      expect(extendedTimesheets[0].regularMinutes).to.equal(600); // 810 hours
      expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].regularWages).to.equal(250); // 10 hours * $25
      expect(extendedTimesheets[0].overtimeWages).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeWages).to.equal(0);
    });

    it.skip("should correctly attribute double overtime hours for a 14-hour day", async () => {
      // Create a timesheet for a 14-hour day
      const startDate = new Date();
      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "22:00",
          startDate,
          numberOfDays: 1,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 840, // 14 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      expect(extendedTimesheets[0].regularMinutes).to.equal(840); // 14 hours
      expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].regularWages).to.equal(350); // 14 hours * $25
      expect(extendedTimesheets[0].overtimeWages).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeWages).to.equal(0);
    });
  });

  describe("Weekly overtime scenarios", () => {
    it("should correctly attribute weekly overtime across multiple days", async () => {
      // Create timesheets for 5 days, 9 hours each (45 hours total)
      const startDate = new Date();
      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "17:00",
          startDate,
          numberOfDays: 5,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 540, // 9 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      // First 4 days should be all regular hours (8 hours each)
      for (let i = 0; i < 4; i++) {
        expect(extendedTimesheets[i].regularMinutes).to.equal(540); // 9 hours
        expect(extendedTimesheets[i].overtimeMinutes).to.equal(0); // 0 hours ot
        expect(extendedTimesheets[i].doubleOvertimeMinutes).to.equal(0);
        expect(extendedTimesheets[i].regularWages).to.equal(225); // 9 hours * $25
        expect(extendedTimesheets[i].overtimeWages).to.equal(0); // 0 hours ot
        expect(extendedTimesheets[i].doubleOvertimeWages).to.equal(0);
      }

      // Last day should have some overtime attributed to weekly overtime
      expect(extendedTimesheets[4].regularMinutes).to.equal(240); // 4 hours
      expect(extendedTimesheets[4].overtimeMinutes).to.equal(300); // 5 hours
      expect(extendedTimesheets[4].doubleOvertimeMinutes).to.equal(0);
      expect(extendedTimesheets[4].regularWages).to.equal(100); // 4 hours * $25
      expect(extendedTimesheets[4].overtimeWages).to.equal(187.5); // 5 hours * $37.5
      expect(extendedTimesheets[4].doubleOvertimeWages).to.equal(0);
    });

    it("should correctly attribute weekly overtime when exceeding weekly threshold", async () => {
      // Create timesheets for 5 days, 10 hours each (50 hours total)
      const startDate = new Date();
      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "18:00",
          startDate,
          numberOfDays: 5,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 600, // 10 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      // First 4 days should be all regular hours (8 hours each) and daily overtime (2 hours each)
      for (let i = 0; i < 4; i++) {
        expect(extendedTimesheets[i].regularMinutes).to.equal(480); // 8 hours
        expect(extendedTimesheets[i].overtimeMinutes).to.equal(120); // 2 hours
        expect(extendedTimesheets[i].doubleOvertimeMinutes).to.equal(0);
        expect(extendedTimesheets[i].regularWages).to.equal(200); // 8 hours * $25
        expect(extendedTimesheets[i].overtimeWages).to.equal(75); // 2 hours * $37.50
        expect(extendedTimesheets[i].doubleOvertimeWages).to.equal(0);
      }

      // Last day should have some overtime attributed to weekly overtime
      // Weekly threshold is 40 hours (2400 minutes), so we've exceeded it by 600 minutes
      // The last day should have 8 hours regular, 2 hours overtime
      expect(extendedTimesheets[4].regularMinutes).to.equal(480); // 8 hours
      expect(extendedTimesheets[4].overtimeMinutes).to.equal(120); // 2 hours
      expect(extendedTimesheets[4].doubleOvertimeMinutes).to.equal(0);
      expect(extendedTimesheets[4].regularWages).to.equal(200); // 8 hours * $25
      expect(extendedTimesheets[4].overtimeWages).to.equal(75); // 2 hours * $37.50
      expect(extendedTimesheets[4].doubleOvertimeWages).to.equal(0);
    });
  });

  describe("Override day scenarios", () => {
    it("should correctly attribute all hours as overtime on an overtime override day", async () => {
      // Create a timesheet for a day that is an overtime override day
      const startDate = new Date();

      // Set the day of week to be an overtime override day
      const dayOfWeek = dayjs(startDate).day();
      const dayName = WEEK_DAYS[dayOfWeek];

      // Update organization's overtime settings to include this day as an overtime day
      organization.overtimeSettings.overtimeDays = [dayName];

      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "16:00",
          startDate,
          numberOfDays: 1,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 480, // 8 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      expect(extendedTimesheets[0].regularMinutes).to.equal(0);
      expect(extendedTimesheets[0].overtimeMinutes).to.equal(480); // All 8 hours as overtime
      expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].regularWages).to.equal(0);
      expect(extendedTimesheets[0].overtimeWages).to.equal(300); // 8 hours * $37.50
      expect(extendedTimesheets[0].doubleOvertimeWages).to.equal(0);
    });

    it.skip("should correctly attribute all hours as double overtime on a double overtime override day", async () => {
      // Create a timesheet for a day that is a double overtime override day
      const startDate = new Date();

      // Set the day of week to be a double overtime override day
      const dayOfWeek = dayjs(startDate).day();
      const dayName = WEEK_DAYS[dayOfWeek];

      // Update organization's overtime settings to include this day as a double overtime day
      organization.overtimeSettings.doubleOvertimeDays = [dayName];

      const timesheets = await createTimesheetSeries(
        user.id,
        organization.id,
        project.id,
        regEarningRate.id,
        otEarningRate.id,
        {
          clockInTime: "08:00",
          clockOutTime: "16:00",
          startDate,
          numberOfDays: 1,
        }
      );

      // Convert to ExtendedTimesheet format
      const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
        const plainTs = ts.get({ plain: true });

        return {
          ...plainTs,
          totalMinutes: 480, // 8 hours * 60 minutes
          regEarningRate: {
            id: regEarningRate.id,
            amount: regEarningRate.amount,
            period: regEarningRate.period,
          },
          otEarningRate: {
            id: otEarningRate.id,
            amount: otEarningRate.amount,
            period: otEarningRate.period,
          },
          project: {
            id: project.id,
            name: project.name,
            isPrevailingWage: false,
          },
        };
      });

      // Run the function
      refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

      // Check the results
      expect(extendedTimesheets[0].regularMinutes).to.equal(0);
      expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(480); // All 8 hours as double overtime
      expect(extendedTimesheets[0].regularWages).to.equal(0);
      expect(extendedTimesheets[0].overtimeWages).to.equal(0);
      expect(extendedTimesheets[0].doubleOvertimeWages).to.equal(400); // 8 hours * $50
    });
  });
});
