import { beforeAll } from "vitest";
import { successfullyConnectedToPostgress } from "@/lib/sequelize";
import { createRelations } from "@/models/relations";
import Organization from "@/models/organization";
import PaySchedule from "@/models/payschedule";
import TimeTrackingSettings from "@/models/timetrackingsettings";
import OvertimeSettings from "@/models/overtimesettings";
import PrevailingWageSettings from "@/models/prevailingwagesettings";
import { FeatureFlags } from "@/models";

function ensureDefaultDataForTests() {
  Organization.afterCreate(async (organization) => {
    await Promise.all([
      TimeTrackingSettings.create({
        organizationId: organization.id,
        allowWorkersToAddEditTime: true,
        areBreaksPaid: true,
        areRealtimeBreaksEnabled: true,
        areRealtimeBreakRemindersEnabled: true,
        useDecimalHours: true,
        breakOptions: [0, 15, 30, 60],
        locationBreadcrumbingEnabled: true,
      }),
      OvertimeSettings.create({
        organizationId: organization.id,
        weeklyOvertimeEnabled: false,
        dailyOvertimeEnabled: false,
        dailyOvertimeThreshold: 480,
        weeklyOvertimeThreshold: 2400,
        overtimeMultiplier: 1.5,
        name: "default",
        isDefault: true,
      }),
      PrevailingWageSettings.create({
        organizationId: organization.id,
        allowCustomPrevailingWagePerEmployee: false,
      }),

      PaySchedule.create({
        payFrequency: "weekly",
        checkPayScheduleId: "",
        organizationId: organization.id,
        firstPayday: "2024-06-06",
        isActive: true,
      }),
      FeatureFlags.create({
        isPayrollEnabled: true,
        isSchedulingEnabled: true,
        isMessagingEnabled: true,
        organizationId: organization.id,
      }),
    ]);
  });
}

beforeAll(async () => {
  await successfullyConnectedToPostgress();
  createRelations();
  ensureDefaultDataForTests();
});
