import { describe, it, expect, beforeEach } from "vitest";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import { refinedAttributedWeeklyTimesheets } from "@/util/financial";
import { ExtendedTimesheet } from "@/models/timesheet";
import Organization from "@/models/organization";
import EarningRate from "@/models/earningrate";
import CompanyBenefit from "@/models/companybenefit";
import EmployeeBenefit from "@/models/employeebenefit";

// Import factories with correct names
import { createBasicOrganization } from "../factories/OrganizationFactory";
import { createHourlyWorker } from "../factories/UserFactory";
import { createBasicProject, createPrevailingWageProject } from "../factories/ProjectFactory";
import { CostCodeBuilder } from "../factories/CostCodeFactory";
import { ClassificationBuilder } from "../factories/ClassificationFactory";
import { UserClassificationFactory } from "../factories/UserClassificationFactory";
import { createTimesheetSeries } from "../factories/TimesheetFactory";
import { WageTableBuilder } from "../factories/WageTableFactory";
import { createHealthBenefit, createDynamicFringeBenefit } from "../factories/CompanyBenefitFactory";
import { createAmountBenefit, createDynamicBenefit } from "../factories/EmployeeBenefitFactory";
import { UserClassification } from "@/models/index";

dayjs.extend(utc);
dayjs.extend(timezone);

// Test case for prevailing wage worker with dynamic fringe benefits
describe("Timesheet Calculations with Prevailing Wage and Benefits", () => {
  let organization: Organization;
  let user: any;
  let pwProject: any;
  let regProject: any;
  let regEarningRate: any;
  let otEarningRate: any;
  let costCode: any;
  let classification: any;
  let wageTable: any;
  let healthBenefit: CompanyBenefit;
  let dynamicFringeBenefit: CompanyBenefit;
  let employeeHealthBenefit: EmployeeBenefit;
  let employeeDynamicBenefit: EmployeeBenefit;
  let userClassification: UserClassification;

  beforeEach(async () => {
    // Create organization with Pacific timezone and daily/weekly overtime
    organization = await createBasicOrganization();

    // Configure settings
    organization.timezone = "America/Los_Angeles";
    organization.isPayrollEnabled = true;

    // Configure organization settings - update properties instead of replacing
    organization.timeTrackingSettings.areBreaksPaid = true;
    organization.timeTrackingSettings.areRealtimeBreaksEnabled = true;
    organization.timeTrackingSettings.breakOptions = [15, 30, 45, 60];

    // Update overtime settings properties individually
    organization.overtimeSettings.dailyOvertimeEnabled = true;
    organization.overtimeSettings.weeklyOvertimeEnabled = true;
    organization.overtimeSettings.dailyDoubleOvertimeEnabled = true;
    organization.overtimeSettings.overtimeDays = ["SATURDAY"];
    organization.overtimeSettings.doubleOvertimeDays = ["SUNDAY"];
    organization.overtimeSettings.dailyOvertimeThreshold = 8 * 60;
    organization.overtimeSettings.weeklyOvertimeThreshold = 40 * 60;
    organization.overtimeSettings.dailyDoubleOvertimeThreshold = 12 * 60;
    organization.overtimeSettings.overtimeMultiplier = 1.5;
    // Comment out the doubleOvertimeMultiplier if it's not a direct property
    // If needed, this property would be handled by the model internally
    // organization.overtimeSettings.set('doubleOvertimeMultiplier', 2.0);

    // Update prevailing wage settings
    organization.prevailingWageSettings.overridePwIfBelowRegularRate = false;

    // Instead of setting payScheduleSettings directly, set it on the organization model
    // if the property doesn't exist directly
    organization.set("payScheduleSettings", {
      type: "weekly",
      firstPayDay: new Date("2024-01-11"),
      firstPeriodEnd: new Date("2024-01-07"),
      isActive: true,
      checkPayScheduleId: "test-pay-schedule-id",
    });

    // Create wage table and classification
    wageTable = await new WageTableBuilder()
      .withOrganization(organization.id)
      .withName("Test Prevailing Wage Table")
      .withDescription("Test wage table for testing")
      .buildAndCreate();

    // Use ClassificationBuilder instead of createClassification
    classification = await new ClassificationBuilder()
      .withOrganization(organization.id)
      .withWageTable(wageTable.id)
      .withAllocatedPay("35.00", "25.00")
      .buildAndCreate();

    // Create a user
    user = await createHourlyWorker(organization.id, 25.0);

    // Create user classification with UserClassificationFactory
    userClassification = await new UserClassificationFactory()
      .withOrganization(organization.id)
      .withWageTable(wageTable.id)
      .withClassification(classification.id)
      .withUser(user.id)
      .buildAndCreate();

    // Create projects
    pwProject = await createPrevailingWageProject(organization.id, wageTable.id);
    regProject = await createBasicProject(organization.id);

    // Create cost code with CostCodeBuilder
    costCode = await new CostCodeBuilder().withOrganization(organization.id).withName("TestCostCode").buildAndCreate();

    // Get user's earning rates
    await user.reload({
      include: [{ model: EarningRate, as: "earningRates" }],
    });

    regEarningRate = user.earningRates.find((rate: any) => rate.type === "REG");
    otEarningRate = user.earningRates.find((rate: any) => rate.type === "OT");

    if (!regEarningRate || !otEarningRate) {
      throw new Error("Failed to find earning rates for user");
    }

    // Create benefits
    healthBenefit = await createHealthBenefit(organization.id);
    dynamicFringeBenefit = await createDynamicFringeBenefit(organization.id);

    // Create employee benefits
    employeeHealthBenefit = await createAmountBenefit(
      user.id,
      healthBenefit.id,
      5.0, // Employee contributes $5
      10.0 // Company contributes $10
    );

    employeeDynamicBenefit = await createDynamicBenefit(
      user.id,
      dynamicFringeBenefit.id,
      15.0 // Employee contributes 15%
    );
  });

  it("should calculate prevailing wage rates correctly with dynamic benefits", async () => {
    // Create a timesheet with 10 hours on a regular day for prevailing wage project
    const startDate = new Date();

    console.log(JSON.stringify(organization, null, 2), "this is the organization");

    // Add costCodeId after creating the timesheet
    const pwTimesheets = await createTimesheetSeries(
      user.id,
      organization.id,
      pwProject.id,
      regEarningRate.id,
      otEarningRate.id,
      {
        clockInTime: "08:00",
        clockOutTime: "18:00", // 10 hours
        startDate,
        numberOfDays: 1,
      },
      userClassification.id
    );

    console.log(pwTimesheets, "this is the pw timesheets");

    // Set costCodeId directly on the timesheet after creation
    await pwTimesheets[0].update({ costCodeId: costCode.id });

    // Convert to ExtendedTimesheet format with all needed properties
    const extendedPwTimesheets: ExtendedTimesheet[] = pwTimesheets.map((ts) => {
      const plainTs = ts.get({ plain: true });

      return {
        ...plainTs,
        totalMinutes: 600, // 10 hours
        costCodeId: costCode.id,
        regEarningRate: {
          id: regEarningRate.id,
          amount: regEarningRate.amount,
          period: regEarningRate.period,
        },
        otEarningRate: {
          id: otEarningRate.id,
          amount: otEarningRate.amount,
          period: otEarningRate.period,
        },
        project: {
          id: pwProject.id,
          name: pwProject.name,
          isPrevailingWage: true,
        },
        userClassification: {
          id: classification.id,
          basePay: "35.00",
          fringePay: "25.00",
        },
        fringeBenefitClassifications: [], // No fringe benefit classifications for this test
        user: {
          id: user.id,
          employeeBenefits: [
            {
              id: employeeHealthBenefit.id,
              contributionType: "AMOUNT",
              employeeContributionAmount: 5.0,
              companyContributionAmount: 10.0,
              fringeHourlyContribution: 10.0 / 40, // $10 per week / 40 hours
              companyBenefit: healthBenefit,
            },
            {
              id: employeeDynamicBenefit.id,
              contributionType: "DYNAMIC",
              employeeContributionPercent: 15.0,
              companyContributionPercent: 0,
              fringeHourlyContribution: 0, // Will be calculated
              companyBenefit: dynamicFringeBenefit,
            },
          ],
        },
      };
    });

    // Run the calculation function
    refinedAttributedWeeklyTimesheets(extendedPwTimesheets, organization);

    // Calculate expected values
    // For PW project with 10 hours:
    // - 8 hours regular, 2 hours overtime
    // - Base pay = $35/hr, Fringe pay = $25/hr
    // - Should use dynamic fringe for the $25 fringe since the user has a DYNAMIC benefit

    // Check PW timesheet calculations
    const pwTimesheet = extendedPwTimesheets[0];

    // Should be 8 hours regular, 2 hours overtime
    expect(pwTimesheet.regularMinutes).to.equal(480); // 8 hours
    expect(pwTimesheet.overtimeMinutes).to.equal(120); // 2 hours

    // Hourly wage should be basePay + cashFringeRate (which is 0 because of dynamic fringe)
    expect(pwTimesheet.hourlyWage).to.equal(35); // BasePay only since fringe is dynamic

    // OT hourly wage should be basePay * 1.5 + cashFringeRate
    expect(pwTimesheet.otHourlyWage).to.equal(35 * 1.5); // No cash fringe with dynamic benefits

    // Should have dynamic fringe contribution rate
    expect(pwTimesheet.companyDynamicFringeContributionRate).to.equal(25); // Fringe pay amount

    // Should have employee dynamic fringe contribution percent
    expect(pwTimesheet.employeeDynamicFringeContributionPerecent).to.equal(15);

    // Regular wages = regularMinutes / 60 * hourlyWage
    expect(pwTimesheet.regularWages).to.be.closeTo((480 / 60) * 35, 0.01);

    // Overtime wages = overtimeMinutes / 60 * otHourlyWage
    expect(pwTimesheet.overtimeWages).to.be.closeTo((120 / 60) * (35 * 1.5), 0.01);

    // Company dynamic fringe allocation = (regularMinutes + overtimeMinutes) / 60 * companyDynamicFringeContributionRate
    expect(pwTimesheet.companyDynamicFringeAllocation).to.be.closeTo((600 / 60) * 25, 0.01);

    // Employee dynamic fringe allocation = (regularMinutes + overtimeMinutes) / 60 * hourlyWage * (employeeDynamicFringeContributionPerecent / 100)
    expect(pwTimesheet.employeeDynamicFringeAllocation).to.be.closeTo((600 / 60) * 35 * 0.15, 0.01);
  });

  it("should calculate regular wages correctly on a non-PW project", async () => {
    // Create a timesheet with 10 hours on a regular day for non-PW project
    const startDate = new Date();
    const regTimesheets = await createTimesheetSeries(
      user.id,
      organization.id,
      regProject.id,
      regEarningRate.id,
      otEarningRate.id,
      {
        clockInTime: "08:00",
        clockOutTime: "18:00", // 10 hours
        startDate,
        numberOfDays: 1,
      }
    );

    // Convert to ExtendedTimesheet format
    const extendedRegTimesheets: ExtendedTimesheet[] = regTimesheets.map((ts) => {
      const plainTs = ts.get({ plain: true });

      return {
        ...plainTs,
        totalMinutes: 600, // 10 hours
        regEarningRate: {
          id: regEarningRate.id,
          amount: regEarningRate.amount,
          period: regEarningRate.period,
        },
        otEarningRate: {
          id: otEarningRate.id,
          amount: otEarningRate.amount,
          period: otEarningRate.period,
        },
        project: {
          id: regProject.id,
          name: regProject.name,
          isPrevailingWage: false,
        },
      };
    });

    // Run the calculation function
    refinedAttributedWeeklyTimesheets(extendedRegTimesheets, organization);

    // For regular project with 10 hours:
    // - 8 hours regular, 2 hours overtime
    // - Regular pay = $25/hr, OT pay = $25 * 1.5 = $37.50/hr

    // Check regular timesheet calculations
    const regTimesheet = extendedRegTimesheets[0];

    // Should be 8 hours regular, 2 hours overtime
    expect(regTimesheet.regularMinutes).to.equal(480); // 8 hours
    expect(regTimesheet.overtimeMinutes).to.equal(120); // 2 hours

    // Hourly wage should be regular rate
    expect(regTimesheet.hourlyWage).to.equal(25);

    // OT hourly wage should be regular rate * 1.5
    expect(regTimesheet.otHourlyWage).to.equal(25 * 1.5);

    // Regular wages = regularMinutes / 60 * hourlyWage
    expect(regTimesheet.regularWages).to.be.closeTo((480 / 60) * 25, 0.01);

    // Overtime wages = overtimeMinutes / 60 * otHourlyWage
    expect(regTimesheet.overtimeWages).to.be.closeTo((120 / 60) * (25 * 1.5), 0.01);
  });

  it("should handle overtime days correctly", async () => {
    // Find a Saturday (overtime day) for testing
    const saturdayDate = new Date();
    while (saturdayDate.getDay() !== 6) {
      // 6 = Saturday
      saturdayDate.setDate(saturdayDate.getDate() + 1);
    }

    // Create a timesheet for Saturday (8 hours)
    const saturdayTimesheets = await createTimesheetSeries(
      user.id,
      organization.id,
      regProject.id,
      regEarningRate.id,
      otEarningRate.id,
      {
        clockInTime: "08:00",
        clockOutTime: "16:00", // 8 hours
        startDate: saturdayDate,
        numberOfDays: 1,
      }
    );

    // Convert to ExtendedTimesheet format
    const extendedSaturdayTimesheets: ExtendedTimesheet[] = saturdayTimesheets.map((ts) => {
      const plainTs = ts.get({ plain: true });

      return {
        ...plainTs,
        totalMinutes: 480, // 8 hours
        regEarningRate: {
          id: regEarningRate.id,
          amount: regEarningRate.amount,
          period: regEarningRate.period,
        },
        otEarningRate: {
          id: otEarningRate.id,
          amount: otEarningRate.amount,
          period: otEarningRate.period,
        },
        project: {
          id: regProject.id,
          name: regProject.name,
          isPrevailingWage: false,
        },
      };
    });

    // Run the calculation function
    refinedAttributedWeeklyTimesheets(extendedSaturdayTimesheets, organization);

    // For Saturday overtime day:
    // - All 8 hours should be overtime
    // - OT pay = $25 * 1.5 = $37.50/hr

    // Check overtime day calculations
    const saturdayTimesheet = extendedSaturdayTimesheets[0];

    // Should be all overtime
    expect(saturdayTimesheet.regularMinutes).to.equal(0);
    expect(saturdayTimesheet.overtimeMinutes).to.equal(480); // All 8 hours

    // Overtime wages = overtimeMinutes / 60 * otHourlyWage
    expect(saturdayTimesheet.overtimeWages).to.be.closeTo((480 / 60) * (25 * 1.5), 0.01);
    expect(saturdayTimesheet.regularWages).to.equal(0);
  });

  it("should handle weekly overtime correctly across multiple days", async () => {
    // Create 5 days of 9-hour timesheets (45 hours total)
    const startDate = new Date();
    // Make sure we start on a weekday that's not an overtime day
    while (startDate.getDay() === 0 || startDate.getDay() === 6) {
      // 0 = Sunday, 6 = Saturday
      startDate.setDate(startDate.getDate() + 1);
    }

    const weekTimesheets = await createTimesheetSeries(
      user.id,
      organization.id,
      regProject.id,
      regEarningRate.id,
      otEarningRate.id,
      {
        clockInTime: "08:00",
        clockOutTime: "17:00", // 9 hours
        startDate,
        numberOfDays: 5,
      }
    );

    // Convert to ExtendedTimesheet format
    const extendedWeekTimesheets: ExtendedTimesheet[] = weekTimesheets.map((ts) => {
      const plainTs = ts.get({ plain: true });

      return {
        ...plainTs,
        totalMinutes: 540, // 9 hours
        regEarningRate: {
          id: regEarningRate.id,
          amount: regEarningRate.amount,
          period: regEarningRate.period,
        },
        otEarningRate: {
          id: otEarningRate.id,
          amount: otEarningRate.amount,
          period: otEarningRate.period,
        },
        project: {
          id: regProject.id,
          name: regProject.name,
          isPrevailingWage: false,
        },
      };
    });

    // Run the calculation function
    refinedAttributedWeeklyTimesheets(extendedWeekTimesheets, organization);

    // For 5 days of 9 hours each (45 hours total):
    // - With daily OT: Each day should have 8 hours regular, 1 hour overtime
    // - With weekly OT: Once we hit 40 hours, the rest should be overtime

    // Each day should have daily overtime (1 hour)
    for (const timesheet of extendedWeekTimesheets) {
      expect(timesheet.regularMinutes).to.equal(480); // 8 hours
      expect(timesheet.overtimeMinutes).to.equal(60); // 1 hour

      expect(timesheet.regularWages).to.be.closeTo((480 / 60) * 25, 0.01);
      expect(timesheet.overtimeWages).to.be.closeTo((60 / 60) * (25 * 1.5), 0.01);
    }

    // Calculate total minutes
    const totalRegularMinutes = extendedWeekTimesheets.reduce((sum, ts) => sum + ts.regularMinutes, 0);
    const totalOvertimeMinutes = extendedWeekTimesheets.reduce((sum, ts) => sum + ts.overtimeMinutes, 0);

    // 40 hours regular + 5 hours overtime = 45 hours total
    expect(totalRegularMinutes).to.equal(40 * 60); // 40 hours
    expect(totalOvertimeMinutes).to.equal(5 * 60); // 5 hours
  });
});
