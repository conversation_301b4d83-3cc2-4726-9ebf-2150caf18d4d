import { describe, it, expect, beforeEach } from "vitest";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import { refinedAttributedWeeklyTimesheets } from "@/util/financial";
import { ExtendedTimesheet } from "@/models/timesheet";
import Organization from "@/models/organization";
import { OVERTIME_DISTRIBUTION, WEEK_DAYS } from "@/models/overtimesettings";
import EarningRate from "@/models/earningrate";
import { getDayOfWeekString } from "@/util/dateHelper";

// Import factories with builder classes
import { OrganizationBuilder } from "../factories/OrganizationFactory";
import { createHourlyWorker } from "../factories/UserFactory";
import { createBasicProject, createPrevailingWageProject } from "../factories/ProjectFactory";
import { CostCodeBuilder } from "../factories/CostCodeFactory";
import { ClassificationBuilder } from "../factories/ClassificationFactory";
import { UserClassificationFactory } from "../factories/UserClassificationFactory";
import { createTimesheetSeries } from "../factories/TimesheetFactory";
import { WageTableBuilder } from "../factories/WageTableFactory";
import { createDynamicFringeBenefit } from "../factories/CompanyBenefitFactory";
import { createDynamicBenefit } from "../factories/EmployeeBenefitFactory";

dayjs.extend(utc);
dayjs.extend(timezone);

// Test configuration types
interface PayScheduleConfig {
  type: "weekly" | "biweekly";
  firstPayday: string; // ISO date string
  firstPeriodEnd: string; // ISO date string
}

interface TimeTrackingConfig {
  areBreaksPaid: boolean;
  realTimeBreaksEnabled: boolean;
}

interface OvertimeConfig {
  dailyOvertimeEnabled: boolean;
  weeklyOvertimeEnabled: boolean;
  doubleOvertimeEnabled: boolean;
  overtimeDays: string[]; // e.g., ["THURSDAY", "FRIDAY"]
  doubleOvertimeDays: string[]; // e.g., ["SATURDAY", "SUNDAY"]
  dailyOvertimeThreshold: number; // in hours
  weeklyOvertimeThreshold: number; // in hours
  doubleOvertimeThreshold: number; // in hours
  overtimeMultiplier: number;
  doubleOvertimeMultiplier: number;
  overtimeDistribution: typeof OVERTIME_DISTRIBUTION[number];
}

interface PrevailingWageConfig {
  overridePwIfBelowRegularRate: boolean;
}

interface OrganizationConfig {
  isPayrollEnabled: boolean;
  timezone: string;
}

interface TestConfig {
  name: string;
  paySchedule: PayScheduleConfig;
  timeTracking: TimeTrackingConfig;
  overtime: OvertimeConfig;
  prevailingWage: PrevailingWageConfig;
  org: OrganizationConfig;
  userType: "hourly" | "salary" | "contractor";
  isPrevailingWage: boolean;
}

// Organize test configurations by category
const payScheduleConfigs = {
  weekly: {
    type: "weekly" as const,
    firstPayday: "2024-01-11",
    firstPeriodEnd: "2024-01-07",
  },
  biweekly: {
    type: "biweekly" as const,
    firstPayday: "2024-01-19",
    firstPeriodEnd: "2024-01-16",
  },
};

const timeTrackingConfigs = {
  paidBreaksEnabled: { areBreaksPaid: true, realTimeBreaksEnabled: true },
  paidBreaksDisabled: { areBreaksPaid: true, realTimeBreaksEnabled: false },
  unpaidBreaksEnabled: { areBreaksPaid: false, realTimeBreaksEnabled: true },
  unpaidBreaksDisabled: { areBreaksPaid: false, realTimeBreaksEnabled: false },
};

const overtimeConfigs = {
  noOvertimeSequential: {
    dailyOvertimeEnabled: false,
    weeklyOvertimeEnabled: false,
    doubleOvertimeEnabled: false,
    overtimeDays: [] as string[],
    doubleOvertimeDays: [] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
  dailyOnlySequential: {
    dailyOvertimeEnabled: true,
    weeklyOvertimeEnabled: false,
    doubleOvertimeEnabled: false,
    overtimeDays: [] as string[],
    doubleOvertimeDays: [] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
  weeklyOnlySequential: {
    dailyOvertimeEnabled: false,
    weeklyOvertimeEnabled: true,
    doubleOvertimeEnabled: false,
    overtimeDays: [] as string[],
    doubleOvertimeDays: [] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
  dailyAndWeeklySequential: {
    dailyOvertimeEnabled: true,
    weeklyOvertimeEnabled: true,
    doubleOvertimeEnabled: false,
    overtimeDays: [] as string[],
    doubleOvertimeDays: [] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
  withSpecialDays: {
    dailyOvertimeEnabled: true,
    weeklyOvertimeEnabled: true,
    doubleOvertimeEnabled: true,
    overtimeDays: ["THURSDAY", "FRIDAY"] as string[],
    doubleOvertimeDays: ["SATURDAY", "SUNDAY"] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
  doubleOnlySequential: {
    dailyOvertimeEnabled: false,
    weeklyOvertimeEnabled: false,
    doubleOvertimeEnabled: true,
    overtimeDays: [] as string[],
    doubleOvertimeDays: [] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
  dailyWeeklyAndDoubleSequential: {
    dailyOvertimeEnabled: true,
    weeklyOvertimeEnabled: true,
    doubleOvertimeEnabled: true,
    overtimeDays: [] as string[],
    doubleOvertimeDays: [] as string[],
    dailyOvertimeThreshold: 8,
    weeklyOvertimeThreshold: 40,
    doubleOvertimeThreshold: 12,
    overtimeMultiplier: 1.5,
    doubleOvertimeMultiplier: 2.0,
    overtimeDistribution: "SEQUENTIAL",
  },
};

// Define test cases using the categorized configs
const testConfigurations: TestConfig[] = [
  // Base case: hourly worker, standard settings
  {
    name: "Standard hourly worker with daily/weekly OT (Sequential)",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.dailyAndWeeklySequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: true,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: false,
  },

  // Prevailing wage with special days
  {
    name: "Prevailing wage with special OT days (Sequential)",
    paySchedule: payScheduleConfigs.biweekly,
    timeTracking: timeTrackingConfigs.unpaidBreaksDisabled,
    overtime: overtimeConfigs.withSpecialDays,
    prevailingWage: {
      overridePwIfBelowRegularRate: false,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/New_York",
    },
    userType: "hourly",
    isPrevailingWage: true,
  },

  // Weekly only overtime
  {
    name: "Worker with weekly OT only (Sequential)",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.weeklyOnlySequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: true,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: false,
  },

  // No overtime at all
  {
    name: "Worker with no overtime (Sequential)",
    paySchedule: payScheduleConfigs.biweekly,
    timeTracking: timeTrackingConfigs.paidBreaksDisabled,
    overtime: overtimeConfigs.noOvertimeSequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: true,
    },
    org: {
      isPayrollEnabled: false,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: false,
  },

  // Test daily double overtime threshold
  {
    name: "Worker with only daily double overtime (Sequential)",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.doubleOnlySequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: true,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: false,
  },
  {
    name: "Worker with daily and weekly OT and double OT (Sequential)",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.dailyWeeklyAndDoubleSequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: true,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: false,
  },

  // Test custom overtime thresholds
  {
    name: "Custom overtime thresholds (10h daily/50h weekly) (Sequential)",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: {
      ...overtimeConfigs.dailyAndWeeklySequential,
      dailyOvertimeThreshold: 10, // 10 hours threshold
      weeklyOvertimeThreshold: 50, // 50 hours threshold
    },
    prevailingWage: {
      overridePwIfBelowRegularRate: true,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: false,
  },

  // Add comprehensive prevailing wage scenarios:
  {
    name: "Prevailing wage with higher rates than user's base rate",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.dailyAndWeeklySequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: false, // Use PW rate even if lower
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: true,
  },

  {
    name: "Prevailing wage with lower rates than user's base rate and override enabled",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.dailyAndWeeklySequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: true, // Use higher of PW rate or regular rate
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: true,
  },

  {
    name: "Prevailing wage with double overtime",
    paySchedule: payScheduleConfigs.weekly,
    timeTracking: timeTrackingConfigs.paidBreaksEnabled,
    overtime: overtimeConfigs.dailyWeeklyAndDoubleSequential,
    prevailingWage: {
      overridePwIfBelowRegularRate: false,
    },
    org: {
      isPayrollEnabled: true,
      timezone: "America/Los_Angeles",
    },
    userType: "hourly",
    isPrevailingWage: true,
  },

  // Add more test configurations as needed...
];

describe("Refined Timesheet Calculations Integration Tests", () => {
  testConfigurations.forEach((config) => {
    describe(config.name, () => {
      let organization: Organization;
      let user: any;
      let project: any;
      let regEarningRate: any;
      let otEarningRate: any;
      const _costCodes: any[] = [];
      const classifications: any[] = [];
      let wageTable: any;
      let journeymanClassification: any;
      let apprenticeClassification: any;
      let userJourneymanClassification: any;

      beforeEach(async () => {
        // Create organization with builder pattern
        const orgBuilder = new OrganizationBuilder();
        orgBuilder.withName(config.name);

        // Apply organization settings from config
        if (config.org.isPayrollEnabled) {
          orgBuilder.withFeatures({ isPayrollEnabled: true });
        }

        organization = await orgBuilder.buildAndCreate();

        // Update organization settings individually
        // Time tracking settings
        organization.timeTrackingSettings.areBreaksPaid = config.timeTracking.areBreaksPaid;
        organization.timeTrackingSettings.areRealtimeBreaksEnabled = config.timeTracking.realTimeBreaksEnabled;
        organization.timeTrackingSettings.breakOptions = [15, 30, 45, 60];

        // Overtime settings
        organization.overtimeSettings.dailyOvertimeEnabled = config.overtime.dailyOvertimeEnabled;
        organization.overtimeSettings.weeklyOvertimeEnabled = config.overtime.weeklyOvertimeEnabled;
        organization.overtimeSettings.dailyDoubleOvertimeEnabled = config.overtime.doubleOvertimeEnabled;
        // Cast to any to avoid type issues with the model's specific day types
        organization.overtimeSettings.overtimeDays = config.overtime.overtimeDays as any;
        organization.overtimeSettings.doubleOvertimeDays = config.overtime.doubleOvertimeDays as any;
        organization.overtimeSettings.dailyOvertimeThreshold = config.overtime.dailyOvertimeThreshold * 60; // convert to minutes
        organization.overtimeSettings.weeklyOvertimeThreshold = config.overtime.weeklyOvertimeThreshold * 60; // convert to minutes
        organization.overtimeSettings.dailyDoubleOvertimeThreshold = config.overtime.doubleOvertimeThreshold * 60; // convert to minutes
        organization.overtimeSettings.overtimeMultiplier = config.overtime.overtimeMultiplier;
        organization.overtimeSettings.overtimeDistribution = config.overtime.overtimeDistribution;

        // The doubleOvertimeMultiplier might not be a direct property in the model
        // Since it doesn't exist in OvertimeSettings, we'll omit this assignment
        // organization.overtimeSettings.doubleOvertimeMultiplier = config.overtime.doubleOvertimeMultiplier;

        // Prevailing wage settings
        organization.prevailingWageSettings.overridePwIfBelowRegularRate =
          config.prevailingWage.overridePwIfBelowRegularRate;

        // Set payScheduleSettings via the organization model's set method
        organization.set("payScheduleSettings", {
          type: config.paySchedule.type,
          firstPayDay: new Date(config.paySchedule.firstPayday),
          firstPeriodEnd: new Date(config.paySchedule.firstPeriodEnd),
          isActive: true,
          checkPayScheduleId: "test-pay-schedule-id",
        });

        // Create a user based on config - for now, we'll just use hourly worker
        // as we don't have the other factory methods
        user = await createHourlyWorker(organization.id, 25.0);

        // Create cost codes with builder (we need at least one for PW projects)
        const _costCode = await new CostCodeBuilder()
          .withOrganization(organization.id)
          .withName("CostCode 1")
          .buildAndCreate();

        // Create classifications if testing prevailing wage
        if (config.isPrevailingWage) {
          // Create wage table with builder
          wageTable = await new WageTableBuilder()
            .withOrganization(organization.id)
            .withName("Test Prevailing Wage Table")
            .withDescription("Test PW wage table for testing")
            .buildAndCreate();

          // Create classifications with builder
          journeymanClassification = await new ClassificationBuilder()
            .withOrganization(organization.id)
            .withWageTable(wageTable.id)
            .withAllocatedPay("35.00", "25.00")
            .buildAndCreate();

          apprenticeClassification = await new ClassificationBuilder()
            .withOrganization(organization.id)
            .withWageTable(wageTable.id)
            .withAllocatedPay("25.00", "18.37")
            .buildAndCreate();

          classifications.push(journeymanClassification, apprenticeClassification);

          // Create user classification with UserClassificationFactory
          userJourneymanClassification = await new UserClassificationFactory()
            .withOrganization(organization.id)
            .withWageTable(wageTable.id)
            .withClassification(journeymanClassification.id)
            .withUser(user.id)
            .buildAndCreate();

          // Create a dynamic fringe benefit for testing
          const dynamicFringeBenefit = await createDynamicFringeBenefit(organization.id, "PW Dynamic Fringe");

          // Create the employee benefit that references the dynamic fringe
          const employeeDynamicBenefit = await createDynamicBenefit(
            user.id,
            dynamicFringeBenefit.id,
            15.0 // Employee contributes 15%
          );

          // Add employee benefits to the user object
          user.employeeBenefits = [
            {
              id: employeeDynamicBenefit.id,
              contributionType: "DYNAMIC",
              employeeContributionPercent: 15.0,
              companyContributionPercent: 0,
              fringeHourlyContribution: 0,
              companyBenefit: {
                id: dynamicFringeBenefit.id,
                contributionType: "DYNAMIC",
                name: dynamicFringeBenefit.name,
              },
            },
          ];
        }

        // Create project (prevailing wage or not)
        if (config.isPrevailingWage) {
          project = await createPrevailingWageProject(organization.id, wageTable.id);
        } else {
          project = await createBasicProject(organization.id);
        }

        // Get/set earning rates for the user
        await user.reload({
          include: [{ model: EarningRate, as: "earningRates" }],
        });

        regEarningRate = user.earningRates.find((rate: any) => rate.type === "REG");
        otEarningRate = user.earningRates.find((rate: any) => rate.type === "OT");

        if (!regEarningRate || !otEarningRate) {
          throw new Error("Failed to find earning rates for user");
        }
      });

      // Test cases for this configuration

      it("should correctly calculate regular hours for standard day", async () => {
        // Create a timesheet for 8 hours
        const startDate = new Date();
        const timesheets = await createTimesheetSeries(
          user.id,
          organization.id,
          project.id,
          regEarningRate.id,
          otEarningRate.id,
          {
            clockInTime: "08:00",
            clockOutTime: "16:00",
            startDate,
            numberOfDays: 1,
          },
          config.isPrevailingWage ? userJourneymanClassification.id : undefined
        );

        // Convert to ExtendedTimesheet format
        const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
          const plainTs = ts.get({ plain: true });

          return {
            ...plainTs,
            totalMinutes: 480, // 8 hours
            regEarningRate: {
              id: regEarningRate.id,
              amount: regEarningRate.amount,
              period: regEarningRate.period,
            },
            otEarningRate: {
              id: otEarningRate.id,
              amount: otEarningRate.amount,
              period: otEarningRate.period,
            },
            project: {
              id: project.id,
              name: project.name,
              isPrevailingWage: config.isPrevailingWage,
            },
            userClassification: config.isPrevailingWage
              ? {
                  id: userJourneymanClassification.id,
                  basePay: journeymanClassification.basePay,
                  fringePay: journeymanClassification.fringePay,
                }
              : undefined,
            fringeBenefitClassifications: [],
            user: config.isPrevailingWage
              ? {
                  id: user.id,
                  earningRates: [regEarningRate, otEarningRate],
                  employeeBenefits: user.employeeBenefits || [],
                }
              : {
                  id: user.id,
                  earningRates: [regEarningRate, otEarningRate],
                  employeeBenefits: [],
                },
          };
        });

        // Run the calculation function
        refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

        // Check if the day is a special day (OT or DOT override day)
        const dayOfWeekInt = dayjs.tz(extendedTimesheets[0].clockIn, organization.timezone).day();
        const dayOfWeek = getDayOfWeekString(dayOfWeekInt);
        const isSpecialDay =
          (config.overtime.overtimeDays && config.overtime.overtimeDays.includes(dayOfWeek)) ||
          (config.overtime.doubleOvertimeDays && config.overtime.doubleOvertimeDays.includes(dayOfWeek));

        // Expectations based on configuration
        if (!config.overtime.dailyOvertimeEnabled && !config.overtime.weeklyOvertimeEnabled) {
          // No overtime enabled - should be all regular time
          expect(extendedTimesheets[0].regularMinutes).to.equal(480);
          expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
          if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
          }
        } else if (config.overtime.dailyOvertimeEnabled) {
          // If the day is a special day (OT or DOT override day)
          if (isSpecialDay) {
            if (config.overtime.doubleOvertimeDays && config.overtime.doubleOvertimeDays.includes(dayOfWeek)) {
              // Double OT day - all should be double OT
              expect(extendedTimesheets[0].regularMinutes).to.equal(0);
              expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
              expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(480);
            } else {
              // OT day - all should be OT
              expect(extendedTimesheets[0].regularMinutes).to.equal(0);
              expect(extendedTimesheets[0].overtimeMinutes).to.equal(480);
              if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
                expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
              }
            }
          } else {
            // If daily OT enabled with 8 hour threshold and not a special day
            if (config.overtime.dailyOvertimeThreshold === 8) {
              expect(extendedTimesheets[0].regularMinutes).to.equal(480); // All regular
              expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
              if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
                expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
              }
            }
          }
        }

        // Check wages based on config
        if (config.isPrevailingWage && !config.prevailingWage.overridePwIfBelowRegularRate) {
          // Use PW wage
          expect(extendedTimesheets[0].hourlyWage).to.be.closeTo(35 + 0, 0.01); // basePay + cash fringe
        } else {
          // Use regular wage
          expect(extendedTimesheets[0].regularWages).to.be.closeTo(
            (extendedTimesheets[0].regularMinutes / 60) * extendedTimesheets[0].hourlyWage,
            0.01
          );
        }
      });

      it("should correctly calculate overtime hours for longer day", async () => {
        // Create a timesheet for 10 hours
        const startDate = new Date();
        const timesheets = await createTimesheetSeries(
          user.id,
          organization.id,
          project.id,
          regEarningRate.id,
          otEarningRate.id,
          {
            clockInTime: "08:00",
            clockOutTime: "18:00",
            startDate,
            numberOfDays: 1,
          },
          config.isPrevailingWage ? userJourneymanClassification.id : undefined
        );

        // Convert to ExtendedTimesheet format
        const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
          const plainTs = ts.get({ plain: true });

          return {
            ...plainTs,
            totalMinutes: 600, // 10 hours
            regEarningRate: {
              id: regEarningRate.id,
              amount: regEarningRate.amount,
              period: regEarningRate.period,
            },
            otEarningRate: {
              id: otEarningRate.id,
              amount: otEarningRate.amount,
              period: otEarningRate.period,
            },
            project: {
              id: project.id,
              name: project.name,
              isPrevailingWage: config.isPrevailingWage,
            },
            userClassification: config.isPrevailingWage
              ? {
                  id: userJourneymanClassification.id,
                  basePay: journeymanClassification.basePay,
                  fringePay: journeymanClassification.fringePay,
                }
              : undefined,
            fringeBenefitClassifications: [],
            user: config.isPrevailingWage
              ? {
                  id: user.id,
                  earningRates: [regEarningRate, otEarningRate],
                  employeeBenefits: user.employeeBenefits || [],
                }
              : {
                  id: user.id,
                  earningRates: [regEarningRate, otEarningRate],
                  employeeBenefits: [],
                },
          };
        });

        // Run the calculation function
        refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

        // Check if the day is a special day (OT or DOT override day)
        const dayOfWeekInt = dayjs.tz(extendedTimesheets[0].clockIn, organization.timezone).day();
        const dayOfWeek = getDayOfWeekString(dayOfWeekInt);
        const isSpecialDay =
          (config.overtime.overtimeDays && config.overtime.overtimeDays.includes(dayOfWeek)) ||
          (config.overtime.doubleOvertimeDays && config.overtime.doubleOvertimeDays.includes(dayOfWeek));

        // Expectations based on configuration
        if (isSpecialDay) {
          // If it's a special day
          if (config.overtime.doubleOvertimeDays && config.overtime.doubleOvertimeDays.includes(dayOfWeek)) {
            // Double OT day - all should be double OT
            expect(extendedTimesheets[0].regularMinutes).to.equal(0);
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(600);
          } else {
            // OT day - all should be OT
            expect(extendedTimesheets[0].regularMinutes).to.equal(0);
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(600);
            if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
              expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
            }
          }
        } else if (config.overtime.dailyOvertimeEnabled && config.overtime.dailyOvertimeThreshold === 8) {
          // Should have 8 hours regular, 2 hours overtime
          expect(extendedTimesheets[0].regularMinutes).to.equal(480);
          expect(extendedTimesheets[0].overtimeMinutes).to.equal(120);
          if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
          }
        } else if (!config.overtime.dailyOvertimeEnabled && !config.overtime.weeklyOvertimeEnabled) {
          // No overtime enabled
          expect(extendedTimesheets[0].regularMinutes).to.equal(600);
          expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
          if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
          }
        }
      });

      // Only run weekly test for weekly OT configs
      if (config.overtime.weeklyOvertimeEnabled) {
        it("should correctly calculate weekly overtime across multiple days", async () => {
          // Create 5 days of 9-hour timesheets (45 hours total)
          const startDate = new Date();
          const timesheets = await createTimesheetSeries(
            user.id,
            organization.id,
            project.id,
            regEarningRate.id,
            otEarningRate.id,
            {
              clockInTime: "08:00",
              clockOutTime: "17:00", // 9 hours
              startDate,
              numberOfDays: 5,
            },
            config.isPrevailingWage ? userJourneymanClassification.id : undefined
          );

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
            const plainTs = ts.get({ plain: true });

            return {
              ...plainTs,
              totalMinutes: 540, // 9 hours
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // With weekly overtime at 40 hours, we should have 40 regular + 5 overtime
          // If daily OT is also enabled, first 4 days might have OT too

          // Last day should have some weekly overtime
          const totalRegularMinutes = extendedTimesheets.reduce((sum, ts) => sum + ts.regularMinutes, 0);
          const totalOvertimeMinutes = extendedTimesheets.reduce((sum, ts) => sum + ts.overtimeMinutes, 0);

          // Total should be 45 hours
          expect(totalRegularMinutes + totalOvertimeMinutes).to.equal(45 * 60);

          // If weekly OT is enabled, reg minutes should be capped at 40 hours
          if (config.overtime.weeklyOvertimeEnabled && !config.overtime.dailyOvertimeEnabled) {
            expect(totalRegularMinutes).to.equal(40 * 60);
            expect(totalOvertimeMinutes).to.equal(5 * 60);
          }

          // If both daily and weekly OT are enabled, will be more complex
          // but should still follow the general weekly cap
        });
      }

      // Test for overtime override day if configuration includes it
      if (config.overtime.overtimeDays.length > 0) {
        it("should treat specific days as overtime days", async () => {
          // Get a date that falls on the first overtime day
          const dayIndex = WEEK_DAYS.findIndex((day) => day === config.overtime.overtimeDays[0]);
          const targetDate = new Date();
          while (targetDate.getDay() !== dayIndex) {
            targetDate.setDate(targetDate.getDate() + 1);
          }

          // Create a timesheet for this overtime day
          const timesheets = await createTimesheetSeries(
            user.id,
            organization.id,
            project.id,
            regEarningRate.id,
            otEarningRate.id,
            {
              clockInTime: "08:00",
              clockOutTime: "16:00", // 8 hours
              startDate: targetDate,
              numberOfDays: 1,
            },
            config.isPrevailingWage ? userJourneymanClassification.id : undefined
          );

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
            const plainTs = ts.get({ plain: true });

            return {
              ...plainTs,
              totalMinutes: 480, // 8 hours
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // On an overtime day, all hours should be overtime
          expect(extendedTimesheets[0].regularMinutes).to.equal(0);
          expect(extendedTimesheets[0].overtimeMinutes).to.equal(480);
          if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0);
          }
        });
      }

      // Test for double overtime override day if configuration includes it
      if (config.overtime.doubleOvertimeDays.length > 0) {
        it.skip("should treat specific days as double overtime days", async () => {
          // Get a date that falls on the first double overtime day
          const dayIndex = WEEK_DAYS.findIndex((day) => day === config.overtime.doubleOvertimeDays[0]);

          // Create a date in the organization's timezone
          const now = dayjs.tz(new Date(), organization.timezone);
          // Find the next occurrence of the target day of week
          let targetDate = now;
          while (targetDate.day() !== dayIndex) {
            targetDate = targetDate.add(1, "day");
          }

          // Convert to native Date object, maintaining the timezone awareness
          const localTargetDate = targetDate.toDate();

          // Create a timesheet for this double overtime day
          const timesheets = await createTimesheetSeries(
            user.id,
            organization.id,
            project.id,
            regEarningRate.id,
            otEarningRate.id,
            {
              clockInTime: "08:00",
              clockOutTime: "16:00", // 8 hours
              startDate: localTargetDate,
              numberOfDays: 1,
            },
            config.isPrevailingWage ? userJourneymanClassification.id : undefined
          );

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
            const plainTs = ts.get({ plain: true });

            return {
              ...plainTs,
              totalMinutes: 480, // 8 hours
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // On a double overtime day, all hours should be double overtime
          expect(extendedTimesheets[0].regularMinutes).to.equal(0);
          expect(extendedTimesheets[0].overtimeMinutes).to.equal(0);
          if ("doubleOvertimeMinutes" in extendedTimesheets[0]) {
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(480);
          }
        });
      }

      // Add this test case inside the describe block for configs with double overtime
      if (config.overtime.doubleOvertimeEnabled) {
        it.skip("should correctly calculate double overtime for very long day", async () => {
          // Create a timesheet for 14 hours
          const startDate = new Date();
          const timesheets = await createTimesheetSeries(
            user.id,
            organization.id,
            project.id,
            regEarningRate.id,
            otEarningRate.id,
            {
              clockInTime: "08:00",
              clockOutTime: "22:00", // 14 hours
              startDate,
              numberOfDays: 1,
            },
            config.isPrevailingWage ? userJourneymanClassification.id : undefined
          );

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
            const plainTs = ts.get({ plain: true });

            return {
              ...plainTs,
              totalMinutes: 840, // 14 hours
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // Check if this is the "double OT only" configuration
          if (
            !config.overtime.dailyOvertimeEnabled &&
            !config.overtime.weeklyOvertimeEnabled &&
            config.overtime.doubleOvertimeEnabled
          ) {
            // For doubleOnlySequential config - special handling
            // - First 12 hours (720 minutes) should be regular time
            // - Hours beyond 12 hours should be double overtime
            // - No regular overtime at all
            expect(extendedTimesheets[0].regularMinutes).to.equal(720); // 12 hours
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(0); // No regular OT
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(120); // 2 hours double OT
          } else {
            // With daily OT at 8 hours and double OT at 12 hours:
            // - 8 hours regular
            // - 4 hours overtime
            // - 2 hours double overtime
            expect(extendedTimesheets[0].regularMinutes).to.equal(480); // 8 hours
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(240); // 4 hours
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(120); // 2 hours
          }
        });

        // Add edge case: exactly at double overtime threshold
        it.skip("should correctly calculate at exactly the double overtime threshold", async () => {
          // Create a timesheet for exactly 12 hours (at double OT threshold)
          const startDate = new Date();
          const timesheets = await createTimesheetSeries(
            user.id,
            organization.id,
            project.id,
            regEarningRate.id,
            otEarningRate.id,
            {
              clockInTime: "08:00",
              clockOutTime: "20:00", // 12 hours
              startDate,
              numberOfDays: 1,
            },
            config.isPrevailingWage ? userJourneymanClassification.id : undefined
          );

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
            const plainTs = ts.get({ plain: true });

            return {
              ...plainTs,
              totalMinutes: 720, // 12 hours
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // For doubleOnlySequential, check double OT threshold behavior
          if (
            !config.overtime.dailyOvertimeEnabled &&
            !config.overtime.weeklyOvertimeEnabled &&
            config.overtime.doubleOvertimeEnabled
          ) {
            // When exactly at the threshold with doubleOnlySequential:
            // - All 12 hours should be regular time
            // - No double overtime yet (we're exactly at the threshold)
            expect(extendedTimesheets[0].regularMinutes).to.equal(720); // 12 hours
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(0); // 0 hours
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0); // 0 hours
          } else {
            // At exactly the threshold with normal settings:
            // - 8 hours regular
            // - 4 hours overtime
            // - 0 hours double overtime
            expect(extendedTimesheets[0].regularMinutes).to.equal(480); // 8 hours
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(240); // 4 hours
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(0); // 0 hours
          }
        });

        // Add edge case: just past the double overtime threshold
        it.skip("should correctly calculate one minute past double overtime threshold", async () => {
          // Create a timesheet for 12 hours + 1 minute
          const startDate = new Date();
          const timesheets = await createTimesheetSeries(
            user.id,
            organization.id,
            project.id,
            regEarningRate.id,
            otEarningRate.id,
            {
              clockInTime: "08:00",
              clockOutTime: "20:01", // 12 hours + 1 minute
              startDate,
              numberOfDays: 1,
            },
            config.isPrevailingWage ? userJourneymanClassification.id : undefined
          );

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
            const plainTs = ts.get({ plain: true });

            return {
              ...plainTs,
              totalMinutes: 721, // 12 hours + 1 minute
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // Just past the threshold:
          // - 8 hours regular
          // - 4 hours overtime
          // - 1 minute double overtime
          expect(extendedTimesheets[0].regularMinutes).to.equal(480); // 8 hours
          expect(extendedTimesheets[0].overtimeMinutes).to.equal(240); // 4 hours
          expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(1); // 1 minute double OT
        });

        // Add weekly scenario with double overtime
        it.skip("should correctly attribute double overtime across multiple days", async () => {
          // Create 5 days of varying lengths including some that go past double OT threshold
          const startDate = new Date();
          // Create days with different hour counts
          const hourCounts = [8, 10, 14, 12, 9]; // Total: 53 hours with some double OT
          const timesheets = [];

          for (let i = 0; i < hourCounts.length; i++) {
            const dayHours = hourCounts[i];
            const endHour = 8 + dayHours; // Start at 8am
            const endTime = `${endHour.toString().padStart(2, "0")}:00`;

            const dayTimesheets = await createTimesheetSeries(
              user.id,
              organization.id,
              project.id,
              regEarningRate.id,
              otEarningRate.id,
              {
                clockInTime: "08:00",
                clockOutTime: endTime,
                startDate: new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000), // Add days
                numberOfDays: 1,
              },
              config.isPrevailingWage ? userJourneymanClassification.id : undefined
            );

            timesheets.push(...dayTimesheets);
          }

          // Convert to ExtendedTimesheet format
          const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts, index) => {
            const plainTs = ts.get({ plain: true });
            const dayHours = hourCounts[index];

            return {
              ...plainTs,
              totalMinutes: dayHours * 60, // Convert hours to minutes
              regEarningRate: {
                id: regEarningRate.id,
                amount: regEarningRate.amount,
                period: regEarningRate.period,
              },
              otEarningRate: {
                id: otEarningRate.id,
                amount: otEarningRate.amount,
                period: otEarningRate.period,
              },
              project: {
                id: project.id,
                name: project.name,
                isPrevailingWage: config.isPrevailingWage,
              },
              userClassification: config.isPrevailingWage
                ? {
                    id: userJourneymanClassification.id,
                    basePay: journeymanClassification.basePay,
                    fringePay: journeymanClassification.fringePay,
                  }
                : undefined,
              fringeBenefitClassifications: [],
              user: config.isPrevailingWage
                ? {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: user.employeeBenefits || [],
                  }
                : {
                    id: user.id,
                    earningRates: [regEarningRate, otEarningRate],
                    employeeBenefits: [],
                  },
            };
          });

          // Run the calculation function
          refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

          // Calculate totals
          const totalRegularMinutes = extendedTimesheets.reduce((sum, ts) => sum + ts.regularMinutes, 0);
          const totalOvertimeMinutes = extendedTimesheets.reduce((sum, ts) => sum + ts.overtimeMinutes, 0);
          const totalDoubleOvertimeMinutes = extendedTimesheets.reduce(
            (sum, ts) => sum + (ts.doubleOvertimeMinutes || 0),
            0
          );
          const totalMinutes = totalRegularMinutes + totalOvertimeMinutes + totalDoubleOvertimeMinutes;

          // Total hours should match our input (53 hours = 3180 minutes)
          expect(totalMinutes).to.equal(3180);

          // Day 3 should have double overtime (14 hours - with 2 hours of double OT)
          const day3Index = 2; // 0-based index for the third day
          expect(extendedTimesheets[day3Index].doubleOvertimeMinutes).to.equal(120); // 2 hours double OT

          // Test specific expectations based on the overtime config
          if (config.overtime.dailyOvertimeEnabled && config.overtime.weeklyOvertimeEnabled) {
            // With both daily and weekly OT, we should have:
            // - Some regular hours (capped by daily and weekly thresholds)
            // - Some OT hours (from daily OT and weekly OT)
            // - Some double OT hours (from exceeding the 12-hour threshold)

            // Weekly cap on regular hours should be 40 hours (2400 minutes)
            expect(totalRegularMinutes).to.be.at.most(2400);

            // Should have some double overtime from the 14-hour day
            expect(totalDoubleOvertimeMinutes).to.be.greaterThan(0);
          }
        });
      }

      // Add these additional tests inside the describe block for prevailing wage tests
      if (config.isPrevailingWage) {
        describe("Additional prevailing wage tests", () => {
          it("should use prevailing wage rates for calculations", async () => {
            // Create a timesheet for the first user (with journeyman classification)
            const startDate = new Date();
            const timesheets = await createTimesheetSeries(
              user.id,
              organization.id,
              project.id,
              regEarningRate.id,
              otEarningRate.id,
              {
                clockInTime: "08:00",
                clockOutTime: "17:00", // 9 hours
                startDate,
                numberOfDays: 1,
              },
              userJourneymanClassification.id // Specify user classification
            );

            // Convert to ExtendedTimesheet format with prevailing wage details
            const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
              const plainTs = ts.get({ plain: true });

              return {
                ...plainTs,
                totalMinutes: 540, // 9 hours
                regEarningRate: {
                  id: regEarningRate.id,
                  amount: regEarningRate.amount,
                  period: regEarningRate.period,
                },
                otEarningRate: {
                  id: otEarningRate.id,
                  amount: otEarningRate.amount,
                  period: otEarningRate.period,
                },
                project: {
                  id: project.id,
                  name: project.name,
                  isPrevailingWage: true,
                },
                userClassification: {
                  id: userJourneymanClassification.id,
                  basePay: journeymanClassification.basePay,
                  fringePay: journeymanClassification.fringePay,
                },
                fringeBenefitClassifications: [],
                user: config.isPrevailingWage
                  ? {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: user.employeeBenefits || [],
                    }
                  : {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: [],
                    },
              };
            });

            // Run the calculation function
            refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

            // With daily OT at 8 hours:
            // - 8 hours regular at PW rate
            // - 1 hour overtime at PW OT rate
            expect(extendedTimesheets[0].regularMinutes).to.equal(480); // 8 hours regular
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(60); // 1 hour OT

            // Check that prevailing wage rates were used
            // Should use basePay + fringePay (often structured as cash fringe)
            const expectedHourlyWage =
              parseFloat(journeymanClassification.basePay) + extendedTimesheets[0].cashFringeRate;
            expect(extendedTimesheets[0].hourlyWage).to.be.closeTo(expectedHourlyWage, 0.01);

            // OT should be at 1.5x base pay + cash fringe
            const expectedOtWage =
              parseFloat(journeymanClassification.basePay) * 1.5 + extendedTimesheets[0].cashFringeRate;
            expect(extendedTimesheets[0].otHourlyWage).to.be.closeTo(expectedOtWage, 0.01);

            // Calculate expected wages
            const expectedRegularWages = (480 / 60) * expectedHourlyWage;
            const expectedOtWages = (60 / 60) * expectedOtWage;

            expect(extendedTimesheets[0].regularWages).to.be.closeTo(expectedRegularWages, 0.01);
            expect(extendedTimesheets[0].overtimeWages).to.be.closeTo(expectedOtWages, 0.01);
          });

          it("should handle override PW if below regular rate setting when enabled", async () => {
            // Only run this test if the override setting is enabled
            if (!config.prevailingWage.overridePwIfBelowRegularRate) {
              return;
            }

            // Create a timesheet for the second user with a lower PW rate than their base rate
            const baseHourlyRate = 30.0; // Higher than the apprentice PW rate
            const specialUser = await createHourlyWorker(organization.id, baseHourlyRate);

            await specialUser.reload({
              include: [{ model: EarningRate, as: "earningRates" }],
            });

            const specialUserRegRate = specialUser.earningRates.find((rate: any) => rate.type === "REG");
            const specialUserOtRate = specialUser.earningRates.find((rate: any) => rate.type === "OT");

            // Assign to apprentice classification (which has lower rates)
            const specialUserClassification = await new UserClassificationFactory()
              .withOrganization(organization.id)
              .withWageTable(wageTable.id)
              .withClassification(apprenticeClassification.id)
              .withUser(specialUser.id)
              .buildAndCreate();

            // Create the timesheet
            const startDate = new Date();
            const timesheets = await createTimesheetSeries(
              specialUser.id,
              organization.id,
              project.id,
              specialUserRegRate.id,
              specialUserOtRate.id,
              {
                clockInTime: "08:00",
                clockOutTime: "17:00", // 9 hours
                startDate,
                numberOfDays: 1,
              },
              specialUserClassification.id
            );

            // Convert to ExtendedTimesheet format
            const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
              const plainTs = ts.get({ plain: true });

              return {
                ...plainTs,
                totalMinutes: 540, // 9 hours
                regEarningRate: {
                  id: specialUserRegRate.id,
                  amount: specialUserRegRate.amount,
                  period: specialUserRegRate.period,
                },
                otEarningRate: {
                  id: specialUserOtRate.id,
                  amount: specialUserOtRate.amount,
                  period: specialUserOtRate.period,
                },
                project: {
                  id: project.id,
                  name: project.name,
                  isPrevailingWage: config.isPrevailingWage,
                },
                userClassification: config.isPrevailingWage
                  ? {
                      id: specialUserClassification.id,
                      basePay: apprenticeClassification.basePay,
                      fringePay: apprenticeClassification.fringePay,
                    }
                  : undefined,
                fringeBenefitClassifications: [],
                user: config.isPrevailingWage
                  ? {
                      id: specialUser.id,
                      earningRates: [specialUserRegRate, specialUserOtRate],
                      employeeBenefits: specialUser.employeeBenefits || [],
                    }
                  : {
                      id: specialUser.id,
                      earningRates: [specialUserRegRate, specialUserOtRate],
                      employeeBenefits: [],
                    },
              };
            });

            // Run the calculation function
            refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

            // Regular rate: $30.00
            // PW rate: $20.00 (base) + cashFringeRate
            const pwHourlyRate = parseFloat(apprenticeClassification.basePay) + extendedTimesheets[0].cashFringeRate;

            // If PW is higher than regular rate, use PW
            if (pwHourlyRate > baseHourlyRate) {
              expect(extendedTimesheets[0].hourlyWage).to.be.closeTo(pwHourlyRate, 0.01);
            }
            // If regular rate is higher than PW, use regular rate
            else {
              expect(extendedTimesheets[0].hourlyWage).to.be.closeTo(baseHourlyRate, 0.01);
            }
          });

          it("should calculate prevailing wage with dynamic fringe benefits", async () => {
            // This test is for more complex prevailing wage setup with dynamic fringe

            // 1. Create the company dynamic fringe benefit
            const dynamicFringeBenefit = await createDynamicFringeBenefit(organization.id, "PW Dynamic Fringe");

            // 2. Create the employee benefit that references the dynamic fringe
            const employeeDynamicBenefit = await createDynamicBenefit(
              user.id,
              dynamicFringeBenefit.id,
              15.0 // Employee contributes 15%
            );

            // Create a timesheet for 10 hours
            const startDate = new Date();
            const timesheets = await createTimesheetSeries(
              user.id,
              organization.id,
              project.id,
              regEarningRate.id,
              otEarningRate.id,
              {
                clockInTime: "08:00",
                clockOutTime: "18:00", // 10 hours
                startDate,
                numberOfDays: 1,
              },
              userJourneymanClassification.id
            );

            // Convert to ExtendedTimesheet format with dynamic fringe setup
            const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
              const plainTs = ts.get({ plain: true });

              return {
                ...plainTs,
                totalMinutes: 600, // 10 hours
                regEarningRate: {
                  id: regEarningRate.id,
                  amount: regEarningRate.amount,
                  period: regEarningRate.period,
                },
                otEarningRate: {
                  id: otEarningRate.id,
                  amount: otEarningRate.amount,
                  period: otEarningRate.period,
                },
                project: {
                  id: project.id,
                  name: project.name,
                  isPrevailingWage: true,
                },
                userClassification: {
                  id: userJourneymanClassification.id,
                  basePay: journeymanClassification.basePay,
                  fringePay: journeymanClassification.fringePay,
                },
                fringeBenefitClassifications: [], // No fringe benefit classifications for this test
                // The cash fringe rate will be calculated as 0 by the function
                cashFringeRate: 0, // We expect this to be calculated as 0 in the function
                user: config.isPrevailingWage
                  ? {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: [
                        {
                          id: employeeDynamicBenefit.id,
                          contributionType: "DYNAMIC",
                          employeeContributionPercent: 15.0,
                          companyContributionPercent: 0,
                          fringeHourlyContribution: 0,
                          companyBenefit: {
                            id: dynamicFringeBenefit.id,
                            contributionType: "DYNAMIC",
                            name: dynamicFringeBenefit.name,
                          },
                        },
                      ],
                    }
                  : {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: [],
                    },
              };
            });

            // Run the calculation function
            refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

            // Check dynamic fringe allocation
            // With a 10-hour day and daily OT at 8 hours:
            // - 8 hours regular, 2 hours overtime
            // - Dynamic fringe benefit should be applied to all hours worked

            // Calculate expected values
            const basePay = parseFloat(journeymanClassification.basePay);
            const fringePay = parseFloat(journeymanClassification.fringePay);

            // Expected values from the calculation in attributeWeeklyTimesheetCalculations:
            // 1. The dynamic fringe contribution rate should be set to the cash fringe rate
            expect(extendedTimesheets[0].companyDynamicFringeContributionRate).to.be.closeTo(fringePay, 0.01);

            // 2. The employee contribution percent should be set to the value from the benefit
            expect(extendedTimesheets[0].employeeDynamicFringeContributionPerecent).to.equal(15.0);

            // 3. The cash fringe rate should be 0 since we're using dynamic fringe
            expect(extendedTimesheets[0].cashFringeRate).to.equal(0);

            // Company contribution for dynamic fringe (for all hours worked)
            const expectedCompanyFringeContribution = (600 / 60) * fringePay;

            // Employee contribution (15% of base wage)
            const totalMinutes = 480 + 120;
            const calculatedFringeRate = basePay * (15.0 / 100.0);
            const expectedEmployeeFringeContribution = calculatedFringeRate * (totalMinutes / 60);

            // Check the dynamic fringe allocation
            if ("companyDynamicFringeAllocation" in extendedTimesheets[0]) {
              expect(extendedTimesheets[0].companyDynamicFringeAllocation).to.be.closeTo(
                expectedCompanyFringeContribution,
                0.01
              );
            }

            if ("employeeDynamicFringeAllocation" in extendedTimesheets[0]) {
              expect(extendedTimesheets[0].employeeDynamicFringeAllocation).to.be.closeTo(
                expectedEmployeeFringeContribution,
                0.01
              );
            }
          });

          it.skip("should calculate double overtime correctly for prevailing wage", async () => {
            // Skip if double overtime isn't enabled for this config
            if (!config.overtime.doubleOvertimeEnabled) {
              return;
            }

            // Create a timesheet for 14 hours (exceeding double OT threshold)
            const startDate = new Date();
            const timesheets = await createTimesheetSeries(
              user.id,
              organization.id,
              project.id,
              regEarningRate.id,
              otEarningRate.id,
              {
                clockInTime: "08:00",
                clockOutTime: "22:00", // 14 hours
                startDate,
                numberOfDays: 1,
              },
              userJourneymanClassification.id
            );

            // Convert to ExtendedTimesheet format
            const extendedTimesheets: ExtendedTimesheet[] = timesheets.map((ts) => {
              const plainTs = ts.get({ plain: true });

              return {
                ...plainTs,
                totalMinutes: 840, // 14 hours
                regEarningRate: {
                  id: regEarningRate.id,
                  amount: regEarningRate.amount,
                  period: regEarningRate.period,
                },
                otEarningRate: {
                  id: otEarningRate.id,
                  amount: otEarningRate.amount,
                  period: otEarningRate.period,
                },
                project: {
                  id: project.id,
                  name: project.name,
                  isPrevailingWage: true,
                },
                userClassification: {
                  id: userJourneymanClassification.id,
                  basePay: journeymanClassification.basePay,
                  fringePay: journeymanClassification.fringePay,
                },
                fringeBenefitClassifications: [],
                user: config.isPrevailingWage
                  ? {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: user.employeeBenefits || [],
                    }
                  : {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: [],
                    },
              };
            });

            // Run the calculation function
            refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

            // With daily OT at 8 hours and double OT at 12 hours:
            // - 8 hours regular
            // - 4 hours overtime
            // - 2 hours double overtime
            expect(extendedTimesheets[0].regularMinutes).to.equal(480); // 8 hours
            expect(extendedTimesheets[0].overtimeMinutes).to.equal(240); // 4 hours
            expect(extendedTimesheets[0].doubleOvertimeMinutes).to.equal(120); // 2 hours

            // Calculate expected wages
            const basePay = parseFloat(journeymanClassification.basePay);
            const fringePay = parseFloat(journeymanClassification.fringePay);

            // Regular hourly wage = basePay + fringePay
            const expectedHourlyWage = basePay + fringePay;

            // OT hourly wage = basePay * 1.5 + fringePay
            const expectedOtWage = basePay * 1.5 + fringePay;

            // Double OT hourly wage = basePay * 2.0 + fringePay
            const expectedDoubleOtWage = basePay * 2.0 + fringePay;

            // Check that wages are calculated correctly
            const expectedRegularWages = (480 / 60) * expectedHourlyWage;
            const expectedOtWages = (240 / 60) * expectedOtWage;
            const expectedDoubleOtWages = (120 / 60) * expectedDoubleOtWage;

            expect(extendedTimesheets[0].regularWages).to.be.closeTo(expectedRegularWages, 0.01);
            expect(extendedTimesheets[0].overtimeWages).to.be.closeTo(expectedOtWages, 0.01);
            if ("doubleOvertimeWages" in extendedTimesheets[0]) {
              expect(extendedTimesheets[0].doubleOvertimeWages).to.be.closeTo(expectedDoubleOtWages, 0.01);
            }
          });

          it("should handle multiple prevailing wage timesheets across different projects", async () => {
            // Create a second prevailing wage project with a different wage table
            const secondWageTable = await new WageTableBuilder()
              .withOrganization(organization.id)
              .withName("Second Prevailing Wage Table")
              .withDescription("Higher rates wage table")
              .buildAndCreate();

            // Create a classification with higher rates
            const higherClassification = await new ClassificationBuilder()
              .withOrganization(organization.id)
              .withWageTable(secondWageTable.id)
              .withAllocatedPay("45.00", "30.00") // Higher rates
              .buildAndCreate();

            // Create user classification for the higher rate
            const higherUserClassification = await new UserClassificationFactory()
              .withOrganization(organization.id)
              .withWageTable(secondWageTable.id)
              .withClassification(higherClassification.id)
              .withUser(user.id)
              .buildAndCreate();

            // Create second project with higher rates
            const secondProject = await createPrevailingWageProject(organization.id, secondWageTable.id);

            // Create timesheets for both projects on the same day
            const startDate = new Date();

            // Morning shift on first project
            const morningTimesheets = await createTimesheetSeries(
              user.id,
              organization.id,
              project.id,
              regEarningRate.id,
              otEarningRate.id,
              {
                clockInTime: "08:00",
                clockOutTime: "12:00", // 4 hours
                startDate,
                numberOfDays: 1,
              },
              userJourneymanClassification.id
            );

            // Afternoon shift on second project
            const afternoonTimesheets = await createTimesheetSeries(
              user.id,
              organization.id,
              secondProject.id,
              regEarningRate.id,
              otEarningRate.id,
              {
                clockInTime: "13:00",
                clockOutTime: "17:00", // 4 hours
                startDate,
                numberOfDays: 1,
              },
              higherUserClassification.id
            );

            // Combine timesheets
            const allTimesheets = [...morningTimesheets, ...afternoonTimesheets];

            // Convert to ExtendedTimesheet format
            const extendedTimesheets: ExtendedTimesheet[] = allTimesheets.map((ts, index) => {
              const plainTs = ts.get({ plain: true });
              const isAfternoon = index >= morningTimesheets.length;

              return {
                ...plainTs,
                totalMinutes: 240, // 4 hours each
                regEarningRate: {
                  id: regEarningRate.id,
                  amount: regEarningRate.amount,
                  period: regEarningRate.period,
                },
                otEarningRate: {
                  id: otEarningRate.id,
                  amount: otEarningRate.amount,
                  period: otEarningRate.period,
                },
                project: {
                  id: isAfternoon ? secondProject.id : project.id,
                  name: isAfternoon ? secondProject.name : project.name,
                  isPrevailingWage: true,
                },
                userClassification: {
                  id: isAfternoon ? higherUserClassification.id : userJourneymanClassification.id,
                  basePay: isAfternoon ? higherClassification.basePay : journeymanClassification.basePay,
                  fringePay: isAfternoon ? higherClassification.fringePay : journeymanClassification.fringePay,
                },
                fringeBenefitClassifications: [],
                user: config.isPrevailingWage
                  ? {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: user.employeeBenefits || [],
                    }
                  : {
                      id: user.id,
                      earningRates: [regEarningRate, otEarningRate],
                      employeeBenefits: [],
                    },
              };
            });

            // Run the calculation function
            refinedAttributedWeeklyTimesheets(extendedTimesheets, organization);

            // Check that each timesheet uses the appropriate PW rate for its project

            // Morning timesheet - should use first project rates
            const morningSheet = extendedTimesheets[0];
            const morningBasePay = parseFloat(journeymanClassification.basePay);
            const expectedMorningWage = morningBasePay + morningSheet.cashFringeRate;

            // Afternoon timesheet - should use second project rates
            const afternoonSheet = extendedTimesheets[1];
            const afternoonBasePay = parseFloat(higherClassification.basePay);
            const expectedAfternoonWage = afternoonBasePay + afternoonSheet.cashFringeRate;

            // Verify correct rates were used for each timesheet
            expect(morningSheet.hourlyWage).to.be.closeTo(expectedMorningWage, 0.01);
            expect(afternoonSheet.hourlyWage).to.be.closeTo(expectedAfternoonWage, 0.01);

            // Both should be regular time since each is only 4 hours
            expect(morningSheet.regularMinutes).to.equal(240); // 4 hours
            expect(afternoonSheet.regularMinutes).to.equal(240); // 4 hours

            // Check wages are calculated correctly
            expect(morningSheet.regularWages).to.be.closeTo((240 / 60) * expectedMorningWage, 0.01);
            expect(afternoonSheet.regularWages).to.be.closeTo((240 / 60) * expectedAfternoonWage, 0.01);
          });
        });
      }
    });
  });
});
