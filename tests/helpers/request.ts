import { Application } from "express";
import { inject } from "light-my-request";

interface RequestOptions {
  method: "GET" | "POST" | "PATCH" | "PUT" | "DELETE";
  url: string;
  payload?: Record<string, any>;
  headers?: Record<string, string>;
  throwOnError?: boolean
}

export async function makeRequest(app: Application, options: RequestOptions) {
  const { method, url, payload, headers = {}, throwOnError = true } = options;

  return inject(app, {
    method,
    url: `/api/v1/${url}`,
    headers: {
      "Content-Type": "application/json",
      "x-api-key": process.env.API_PUBLIC_TOKEN,
      ...headers,
    },
    ...(payload && { payload: JSON.stringify(payload) }),
  }).then(response => {
    if(throwOnError && response.statusCode >= 400){
      console.error(response.json())
      throw response.json()
    }

    return response
  });
}
