import { startAPI } from "@/routes/api";
import Organization from "@/models/organization";
import User from "@/models/user";
import OvertimeSettings from "@/models/overtimesettings";

interface TestSetupOptions {
  userRole?: "ADMIN" | "WORKER";
  organizationProps?: Partial<Organization>;
  userProps?: Partial<User>;
  authed?: boolean;
}

// Base port number
const BASE_PORT = 3010;

// Generate a random port between BASE_PORT and BASE_PORT + 1000
function getRandomPort(): number {
  return BASE_PORT + Math.floor(Math.random() * 1000);
}

// Keep track of used ports
const usedPorts = new Set<number>();

// Get an available port
function getAvailablePort(): number {
  let port: number;
  do {
    port = getRandomPort();
  } while (usedPorts.has(port));

  usedPorts.add(port);
  return port;
}

export async function setupTestEnvironment(options: TestSetupOptions = {}) {
  const { organizationProps = {}, userProps = {}, authed = true } = options;

  // Create test organization
  const testOrganization = (await Organization.create({
    name: "Test Organization",
    checkCompanyId: "check_company_123",
    ...organizationProps,
  })) as Organization & { overtimeSettings: OvertimeSettings[] };

  // It looks like creating a model instance doesn't automatically reload the default scope
  //  if the child models are created in the .afterCreate model hook
  await testOrganization.reload();

  // Create test user
  const testUser = await User.create({
    organizationId: testOrganization.id,
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    status: "ONBOARDED",
    checkEmployeeId: "123abc",
    ...userProps,
    role: userProps.role ?? "ADMIN",
  });

  const sessionData = {
    user: {
      id: testUser.id,
      role: testUser.role,
      organizationId: testUser.organizationId,
      isAuthenticated: true,
    },
  };

  // Get a unique port for this test
  const port = getAvailablePort();
  process.env.API_PORT = port.toString();

  const app = startAPI((req, _, next) => {
    if (authed) {
      (req.session as any) = req.session ?? {};
      (req.session as any).user = sessionData.user;
    }
    next();
  }, false);

  return {
    app,
    testUser,
    testOrganization,
    cleanup: () => {
      usedPorts.delete(port);
    },
  };
}
