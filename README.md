# Hammr API

API for Hammr Application

## Running locally

### npm install

To install packages

### [Setup Postgres](#postgres)

### npm start

To run project in local-dev mode.

### Enviroment Variables

You can have to copy the `.env.development` to `.env` and customize the `POSTGRESS_DB_URI` variable to your local postgress server, this app is using `dot-env`.

### Add phone number to database

You can't use any arbitrary phone number to test. The phone number has to already exist in the database, since that's how we will limit which users get access to the product.

Install [DBeaver](https://dbeaver.com/) to query the postgres database. Insert your phone number in the `Users` table.

If you wish to test OTP on the same phone number again, DROP all the tables and restart the backend. It will recreate all the tables, so you can insert your phone number again and test.

## Documentation

### Repo documentation

To generate API docs, run `npm run build-documentation` and open `api-docs/index.html` in your browser.

### Postman

All endpoints are documented using Postman. You can read it here: https://documenter.getpostman.com/view/1882607/2s8YChhC8u

Import the Postman collection by clicking on "Run in Postman" button in the top right.

Postman has an environment variable `{{API-SERVER}}` that is set to the AWS URL for testing. Update this to localhost when testing locally.

## Staging

The backend is deployed on Render (render.com).

The backend is deployed on AWS. This is the staging API: staging-7xg8:4000

For querying the staging database and inserting your phone number, use DBeaver with the following settings:
<img width="376" alt="image (3)" src="https://user-images.githubusercontent.com/1585037/198890465-6ed5a10b-7131-4b9c-ae59-8dad5d1945e0.png">

Host: hammr-staging.clnpljl4d73j.us-east-1.rds.amazonaws.com

Password: rlag1Ij7S4ksqazGhRDO

Follow this tutorial for more details if needed: https://aws.amazon.com/getting-started/hands-on/create-connect-postgresql-db/

## Render

1. Render is set to build and deploy staging from `main` and production from `production` branch
2. Route53 has setup punchout.hammr.com to point to staging and api.hammr.com to production on Render

## Customer Data Initial Load

- We can bulk load customer data (employees, projects and cost codes) from a CSV file into our database. [This](https://docs.google.com/spreadsheets/d/1FyPp4W0kT0w-K4-Qr0NiQPvXneBL22cKxi3KI724YXM/edit#gid=0) is where we capture customer data.
- Export the google sheet for the customer you want to load data, into a CSV
- Sanitize all the fields and make them consistent with other existing customer sheers e.g. remove any characters such as periods, dashes and parentheses from phone numbers, commas from all fields
- Place the csv in [/scripts](https://github.com/Hammr-Inc/backend/tree/main/scripts)
- Run `node importOrganization.js customer_data.csv Business_Name` while the backend is running
- Run all the UPTABLE queries listed in [this PR](https://github.com/Hammr-Inc/backend/pull/117) to set `clock_in_reminder_at` and other properties for the newly added organization and it's users

## Postgres

### Install using homebrew

PostgreSQL needs to be installed, if not installed then install it like this.

- `brew install postgresql@14`
- `brew services start postgresql`

We use PostGIS to store location data in TimeSheets and Projects. To install this extension on Postgres, follow these steps:

- `brew install postgis` (using brew, will need to figure out how to install this on RDS)
- `psql postgres --port=5433` to connect to your postgres from terminal
- `\c hammr` to connect to the database from terminal psql
- `CREATE EXTENSION postgis;` to create the extension
- `SELECT PostGIS_Version();` to verify the installation

### Install using docker

```console
docker run -p 5432:5432 -d \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=123 \
    -e POSTGRES_DB=first-db \
    postgis/postgis
```

## Running the backend

- To run the backend, you will need to create an organization, a user and a few other objects
- Contact Sanket to help you setup with these, we will have to follow some steps listed in this PR: https://github.com/Hammr-Inc/backend/pull/655

## Debugging

### Debug staging on local backend

Copy `.env.staging` to `.env` and update domain to `localhost` (so that the cookie issued by the backend has the right domain)

### Tests

The tests need some scaffolding in order to work, mostly creating a dummy DB called `hammr-test` and setting postgis, there's a bash script which will do the setup for you, it only has to be run once.

`npm run setup-tests`

Use the `npm run test` to run the test suite and `npm run coverage` to check the coverage

### Queuing
We are using [Inngest](https://www.inngest.com/) as a queuing service.
Locally, the server can be started using `npm run start:inngest:dev`.
The dev server can be accessed at http://localhost:8288
